# CSV文件中文乱码问题修复说明

## 问题描述
在`parseAndValidateCsv`方法中，CSV文件解析时出现中文乱码，如：
```
"statName": "0GA-����700PAHHO"
```

## 问题原因
原代码使用固定的UTF-8编码读取CSV文件，但实际的CSV文件可能是GBK编码格式，导致中文字符解析错误。

## 解决方案

### 1. 自动编码检测
- 添加了`detectFileEncoding()`方法，自动检测文件编码
- 优先尝试GBK编码（因为旧系统使用GBK）
- 支持多种编码：GBK、UTF-8、GB2312、ISO-8859-1

### 2. 编码验证算法
- 检测替换字符（\uFFFD）
- 检测常见乱码模式
- 验证中文字符范围（0x4E00-0x9FFF）
- 检测明显的乱码字符序列

### 3. 新增接口支持
添加了支持指定编码的新接口：
```
POST /apiWeb/transferNew/zhuanTransportJob/parseAndValidateCsvWithEncoding
```

参数：
- `filePath`: CSV文件路径（必填）
- `encoding`: 文件编码，如"GBK"、"UTF-8"（可选）
- `jobMeta`: 作业元数据（必填）

## 使用方法

### 方法1：自动检测编码（推荐）
```java
// 使用原有接口，现在会自动检测编码
Map<String, Object> result = transportJobNewWebService.parseAndValidateCsv(filePath, jobMeta);
```

### 方法2：指定编码
```java
// 如果自动检测失败，可以手动指定编码
Map<String, Object> result = transportJobNewWebService.parseAndValidateCsv(filePath, jobMeta, "GBK");
```

### 方法3：通过新接口指定编码
```bash
curl -X POST "http://localhost:8011/apiWeb/transferNew/zhuanTransportJob/parseAndValidateCsvWithEncoding" \
  -H "Content-Type: application/json" \
  -d '{
    "filePath": "/path/to/your/file.csv",
    "encoding": "GBK",
    "jobMeta": {
      "jobId": "123",
      "jobBranchId": "456"
    }
  }'
```

## 返回结果
修复后的返回结果会包含检测到的编码信息：
```json
{
  "success": true,
  "data": {
    "validRows": [...],
    "invalidRows": [...]
  },
  "detectedEncoding": "GBK"
}
```

## 编码检测优先级
1. **GBK** - 优先检测，因为旧系统使用GBK
2. **UTF-8** - 标准Unicode编码
3. **GB2312** - 简体中文编码
4. **ISO-8859-1** - 西欧编码

## 测试建议
1. 准备包含中文的GBK编码CSV文件
2. 测试自动检测功能
3. 测试手动指定编码功能
4. 验证中文字符是否正确显示

## 注意事项
- 编码检测基于文件前4KB内容
- 如果文件很小或不包含中文字符，可能需要手动指定编码
- 建议在生产环境中监控编码检测的准确性
- 控制台会输出检测到的编码信息，便于调试

## 兼容性
- 保持了原有接口的兼容性
- 新增的编码检测功能不会影响现有功能
- 如果编码检测失败，会回退到GBK编码
