# TRANSPORT_FILE_NEW表和服务类使用说明

## 功能概述

TRANSPORT_FILE_NEW表用于存储传输任务相关的文件信息，包括CSV数据文件和各种材料附件。支持多种文件格式，提供完整的文件管理功能。

## 数据库表结构

### 主要字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | NUMBER(19) | 主键ID | 1001 |
| job_id | NUMBER(19) | 关联的任务ID | 123 |
| file_local_name | VARCHAR2(255) | 原始文件名称 | "数据文件.csv" |
| file_renamed | VARCHAR2(255) | 重命名后的文件名称 | "数据文件_20240101120000001.csv" |
| file_path | VARCHAR2(500) | 文件存储路径 | "/upload/files/数据文件_20240101120000001.csv" |
| file_size | NUMBER(19) | 文件大小（字节） | 1048576 |
| file_state | NUMBER(2) | 文件状态 | 1 |
| file_type | VARCHAR2(10) | 文件类型 | "1" |
| data_type | VARCHAR2(50) | 数据类型 | "全量" |
| file_extension | VARCHAR2(20) | 文件扩展名 | "csv" |
| mime_type | VARCHAR2(100) | 文件MIME类型 | "text/csv" |
| file_md5 | VARCHAR2(32) | 文件MD5值 | "D41D8CD98F00B204E9800998ECF8427E" |
| is_deleted | NUMBER(2) | 是否删除 | 0 |
| uploaded_by | VARCHAR2(100) | 上传者 | "admin" |
| file_description | VARCHAR2(500) | 文件描述 | "基站数据文件" |

### 字段值说明

**file_state（文件状态）**：
- 0：创建
- 1：上传成功
- 2：处理完成
- 3：处理异常

**file_type（文件类型）**：
- 0：附件
- 1：CSV数据文件
- 2：材料附件

**is_deleted（删除标志）**：
- 0：未删除
- 1：已删除

## 支持的文件格式

### 文档类型
- **txt**: 文本文件
- **doc**: Word文档（旧版）
- **docx**: Word文档（新版）
- **pdf**: PDF文档

### 表格类型
- **xls**: Excel表格（旧版）
- **xlsx**: Excel表格（新版）
- **csv**: CSV数据文件

### 图片类型
- **jpg/jpeg**: JPEG图片
- **png**: PNG图片

## API接口说明

### 1. 文件上传

**接口地址**：`POST /apiWeb/transferNew/transportFile/upload`

**请求参数**：
- `file`: MultipartFile - 上传的文件（必填）
- `jobId`: Long - 关联的任务ID（必填）
- `fileType`: String - 文件类型（必填）
- `dataType`: String - 数据类型（可选）
- `uploadedBy`: String - 上传者（可选，默认admin）
- `fileDescription`: String - 文件描述（可选）

**请求示例**：
```bash
curl -X POST "http://localhost:8011/apiWeb/transferNew/transportFile/upload" \
  -F "file=@data.csv" \
  -F "jobId=123" \
  -F "fileType=1" \
  -F "dataType=全量" \
  -F "uploadedBy=admin" \
  -F "fileDescription=基站数据文件"
```

**返回结果**：
```json
{
  "success": true,
  "data": {
    "id": 1001,
    "jobId": 123,
    "fileLocalName": "data.csv",
    "fileRenamed": "data_20240101120000001.csv",
    "filePath": "/upload/files/data_20240101120000001.csv",
    "fileSize": 1048576,
    "fileState": 1,
    "fileType": "1",
    "dataType": "全量",
    "fileExtension": "csv",
    "mimeType": "text/csv",
    "fileMd5": "D41D8CD98F00B204E9800998ECF8427E",
    "uploadedBy": "admin",
    "fileDescription": "基站数据文件",
    "createdAt": "2024-01-01T12:00:00"
  }
}
```

### 2. 查询文件列表

**接口地址**：`GET /apiWeb/transferNew/transportFile/findByJobId`

**请求参数**：
- `jobId`: Long - 任务ID（必填）

**请求示例**：
```bash
curl -X GET "http://localhost:8011/apiWeb/transferNew/transportFile/findByJobId?jobId=123"
```

### 3. 按类型查询文件

**接口地址**：`GET /apiWeb/transferNew/transportFile/findByJobIdAndFileType`

**请求参数**：
- `jobId`: Long - 任务ID（必填）
- `fileType`: String - 文件类型（必填）

### 4. 分页查询文件

**接口地址**：`POST /apiWeb/transferNew/transportFile/findPageWithConditions`

**请求体**：
```json
{
  "jobId": 123,
  "fileType": "1",
  "fileState": 1,
  "fileLocalName": "data",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "page": 1,
  "rows": 10
}
```

### 5. 更新文件状态

**接口地址**：`PUT /apiWeb/transferNew/transportFile/updateFileState`

**请求参数**：
- `id`: Long - 文件ID（必填）
- `fileState`: Integer - 新状态（必填）

### 6. 删除文件

**接口地址**：`DELETE /apiWeb/transferNew/transportFile/delete`

**请求参数**：
- `id`: Long - 文件ID（必填）

### 7. 统计文件数量

**接口地址**：`GET /apiWeb/transferNew/transportFile/countByJobIdGroupByFileType`

**请求参数**：
- `jobId`: Long - 任务ID（必填）

## 服务类使用

### 在代码中使用服务

```java
@Autowired
private TransportFileNewWebService transportFileNewWebService;

// 上传文件
Map<String, Object> uploadResult = transportFileNewWebService.uploadFile(
    file, jobId, "1", "全量", "admin", "基站数据文件"
);

// 查询文件列表
Map<String, Object> fileList = transportFileNewWebService.findByJobId(123L);

// 更新文件状态
Map<String, Object> updateResult = transportFileNewWebService.updateFileState(1001L, 2);
```

## 文件安全特性

### 1. 文件类型校验
- 严格限制支持的文件扩展名
- 检查文件MIME类型
- 防止恶意文件上传

### 2. 文件大小限制
- 默认限制文件大小为100MB
- 可通过配置调整大小限制

### 3. 重复文件检测
- 通过MD5值检测重复文件
- 避免重复存储相同文件

### 4. 文件重命名
- 自动生成唯一文件名
- 避免文件名冲突
- 保留原始文件名信息

### 5. 软删除机制
- 使用is_deleted字段标记删除
- 保留文件历史记录
- 支持数据恢复

## 配置说明

### 文件存储路径配置

在`application.yml`中配置：
```yaml
caict:
  myFilePath: D:/caict/bsm/4.0/file/
```

### 文件大小限制配置

在`application.yml`中配置：
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
```

## 注意事项

1. **文件存储**：确保配置的文件存储目录有足够的磁盘空间
2. **权限控制**：建议添加用户权限验证
3. **文件清理**：定期清理已删除的文件
4. **备份策略**：重要文件建议定期备份
5. **监控告警**：监控文件上传失败和存储空间使用情况

## 扩展功能建议

1. **文件预览**：添加文件在线预览功能
2. **文件下载**：添加文件下载接口
3. **文件压缩**：支持文件压缩存储
4. **云存储**：支持云存储服务（如阿里云OSS）
5. **文件版本**：支持文件版本管理
6. **文件分享**：支持文件分享链接生成
