-- 创建TRANSPORT_FILE_NEW表
CREATE TABLE TRANSPORT_FILE_NEW (
    id NUMBER(19) PRIMARY KEY,
    job_id NUMBER(19) NOT NULL,
    file_local_name VARCHAR2(255) NOT NULL,
    file_renamed VARCHAR2(255) NOT NULL,
    file_path VARCHAR2(500) NOT NULL,
    file_size NUMBER(19) NOT NULL,
    file_state NUMBER(2) DEFAULT 0 NOT NULL,
    file_type VARCHAR2(10) NOT NULL,
    data_type VARCHAR2(50),
    file_extension VARCHAR2(20),
    mime_type VARCHAR2(100),
    file_md5 VARCHAR2(32),
    is_deleted NUMBER(2) DEFAULT 0 NOT NULL,
    uploaded_by VARCHAR2(100),
    file_description VARCHAR2(500),
    created_at DATE DEFAULT SYSDATE NOT NULL,
    updated_at DATE DEFAULT SYSDATE NOT NULL
);

-- 添加注释
COMMENT ON TABLE TRANSPORT_FILE_NEW IS '传输文件表（新版本）';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.id IS '主键ID';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.job_id IS '关联的任务ID';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_local_name IS '原始文件名称';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_renamed IS '重命名后的文件名称';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_path IS '文件存储路径';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_size IS '文件大小（字节）';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_state IS '文件状态（0-创建，1-上传成功，2-处理完成，3-处理异常）';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_type IS '文件类型（0-附件，1-CSV数据文件，2-材料附件）';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.data_type IS '数据类型（全量、增量、注销等）';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_extension IS '文件扩展名';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.mime_type IS '文件MIME类型';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_md5 IS '文件MD5值';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.is_deleted IS '是否删除（0-未删除，1-已删除）';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.uploaded_by IS '上传者';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.file_description IS '文件描述';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.created_at IS '创建时间';
COMMENT ON COLUMN TRANSPORT_FILE_NEW.updated_at IS '更新时间';

-- 创建索引
CREATE INDEX IDX_TRANSPORT_FILE_NEW_JOB_ID ON TRANSPORT_FILE_NEW(job_id);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_FILE_TYPE ON TRANSPORT_FILE_NEW(file_type);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_FILE_STATE ON TRANSPORT_FILE_NEW(file_state);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_MD5 ON TRANSPORT_FILE_NEW(file_md5);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_CREATED_AT ON TRANSPORT_FILE_NEW(created_at);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_IS_DELETED ON TRANSPORT_FILE_NEW(is_deleted);

-- 创建复合索引
CREATE INDEX IDX_TRANSPORT_FILE_NEW_JOB_TYPE ON TRANSPORT_FILE_NEW(job_id, file_type, is_deleted);

-- 创建序列（如果使用Oracle数据库）
CREATE SEQUENCE SEQ_TRANSPORT_FILE_NEW
    START WITH 1
    INCREMENT BY 1
    NOMAXVALUE
    NOCYCLE
    CACHE 20;

-- 创建触发器自动生成ID（如果使用Oracle数据库）
CREATE OR REPLACE TRIGGER TRG_TRANSPORT_FILE_NEW_ID
    BEFORE INSERT ON TRANSPORT_FILE_NEW
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT SEQ_TRANSPORT_FILE_NEW.NEXTVAL INTO :NEW.id FROM DUAL;
    END IF;
END;
/

-- 创建触发器自动更新updated_at字段
CREATE OR REPLACE TRIGGER TRG_TRANSPORT_FILE_NEW_UPDATE
    BEFORE UPDATE ON TRANSPORT_FILE_NEW
    FOR EACH ROW
BEGIN
    :NEW.updated_at := SYSDATE;
END;
/
