package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtApplySyncRepository;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtApplySyncRepositoryI extends BasicRepositoryI<RsbtApply, JdbcTemplate> implements RsbtApplySyncRepository {
    @Override
    public List<RsbtApply> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_Apply " +
                    "where APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtApply.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtApply> rsbtApplyList, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_APPLY(GUID,ORG_GUID,NET_GUID,ORG_MANAGER_GUID,APP_Code,APP_Type,APP_SUB_Type,APP_Object_Type,APP_Date," +
                "APP_FTLB,APP_FTLE,MEMO) " +
                " values(?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, rsbtApplyList.get(i).getGuid());
                    ps.setString(2, rsbtApplyList.get(i).getOrgGuid());
                    ps.setString(3, rsbtApplyList.get(i).getNetGuid());
                    ps.setString(4, rsbtApplyList.get(i).getOrgManagerGuid());
                    ps.setString(5, rsbtApplyList.get(i).getAppCode());
                    ps.setString(6, rsbtApplyList.get(i).getAppType());
                    ps.setString(7, rsbtApplyList.get(i).getAppSubType());
                    ps.setString(8, rsbtApplyList.get(i).getAppObjectType());
                    ps.setDate(9, rsbtApplyList.get(i).getAppDate() != null ? new java.sql.Date(rsbtApplyList.get(i).getAppDate().getTime()) : null);
                    ps.setDate(10, rsbtApplyList.get(i).getAppFtlb() != null ? new java.sql.Date(rsbtApplyList.get(i).getAppFtlb().getTime()) : null);
                    ps.setDate(11, rsbtApplyList.get(i).getAppFtle() != null ? new java.sql.Date(rsbtApplyList.get(i).getAppFtle().getTime()) : null);
                    ps.setString(12, rsbtApplyList.get(i).getMemo());
                }

                @Override
                public int getBatchSize() {
                    return rsbtApplyList.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return rsbtApplyList.size();
    }

    @Override
    public String findMaxAppCode(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select max(app_code) from RSBT_STATION ";
            return jdbcTemplate.queryForObject(sql, String.class);
        } catch (Exception e) {
            return null;
        }
    }
}
