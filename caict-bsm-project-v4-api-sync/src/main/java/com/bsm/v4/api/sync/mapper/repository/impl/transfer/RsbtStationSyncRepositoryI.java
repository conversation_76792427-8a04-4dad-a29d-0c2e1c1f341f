package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtStationSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtStationSyncRepositoryI extends BasicRepositoryI<RsbtStation, JdbcTemplate> implements RsbtStationSyncRepository {
    @Override
    public List<RsbtStation> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_Station " +
                    "where APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtStation.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtStation> rsbtStationSyncList, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_STATION(GUID,NET_GUID,ORG_CODE,APP_CODE,STAT_APP_TYPE,STAT_TDI,STAT_NAME,STAT_ADDR,STAT_AREA_CODE,STAT_TYPE," +
                "STAT_WORK,STAT_STATUS,STAT_EQU_SUM,STAT_LG,STAT_LA,STAT_AT,STAT_DATE_START,MEMO) " +
                " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, rsbtStationSyncList.get(i).getGuid());
                    ps.setString(2, rsbtStationSyncList.get(i).getNetGuid());
                    ps.setString(3, rsbtStationSyncList.get(i).getOrgCode());
                    ps.setString(4, rsbtStationSyncList.get(i).getAppCode());
                    ps.setString(5, rsbtStationSyncList.get(i).getStatAppType());
                    ps.setString(6, rsbtStationSyncList.get(i).getStatTdi());
                    ps.setString(7, rsbtStationSyncList.get(i).getStatName());
                    ps.setString(8, rsbtStationSyncList.get(i).getStatAddr());
                    ps.setString(9, rsbtStationSyncList.get(i).getStatAreaCode());
                    ps.setString(10, rsbtStationSyncList.get(i).getStatType());
                    ps.setString(11, rsbtStationSyncList.get(i).getStatWork());
                    ps.setString(12, rsbtStationSyncList.get(i).getStatStatus());
                    ps.setInt(13, rsbtStationSyncList.get(i).getStatEquSum());
                    ps.setDouble(14, rsbtStationSyncList.get(i).getStatLg());
                    ps.setDouble(15, rsbtStationSyncList.get(i).getStatLa());
                    ps.setDouble(16, rsbtStationSyncList.get(i).getStatAt());
                    ps.setDate(17, rsbtStationSyncList.get(i).getStatDateStart() != null ? new Date(rsbtStationSyncList.get(i).getStatDateStart().getTime()) : null);
                    ps.setString(18, rsbtStationSyncList.get(i).getMemo());
                }

                @Override
                public int getBatchSize() {
                    return rsbtStationSyncList.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return rsbtStationSyncList.size();
    }

    @Override
    public int batchUpdateByGuid(List<RsbtStation> stations, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_STATION SET NET_GUID=?,ORG_CODE=?,APP_CODE=?,STAT_APP_TYPE=?,STAT_TDI=?,STAT_NAME=?,STAT_ADDR=?,STAT_AREA_CODE=?,STAT_TYPE=?," +
                "STAT_WORK=?,STAT_STATUS=?,STAT_EQU_SUM=?,STAT_LG=?,STAT_LA=?,STAT_AT=?,STAT_DATE_START=?,MEMO=? " +
                "where guid = ? ";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, stations.get(i).getNetGuid());
                    ps.setString(2, stations.get(i).getOrgCode());
                    ps.setString(3, stations.get(i).getAppCode());
                    ps.setString(4, stations.get(i).getStatAppType());
                    ps.setString(5, stations.get(i).getStatTdi());
                    ps.setString(6, stations.get(i).getStatName());
                    ps.setString(7, stations.get(i).getStatAddr());
                    ps.setString(8, stations.get(i).getStatAreaCode());
                    ps.setString(9, stations.get(i).getStatType());
                    ps.setString(10, stations.get(i).getStatWork());
                    ps.setString(11, stations.get(i).getStatStatus());
                    ps.setInt(12, stations.get(i).getStatEquSum());
                    ps.setDouble(13, stations.get(i).getStatLg());
                    ps.setDouble(14, stations.get(i).getStatLa());
                    ps.setDouble(15, stations.get(i).getStatAt());
                    ps.setDate(16, stations.get(i).getStatDateStart() != null ? new Date(stations.get(i).getStatDateStart().getTime()) : null);
                    ps.setString(17, stations.get(i).getMemo());
                    ps.setString(18, stations.get(i).getGuid());
                }

                @Override
                public int getBatchSize() {
                    return stations.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return stations.size();
    }

    @Override
    public RsbtStation findOneByStatName(String statName, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_Station " +
                    "where stat_name = ?  ";
            Object[] objects = {statName};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<>(RsbtStation.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }
}
