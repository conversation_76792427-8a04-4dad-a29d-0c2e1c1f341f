package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtEafSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtEaf;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtEafSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtEafSyncRepository eafSyncRepository;

    public List<RsbtEaf> findByAppCode(String appCode) {
        return eafSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtEaf> eafs) {
        return eafSyncRepository.batchInsert(eafs, oracleStandardJdbcTemplate);
    }
}
