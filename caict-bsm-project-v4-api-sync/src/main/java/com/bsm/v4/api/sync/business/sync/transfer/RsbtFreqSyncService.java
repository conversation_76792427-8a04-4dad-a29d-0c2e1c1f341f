package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtFreqSyncRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.FreqAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtFreqSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtFreqSyncRepository freqSyncRepository;

    public List<RsbtFreq> findByAppCode(String appCode) {
        return freqSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public List<FreqAllDTO> findAllByAppCode(String appCode) {
        return freqSyncRepository.findAllByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtFreq> freqs) {
        return freqSyncRepository.batchInsert(freqs, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByFtCode(List<FreqAllDTO> freqs) {
        return freqSyncRepository.batchUpdateByFtCode(freqs, oracleStandardJdbcTemplate);
    }
}
