package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtLicenseSyncRepository;
import com.bsm.v4.system.model.dto.business.license.LicensePdfDTO;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfChildrenDTO;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfDTO;
import com.bsm.v4.system.model.dto.business.station.ISectionDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtLicenseSyncRepositoryI extends BasicRepositoryI<RsbtLicense, JdbcTemplate> implements RsbtLicenseSyncRepository {
    @Override
    public List<RsbtLicense> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_LICENSE  where APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<RsbtLicense>(RsbtLicense.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public RsbtLicense findByStatGuid(String statGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_LICENSE  where station_guid = ?  ";
            Object[] objects = {statGuid};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<RsbtLicense>(RsbtLicense.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtLicense> licenses, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_LICENSE(GUID,STATION_GUID,APP_CODE,STAT_TDI,STAT_APP_TYPE,LICENSE_TYPE,LICENSE_CODE,LICENSE_ORG_NAME,LICENSE_MANAGER," +
                "LICENSE_DATE_B,LICENSE_DATE_E,LICENSE_DATE,MEMO)   values(?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, licenses.get(i).getGuid());
                    ps.setString(2, licenses.get(i).getStationGuid());
                    ps.setString(3, licenses.get(i).getAppCode());
                    ps.setString(4, licenses.get(i).getStatTdi());
                    ps.setString(5, licenses.get(i).getStatAppType());
                    ps.setString(6, licenses.get(i).getLicenseType());
                    ps.setString(7, licenses.get(i).getLicenseCode());
                    ps.setString(8, licenses.get(i).getLicenseOrgName());
                    ps.setString(9, licenses.get(i).getLicenseManager());
                    ps.setDate(10, licenses.get(i).getLicenseDateB() != null ? new java.sql.Date(licenses.get(i).getLicenseDateB().getTime()) : null);
                    ps.setDate(11, licenses.get(i).getLicenseDateE() != null ? new java.sql.Date(licenses.get(i).getLicenseDateE().getTime()) : null);
                    ps.setDate(12, licenses.get(i).getLicenseDate() != null ? new java.sql.Date(licenses.get(i).getLicenseDate().getTime()) : null);
                    ps.setString(13, licenses.get(i).getMemo());
                }

                @Override
                public int getBatchSize() {
                    return licenses.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return licenses.size();
    }

    @Override
    public int batchUpdateByStatGuid(List<RsbtLicense> licenses, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_LICENSE SET STATION_GUID=?,APP_CODE=?,STAT_TDI=?,STAT_APP_TYPE=?,LICENSE_TYPE=?,LICENSE_CODE=?,LICENSE_ORG_NAME=?,LICENSE_MANAGER=?," +
                "LICENSE_DATE_B=?,LICENSE_DATE_E=?,LICENSE_DATE=?,MEMO=?) " +
                "where station_guid =? ";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, licenses.get(i).getStationGuid());
                    ps.setString(2, licenses.get(i).getAppCode());
                    ps.setString(3, licenses.get(i).getStatTdi());
                    ps.setString(4, licenses.get(i).getStatAppType());
                    ps.setString(5, licenses.get(i).getLicenseType());
                    ps.setString(6, licenses.get(i).getLicenseCode());
                    ps.setString(7, licenses.get(i).getLicenseOrgName());
                    ps.setString(8, licenses.get(i).getLicenseManager());
                    ps.setDate(9, licenses.get(i).getLicenseDateB() != null ? new java.sql.Date(licenses.get(i).getLicenseDateB().getTime()) : null);
                    ps.setDate(10, licenses.get(i).getLicenseDateE() != null ? new java.sql.Date(licenses.get(i).getLicenseDateE().getTime()) : null);
                    ps.setDate(11, licenses.get(i).getLicenseDate() != null ? new java.sql.Date(licenses.get(i).getLicenseDate().getTime()) : null);
                    ps.setString(12, licenses.get(i).getMemo());
                    ps.setString(13, licenses.get(i).getStationGuid());
                }

                @Override
                public int getBatchSize() {
                    return licenses.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return licenses.size();
    }

    public int selectCountAll(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select count(*) from RSBT_LICENSE";
            return jdbcTemplate.queryForObject(sql, Integer.class);
        } catch (Exception e) {
            return 0;
        }
    }

    public List<RsbtLicense> findAllPage(int rowStart, int rowEnd, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from (select RSBT_LICENSE.*,ROWNUM rn from RSBT_LICENSE where ROWNUM <= ?) where rn > ?";
            Object[] objects = {rowEnd, rowStart};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<RsbtLicense>(RsbtLicense.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    public int selectLicensePdfDto(String licenseCode, String stationCode, String stationName, Date licenseStartDate, Date licenseEndDate, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select count(*) from (" +
                    "select RSBT_LICENSE.Station_GUID as stationGuid,RSBT_LICENSE.License_Code as linceseCode,RSBT_LICENSE.License_Date_B as linceseStartDate,RSBT_LICENSE.License_Date_E as linceseEndDate," +
                    "RSBT_STATION.STAT_Name as staName,RSBT_STATION_T.ST_C_Code as idenCode,RSBT_ORG.ORG_Name as userName,RSBT_ORG.ORG_Code as orgCode,RSBT_STATION.STAT_ADDR as location," +
                    "RSBT_STATION.STAT_LG as longitude,RSBT_STATION.STAT_LA as latitude,RSBT_LICENSE.MEMO as specialCase,ROWNUM rn " +
                    "from RSBT_LICENSE " +
                    "inner join RSBT_STATION on RSBT_LICENSE.Station_GUID = RSBT_STATION.GUID " +
                    "inner join RSBT_APPLY on RSBT_APPLY.APP_Code = RSBT_LICENSE.APP_CODE and RSBT_APPLY.APP_Code = RSBT_STATION.APP_Code " +
                    "inner join RSBT_ORG on RSBT_APPLY.ORG_GUID = RSBT_ORG.GUID  " +
                    "inner join RSBT_STATION_T on RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
                    "where (RSBT_LICENSE.License_Code = ? or ? is null) and (RSBT_STATION_T.ST_C_Code = ? or ? is null) and (RSBT_STATION.STAT_Name = ? or ? is null) " +
                    "and (RSBT_LICENSE.License_Date_B >= ? or ? is null) and (RSBT_LICENSE.License_Date_E <= ? or ? is null)" +
                    ")";
            Object[] objects = {licenseCode, licenseCode, stationCode, stationCode, stationName, stationName, licenseStartDate, licenseStartDate, licenseEndDate, licenseEndDate};
            return jdbcTemplate.queryForObject(sql, objects, Integer.class);
        } catch (Exception e) {
            return 0;
        }
    }

    public List<RsbtLicensePdfDTO> findAllPdfDto(String licenseCode, String stationCode, String stationName, Date licenseStartDate, Date licenseEndDate, int rowStart, int rowEnd, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from (select a.*,ROWNUM rn from (" +
                    "select RSBT_LICENSE.Station_GUID as stationGuid,RSBT_LICENSE.License_Code as linceseCode,RSBT_LICENSE.License_Date_B as linceseStartDate,RSBT_LICENSE.License_Date_E as linceseEndDate," +
                    "RSBT_STATION.STAT_Name as staName,RSBT_STATION_T.ST_C_Code as idenCode,RSBT_ORG.ORG_Name as userName,RSBT_ORG.ORG_Code as orgCode,RSBT_STATION.STAT_ADDR as location," +
                    "RSBT_STATION.STAT_LG as longitude,RSBT_STATION.STAT_LA as latitude,RSBT_LICENSE.MEMO as specialCase " +
                    "from RSBT_LICENSE " +
                    "inner join RSBT_STATION on RSBT_LICENSE.Station_GUID = RSBT_STATION.GUID " +
                    "inner join RSBT_APPLY on RSBT_APPLY.APP_Code = RSBT_LICENSE.APP_CODE and RSBT_APPLY.APP_Code = RSBT_STATION.APP_Code " +
                    "inner join RSBT_ORG on RSBT_APPLY.ORG_GUID = RSBT_ORG.GUID  " +
                    "inner join RSBT_STATION_T on RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
                    "where (RSBT_LICENSE.License_Code = ? or ? is null) and (RSBT_STATION_T.ST_C_Code = ? or ? is null) and (RSBT_STATION.STAT_Name = ? or ? is null) " +
                    "and (RSBT_LICENSE.License_Date >= ? or ? is null) and (RSBT_LICENSE.License_Date <= ? or ? is null) " +
                    ") a where ROWNUM <= ?) where rn > ?";
            Object[] objects = {licenseCode, licenseCode, stationCode, stationCode, stationName, stationName, licenseStartDate, licenseStartDate, licenseEndDate, licenseEndDate, rowEnd, rowStart};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtLicensePdfDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    public List<RsbtLicensePdfChildrenDTO> findPdfChildrenDto(String stationGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select RSBT_APPFILELIST.APP_File_NO as freqPass,(RSBT_FREQ.FREQ_EFB ||'~'|| RSBT_FREQ.FREQ_EFE) as freqEf,(RSBT_FREQ.FREQ_RFB ||'~'|| RSBT_FREQ.FREQ_RFE) as freqRf," +
                    "RSBT_EQU.EQU_POW as equPow,RSBT_FREQ.FREQ_E_Band as freqBand,RSBT_EQU.EQU_AUTH as equAuth,RSBT_ANTFEED.ANT_Gain as antGain,RSBT_ANTFEED.ANT_POLE as antEpole,RSBT_ANTFEED.ANT_Hight as antHight " +
                    "from RSBT_STATION,RSBT_APPFILELIST,RSBT_EQU,RSBT_ANTFEED,RSBT_FREQ,RSBT_EQU_T,RSBT_ANTFEED_T,RSBT_FREQ_T " +
                    "where RSBT_STATION.APP_Code = RSBT_APPFILELIST.APP_Code and RSBT_STATION.GUID = RSBT_EQU.Station_GUID and RSBT_STATION.GUID = RSBT_ANTFEED.Station_GUID and RSBT_STATION.GUID = RSBT_FREQ.Station_GUID " +
                    "and RSBT_EQU.GUID = RSBT_EQU_T.GUID and RSBT_ANTFEED.GUID = RSBT_ANTFEED_T.GUID and RSBT_FREQ.GUID = RSBT_FREQ_T.GUID " +
                    "and RSBT_EQU_T.ET_EQU_CCode = RSBT_ANTFEED_T.AT_CCode and RSBT_EQU_T.ET_EQU_CCode = RSBT_FREQ_T.FT_FREQ_CCode " +
                    "and RSBT_STATION.GUID = ?";
            Object[] objects = {stationGuid};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtLicensePdfChildrenDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<ISectionDTO> findPdfSectionDto(String stationGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select RSBT_APPFILELIST.APP_File_NO as freqPass,(RSBT_FREQ.FREQ_EFB ||'~'|| RSBT_FREQ.FREQ_EFE) as freqEf,(RSBT_FREQ.FREQ_RFB ||'~'|| RSBT_FREQ.FREQ_RFE) as freqRf," +
                    "RSBT_EQU.EQU_POW as equPow,RSBT_FREQ.FREQ_E_Band as freqBand,RSBT_EQU.EQU_AUTH as equAuth,RSBT_ANTFEED.ANT_Gain as antGain,RSBT_ANTFEED.ANT_POLE as antEpole,RSBT_ANTFEED.ANT_Hight as antHight " +
                    "from RSBT_STATION,RSBT_APPFILELIST,RSBT_EQU,RSBT_ANTFEED,RSBT_FREQ,RSBT_EQU_T,RSBT_ANTFEED_T,RSBT_FREQ_T " +
                    "where RSBT_STATION.APP_Code = RSBT_APPFILELIST.APP_Code and RSBT_STATION.GUID = RSBT_EQU.Station_GUID and RSBT_STATION.GUID = RSBT_ANTFEED.Station_GUID and RSBT_STATION.GUID = RSBT_FREQ.Station_GUID " +
                    "and RSBT_EQU.GUID = RSBT_EQU_T.GUID and RSBT_ANTFEED.GUID = RSBT_ANTFEED_T.GUID and RSBT_FREQ.GUID = RSBT_FREQ_T.GUID " +
                    "and RSBT_EQU_T.ET_EQU_CCode = RSBT_ANTFEED_T.AT_CCode and RSBT_EQU_T.ET_EQU_CCode = RSBT_FREQ_T.FT_FREQ_CCode " +
                    "and RSBT_STATION.GUID = ?";
            Object[] objects = {stationGuid};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(ISectionDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    public LicensePdfDTO findOnePdfDto(String stationGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select distinct RSBT_LICENSE.Station_GUID as stationGuid,RSBT_LICENSE.License_Code as licenseCode,RSBT_LICENSE.License_Date_B as licenseStartDate,RSBT_LICENSE.License_Date_E as licenseEndDate," +
                    "RSBT_STATION.STAT_Name as stationName,RSBT_STATION_T.ST_C_Code as stationCode,RSBT_ORG.ORG_Name as licensee,RSBT_ORG.ORG_Code as licenseeNo,RSBT_STATION.STAT_ADDR as statAddr," +
                    "RSBT_STATION.STAT_LG as longitude,RSBT_STATION.STAT_LA as latitude,RSBT_LICENSE.MEMO as specialCase " +
                    "from RSBT_LICENSE " +
                    "left join RSBT_STATION on RSBT_LICENSE.Station_GUID = RSBT_STATION.GUID " +
                    "left join RSBT_APPLY on RSBT_APPLY.APP_Code = RSBT_LICENSE.APP_CODE and RSBT_APPLY.APP_Code = RSBT_STATION.APP_Code " +
                    "left join RSBT_ORG on RSBT_APPLY.ORG_GUID = RSBT_ORG.GUID  " +
                    "left join RSBT_STATION_T on RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
                    "where RSBT_LICENSE.Station_GUID = ?";
            Object[] objects = {stationGuid};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<>(LicensePdfDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }
}
