package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

public interface RsbtStationBakSyncRepository extends BasicRepository<RsbtStationBak, JdbcTemplate> {

    List<ApplyJobInDTO> findCountByAppGuid(String appGuid, JdbcTemplate jdbcTemplate);

    /**
     * 查询为审核的数据
     *
     * @param appGuid
     * @return
     */
    int findStationCountByAppGuid(String appGuid, JdbcTemplate jdbcTemplate);

    int updateByAppGuid(String appGuid, String status, JdbcTemplate jdbcTemplate);
}
