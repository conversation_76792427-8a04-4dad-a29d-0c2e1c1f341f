package com.bsm.v4.api.sync.mapper.repository.impl.zy;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.zy.SyncZyDataRepository;
import com.bsm.v4.system.model.dto.business.transfer.SyncZyDataDTO;
import com.bsm.v4.system.model.entity.business.transfer.SyncZyData;
import com.bsm.v4.system.model.entity.business.transfer.SyncZyDataLog;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * SyncZyDataRepositoryI. 同步数据到总院 数据访问
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 7/13/21 9:29 AM
 **/
@Repository
public class SyncZyDataRepositoryI extends BasicRepositoryI<SyncZyData, JdbcTemplate> implements SyncZyDataRepository {

    /**
     * 根据guids查询数据
     *
     * @param syncZyDataDTO syncZyDataDTO
     * @param jdbcTemplate
     * @return list
     */
    @Override
    public List<SyncZyData> findDataByGuids(SyncZyDataDTO syncZyDataDTO, JdbcTemplate jdbcTemplate) {
        try {
            String[] guids = syncZyDataDTO.getGuids();
            Object[] objects = new Object[guids.length + 1];
            StringBuilder sql = new StringBuilder(" select SYNC_ZY_DATA.* from SYNC_ZY_DATA where SYNC_ZY_DATA.JOB_GUID = ? and SYNC_ZY_DATA.GUID IN (");
            objects[0] = syncZyDataDTO.getJobGuid();
            for (int i = 0; i < guids.length; i++) {
                objects[i + 1] = guids[i];
                if (i == guids.length - 1) {
                    sql.append("?");
                } else {
                    sql.append("?, ");
                }
            }
            sql.append(")");
            return jdbcTemplate.query(sql.toString(), objects, new BeanPropertyRowMapper<>(SyncZyData.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据jobGuid查询数据
     *
     * @param syncZyDataDTO syncZyDataDTO
     * @return list
     */
    @Override
    public List<SyncZyData> findDataByJobGuid(SyncZyDataDTO syncZyDataDTO, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from (select a.*,ROWNUM rn from (select SYNC_ZY_DATA.* from SYNC_ZY_DATA " +
                    "where IS_SYNC = 1 and SYNC_ZY_DATA.JOB_GUID = ?) a where ROWNUM <= ?) where rn > ? ";
            Object[] objects = {syncZyDataDTO.getJobGuid(), syncZyDataDTO.getRows() * syncZyDataDTO.getPage(), syncZyDataDTO.getRows() * (syncZyDataDTO.getPage() - 1)};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(SyncZyData.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查询是否有数据需要同步
     *
     * @param jdbcTemplate jdbcTemplate
     * @return list
     */
    @Override
    public List<String> findData(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select SYNC_ZY_DATA.JOB_GUID from SYNC_ZY_DATA group by SYNC_ZY_DATA.JOB_GUID ";
            Object[] objects = {};
            return jdbcTemplate.queryForList(sql, objects, String.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据jobGuid查询条数
     *
     * @param jobGuid      jobGuid
     * @param jdbcTemplate
     * @return int
     */
    @Override
    public int selectCountByJobGuid(String jobGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select count(0) from SYNC_ZY_DATA where IS_SYNC = 1 and SYNC_ZY_DATA.JOB_GUID = ? ";
            Object[] objects = {jobGuid};
            return jdbcTemplate.queryForObject(sql, objects, Integer.class);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 根据jobGuid查询条数
     *
     * @param jobGuid      jobGuid
     * @param jdbcTemplate
     * @return int
     */
    @Override
    public int selectCountFromJob(String jobGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select APPROVAL_COUNT from TRANSPORT_JOB where GUID = ? ";
            Object[] objects = {jobGuid};
            return jdbcTemplate.queryForObject(sql, objects, Integer.class);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 根据jobGuid查询orgType
     *
     * @param jobGuid      jobGuid
     * @param jdbcTemplate
     * @return str
     */
    @Override
    public String findOrgTypeByJobGuid(String jobGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select ORG_TYPE from APPROVAL_SCHEDULE_LOG where JOB_GUID = ? group by ORG_TYPE ";
            Object[] objects = {jobGuid};
            return jdbcTemplate.queryForObject(sql, objects, String.class);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 批量更新数据
     *
     * @param list         list
     * @param jdbcTemplate
     */
    @Override
    public int updateBatchData(List<SyncZyData> list, JdbcTemplate jdbcTemplate) {
        String sql = "UPDATE BSM_XJ.SYNC_ZY_DATA t SET t.IS_SYNC = ? WHERE t.GUID LIKE ? ";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, list.get(i).getIsSync());
                    ps.setString(2, list.get(i).getGuid());
                }

                @Override
                public int getBatchSize() {
                    return list.size();
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return 0;
    }

    /**
     * 批量插入数据
     *
     * @param syncZyDataLogList syncZyDataLogList
     * @param jdbcTemplate      jdbcTemplate
     * @return int
     */
    @Override
    public int batchInsert(List<SyncZyDataLog> syncZyDataLogList, JdbcTemplate jdbcTemplate) {
        String sql = "INSERT INTO SYNC_ZY_DATA_LOG (GUID, JOB_GUID, CELL_NAME, CELL_ID, BTS_NAME, BTS_ID, TECH_TYPE, " +
                "LOCATION, COUNTY, LONGITUDE, LATITUDE, SEND_START_FREQ, SEND_END_FREQ, ACC_START_FREQ, ACC_END_FREQ, " +
                "MAX_EMISSIVE_POWER, HEIGHT, DEVICE_FACTORY, DEVICE_MODEL, MODEL_CODE, ANTENNA_GAIN, ANTENNA_MODEL, " +
                "ANTENNA_FACTORY, POLARIZATION_MODE, ANTENNA_AZIMUTH, FEEDER_LOSS, ALTITUDE) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, syncZyDataLogList.get(i).getGuid());
                    ps.setString(2, syncZyDataLogList.get(i).getJobGuid());
                    ps.setString(3, syncZyDataLogList.get(i).getCellName());
                    ps.setString(4, syncZyDataLogList.get(i).getCellId());
                    ps.setString(5, syncZyDataLogList.get(i).getBtsName());
                    ps.setString(6, syncZyDataLogList.get(i).getBtsId());
                    ps.setString(7, syncZyDataLogList.get(i).getTechType());
                    ps.setString(8, syncZyDataLogList.get(i).getLocation());
                    ps.setString(9, syncZyDataLogList.get(i).getCounty());
                    ps.setString(10, syncZyDataLogList.get(i).getLongitude());
                    ps.setString(11, syncZyDataLogList.get(i).getLatitude());
                    ps.setString(12, syncZyDataLogList.get(i).getSendStartFreq());
                    ps.setString(13, syncZyDataLogList.get(i).getSendEndFreq());
                    ps.setString(14, syncZyDataLogList.get(i).getAccStartFreq());
                    ps.setString(15, syncZyDataLogList.get(i).getAccEndFreq());
                    ps.setString(16, syncZyDataLogList.get(i).getMaxEmissivePower());
                    ps.setString(17, syncZyDataLogList.get(i).getHeight());
                    ps.setString(18, syncZyDataLogList.get(i).getDeviceFactory());
                    ps.setString(19, syncZyDataLogList.get(i).getDeviceModel());
                    ps.setString(20, syncZyDataLogList.get(i).getModelCode());
                    ps.setString(21, syncZyDataLogList.get(i).getAntennaGain());
                    ps.setString(22, syncZyDataLogList.get(i).getAntennaModel());
                    ps.setString(23, syncZyDataLogList.get(i).getAntennaFactory());
                    ps.setString(24, syncZyDataLogList.get(i).getPolarizationMode());
                    ps.setString(25, syncZyDataLogList.get(i).getAntennaAzimuth());
                    ps.setString(26, syncZyDataLogList.get(i).getFeederLoss());
                    ps.setString(27, syncZyDataLogList.get(i).getAltitude());
                }

                @Override
                public int getBatchSize() {
                    return syncZyDataLogList.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return syncZyDataLogList.size();
    }

    /**
     * 批量删除
     *
     * @param guids        guids
     * @param jdbcTemplate jdbcTemplate
     * @return int
     */
    @Override
    public int batchDelete(List<String> guids, JdbcTemplate jdbcTemplate) {

        try {
            Object[] objects = new Object[guids.size()];
            StringBuilder sql = new StringBuilder(" delete from SYNC_ZY_DATA where GUID IN (");
            for (int i = 0; i < guids.size(); i++) {
                objects[i] = guids.get(i);
                if (i == guids.size() - 1) {
                    sql.append("?");
                } else {
                    sql.append("?, ");
                }
            }
            sql.append(")");
            return jdbcTemplate.update(sql.toString(), objects);
        } catch (Exception e) {
            return 0;
        }
    }
}
