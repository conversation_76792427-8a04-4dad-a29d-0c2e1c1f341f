package com.bsm.v4.api.sync.mapper.repository.interf.stander;

import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.EquAllDTO;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/2.
 */
public interface StanderRepository {

    String getAppCode(JdbcTemplate jdbcTemplate);

    List<RsbtApplyDTO> checkAppCode(String appCode, JdbcTemplate jdbcTemplate);

    List<EquAllDTO> test(JdbcTemplate jdbcTemplate);
}
