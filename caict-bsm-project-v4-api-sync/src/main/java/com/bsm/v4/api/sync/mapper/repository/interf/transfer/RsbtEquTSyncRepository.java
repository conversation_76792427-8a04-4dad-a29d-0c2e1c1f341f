package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtEquT;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtEquTSyncRepository extends BasicRepository<RsbtEquT, JdbcTemplate> {
    List<RsbtEquT> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    List<RsbtEquT> findCCode(String statGuid, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtEquT> equTs, JdbcTemplate jdbcTemplate);

    int batchUpdateByGuid(List<RsbtEquT> equTs, JdbcTemplate jdbcTemplate);
}
