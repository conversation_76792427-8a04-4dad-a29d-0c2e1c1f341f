package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtStationTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtStationT;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtStationTSyncRepositoryI extends BasicRepositoryI<RsbtStationT, JdbcTemplate> implements RsbtStationTSyncRepository {
    @Override
    public List<RsbtStationT> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_Station_T.* from Rsbt_Station,Rsbt_Station_T " +
                    "where Rsbt_Station.guid = Rsbt_Station_T.guid " +
                    "and Rsbt_Station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtStationT.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public RsbtStationT findByStatGuid(String statGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_Station_t " +
                    "where guid = ?  ";
            Object[] objects = {statGuid};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<>(RsbtStationT.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtStationT> rsbtStationTs, JdbcTemplate jdbcTemplate) {
        try {
            String sql = " insert into RSBT_STATION_T(GUID,ST_C_Code,ST_C_Sum,ST_D_TEC_Type) " +
                    " values(?,?,?,?)";
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, rsbtStationTs.get(i).getGuid());
                    ps.setString(2, rsbtStationTs.get(i).getStCCode());
                    ps.setInt(3, rsbtStationTs.get(i).getStCSum());
                    ps.setString(4, rsbtStationTs.get(i).getStDTecType());
                }

                @Override
                public int getBatchSize() {
                    return rsbtStationTs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public int batchUpdateByGuid(List<RsbtStationT> rsbtStationTs, JdbcTemplate jdbcTemplate) {
        try {
            String sql = " UPDATE RSBT_STATION_T SET ST_C_Code = ?,ST_C_Sum = ?,ST_D_TEC_Type = ? " +
                    "where guid = ? ";
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, rsbtStationTs.get(i).getStCCode());
                    ps.setInt(2, rsbtStationTs.get(i).getStCSum());
                    ps.setString(3, rsbtStationTs.get(i).getStDTecType());
                    ps.setString(4, rsbtStationTs.get(i).getGuid());
                }

                @Override
                public int getBatchSize() {
                    return rsbtStationTs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
}
