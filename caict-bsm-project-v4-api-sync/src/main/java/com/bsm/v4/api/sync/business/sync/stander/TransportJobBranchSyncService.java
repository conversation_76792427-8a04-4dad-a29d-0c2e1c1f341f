package com.bsm.v4.api.sync.business.sync.stander;

import com.bsm.v4.api.sync.mapper.repository.interf.stander.TransportJobBranchSyncRepository;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class TransportJobBranchSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    private TransportJobBranchSyncRepository transportJobBranchSyncRepository;

    public int updateByNewAppCode(String appCode, String status) {
        return transportJobBranchSyncRepository.updateByNewAppCode(appCode, status, oracleJdbcTemplate);
    }

    public List<TransportJobBranch> findAllComplete(String appCode) {
        return transportJobBranchSyncRepository.findAllComplete(appCode, oracleJdbcTemplate);
    }

}
