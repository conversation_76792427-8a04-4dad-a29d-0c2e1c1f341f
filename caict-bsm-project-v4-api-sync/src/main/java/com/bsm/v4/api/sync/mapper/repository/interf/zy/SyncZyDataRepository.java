package com.bsm.v4.api.sync.mapper.repository.interf.zy;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.dto.business.transfer.SyncZyDataDTO;
import com.bsm.v4.system.model.entity.business.transfer.SyncZyData;
import com.bsm.v4.system.model.entity.business.transfer.SyncZyDataLog;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * SyncZyDataRepository. 同步数据到总院 接口
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 7/13/21 9:29 AM
 **/
public interface SyncZyDataRepository extends BasicRepository<SyncZyData, JdbcTemplate> {

    /**
     * 根据guids查询数据
     *
     * @param syncZyDataDTO syncZyDataDTO
     * @param jdbcTemplate  jdbcTemplate
     * @return list
     */
    List<SyncZyData> findDataByGuids(SyncZyDataDTO syncZyDataDTO, JdbcTemplate jdbcTemplate);

    /**
     * 根据jobGuid查询数据
     *
     * @param syncZyDataDTO syncZyDataDTO
     * @param jdbcTemplate  jdbcTemplate
     * @return list
     */
    List<SyncZyData> findDataByJobGuid(SyncZyDataDTO syncZyDataDTO, JdbcTemplate jdbcTemplate);

    /**
     * 根据jobGuid查询数据
     *
     * @param jdbcTemplate jdbcTemplate
     * @return list
     */
    List<String> findData(JdbcTemplate jdbcTemplate);

    /**
     * 根据jobGuid查询条数
     *
     * @param jobGuid      jobGuid
     * @param jdbcTemplate jdbcTemplate
     * @return int
     */
    int selectCountByJobGuid(String jobGuid, JdbcTemplate jdbcTemplate);

    /**
     * 根据jobGuid查询条数
     *
     * @param jobGuid      jobGuid
     * @param jdbcTemplate jdbcTemplate
     * @return int
     */
    int selectCountFromJob(String jobGuid, JdbcTemplate jdbcTemplate);

    /**
     * 根据jobGuid查询orgType
     *
     * @param jobGuid      jobGuid
     * @param jdbcTemplate jdbcTemplate
     * @return str
     */
    String findOrgTypeByJobGuid(String jobGuid, JdbcTemplate jdbcTemplate);


    /**
     * 批量更新数据
     *
     * @param list         list
     * @param jdbcTemplate jdbcTemplate
     * @return int
     */
    int updateBatchData(List<SyncZyData> list, JdbcTemplate jdbcTemplate);

    /**
     * 批量插入数据
     *
     * @param syncZyDataLogList syncZyDataLogList
     * @param jdbcTemplate      jdbcTemplate
     * @return int
     */
    int batchInsert(List<SyncZyDataLog> syncZyDataLogList, JdbcTemplate jdbcTemplate);

    /**
     * 批量删除
     *
     * @param guids        guids
     * @param jdbcTemplate jdbcTemplate
     * @return int
     */
    int batchDelete(List<String> guids, JdbcTemplate jdbcTemplate);
}
