package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtAntFeedTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeedT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtAntFeedTSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtAntFeedTSyncRepository antFeedTSyncRepository;

    public List<RsbtAntfeedT> findByAppCode(String appCode) {
        return antFeedTSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtAntfeedT> antfeedTs) {
        return antFeedTSyncRepository.batchInsert(antfeedTs, oracleStandardJdbcTemplate);
    }
}
