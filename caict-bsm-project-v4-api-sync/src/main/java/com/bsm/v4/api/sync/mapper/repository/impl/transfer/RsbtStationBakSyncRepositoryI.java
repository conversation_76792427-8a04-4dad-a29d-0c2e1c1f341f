package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtStationBakSyncRepository;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RsbtStationBakSyncRepositoryI extends BasicRepositoryI<RsbtStationBak, JdbcTemplate> implements RsbtStationBakSyncRepository {
    @Override
    public List<ApplyJobInDTO> findCountByAppGuid(String appGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from APPLY_JOB_IN " +
                    " where APP_GUID = ? " +
                    "and is_compare != 5 ";

            Object[] objects = {appGuid};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(ApplyJobInDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public int updateByAppGuid(String appGuid, String status, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "update APPROVAL_TRANSPORT_JOB SET IS_COMPARE = ? " +
                    "            where GUID = ? ";
            Object[] objects = {status, appGuid};
            return jdbcTemplate.update(sql, objects);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 查询为审核的数据
     *
     * @param appGuid
     * @return
     */
    @Override
    public int findStationCountByAppGuid(String appGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = " select count(*) from RSBT_STATION_BAK where APP_GUID = ? and IS_SHOW = '0' ";
            Object[] objects = {appGuid};
            return jdbcTemplate.queryForObject(sql, objects, Integer.class);
        } catch (Exception e) {
            return 0;
        }
    }
}
