package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtAntFeedTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeedT;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtAntFeedTSyncRepositoryI extends BasicRepositoryI<RsbtAntfeedT, JdbcTemplate> implements RsbtAntFeedTSyncRepository {
    @Override
    public List<RsbtAntfeedT> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_antfeed_t.* from Rsbt_Station,Rsbt_antfeed,Rsbt_antfeed_t " +
                    "                    where Rsbt_Station.Guid = Rsbt_antfeed.Station_Guid " +
                    "                    and Rsbt_antfeed.guid = Rsbt_antfeed_t.Guid " +
                    "                    and Rsbt_Station.App_Code = ? ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtAntfeedT.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtAntfeedT> antfeedTs, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_ANTFEED_T(GUID,AT_CCODE,AT_CSGN,AT_ANG_B,AT_ANG_E,AT_3DBE,AT_3DBR) " +
                " values(?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, antfeedTs.get(i).getGuid());
                    ps.setString(2, antfeedTs.get(i).getAtCcode());
                    ps.setString(3, antfeedTs.get(i).getAtCsgn());
                    ps.setDouble(4, antfeedTs.get(i).getAtAngB() == null ? 0 : antfeedTs.get(i).getAtAngB());
                    ps.setDouble(5, antfeedTs.get(i).getAtAngE() == null ? 0 : antfeedTs.get(i).getAtAngE());
                    ps.setDouble(6, antfeedTs.get(i).getAt3dbr() == null ? 0 : antfeedTs.get(i).getAt3dbr());
                    ps.setDouble(7, antfeedTs.get(i).getAt3dbe() == null ? 0 : antfeedTs.get(i).getAt3dbe());
                }

                @Override
                public int getBatchSize() {
                    return antfeedTs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
}
