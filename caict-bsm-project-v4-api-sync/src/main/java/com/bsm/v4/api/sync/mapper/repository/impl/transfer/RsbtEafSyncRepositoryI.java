package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtEafSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtEaf;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtEafSyncRepositoryI extends BasicRepositoryI<RsbtEaf, JdbcTemplate> implements RsbtEafSyncRepository {
    @Override
    public List<RsbtEaf> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_eaf.* from Rsbt_eaf,Rsbt_Station " +
                    "where Rsbt_eaf.station_guid = Rsbt_Station.guid " +
                    "and Rsbt_Station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtEaf.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtEaf> eafs, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_EAF(GUID,STATION_GUID,EQU_GUID,ANT_GUID,FREQ_GUID) " +
                " values(?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, eafs.get(i).getGuid());
                    ps.setString(2, eafs.get(i).getStationGuid());
                    ps.setString(3, eafs.get(i).getEquGuid());
                    ps.setString(4, eafs.get(i).getAntGuid());
                    ps.setString(5, eafs.get(i).getFreqGuid());
                }

                @Override
                public int getBatchSize() {
                    return eafs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return eafs.size();
    }
}
