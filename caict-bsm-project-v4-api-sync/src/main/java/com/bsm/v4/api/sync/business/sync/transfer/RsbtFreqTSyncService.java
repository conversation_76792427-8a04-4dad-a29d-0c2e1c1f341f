package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtFreqTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtFreqT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtFreqTSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtFreqTSyncRepository freqTSyncRepository;

    public List<RsbtFreqT> findByAppCode(String appCode) {
        return freqTSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtFreqT> freqTs) {
        return freqTSyncRepository.batchInsert(freqTs, oracleStandardJdbcTemplate);
    }
}
