package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.ApprovalTransportJobSyncRepository;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ApprovalTransportJobSyncRepositoryI.
 *
 * <AUTHOR>
 * @version jdk_1.8.0_144
 * @since 2022/1/12 15:16
 */
@Repository
public class ApprovalTransportJobSyncRepositoryI extends BasicRepositoryI<ApprovalTransportJob, JdbcTemplate> implements ApprovalTransportJobSyncRepository {

    /**
     * 根据job_guid和is_compare 查询数据
     *
     * @param appGuid appGuid
     * @return list
     */
    @Override
    public List<ApprovalTransportJob> findByAppGuidAndIsCompare(String appGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from APPROVAL_TRANSPORT_JOB where JOB_GUID = (select JOB_GUID from APPROVAL_TRANSPORT_JOB where GUID = ?) and is_compare not in ('18', '19')";
            Object[] objects = {appGuid};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(ApprovalTransportJob.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据ID查询
     *
     * @param appGuid appGuid
     * @return T
     */
    @Override
    public ApprovalTransportJob findByGuid(String appGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from APPROVAL_TRANSPORT_JOB where GUID = ? ";
            Object[] objects = {appGuid};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<>(ApprovalTransportJob.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 根据ID更新
     *
     * @param jobGuid    appGuid
     * @param dataType   dataType
     * @param genNum     genNum
     * @param regionCode regionCode
     */
    @Override
    public void updateByJobUserTypeGen(String jobGuid, String techType, String dataType, String genNum, String regionCode, String isCompare, JdbcTemplate jdbcTemplate) {
        try {
            String sql = " update TRANSPORT_JOB_BRANCH b set is_compare = ? where b.GUID IN (select guid from TRANSPORT_JOB_BRANCH where JOB_GUID = ? and DATA_TYPE = ? and GEN_NUM = ? and REGION_CODE = ? and TECH_TYPE = ? ) ";
            Object[] objects = {isCompare, jobGuid, dataType, genNum, regionCode, techType};
            jdbcTemplate.update(sql, objects);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
