package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtEquSyncRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.EquAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtEquSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;

    @Autowired
    private RsbtEquSyncRepository equSyncRepository;

    public List<RsbtEqu> findByAppCode(String appCode) {
        return equSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public List<EquAllDTO> findAllByAppCode(String appCode) {
        return equSyncRepository.findAllByAppCode(appCode, oracleJdbcTemplate);
    }

    public RsbtEqu findByGuid(String equGuid) {
        return equSyncRepository.findByGuid(equGuid, oracleJdbcTemplate);
    }

    public String findByStatGuid(String appCode, String code) {
        return equSyncRepository.findByStatGuid(appCode, code, oracleStandardJdbcTemplate);
    }

    public int batchInsert(List<RsbtEqu> equs) {
        return equSyncRepository.batchInsert(equs, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByGuid(List<RsbtEqu> equs) {
        return equSyncRepository.batchUpdateByGuid(equs, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByEtCode(List<EquAllDTO> equAllDTOs) {
        return equSyncRepository.batchUpdateByEtCode(equAllDTOs, oracleStandardJdbcTemplate);
    }
}
