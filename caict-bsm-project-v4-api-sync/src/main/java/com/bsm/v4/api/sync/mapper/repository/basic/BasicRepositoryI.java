package com.bsm.v4.api.sync.mapper.repository.basic;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import com.caictframework.utils.util.Transform;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

/**
 * Created by dengsy on 2019/7/3.
 * 持久层实现类
 */
public class BasicRepositoryI<T, J extends JdbcTemplate> implements BasicRepository<T, J> {

    private Class<T> clazz;
    private String sql = "";

    private static final Logger LOG = LoggerFactory.getLogger(BasicRepositoryI.class);

    public BasicRepositoryI() {
        //获取子类的真实类型;
        Class c = this.getClass();
        //获取泛型父类
        Type t = c.getGenericSuperclass();
        if (t instanceof ParameterizedType) {
            //通过该泛型父类来获取真实的类型参数的数组
            Type[] p = ((ParameterizedType) t).getActualTypeArguments();
            //获取第一个得到T的真实类型
            this.clazz = (Class<T>) p[0];
        }
    }

    protected Object[] insertInto(Object param) {
        sql = "insert into " + myTableName(clazz);
        sql += "(";
        Map<String, Object[]> map = myDeclaredFields(param, "insert");
        Object[] objects = map.get("objects");
        sql = sql.substring(0, sql.length() - 1);
        sql += ") values (";
        for (int i = 0; i < objects.length; i++) {
            if (objects[i] != null) {
                sql += "?,";
            }
        }
        sql = sql.substring(0, sql.length() - 1);
        sql += ")";
        return objects;
    }

    protected void insertsInto() {
        try {
            sql = "insert into " + myTableName(clazz);
            sql += "(";
            for (Field field : clazz.getDeclaredFields()) {
                //设置访问权限
                field.setAccessible(true);
                sql += myTableFieId(field) + ",";
            }
            sql = sql.substring(0, sql.length() - 1);
            sql += ") values (";
            //拼接参数占位符
            for (Field field : clazz.getDeclaredFields()) {
                sql += "?,";
            }
            sql = sql.substring(0, sql.length() - 1);
            sql += ")";
        } catch (Exception e) {
            LOG.error("myDeclaredTableFieId SQL exception:" + e);
        }
    }

    /**
     * 公共添加方法
     * param:实体类对象
     */
    public int insert(Object param, J jdbcTemplate) {
        Object[] objects = insertInto(param);
        return jdbcTemplate.update(sql, objects);
    }

    /**
     * 公共添加方法
     * params:实体类对象集合
     */
    public int[] inserts(List<T> params, J jdbcTemplate) {
        List<Object[]> list = new ArrayList<Object[]>();
        Object param = params.get(0);
        insertInto(param);
        for (int i = 0; i < params.size(); i++) {
            Map<String, Object[]> map = myDeclaredFields(params.get(i), "inserts");
            Object[] objects = map.get("objects");
            list.add(objects);
        }
        return jdbcTemplate.batchUpdate(sql, list);
//        insertsInto();
//        return jdbcTemplate.batchUpdate(sql,new BatchPreparedStatementSetter(){
//            @Override
//            public void setValues(PreparedStatement ps, int i) throws SQLException {
//                for(int j = 0;j<params.size();j++){
//                    try {
//                        for (Field field : clazz.getDeclaredFields()) {
//                            //设置访问权限
//                            field.setAccessible(true);
//                            if (field.get(params.get(j)) != null) {
//                                //判断属性并将不为Null的字段,拼接到sql
//                                setPreparedStatement(ps,field.getGenericType().toString(),j+1,field.get(params.get(j)));
//                            } else {
//                                //判断值为Null的字段,将空字符串拼接到sql
//                                setPreparedStatement(ps,field.getGenericType().toString(),j+1,"");
//                            }
//                        }
//                    }catch (Exception e){
//                        LOG.error("inserts SQL exception:"+e);
//                    }
//                }
//            }
//
//            @Override
//            public int getBatchSize() {
//                return params.size();
//            }
//        });
    }

    /**
     * 公共修改方法
     * param:实体类对象
     */
    public int update(Object param, J jdbcTemplate) {
        //生成修改语句表名
        sql = "update " + myTableName(clazz) + " set ";
        //获取所有属性并循环取值,拼接到sql
        Map<String, Object[]> map = myDeclaredFields(param, "update");
        Object[] objectsSet = map.get("objects");

        sql = sql.substring(0, sql.length() - 1);
        Map<String, Object[]> mapWhere = myDeclaredFields(param, "updateWhereId");
        Object[] objectsWhere = mapWhere.get("objects");

        int l = objectsSet.length + objectsWhere.length;
        Object[] objects = new Object[l];
        for (int i = 0; i < l; i++) {
            if (i == (l - 1)) {
                objects[i] = objectsWhere[0];
            } else {
                objects[i] = objectsSet[i];
            }
        }
        return jdbcTemplate.update(sql, objects);
    }

    /**
     * 公共修改方法（批量修改）
     * param:实体类对象
     */
    public int[] updates(List<T> params, J jdbcTemplate) {
        //生成修改语句表名
        sql = "update " + myTableName(clazz) + " set ";
        //获取所有属性并循环取值,拼接到sql
        Map<String, Object[]> map = myDeclaredFields(params.get(0), "update");
        Object[] objectsSet = map.get("objects");
        Object[] objectsSetType = map.get("objectsType");

        sql = sql.substring(0, sql.length() - 1);

        Map<String, Object[]> mapWhere = myDeclaredFields(params.get(0), "updateWhereId");
        Object[] objectsWhere = mapWhere.get("objects");
        Object[] objectsWhereType = mapWhere.get("objectsType");

        int l = objectsSet.length + objectsWhere.length;
        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                for (int j = 0; j < l; j++) {
                    if (j == (l - 1)) {
//                        ps.setString(j+1,objectsWhere[0].toString());
                        setPreparedStatement(ps, objectsWhereType[0].toString(), j + 1, objectsWhere[0]);
                    } else {
//                        ps.setString(j+1,objectsSet[j].toString());
                        setPreparedStatement(ps, objectsSetType[j].toString(), j + 1, objectsSet[j]);
                    }
                }
            }

            @Override
            public int getBatchSize() {
                return params.size();
            }
        });
    }

    protected void setPreparedStatement(PreparedStatement ps, String type, int i, Object object) {
        try {
            if ("String".equals(type) || "class java.lang.String".equals(type) && !"".equals(object)) {
                ps.setString(i, (String) object);
            } else if ("boolean".equals(type) || "class java.lang.Boolean".equals(type) && !"".equals(object)) {
                ps.setString(i, (String) object);
            } else if ("Date".equals(type) || "class java.util.Date".equals(type)) {
                Date date = new Date();
                if (!"".equals(object)) {
                    date = (Date) object;
                }
                ps.setDate(i, new java.sql.Date(date.getTime()));
            } else if ("int".equals(type) || "class java.lang.Integer".equals(type) && !"".equals(object)) {
                ps.setInt(i, (Integer) object);
            } else if ("double".equals(type) || "class java.lang.Double".equals(type) && !"".equals(object)) {
                ps.setDouble(i, (Double) object);
            } else if ("float".equals(type) || "class java.lang.Float".equals(type) && !"".equals(object)) {
                ps.setFloat(i, (Float) object);
            } else if ("short".equals(type) || "class java.lang.Short".equals(type) && !"".equals(object)) {
                ps.setShort(i, (Short) object);
            } else if ("long".equals(type) || "class java.lang.Long".equals(type) && !"".equals(object)) {
                ps.setLong(i, (Long) object);
            } else {
                ps.setString(i, "");
            }
        } catch (Exception e) {
            LOG.error("setPreparedStatement SQL exception:" + e);
        }
    }

    /**
     * 公共修改方法（自定义sql）
     * 参数顺序必须一一对应
     */
    public int[] updateBySql(String sql, List<String> values, List<String> ids, J jdbcTemplate) {
        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                String value = values.get(i);
                String id = ids.get(i);
                ps.setString(1, value);
                ps.setString(2, id);
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    /**
     * 公共删除方法
     * param:实体类对象
     */
    public int delete(String id, J jdbcTemplate) {
        //生成删除语句表名
        sql = "delete from " + myTableName(clazz);
        myDeclaredFields(clazz, "whereId");
        Object[] objects = {id};
        return jdbcTemplate.update(sql, objects);
    }

    /**
     * 公共批量删除方法
     * param:实体类对象
     */
    public int[] deletes(List<String> ids, J jdbcTemplate) {
        //生成删除语句表名
        sql = "delete from " + myTableName(clazz);
        myDeclaredFields(clazz, "whereId");
        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                String id = ids.get(i);
                ps.setString(1, id);
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    /**
     * 公共批量删除方法（自定义sql）
     * 参数顺序必须一一对应
     * param:实体类对象
     */
    public int[] deletesBySql(String sql, List<String> ids, J jdbcTemplate) {
        return jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                String id = ids.get(i);
                ps.setString(1, id);
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    /**
     * 公共查询方法（对象中有参数则拼接sql）
     * param:实体类对象
     */
    public List<T> findByWhere(Object param, J jdbcTemplate) {
        //查询语句
        try {
            sql = "select * from " + myTableName(clazz) + "where 1 = 1";
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<T>(clazz) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 公共查询方法（查询全部）
     */
    public List<T> findAll(J jdbcTemplate) {
        //查询语句
        try {
            sql = "select * from " + myTableName(clazz);
            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<T>(clazz) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 公共查询方法（根据Id查询）
     */
    public T findById(String id, J jdbcTemplate) {
        //查询语句
        try {
            sql = "select * from " + myTableName(clazz);
            myDeclaredFields(clazz, "whereId");
            Object[] objects = {id};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<T>(clazz) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    //获取表名，如果没有赋值则用类名
    protected String myTableName(Class<?> myClass) {
        try {
            if (myClass.isAnnotationPresent(TableName.class)) {
                TableName myTableName = myClass.getAnnotation(TableName.class);
                String value = myTableName.value();
                if (!"".equals(value) && value != null) {
                    return value;
                } else {
                    return myClass.getName().toLowerCase();
                }
            } else {
                return myClass.getName().toLowerCase();
            }
        } catch (Exception e) {
            LOG.error("myTableName SQL exception:" + e);
            return myClass.getName().toLowerCase();
        }
    }

    //获取主键
    protected String myTableId(Field field) {
        try {
            if (field.isAnnotationPresent(TableId.class)) {
                TableId myTableId = field.getAnnotation(TableId.class);
                String value = myTableId.value();
                if (!"".equals(value)) {
                    return value;
                } else {
                    return "";
                }
            } else {
                return "";
            }
        } catch (Exception e) {
            LOG.error("myTableName SQL exception:" + e);
            return field.getName().toLowerCase();
        }
    }

    //获取列名
    protected String myTableFieId(Field field) {
        try {
            if (field.isAnnotationPresent(TableFieId.class)) {
                TableFieId myTableFile = field.getAnnotation(TableFieId.class);
                String value = myTableFile.value();
                if (!"".equals(value)) {
                    return value;
                } else {
                    return Transform.HumpToUnderline(field.getName());
                }
            } else {
                return Transform.HumpToUnderline(field.getName());
            }
        } catch (Exception e) {
            LOG.error("myTableName SQL exception:" + e);
            return field.getName().toLowerCase();
        }
    }

    /**
     * 获取类方法、取值返回值
     */
    protected String myDeclaredMethod(Class<?> myClass, String method) {
        try {
            Method myMethod = myClass.getDeclaredMethod(method);
            Object classInfo = myClass.newInstance();
            return myMethod.invoke(classInfo).toString();
        } catch (Exception e) {
            LOG.error("myDeclaredMethod SQL exception: " + e);
            return "";
        }
    }

    /**
     * 获取所有属性、循环取值、拼接sql，返回参数Object[] 没有值写空
     */
    protected Map<String, Object[]> myDeclaredFieldsIsNull(Object T, String type) {
        try {
            List<Object> list = new ArrayList<Object>();
            List<Object> listType = new ArrayList<>();

            for (Field field : clazz.getDeclaredFields()) {
                //设置访问权限
                field.setAccessible(true);

                Object object = null;
                Object objectType = "";
                if ("insert".equals(type)) {
                    sql += myTableFieId(field) + ",";
                    object = myGenericType(field.getGenericType().toString(), field.get(T));
                    objectType = field.getGenericType().toString();
                } else if ("inserts".equals(type)) {
                    object = myGenericType(field.getGenericType().toString(), field.get(T));
                    objectType = field.getGenericType().toString();
                }
                if (object != null) {
                    list.add(object);
                    listType.add(objectType);
                }
            }

            return setDeclaredFieldsMap(list, listType);
        } catch (Exception e) {
            LOG.error("myDeclaredFields SQL exception: " + e);
            return null;
        }
    }

    /**
     * 获取所有属性、循环取值、拼接sql，返回参数Object[]
     */
    protected Map<String, Object[]> myDeclaredFields(Object T, String type) {
        try {
            List<Object> list = new ArrayList<Object>();
            List<Object> listType = new ArrayList<>();

            for (Field field : clazz.getDeclaredFields()) {
                //设置访问权限
                field.setAccessible(true);
                if ("whereId".equals(type)) {
                    String myTableId = myTableId(field);
                    if (!"".equals(myTableId)) {
                        sql += " where " + myTableId + " = ?";
                        return null;
                    }
                } else {
                    //判断属性并将不为NULL的字段拼接到sql
                    if (field.get(T) != null) {
                        Object object = null;
                        Object objectType = "";
                        if ("insert".equals(type)) {
                            sql += myTableFieId(field) + ",";
                            object = myGenericType(field.getGenericType().toString(), field.get(T));
                            objectType = field.getGenericType().toString();
                        } else if ("inserts".equals(type)) {
                            object = myGenericType(field.getGenericType().toString(), field.get(T));
                            objectType = field.getGenericType().toString();
                        } else if ("update".equals(type)) {
                            sql += myTableFieId(field) + " = ?,";
                            object = myGenericType(field.getGenericType().toString(), field.get(T));
                            objectType = field.getGenericType().toString();
                        } else if ("updateWhereId".equals(type) && !"".equals(myKey(field))) {
                            sql += " where " + myTableFieId(field) + " = ?";
                            object = myGenericType(field.getGenericType().toString(), field.get(T));
                            objectType = field.getGenericType().toString();
                        }
                        if (object != null) {
                            list.add(object);
                            listType.add(objectType);
                        }
                    }
                }
            }

            return setDeclaredFieldsMap(list, listType);
        } catch (Exception e) {
            LOG.error("myDeclaredFields SQL exception: " + e);
            return null;
        }
    }

    protected Map<String, Object[]> setDeclaredFieldsMap(List<Object> list, List<Object> listType) {
        Map<String, Object[]> map = new HashMap<>();

        if (list != null && list.size() != 0) {
            Object[] objects = new Object[list.size()];
            for (int i = 0; i < list.size(); i++) {
                objects[i] = list.get(i);
            }
            map.put("objects", objects);
        }

        if (listType != null && listType.size() != 0) {
            Object[] objectsType = new Object[listType.size()];
            for (int i = 0; i < listType.size(); i++) {
                objectsType[i] = listType.get(i);
            }
            map.put("objectsType", objectsType);
        }

        return map;
    }

    /**
     * 判断属性类型，返回值类型
     */
    protected Object myGenericType(String type, Object value) {
        if ("class java.lang.String".equals(type)) {
            return (String) value;
        } else if ("boolean".equals(type) || "class java.lang.Boolean".equals(type)) {
            return (Boolean) value;
        } else if ("class java.util.Date".equals(type)) {
            return (Date) value;
        } else if ("int".equals(type) || "class java.lang.Integer".equals(type)) {
            return (Integer) value;
        } else if ("double".equals(type) || "class java.lang.Double".equals(type)) {
            return (Double) value;
        } else if ("float".equals(type) || "class java.lang.Float".equals(type)) {
            return (Float) value;
        } else if ("short".equals(type) || "class java.lang.Short".equals(type)) {
            return (Short) value;
        } else if ("long".equals(type) || "class java.lang.Long".equals(type)) {
            return (Long) value;
        } else {
            return "" + value + "";
        }
    }

    /**
     * 获取主键，默认为id
     */
    protected String myKey(Field field) {
        String KEY = myTableId(field);
//        KEY = ("".equals(KEY))?"id":KEY;
        return KEY;
    }
}
