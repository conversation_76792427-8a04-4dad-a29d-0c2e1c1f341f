package com.bsm.v4.api.sync.mapper.repository.interf.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * Created by yanchengpeng on 2020/12/9.
 */
public interface TransportJobSyncRepository extends BasicRepository<TransportJob, JdbcTemplate> {
    int updateComplete(String appCode, String status, JdbcTemplate jdbcTemplate);

    /**
     * 根据id查询数据
     *
     * @param guid guid
     * @return T
     */
    TransportJob findByAppGuid(String guid, JdbcTemplate jdbcTemplate);

    /**
     * 更新数据
     *
     * @param jobGuid   jobGuid
     * @param isCompare isCompare
     */
    void updateIsCompareByJobGuid(String jobGuid, String isCompare, JdbcTemplate jdbcTemplate);
}
