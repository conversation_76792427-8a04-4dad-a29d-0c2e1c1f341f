package com.bsm.v4.api.sync.mapper.repository.impl.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.stander.ApplyJobInSyncRepository;
import com.bsm.v4.system.model.entity.business.applytable.ApplyJobIn;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Repository
public class ApplyJobInSyncRepositoryI extends BasicRepositoryI<ApplyJobIn, JdbcTemplate> implements ApplyJobInSyncRepository {
    @Override
    public int updateByAppCode(String appGuid, String appCode, String status, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "UPDATE APPLY_JOB_IN set IS_COMPARE = ? " +
                    "where APP_GUID = ? " +
                    "and APP_CODE = ? ";
            Object[] objects = {status, appGuid, appCode};
            return jdbcTemplate.update(sql, objects);
        } catch (Exception e) {
            return 0;
        }
    }
}
