package com.bsm.v4.api.sync.business.sync.zy;


import com.bsm.v4.api.sync.mapper.repository.interf.zy.SyncZyDataRepository;
import com.bsm.v4.api.sync.utils.HttpClintUtil;
import com.bsm.v4.system.model.dto.business.transfer.SyncZyDataDTO;
import com.bsm.v4.system.model.entity.business.transfer.SyncZyData;
import com.bsm.v4.system.model.entity.business.transfer.SyncZyDataLog;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SyncZyDataService. 业务层
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 7/8/21 2:09 PM
 **/
@Service
public class SyncZyDataService {

    private static final Logger LOG = LoggerFactory.getLogger(SyncZyDataService.class);

    @Autowired
    private SyncZyDataRepository repository;

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    // todo
    private static final String url = "http://172.19.63.112:8899/G5ManagerDataServices.asmx/saveOp";


    public void autoSyncToZy1(String cookie) {
        String json = getjson1();
        Map<String, Object> map = HttpClintUtil.sendPostAndRead(url, json, cookie, 0, 0);
        String errorStr = (String) map.get("errorStr");
        LOG.error(errorStr);
        LOG.error(map.get("status") + "");
        LOG.error(map.get("response") + "");
        // 如果post异常
        if (StringUtils.isNotEmpty(errorStr)) {
            LOG.error(errorStr);
        }
    }

    /**
     * 自动同步到总院
     */
    public void autoSyncToZy(String cookie) {

        // 查询所有jobGuid
        List<String> data = repository.findData(oracleJdbcTemplate);
        if (data != null && data.size() > 0) {
            LOG.info("data.size():"+data.size());
            List<SyncZyData> list;
            // 一次取一个进行操作
            // 获取批次时间
            String batch_time = new Timestamp(System.currentTimeMillis()).toString().substring(0, 19);
            String batch_no = data.get(0);
            //  "mobile 移动"  "unicom 联通"  "telecom 电信"
            String orgType = repository.findOrgTypeByJobGuid(batch_no, oracleJdbcTemplate);
            LOG.info("orgType:"+orgType);
            // 运营商
            String op = getOrgType(orgType);
            // 此批次数据总数
            int batch_sum = repository.selectCountFromJob(batch_no, oracleJdbcTemplate);
            LOG.info("batch_sum:"+batch_sum);
            // 该次请求上传的数据总数
            int row_num;
            SyncZyDataDTO syncZyDataDTO = new SyncZyDataDTO();
            syncZyDataDTO.setJobGuid(batch_no);
            if (batch_sum > 0 && batch_sum <= 1000) {
                // 查询所有 无需分页
                // 查询该batch_no 下总共有多少条数据需要同步
                syncZyDataDTO.setRows(batch_sum);
                syncZyDataDTO.setPage(1);
                list = repository.findDataByJobGuid(syncZyDataDTO, oracleJdbcTemplate);
                row_num = list.size();
                LOG.info("1row_num:"+row_num);
                // 同步数据到总院
                if (list.size() > 0) {
                    String json = getJson(list, batch_time, batch_no, op, batch_sum, row_num);
                    Map<String, Object> map = HttpClintUtil.sendPostAndRead(url, json, cookie, 0, 0);
                    String errorStr = (String) map.get("errorStr");
                    // 如果post异常
                    if (StringUtils.isNotEmpty(errorStr)) {
                        LOG.error(errorStr);
                    } else {
                        // 删除当前表数据  插入数据到历史表
                        insertAndDel(list);
                    }
                }
            } else {
                // 需要分页
                // 页码
                int j = 1;
                for (int i = 0; i < batch_sum; ) {
                    syncZyDataDTO.setPage(j);
                    j++;
                    // 如果剩余超过1000，继续循环
                    if (batch_sum - i > 1000) {
                        syncZyDataDTO.setRows(1000);
                        i += 1000;
                        // 分页查询
                        list = repository.findDataByJobGuid(syncZyDataDTO, oracleJdbcTemplate);
                        row_num = list.size();
                        LOG.info("2row_num:"+row_num);
                        // 同步数据到总院
                        if (list.size() > 0) {
                            String json = getJson(list, batch_time, batch_no, op, batch_sum, row_num);
                            Map<String, Object> map = HttpClintUtil.sendPostAndRead(url, json, cookie, 0, 0);
                            String errorStr = (String) map.get("errorStr");
                            // 如果post异常
                            if (StringUtils.isNotEmpty(errorStr)) {
                                LOG.error(errorStr);
                            } else {
                                // 删除当前表数据  插入数据到历史表
                                insertAndDel(list);
                            }
                        }
                    } else {
                        // 否则查询剩余的数据
                        syncZyDataDTO.setRows(1000);
                        // 分页查询
                        list = repository.findDataByJobGuid(syncZyDataDTO, oracleJdbcTemplate);
                        row_num = list.size();
                        LOG.info("3row_num:"+row_num);
                        // 同步数据到总院
                        if (list.size() > 0) {
                            String json = getJson(list, batch_time, batch_no, op, batch_sum, row_num);
                            Map<String, Object> map = HttpClintUtil.sendPostAndRead(url, json, cookie, 0, 0);
                            String errorStr = (String) map.get("errorStr");
                            // 如果post异常
                            if (StringUtils.isNotEmpty(errorStr)) {
                                LOG.error(errorStr);
                            } else {
                                // 删除当前表数据  插入数据到历史表
                                insertAndDel(list);
                            }
                        }
                        break;
                    }
                }

            }
        }
    }

    /**
     * 插入数据，删除数据
     *
     * @param list list
     */
    private void insertAndDel(List<SyncZyData> list) {
        // 删除当前表数据  插入数据到历史表
        // 插入数据
        List<SyncZyDataLog> logList = new ArrayList<>();
        SyncZyDataLog syncZyDataLog;
        // bean复制
        for (SyncZyData syncZyData : list) {
            syncZyDataLog = new SyncZyDataLog();
            BeanUtils.copyProperties(syncZyData, syncZyDataLog);
            logList.add(syncZyDataLog);
        }
        int rows = repository.batchInsert(logList, oracleJdbcTemplate);
        if (rows == list.size()) {
            // 删除数据
            repository.batchDelete(list.stream().map(SyncZyData::getGuid).collect(Collectors.toList()), oracleJdbcTemplate);
        }
    }

    /**
     * 同步到总院
     *
     * @param syncZyDataDTO syncZyDataDTO
     * @return str
     */
    public String syncToZy(SyncZyDataDTO syncZyDataDTO) {

        // 获取数据
        if (StringUtils.isNotEmpty(syncZyDataDTO.getJobGuid())) {
            List<SyncZyData> list;
            // 获取批次时间
            String batch_time = new Timestamp(System.currentTimeMillis()).toString().substring(0, 19);
            String batch_no = syncZyDataDTO.getJobGuid();
            //  "mobile 移动"  "unicom 联通"  "telecom 电信"
            String orgType = repository.findOrgTypeByJobGuid(syncZyDataDTO.getJobGuid(), oracleJdbcTemplate);
            // 运营商
            String op = getOrgType(orgType);
            // 此批次数据总数
            int batch_sum;
            // 该次请求上传的数据总数
            int row_num;
            if (syncZyDataDTO.getGuids() != null && syncZyDataDTO.getGuids().length > 0) {
                // 根据guid查询同步
                list = repository.findDataByGuids(syncZyDataDTO, oracleJdbcTemplate);
                batch_sum = list.size();
                row_num = batch_sum;

                // 同步数据到总院
                if (list.size() > 0) {
                    String json = getJson(list, batch_time, batch_no, op, batch_sum, row_num);
                    // todo
//                                Map<String, Object> map = HttpClintUtil.sendPost(url, json);
//                                String errorStr = (String) map.get("errorStr");
                    String errorStr = "";
                    // 如果post异常
                    if (StringUtils.isNotEmpty(errorStr)) {
                        LOG.error(errorStr);
                        return errorStr;
                    } else {
                        // 更新数据
                        list.forEach(syncZyData -> syncZyData.setIsSync("2"));
                        repository.updateBatchData(list, oracleJdbcTemplate);
                    }
                }

            } else {

                // 默认同步
                batch_sum = repository.selectCountByJobGuid(syncZyDataDTO.getJobGuid(), oracleJdbcTemplate);
                if (batch_sum <= 0) {
                    return "同步成功(已被同步完成)";
                } else if (batch_sum <= 1000) {
                    syncZyDataDTO.setRows(batch_sum);
                    row_num = batch_sum;
                    syncZyDataDTO.setPage(1);
                    list = repository.findDataByJobGuid(syncZyDataDTO, oracleJdbcTemplate);
                    // 同步数据到总院
                    if (list.size() > 0) {
                        String json = getJson(list, batch_time, batch_no, op, batch_sum, row_num);
                        // todo
//                                Map<String, Object> map = HttpClintUtil.sendPost(url, json);
//                                String errorStr = (String) map.get("errorStr");
                        String errorStr = "";
                        // 如果post异常
                        if (StringUtils.isNotEmpty(errorStr)) {
                            LOG.error(errorStr);
                            return errorStr;
                        } else {
                            // 更新数据
                            list.forEach(syncZyData -> syncZyData.setIsSync("2"));
                            repository.updateBatchData(list, oracleJdbcTemplate);
                        }
                    }
                } else {
                    List<SyncZyData> listTemp = new ArrayList<>();
                    // 页码
                    int j = 1;
                    for (int i = 0; i < batch_sum; ) {
                        syncZyDataDTO.setPage(j);
                        j++;
                        // 如果剩余超过1000，继续循环
                        if (batch_sum - i > 1000) {
                            syncZyDataDTO.setRows(1000);
                            i += 1000;
                            // 分页查询
                            list = repository.findDataByJobGuid(syncZyDataDTO, oracleJdbcTemplate);

                            // 同步数据到总院
                            if (list.size() > 0) {
                                String json = getJson(list, batch_time, batch_no, op, batch_sum, 1000);
                                // todo
//                                      Map<String, Object> map = HttpClintUtil.sendPost(url, json);
//                                      String errorStr = (String) map.get("errorStr");
                                String errorStr = "";
                                // 如果post异常
                                if (StringUtils.isNotEmpty(errorStr)) {
                                    LOG.error(errorStr);
                                    return errorStr;
                                } else {
                                    // 更新数据
                                    list.forEach(syncZyData -> syncZyData.setIsSync("2"));
                                    listTemp.addAll(list);
                                }
                            }
                        } else {
                            // 否则查询剩余的数据
                            row_num = batch_sum - i;
                            syncZyDataDTO.setRows(1000);
                            // 分页查询
                            list = repository.findDataByJobGuid(syncZyDataDTO, oracleJdbcTemplate);

                            // 同步数据到总院
                            if (list.size() > 0) {
                                String json = getJson(list, batch_time, batch_no, op, batch_sum, row_num);
                                // todo
                                //                                Map<String, Object> map = HttpClintUtil.sendPost(url, json);
                                //                                String errorStr = (String) map.get("errorStr");
                                String errorStr = "";
                                // 如果post异常
                                if (StringUtils.isNotEmpty(errorStr)) {
                                    LOG.error(errorStr);
                                    return errorStr;
                                } else {
                                    // 更新数据
                                    list.forEach(syncZyData -> syncZyData.setIsSync("2"));
                                    listTemp.addAll(list);
                                }
                            }
                            break;
                        }
                    }
                    // 拆分list
                    List<List<SyncZyData>> lists = fixedGrouping(listTemp, 1000);
                    // 保存数据
                    for (List<SyncZyData> zyDataList : lists) {
                        repository.updateBatchData(zyDataList, oracleJdbcTemplate);
                    }
                }
            }
        } else {
            LOG.warn("JOB_GUID为空");
            return "JOB_GUID为空";
        }
        return "同步成功";
    }

    private String getjson1() {
        StringBuilder sb = new StringBuilder();
        sb.append("{" +
                "\"batch_time\": \"2020-01-01 12:34:32\"," +
                "\"batch_no\": \"999\"," +
                "\"op\": \"YD\"," +
                "\"batch_sum\": 1," +
                "\"row_num\": 1," +
                "\"data\": [" +
                "{" +
                "\"ID\": \"AKS-乌什2站-HGH-1\"," +
                "\"CELL_ID\": \"460-00-39024-771\"," +
                "\"BTS_NAME\": \"AKS-乌什2站-HGH\"," +
                "\"BTS_ID\": 6211," +
                "\"TECH_TYPE\": \"GSM\"," +
                "\"LOCATION\": \"阿克苏乌什县二站无线机房\"," +
                "\"COUNTY\": \"乌什县\"," +
                "\"LONGITUDE\": 79.24723399999999," +
                "\"LATITUDE\": 41.224908," +
                "\"SEND_START_FREQ\": 930," +
                "\"SEND_END_FREQ\": 954," +
                "\"ACC_START_FREQ\": 885," +
                "\"ACC_END_FREQ\": 909," +
                "\"MAX_EMISSIVE_POWER\": 5," +
                "\"HEIGHT\": 58.0," +
                "\"DEVICE_FACTORY\": \"华为\"," +
                "\"DEVICE_MODEL\": 111," +
                "\"MODEL_CODE\": 111," +
                "\"ANTENNA_MODEL\": 111," +
                "\"ANTENNA_FACTORY\": 111," +
                "\"POLARIZATION_MODE\": \"双极化\"," +
                "\"ANTENNA_AZIMUTH\": 111," +
                "\"FEEDER_LOSS\": 111," +
                "\"ANTENNA_GAIN\": 111," +
                "\"ALTITUDE\": 111," +
                "\"batch_no\": \"1212\"," +
                "\"op\": \"中国移动\"," +
                "\"update_time\": \"2020-01-01 12:34:32\"," +
                "\"create_time\": \"2020-01-01 12:34:32\"" +
                "}" +
                "]" +
                "}");

        return sb.toString();
    }

    /**
     * 拼json
     *
     * @param list       list
     * @param batch_time batch_time
     * @param batch_no   batch_no
     * @param op         op
     * @param batch_sum  batch_sum
     * @param row_num    row_num
     * @return json
     */
    private String getJson(List<SyncZyData> list, String batch_time, String batch_no, String op, long batch_sum, int row_num) {
        StringBuilder sb = new StringBuilder();
        sb.append("{\"batch_time\":\"").append(batch_time);
        sb.append("\",\"batch_no\":\"").append(batch_no);
        sb.append("\",\"op\":\"").append(op);
        sb.append("\",\"batch_sum\":").append(batch_sum);
        sb.append(",\"row_num\":").append(row_num);
        sb.append(",\"data\":[");
        for (int i = 0; i < list.size(); i++) {
            SyncZyData syncDataRawBtsData = list.get(i);
            sb.append("{\"CELL_NAME\":\"").append(syncDataRawBtsData.getCellName()).append("\"");
            sb.append(",\"CELL_ID\":\"").append(syncDataRawBtsData.getCellId()).append("\"");
            sb.append(",\"BTS_NAME\":\"").append(syncDataRawBtsData.getBtsName()).append("\"");
            sb.append(",\"BTS_ID\":\"").append(syncDataRawBtsData.getBtsId()).append("\"");
            sb.append(",\"TECH_TYPE\":\"").append(syncDataRawBtsData.getTechType()).append("\"");
            sb.append(",\"LOCATION\":\"").append(syncDataRawBtsData.getLocation()).append("\"");
            sb.append(",\"COUNTY\":\"").append(syncDataRawBtsData.getCounty()).append("\"");
            sb.append(",\"LONGITUDE\":\"").append(syncDataRawBtsData.getLongitude()).append("\"");
            sb.append(",\"LATITUDE\":\"").append(syncDataRawBtsData.getLatitude()).append("\"");
            sb.append(",\"SEND_START_FREQ\":\"").append(syncDataRawBtsData.getSendStartFreq()).append("\"");
            sb.append(",\"SEND_END_FREQ\":\"").append(syncDataRawBtsData.getSendEndFreq()).append("\"");
            sb.append(",\"ACC_START_FREQ\":\"").append(syncDataRawBtsData.getAccStartFreq()).append("\"");
            sb.append(",\"ACC_END_FREQ\":\"").append(syncDataRawBtsData.getAccEndFreq()).append("\"");
            sb.append(",\"MAX_EMISSIVE_POWER\":\"").append(syncDataRawBtsData.getMaxEmissivePower()).append("\"");
            sb.append(",\"HEIGHT\":\"").append(syncDataRawBtsData.getHeight()).append("\"");
            sb.append(",\"DEVICE_FACTORY\":\"").append(syncDataRawBtsData.getDeviceFactory()).append("\"");
            sb.append(",\"DEVICE_MODEL\":\"").append(syncDataRawBtsData.getDeviceModel()).append("\"");
            sb.append(",\"MODEL_CODE\":\"").append(syncDataRawBtsData.getModelCode()).append("\"");
            sb.append(",\"ANTENNA_MODEL\":\"").append(syncDataRawBtsData.getAntennaModel()).append("\"");
            sb.append(",\"ANTENNA_FACTORY\":\"").append(syncDataRawBtsData.getAntennaFactory()).append("\"");
            sb.append(",\"POLARIZATION_MODE\":\"").append(syncDataRawBtsData.getPolarizationMode()).append("\"");
            sb.append(",\"ANTENNA_AZIMUTH\":\"").append(syncDataRawBtsData.getAntennaAzimuth()).append("\"");
            sb.append(",\"FEEDER_LOSS\":\"").append(syncDataRawBtsData.getFeederLoss()).append("\"");
            sb.append(",\"ANTENNA_GAIN\":\"").append(syncDataRawBtsData.getAntennaGain()).append("\"");
            if (i == list.size() - 1) {
                sb.append(",\"ALTITUDE\":\"").append(syncDataRawBtsData.getCellId()).append("\"}");
            } else {
                sb.append(",\"ALTITUDE\":\"").append(syncDataRawBtsData.getCellId()).append("\"},");
            }
        }

        sb.append("]}");
        return sb.toString();
    }


    /**
     * 获取运营商
     *
     * @param orgType orgType
     * @return STRING
     */
    private String getOrgType(String orgType) {
        switch (orgType) {
            case "mobile":
                return "YD";
            case "telecom":
                return "DX";
            case "unicom":
                return "LT";
            default:
                return "GD";
        }
    }

    /**
     * 将一组数据固定分组，每组n个元素
     *
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @param <T>    对象
     * @return list
     */
    private static <T> List<List<T>> fixedGrouping(List<T> source, int n) {

        if (null == source || source.size() == 0 || n <= 0)
            return null;
        List<List<T>> result = new ArrayList<>();
        int remainder = source.size() % n;
        int size = (source.size() / n);
        for (int i = 0; i < size; i++) {
            List<T> subset;
            subset = source.subList(i * n, (i + 1) * n);
            result.add(subset);
        }
        if (remainder > 0) {
            List<T> subset;
            subset = source.subList(size * n, size * n + remainder);
            result.add(subset);
        }
        return result;
    }
}
