package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtFreqTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtFreqT;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtFreqTSyncRepositoryI extends BasicRepositoryI<RsbtFreqT, JdbcTemplate> implements RsbtFreqTSyncRepository {
    @Override
    public List<RsbtFreqT> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_Freq_t.* from Rsbt_Station,Rsbt_Freq,Rsbt_Freq_t " +
                    "                    where Rsbt_Station.Guid = Rsbt_Freq.Station_Guid " +
                    "                    and Rsbt_Freq.guid = Rsbt_Freq_t.Guid " +
                    "                    and Rsbt_Station.App_Code = ? ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtFreqT.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtFreqT> freqTs, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_FREQ_T(GUID,FT_FREQ_CCode,FT_FREQ_CSGN) " +
                " values(?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, freqTs.get(i).getGuid());
                    ps.setString(2, freqTs.get(i).getFtFreqCcode());
                    ps.setString(3, freqTs.get(i).getFtFreqCsgn());
                }

                @Override
                public int getBatchSize() {
                    return freqTs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
}
