package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.AntFeedAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtAntFeedSyncRepository extends BasicRepository<RsbtAntfeed, JdbcTemplate> {
    List<RsbtAntfeed> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    List<AntFeedAllDTO> findAllByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtAntfeed> antfeeds, JdbcTemplate jdbcTemplate);

    int batchUpdateByAtCode(List<AntFeedAllDTO> antfeeds, JdbcTemplate jdbcTemplate);
}
