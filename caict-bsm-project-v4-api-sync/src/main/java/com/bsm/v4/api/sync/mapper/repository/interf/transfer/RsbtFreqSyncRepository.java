package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.FreqAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtFreqSyncRepository extends BasicRepository<RsbtFreq, JdbcTemplate> {
    List<RsbtFreq> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtFreq> freqs, JdbcTemplate jdbcTemplate);

    int batchUpdateByFtCode(List<FreqAllDTO> freqAllDTOs, JdbcTemplate jdbcTemplate);

    List<FreqAllDTO> findAllByAppCode(String appCode, JdbcTemplate jdbcTemplate);
}
