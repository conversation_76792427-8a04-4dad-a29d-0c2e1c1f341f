package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtLicenseSyncRepository;
import com.bsm.v4.system.model.dto.business.license.LicensePdfDTO;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfChildrenDTO;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfDTO;
import com.bsm.v4.system.model.dto.business.station.ISectionDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtLicenseSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;

    @Autowired
    private RsbtLicenseSyncRepository licenseSyncRepository;

    public List<RsbtLicense> findByAppCode(String appCode) {
        return licenseSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public RsbtLicense findByStatGuid(String statGuid) {
        return licenseSyncRepository.findByStatGuid(statGuid, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtLicense> licenses) {
        return licenseSyncRepository.batchInsert(licenses, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByStatGuid(List<RsbtLicense> licenses) {
        return licenseSyncRepository.batchUpdateByStatGuid(licenses, oracleStandardJdbcTemplate);
    }



    //电子执照数据查询总数
    public int selectLicensePdfDto(String licenseCode, String stationCode, String stationName, Date licenseStartDate, Date licenseEndDate) {
        return licenseSyncRepository.selectLicensePdfDto(licenseCode, stationCode, stationName, licenseStartDate, licenseEndDate, oracleStandardJdbcTemplate);
    }

    //电子执照数据查询
    public List<RsbtLicensePdfDTO> findAllPdfDto(String licenseCode, String stationCode, String stationName, Date licenseStartDate, Date licenseEndDate, int page, int showRows) {
        List<RsbtLicensePdfDTO> rsbtLicensePdfDTOList = licenseSyncRepository.findAllPdfDto(licenseCode, stationCode, stationName, licenseStartDate, licenseEndDate, (page - 1) * showRows, page * showRows, oracleStandardJdbcTemplate);
        if (rsbtLicensePdfDTOList != null) {
            for (RsbtLicensePdfDTO rsbtLicensePdfDTO : rsbtLicensePdfDTOList) {
                List<RsbtLicensePdfChildrenDTO> rsbtLicensePdfChildrenDTOList = licenseSyncRepository.findPdfChildrenDto(rsbtLicensePdfDTO.getStationGuid(), oracleStandardJdbcTemplate);
                rsbtLicensePdfDTO.setStaPdfChildrenDTOList(rsbtLicensePdfChildrenDTOList);
            }
        }
        return rsbtLicensePdfDTOList;
    }

    //根据基站guid查询单个执照信息
    public LicensePdfDTO findOnePdfDto(String stationGuid) {
        LicensePdfDTO rsbtLicensePdfDTO = licenseSyncRepository.findOnePdfDto(stationGuid, oracleStandardJdbcTemplate);
        List<ISectionDTO> rsbtLicensePdfChildrenDTOList = licenseSyncRepository.findPdfSectionDto(stationGuid, oracleStandardJdbcTemplate);
        rsbtLicensePdfDTO.setiSectionDTOList(rsbtLicensePdfChildrenDTOList);
        return rsbtLicensePdfDTO;
    }
}
