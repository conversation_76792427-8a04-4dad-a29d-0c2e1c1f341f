package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtStationSyncRepository extends BasicRepository<RsbtStation, JdbcTemplate> {

    List<RsbtStation> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtStation> stations, JdbcTemplate jdbcTemplate);

    int batchUpdateByGuid(List<RsbtStation> stations, JdbcTemplate jdbcTemplate);

    RsbtStation findOneByStatName(String statName, JdbcTemplate jdbcTemplate);
}
