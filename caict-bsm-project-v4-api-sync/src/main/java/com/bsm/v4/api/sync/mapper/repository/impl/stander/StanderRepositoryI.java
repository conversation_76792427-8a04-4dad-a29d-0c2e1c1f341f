package com.bsm.v4.api.sync.mapper.repository.impl.stander;

import com.bsm.v4.api.sync.mapper.repository.interf.stander.StanderRepository;
import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.EquAllDTO;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/2.
 */
@Repository
public class StanderRepositoryI implements StanderRepository {
    @Override
    public String getAppCode(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select max(app_code) from RSBT_APPLY ";
            return jdbcTemplate.queryForObject(sql, String.class);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<RsbtApplyDTO> checkAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from RSBT_APPLY " +
                    "where RSBT_APPLY.APP_CODE = ?  ";

            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtApplyDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<EquAllDTO> test(JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from Rsbt_equ,rsbt_equ_t " +
                    "where Rsbt_equ.guid = rsbt_equ_t.guid  ";

            return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(EquAllDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }
}
