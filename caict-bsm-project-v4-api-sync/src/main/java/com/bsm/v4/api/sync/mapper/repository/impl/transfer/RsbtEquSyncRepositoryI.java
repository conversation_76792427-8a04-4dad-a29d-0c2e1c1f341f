package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtEquSyncRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.EquAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtEquSyncRepositoryI extends BasicRepositoryI<RsbtEqu, JdbcTemplate> implements RsbtEquSyncRepository {
    @Override
    public List<RsbtEqu> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_equ.* from Rsbt_equ,Rsbt_station " +
                    "where Rsbt_equ.station_guid = Rsbt_station.guid " +
                    "and Rsbt_station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtEqu.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<EquAllDTO> findAllByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_equ.*,Rsbt_equ_t.* from Rsbt_equ,Rsbt_equ_t,Rsbt_station " +
                    "where Rsbt_equ.station_guid = Rsbt_station.guid " +
                    "and Rsbt_equ.guid = Rsbt_equ_t.guid " +
                    "and Rsbt_station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(EquAllDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public RsbtEqu findByGuid(String equGuid, JdbcTemplate jdbcTemplate) {
        return null;
    }

    @Override
    public String findByStatGuid(String statGuid, String code, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_equ_t.guid from RSBT_equ,Rsbt_equ_t " +
                    "where RSBT_equ.guid = rsbt_equ_t.guid " +
                    "and rsbt_equ.station_guid = ? " +
                    "and rsbt_equ_t.ET_EQU_CCode = ?";
            Object[] objects = {statGuid, code};
            return jdbcTemplate.queryForObject(sql, objects, String.class);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtEqu> equs, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_EQU(GUID,STATION_GUID,EQU_TYPE,EQU_MODEL,EQU_AUTH,EQU_MENU,EQU_CODE,EQU_PF,EQU_POW,EQU_MB) " +
                " values(?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, equs.get(i).getGuid());
                    ps.setString(2, equs.get(i).getStationGuid());
                    ps.setString(3, equs.get(i).getEquType());
                    ps.setString(4, equs.get(i).getEquModel());
                    ps.setString(5, equs.get(i).getEquAuth());
                    ps.setString(6, equs.get(i).getEquMenu());
                    ps.setString(7, equs.get(i).getEquCode());
                    ps.setString(8, equs.get(i).getEquPf());
                    ps.setDouble(9, equs.get(i).getEquPow());
                    ps.setString(10, equs.get(i).getEquMb());
                }

                @Override
                public int getBatchSize() {
                    return equs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return equs.size();
    }

    @Override
    public int batchUpdateByGuid(List<RsbtEqu> equs, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_EQU SET STATION_GUID=?,EQU_TYPE=?,EQU_MODEL=?,EQU_AUTH=?,EQU_MENU=?,EQU_CODE=?,EQU_PF=?,EQU_POW=?,EQU_MB=? " +
                " where guid = ?";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, equs.get(i).getStationGuid());
                    ps.setString(2, equs.get(i).getEquType());
                    ps.setString(3, equs.get(i).getEquModel());
                    ps.setString(4, equs.get(i).getEquAuth());
                    ps.setString(5, equs.get(i).getEquMenu());
                    ps.setString(6, equs.get(i).getEquCode());
                    ps.setString(7, equs.get(i).getEquPf());
                    ps.setDouble(8, equs.get(i).getEquPow());
                    ps.setString(9, equs.get(i).getEquMb());
                    ps.setString(10, equs.get(i).getGuid());
                }

                @Override
                public int getBatchSize() {
                    return equs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return equs.size();
    }

    @Override
    public int batchUpdateByEtCode(List<EquAllDTO> equAllDTOs, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_EQU SET STATION_GUID=?,EQU_TYPE=?,EQU_MODEL=?,EQU_AUTH=?,EQU_MENU=?,EQU_CODE=?,EQU_PF=?,EQU_POW=?,EQU_MB=? " +
                " where guid = (SELECT guid from Rsbt_equ_t where ET_EQU_CCode = ?)";

        String sqlEt = " UPDATE RSBT_EQU_T SET ET_EQU_SUM=?,ET_EQU_UPOW=?,ET_EQU_DPOW=? " +
                " WHERE ET_EQU_CCode = ? ";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, equAllDTOs.get(i).getStationGuid());
                    ps.setString(2, equAllDTOs.get(i).getEquType());
                    ps.setString(3, equAllDTOs.get(i).getEquModel());
                    ps.setString(4, equAllDTOs.get(i).getEquAuth());
                    ps.setString(5, equAllDTOs.get(i).getEquMenu());
                    ps.setString(6, equAllDTOs.get(i).getEquCode());
                    ps.setString(7, equAllDTOs.get(i).getEquPf());
                    ps.setDouble(8, equAllDTOs.get(i).getEquPow());
                    ps.setString(9, equAllDTOs.get(i).getEquMb());
                    ps.setString(10, equAllDTOs.get(i).getEtEquCcode());
                }

                @Override
                public int getBatchSize() {
                    return equAllDTOs.size();
                }
            });

            jdbcTemplate.batchUpdate(sqlEt, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setInt(1, equAllDTOs.get(i).getEtEquSum());
                    ps.setDouble(2, equAllDTOs.get(i).getEtEquUpow());
                    ps.setDouble(3, equAllDTOs.get(i).getEtEquDpow());
                    ps.setString(4, equAllDTOs.get(i).getEtEquCcode());
                }

                @Override
                public int getBatchSize() {
                    return equAllDTOs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return equAllDTOs.size();
    }

}
