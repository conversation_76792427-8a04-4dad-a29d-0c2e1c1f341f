package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtFreqSyncRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.FreqAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtFreqSyncRepositoryI extends BasicRepositoryI<RsbtFreq, JdbcTemplate> implements RsbtFreqSyncRepository {
    @Override
    public List<RsbtFreq> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_Freq.* from Rsbt_Freq,Rsbt_station " +
                    "where Rsbt_Freq.station_guid = Rsbt_station.guid " +
                    "and Rsbt_station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtFreq.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtFreq> freqs, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_FREQ(GUID,STATION_GUID,FREQ_TYPE,FREQ_LC,FREQ_UC,FREQ_EFB,FREQ_EFE,FREQ_E_BAND,FREQ_RFB,FREQ_RFE,FREQ_R_BAND," +
                "FREQ_MOD,FREQ_MB,FREQ_CODE) " +
                " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, freqs.get(i).getGuid());
                    ps.setString(2, freqs.get(i).getStationGuid());
                    ps.setString(3, freqs.get(i).getFreqType());
                    ps.setDouble(4, freqs.get(i).getFreqLc());
                    ps.setDouble(5, freqs.get(i).getFreqUc());
                    ps.setDouble(6, freqs.get(i).getFreqEfb());
                    ps.setDouble(7, freqs.get(i).getFreqEfe());
                    ps.setDouble(8, freqs.get(i).getFreqEBand());
                    ps.setDouble(9, freqs.get(i).getFreqRfb());
                    ps.setDouble(10, freqs.get(i).getFreqRfe());
                    ps.setDouble(11, freqs.get(i).getFreqRBand());
                    ps.setString(12, freqs.get(i).getFreqMod());
                    ps.setString(13, freqs.get(i).getFreqMb());
                    ps.setString(14, freqs.get(i).getFreqCode());
                }

                @Override
                public int getBatchSize() {
                    return freqs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return freqs.size();
    }

    @Override
    public List<FreqAllDTO> findAllByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_Freq.*,Rsbt_freq_t.* from Rsbt_Freq,Rsbt_freq_t,Rsbt_station " +
                    "where Rsbt_Freq.station_guid = Rsbt_station.guid " +
                    "and Rsbt_Freq.guid = Rsbt_Freq_t.guid " +
                    "and Rsbt_station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(FreqAllDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public int batchUpdateByFtCode(List<FreqAllDTO> freqs, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_FREQ SET STATION_GUID=?,FREQ_TYPE=?,FREQ_LC=?,FREQ_UC=?,FREQ_EFB=?,FREQ_EFE=?,FREQ_E_BAND=?,FREQ_RFB=?,FREQ_RFE=?,FREQ_R_BAND=?," +
                "FREQ_MOD=?,FREQ_MB=?,FREQ_CODE=?  " +
                " where guid = (SELECT guid from RSBT_FREQ_T where FT_FREQ_CCode = ?)";

        String sqlFt = " update RSBT_FREQ_T set FT_FREQ_CSGN=? " +
                " where FT_FREQ_CCode = ?";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, freqs.get(i).getStationGuid());
                    ps.setString(2, freqs.get(i).getFreqType());
                    ps.setDouble(3, freqs.get(i).getFreqLc());
                    ps.setDouble(4, freqs.get(i).getFreqUc());
                    ps.setDouble(5, freqs.get(i).getFreqEfb());
                    ps.setDouble(6, freqs.get(i).getFreqEfe());
                    ps.setDouble(7, freqs.get(i).getFreqEBand());
                    ps.setDouble(8, freqs.get(i).getFreqRfb());
                    ps.setDouble(9, freqs.get(i).getFreqRfe());
                    ps.setDouble(10, freqs.get(i).getFreqRBand());
                    ps.setString(11, freqs.get(i).getFreqMod());
                    ps.setString(12, freqs.get(i).getFreqMb());
                    ps.setString(13, freqs.get(i).getFreqCode());
                    ps.setString(14, freqs.get(i).getFtFreqCcode());
                }

                @Override
                public int getBatchSize() {
                    return freqs.size();
                }
            });

            jdbcTemplate.batchUpdate(sqlFt, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, freqs.get(i).getFtFreqCsgn());
                    ps.setString(2, freqs.get(i).getFtFreqCcode());
                }

                @Override
                public int getBatchSize() {
                    return freqs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return freqs.size();
    }
}
