package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtStationBakSyncRepository;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtStationBakSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    private RsbtStationBakSyncRepository rsbtStationBakSyncRepository;

    public List<ApplyJobInDTO> findCountByAppGuid(String appGuid) {
        return rsbtStationBakSyncRepository.findCountByAppGuid(appGuid, oracleJdbcTemplate);
    }

    /**
     * 查询为审核的数据
     *
     * @param appGuid
     * @return
     */
    public int findStationCountByAppGuid(String appGuid) {
        return rsbtStationBakSyncRepository.findStationCountByAppGuid(appGuid, oracleJdbcTemplate);
    }

    public int updateByAppGuid(String appGuid, String status) {
        return rsbtStationBakSyncRepository.updateByAppGuid(appGuid, status, oracleJdbcTemplate);
    }
}
