package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtStationT;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtStationTSyncRepository extends BasicRepository<RsbtStationT, JdbcTemplate> {

    List<RsbtStationT> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    RsbtStationT findByStatGuid(String statGuid, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtStationT> rsbtStationTs, JdbcTemplate jdbcTemplate);

    int batchUpdateByGuid(List<RsbtStationT> rsbtStationTs, JdbcTemplate jdbcTemplate);
}
