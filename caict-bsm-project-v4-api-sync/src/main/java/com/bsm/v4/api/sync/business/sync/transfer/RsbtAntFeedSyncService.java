package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtAntFeedSyncRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.AntFeedAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtAntFeedSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtAntFeedSyncRepository antFeedSyncRepository;

    public List<RsbtAntfeed> findByAppCode(String appCode) {
        return antFeedSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public List<AntFeedAllDTO> findAllByAppCode(String appCode) {
        return antFeedSyncRepository.findAllByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtAntfeed> antfeeds) {
        return antFeedSyncRepository.batchInsert(antfeeds, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByAtCode(List<AntFeedAllDTO> antfeeds) {
        return antFeedSyncRepository.batchUpdateByAtCode(antfeeds, oracleStandardJdbcTemplate);
    }
}
