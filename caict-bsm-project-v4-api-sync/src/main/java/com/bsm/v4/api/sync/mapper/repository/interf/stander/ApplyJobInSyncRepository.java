package com.bsm.v4.api.sync.mapper.repository.interf.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.applytable.ApplyJobIn;
import org.springframework.jdbc.core.JdbcTemplate;

public interface ApplyJobInSyncRepository extends BasicRepository<ApplyJobIn, JdbcTemplate> {

    int updateByAppCode(String appGuid, String appCode, String status, JdbcTemplate jdbcTemplate);
}
