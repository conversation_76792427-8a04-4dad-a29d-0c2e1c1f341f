package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtAntFeedSyncRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.AntFeedAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtAntFeedSyncRepositoryI extends BasicRepositoryI<RsbtAntfeed, JdbcTemplate> implements RsbtAntFeedSyncRepository {

    @Override
    public List<RsbtAntfeed> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_antfeed.* from Rsbt_antfeed,Rsbt_Station " +
                    "where Rsbt_antfeed.station_guid = Rsbt_Station.guid " +
                    "and Rsbt_Station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtAntfeed.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<AntFeedAllDTO> findAllByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_antfeed.*,Rsbt_antfeed_t.* from Rsbt_antfeed,Rsbt_antfeed_t,Rsbt_station " +
                    "where Rsbt_antfeed.station_guid = Rsbt_station.guid " +
                    "and Rsbt_antfeed.guid = Rsbt_antfeed_t.guid " +
                    "and Rsbt_station.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(AntFeedAllDTO.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtAntfeed> antfeeds, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_ANTFEED(GUID,STATION_GUID,ANT_WORK_TYPE,ANT_POLE,ANT_RPOLE,ANT_EPOLE,ANT_TYPE,ANT_MODEL,ANT_MENU," +
                "ANT_HIGHT,ANT_GAIN,ANT_EGAIN,ANT_RGAIN,ANT_ANGLE,ANT_SIZE,FEED_MENU,FEED_MODEL,FEED_LENGTH,FEED_LOSE,ANT_CODE,FEED_CODE) " +
                " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, antfeeds.get(i).getGuid());
                    ps.setString(2, antfeeds.get(i).getStationGuid());
                    ps.setString(3, antfeeds.get(i).getAntWorkType());
                    ps.setString(4, antfeeds.get(i).getAntPole());
                    ps.setString(5, antfeeds.get(i).getAntRpole());
                    ps.setString(6, antfeeds.get(i).getAntEpole());
                    ps.setString(7, antfeeds.get(i).getAntType());
                    ps.setString(8, antfeeds.get(i).getAntModel());
                    ps.setString(9, antfeeds.get(i).getAntMenu());
                    ps.setDouble(10, antfeeds.get(i).getAntHight());
                    ps.setDouble(11, antfeeds.get(i).getAntGain());
                    ps.setDouble(12, antfeeds.get(i).getAntEgain());
                    ps.setDouble(13, antfeeds.get(i).getAntRgain());
                    ps.setDouble(14, antfeeds.get(i).getAntAngle());
                    ps.setString(15, antfeeds.get(i).getAntSize());
                    ps.setString(16, antfeeds.get(i).getFeedMenu());
                    ps.setString(17, antfeeds.get(i).getFeedModel());
                    ps.setDouble(18, antfeeds.get(i).getFeedLength());
                    ps.setDouble(19, antfeeds.get(i).getFeedLose());
                    ps.setLong(20, antfeeds.get(i).getAntCode());
                    ps.setLong(21, antfeeds.get(i).getFeedCode());
                }

                @Override
                public int getBatchSize() {
                    return antfeeds.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return antfeeds.size();
    }

    @Override
    public int batchUpdateByAtCode(List<AntFeedAllDTO> antfeeds, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_ANTFEED SET STATION_GUID=?,ANT_WORK_TYPE=?,ANT_POLE=?,ANT_RPOLE=?,ANT_EPOLE=?,ANT_TYPE=?,ANT_MODEL=?,ANT_MENU=?," +
                "ANT_HIGHT=?,ANT_GAIN=?,ANT_EGAIN=?,ANT_RGAIN=?,ANT_ANGLE=?,ANT_SIZE=?,FEED_MENU=?,FEED_MODEL=?,FEED_LENGTH=?,FEED_LOSE=?,ANT_CODE=?,FEED_CODE=? " +
                " where guid = (SELECT guid from RSBT_ANTFEED_T where AT_CCode = ?)";

        String sqlAt = "update RSBT_ANTFEED_t set AT_CSGN=?,AT_ANG_B=?,AT_ANG_E=?,AT_3DBE=?,AT_3DBR=? " +
                "where AT_CCODE = ?";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, antfeeds.get(i).getStationGuid());
                    ps.setString(2, antfeeds.get(i).getAntWorkType());
                    ps.setString(3, antfeeds.get(i).getAntPole());
                    ps.setString(4, antfeeds.get(i).getAntRpole());
                    ps.setString(5, antfeeds.get(i).getAntEpole());
                    ps.setString(6, antfeeds.get(i).getAntType());
                    ps.setString(7, antfeeds.get(i).getAntModel());
                    ps.setString(8, antfeeds.get(i).getAntMenu());
                    ps.setDouble(9, antfeeds.get(i).getAntHight());
                    ps.setDouble(10, antfeeds.get(i).getAntGain());
                    ps.setDouble(11, antfeeds.get(i).getAntEgain());
                    ps.setDouble(12, antfeeds.get(i).getAntRgain());
                    ps.setDouble(13, antfeeds.get(i).getAntAngle());
                    ps.setString(14, antfeeds.get(i).getAntSize());
                    ps.setString(15, antfeeds.get(i).getFeedMenu());
                    ps.setString(16, antfeeds.get(i).getFeedModel());
                    ps.setDouble(17, antfeeds.get(i).getFeedLength());
                    ps.setDouble(18, antfeeds.get(i).getFeedLose());
                    ps.setLong(19, antfeeds.get(i).getAntCode());
                    ps.setLong(20, antfeeds.get(i).getFeedCode());
                    ps.setString(21, antfeeds.get(i).getAtCcode());
                }

                @Override
                public int getBatchSize() {
                    return antfeeds.size();
                }
            });

            jdbcTemplate.batchUpdate(sqlAt, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, antfeeds.get(i).getAtCsgn());
                    ps.setDouble(2, antfeeds.get(i).getAtAngB());
                    ps.setDouble(3, antfeeds.get(i).getAtAngE());
                    ps.setDouble(4, antfeeds.get(i).getAt3dbr());
                    ps.setDouble(5, antfeeds.get(i).getAt3dbe());
                    ps.setString(6, antfeeds.get(i).getAtCcode());
                }

                @Override
                public int getBatchSize() {
                    return antfeeds.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return antfeeds.size();
    }
}
