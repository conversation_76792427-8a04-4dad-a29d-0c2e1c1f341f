package com.bsm.v4.api.sync.business.sync.stander;

import com.bsm.v4.api.sync.mapper.repository.interf.stander.ApplyLinkSyncRepository;
import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class ApplyLinkSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    private ApplyLinkSyncRepository applyLinkSyncRepository;

    public List<ApplyLink> findByNewAppCode(String appCode) {
        return applyLinkSyncRepository.findByNewAppCode(appCode, oracleJdbcTemplate);
    }

    public int updateByNewAppCode(String appGuid, String appCode, String status) {
        return applyLinkSyncRepository.updateByNewAppCode(appGuid, appCode, status, oracleJdbcTemplate);
    }
}
