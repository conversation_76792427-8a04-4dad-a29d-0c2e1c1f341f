package com.bsm.v4.api.sync.business.sync.stander;

import com.bsm.v4.api.sync.mapper.repository.interf.stander.TransportJobSyncRepository;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

/**
 * Created by yanchengpeng on 2020/12/9.
 */
@Service
public class TransportJobSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    private TransportJobSyncRepository transportJobSyncRepository;

    public int updateComplete(String appCode, String status) {
        return transportJobSyncRepository.updateComplete(appCode, status, oracleJdbcTemplate);
    }

    /**
     * 根据id查询数据
     *
     * @param guid guid
     * @return T
     */
    public TransportJob findByAppGuid(String guid) {
        return transportJobSyncRepository.findByAppGuid(guid, oracleJdbcTemplate);
    }

    /**
     * 更新数据
     *
     * @param jobGuid   jobGuid
     * @param isCompare isCompare
     */
    public void updateIsCompareByJobGuid(String jobGuid, String isCompare) {
        transportJobSyncRepository.updateIsCompareByJobGuid(jobGuid, isCompare, oracleJdbcTemplate);
    }
}
