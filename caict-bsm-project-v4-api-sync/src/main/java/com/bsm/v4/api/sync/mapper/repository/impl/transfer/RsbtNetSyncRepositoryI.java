package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtNetSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtNet;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtNetSyncRepositoryI extends BasicRepositoryI<RsbtNet, JdbcTemplate> implements RsbtNetSyncRepository {
    @Override
    public List<RsbtNet> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select RSBT_NET.* from RSBT_NET,Rsbt_APPLY " +
                    "where RSBT_NET.GUID = Rsbt_APPLY.NET_GUID " +
                    "AND Rsbt_APPLY.APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtNet.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtNet> nets, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_NET(GUID,ORG_GUID,FEE_GUID,ORG_CODE,NET_NAME,NET_SVN,NET_SP,NET_TS,NET_BAND,NET_AREA," +
                "NET_USE,NET_SAT_NAME,NET_LG,NET_START_DATE,NET_CONFIRM_DATE,NET_EXPIRED_DATE) " +
                " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, nets.get(i).getGuid());
                    ps.setString(2, nets.get(i).getOrgGuid());
                    ps.setString(3, nets.get(i).getFeeGuid());
                    ps.setString(4, nets.get(i).getOrgCode());
                    ps.setString(5, nets.get(i).getNetName());
                    ps.setString(6, nets.get(i).getNetSvn());
                    ps.setString(7, nets.get(i).getNetSp());
                    ps.setString(8, nets.get(i).getNetTs());
                    ps.setDouble(9, nets.get(i).getNetBand());
                    ps.setString(10, nets.get(i).getNetArea());
                    ps.setString(11, nets.get(i).getNetUse());
                    ps.setString(12, nets.get(i).getNetStaName());
                    ps.setString(13, nets.get(i).getNetLg());
                    ps.setDate(14, nets.get(i).getNetStartDate() != null ? new Date(nets.get(i).getNetStartDate().getTime()) : null);
                    ps.setDate(15, nets.get(i).getNetConfirmDate() != null ? new Date(nets.get(i).getNetConfirmDate().getTime()) : null);
                    ps.setDate(16, nets.get(i).getNetExpiredDate() != null ? new Date(nets.get(i).getNetExpiredDate().getTime()) : null);
                }

                @Override
                public int getBatchSize() {
                    return nets.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
        return nets.size();
    }
}
