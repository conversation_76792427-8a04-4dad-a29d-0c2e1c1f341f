package com.bsm.v4.api.sync.mapper.repository.impl.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtEquTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtEquT;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.test.annotation.Commit;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class RsbtEquTSyncRepositoryI extends BasicRepositoryI<RsbtEquT, JdbcTemplate> implements RsbtEquTSyncRepository {
    @Override
    public List<RsbtEquT> findByAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_equ_t.* from Rsbt_Station,Rsbt_equ,Rsbt_equ_t " +
                    "                    where Rsbt_Station.Guid = Rsbt_equ.Station_Guid " +
                    "                    and Rsbt_equ.guid = Rsbt_equ_t.Guid " +
                    "                    and Rsbt_Station.App_Code = ? ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtEquT.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<RsbtEquT> findCCode(String statGuid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select Rsbt_equ_t.* from Rsbt_equ,Rsbt_equ_t " +
                    "                    where Rsbt_equ.guid = Rsbt_equ_t.Guid " +
                    "                    and Rsbt_equ.station_guid = ? ";
            Object[] objects = {statGuid};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(RsbtEquT.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Commit
    public int batchInsert(List<RsbtEquT> equTs, JdbcTemplate jdbcTemplate) {
        String sql = " insert into RSBT_EQU_T(GUID,ET_EQU_SUM,ET_EQU_CCode,ET_EQU_UPOW,ET_EQU_DPOW) " +
                " values(?,?,?,?,?)";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, equTs.get(i).getGuid());
                    ps.setInt(2, equTs.get(i).getEtEquSum());
                    ps.setString(3, equTs.get(i).getEtEquCcode());
                    ps.setDouble(4, equTs.get(i).getEtEquUpow());
                    ps.setDouble(5, equTs.get(i).getEtEquDpow());
                }

                @Override
                public int getBatchSize() {
                    return equTs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public int batchUpdateByGuid(List<RsbtEquT> equTs, JdbcTemplate jdbcTemplate) {
        String sql = " UPDATE RSBT_EQU_T SET ET_EQU_SUM=?,ET_EQU_CCode=?,ET_EQU_UPOW=?,ET_EQU_DPOW=? " +
                " WHERE guid = ? ";
        try {
            jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setInt(1, equTs.get(i).getEtEquSum());
                    ps.setString(2, equTs.get(i).getEtEquCcode());
                    ps.setDouble(3, equTs.get(i).getEtEquUpow());
                    ps.setDouble(4, equTs.get(i).getEtEquDpow());
                    ps.setString(5, equTs.get(i).getGuid());
                }

                @Override
                public int getBatchSize() {
                    return equTs.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
}
