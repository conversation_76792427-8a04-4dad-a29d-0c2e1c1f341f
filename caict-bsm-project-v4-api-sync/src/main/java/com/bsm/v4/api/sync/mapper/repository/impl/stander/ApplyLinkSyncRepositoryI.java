package com.bsm.v4.api.sync.mapper.repository.impl.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.stander.ApplyLinkSyncRepository;
import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public class ApplyLinkSyncRepositoryI extends BasicRepositoryI<ApplyLink, JdbcTemplate> implements ApplyLinkSyncRepository {
    @Override
    public List<ApplyLink> findByNewAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from APPLY_LINK " +
                    "where APP_CODE_IN = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(ApplyLink.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public int updateByNewAppCode(String appGuid, String appCode, String status, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "UPDATE APPLY_LINK set SYNC_STATUS = ? " +
                    "where job_guid = ? " +
                    "and APP_CODE_IN = ? ";
            Object[] objects = {status, appGuid, appCode};
            return jdbcTemplate.update(sql, objects);
        } catch (Exception e) {
            return 0;
        }
    }
}
