package com.bsm.v4.api.sync.business.sync.stander;

import com.bsm.v4.api.sync.mapper.repository.interf.stander.ApplyJobInSyncRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class ApplyJobInSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    private ApplyJobInSyncRepository applyJobInSyncRepository;

    public int updateByAppCode(String appGuid, String appCode, String status) {
        return applyJobInSyncRepository.updateByAppCode(appGuid, appCode, status, oracleJdbcTemplate);
    }
}
