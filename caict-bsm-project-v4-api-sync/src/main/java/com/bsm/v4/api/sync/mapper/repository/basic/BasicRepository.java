package com.bsm.v4.api.sync.mapper.repository.basic;

import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by dengsy on 2019/7/3.
 * 持久层接口
 * T:实体类；J:数据源jdbcTemplate
 */
public interface BasicRepository<T extends Object, J extends JdbcTemplate> {

    /**
     * 公共添加方法
     * param:实体类对象
     */
    int insert(Object param, J jdbcTemplate);

    /**
     * 公共添加方法
     * params:实体类对象集合
     */
    int[] inserts(List<T> params, J jdbcTemplate);

    /**
     * 公共修改方法
     * param:实体类对象
     */
    int update(Object param, J jdbcTemplate);

    /**
     * 公共修改方法（批量修改）
     * param:实体类对象
     */
    int[] updates(List<T> params, J jdbcTemplate);

    /**
     * 公共修改方法（自定义sql）
     * 参数顺序必须一一对应
     */
    int[] updateBySql(String sql, List<String> values, List<String> ids, J jdbcTemplate);

    /**
     * 公共删除方法
     * param:实体类对象
     */
    int delete(String id, J jdbcTemplate);

    /**
     * 公共批量删除方法
     * param:实体类对象
     */
    int[] deletes(List<String> ids, J jdbcTemplate);

    /**
     * 公共批量删除方法（自定义sql）
     * 参数顺序必须一一对应
     * param:实体类对象
     */
    int[] deletesBySql(String sql, List<String> ids, J jdbcTemplate);

    /**
     * 公共查询方法（对象中有参数则拼接sql）
     * param:实体类对象
     */
    List<T> findByWhere(Object param, J jdbcTemplate);

    /**
     * 公共查询方法（查询全部）
     */
    List<T> findAll(J jdbcTemplate);

    /**
     * 公共查询方法（根据Id查询）
     */
    T findById(String id, J jdbcTemplate);
}
