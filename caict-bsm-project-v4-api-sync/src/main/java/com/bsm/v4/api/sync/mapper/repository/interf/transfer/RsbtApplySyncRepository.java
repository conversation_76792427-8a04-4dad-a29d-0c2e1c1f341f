package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtApplySyncRepository extends BasicRepository<RsbtApply, JdbcTemplate> {

    List<RsbtApply> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtApply> rsbtApplyList, JdbcTemplate jdbcTemplate);

    String findMaxAppCode(JdbcTemplate jdbcTemplate);
}
