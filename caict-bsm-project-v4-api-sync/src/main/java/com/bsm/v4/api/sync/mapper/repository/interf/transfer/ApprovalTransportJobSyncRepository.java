package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * ApprovalTransportJob Repository
 *
 * <AUTHOR>
 * @version jdk_1.8.0_144
 * @since 2022/1/12 15:07
 */
public interface ApprovalTransportJobSyncRepository extends BasicRepository<ApprovalTransportJob, JdbcTemplate> {

    /**
     * 根据job_guid和is_compare 查询数据
     *
     * @param appGuid appGuid
     * @return list
     */
    List<ApprovalTransportJob> findByAppGuidAndIsCompare(String appGuid, JdbcTemplate jdbcTemplate);

    /**
     * 根据ID查询
     *
     * @param appGuid appGuid
     * @return T
     */
    ApprovalTransportJob findByGuid(String appGuid, JdbcTemplate jdbcTemplate);

    /**
     * 根据ID更新
     *
     * @param jobGuid    appGuid
     * @param dataType   dataType
     * @param genNum     genNum
     * @param regionCode regionCode
     */
    void updateByJobUserTypeGen(String jobGuid, String techType, String dataType, String genNum, String regionCode, String isCompare, JdbcTemplate jdbcTemplate);
}
