package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.dto.business.transfer_in.EquAllDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtEquSyncRepository extends BasicRepository<RsbtEqu, JdbcTemplate> {
    List<RsbtEqu> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    List<EquAllDTO> findAllByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    RsbtEqu findByGuid(String equGuid, JdbcTemplate jdbcTemplate);

    String findByStatGuid(String statGuid, String code, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtEqu> equs, JdbcTemplate jdbcTemplate);

    int batchUpdateByGuid(List<RsbtEqu> equs, JdbcTemplate jdbcTemplate);

    int batchUpdateByEtCode(List<EquAllDTO> equAllDTOs, JdbcTemplate jdbcTemplate);
}
