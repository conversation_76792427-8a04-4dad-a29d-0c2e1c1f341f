package com.bsm.v4.api.sync.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * Created by dengsy on 2019/7/3
 * 多数据源配置
 */
@Configuration
public class DataSourceConfig {


    @Bean
    @Qualifier("oracleDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.oracle")
    public DataSource oracleDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    @Qualifier("oracleStandardDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.oraclestandard")
    public DataSource oracleStandardDataSource() {
        return DataSourceBuilder.create().build();
    }


    @Bean(name = "oracleJdbcTemplate")
    public JdbcTemplate oracleJdbcTemplate(@Qualifier("oracleDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "oracleStandardJdbcTemplate")
    public JdbcTemplate oracleStandardJdbcTemplate(@Qualifier("oracleStandardDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

}
