package com.bsm.v4.api.sync.mapper.repository.impl.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.stander.TransportJobBranchSyncRepository;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/9.
 */
@Repository
public class TransportJobBranchSyncRepositoryI extends BasicRepositoryI<TransportJobBranch, JdbcTemplate> implements TransportJobBranchSyncRepository {
    @Override
    public int updateByNewAppCode(String appCode, String status, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "UPDATE TRANSPORT_JOB_BRANCH set is_compare = ? " +
                    "where APP_CODE = (select app_code_out from apply_link where app_code_in = ?) ";
            Object[] objects = {status, appCode};
            return jdbcTemplate.update(sql, objects);
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public List<TransportJobBranch> findAllComplete(String appCode, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from TRANSPORT_JOB_BRANCH,APPLY_LINK where TRANSPORT_JOB_BRANCH.APP_CODE = APPLY_LINK.APP_CODE_OUT " +
                    "AND TRANSPORT_JOB_BRANCH.is_compare != '15' " +
                    "and APPLY_LINK.APP_CODE_IN = ? ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql, objects, new BeanPropertyRowMapper<>(TransportJobBranch.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }
}
