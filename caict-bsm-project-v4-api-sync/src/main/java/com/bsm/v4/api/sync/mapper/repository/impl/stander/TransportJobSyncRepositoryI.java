package com.bsm.v4.api.sync.mapper.repository.impl.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepositoryI;
import com.bsm.v4.api.sync.mapper.repository.interf.stander.TransportJobSyncRepository;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * Created by yanchengpeng on 2020/12/9.
 */
@Repository
public class TransportJobSyncRepositoryI extends BasicRepositoryI<TransportJob, JdbcTemplate> implements TransportJobSyncRepository {
    @Override
    public int updateComplete(String appCode, String status, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "UPDATE TRANSPORT_JOB set is_compare = ? " +
                    "where GUID = (" +
                    "SELECT JOB_GUID FROM TRANSPORT_JOB_BRANCH WHERE APP_CODE = ? )";
            Object[] objects = {status, appCode};
            int update = jdbcTemplate.update(sql, objects);
            return update;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 根据id查询数据
     *
     * @param guid guid
     * @return T
     */
    @Override
    public TransportJob findByAppGuid(String guid, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "select * from TRANSPORT_JOB where GUID = (select JOB_GUID from APPROVAL_TRANSPORT_JOB where GUID = ? )";
            Object[] objects = {guid};
            return jdbcTemplate.queryForObject(sql, objects, new BeanPropertyRowMapper<>(TransportJob.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 更新数据
     *
     * @param jobGuid   jobGuid
     * @param isCompare isCompare
     */
    @Override
    public void updateIsCompareByJobGuid(String jobGuid, String isCompare, JdbcTemplate jdbcTemplate) {
        try {
            String sql = "update TRANSPORT_JOB set is_compare = ? where GUID = ? ";
            Object[] objects = {isCompare, jobGuid};
            jdbcTemplate.update(sql, objects);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
