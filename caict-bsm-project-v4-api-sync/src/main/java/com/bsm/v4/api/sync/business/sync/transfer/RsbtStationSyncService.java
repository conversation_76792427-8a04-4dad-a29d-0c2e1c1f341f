package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtStationSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtStationSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;

    @Autowired
    private RsbtStationSyncRepository rsbtStationSyncRepository;

    public List<RsbtStation> findByAppCode(String appCode) {
        return rsbtStationSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtStation> stations) {
        return rsbtStationSyncRepository.batchInsert(stations, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByGuid(List<RsbtStation> stations) {
        return rsbtStationSyncRepository.batchUpdateByGuid(stations, oracleStandardJdbcTemplate);
    }

    public RsbtStation findOneByStatName(String statName) {
        return rsbtStationSyncRepository.findOneByStatName(statName, oracleStandardJdbcTemplate);
    }


}
