package com.bsm.v4.api.sync.mapper.repository.interf.transfer;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.dto.business.license.LicensePdfDTO;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfChildrenDTO;
import com.bsm.v4.system.model.dto.business.license.RsbtLicensePdfDTO;
import com.bsm.v4.system.model.dto.business.station.ISectionDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Date;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface RsbtLicenseSyncRepository extends BasicRepository<RsbtLicense, JdbcTemplate> {

    List<RsbtLicense> findByAppCode(String appCode, JdbcTemplate jdbcTemplate);

    RsbtLicense findByStatGuid(String statGuid, JdbcTemplate jdbcTemplate);

    int batchInsert(List<RsbtLicense> licenses, JdbcTemplate jdbcTemplate);

    int batchUpdateByStatGuid(List<RsbtLicense> licenses, JdbcTemplate jdbcTemplate);

    //查询总共的数据
    int selectCountAll(JdbcTemplate jdbcTemplate);

    //分页查询全部
    List<RsbtLicense> findAllPage(int rowStart, int rowEnd, JdbcTemplate jdbcTemplate);

    //分页查询电子执照总数据
    int selectLicensePdfDto(String licenseCode, String stationCode, String stationName, Date licenseStartDate, Date licenseEndDate, JdbcTemplate jdbcTemplate);

    //分页查询电子执照数据
    List<RsbtLicensePdfDTO> findAllPdfDto(String licenseCode, String stationCode, String stationName, Date licenseStartDate, Date licenseEndDate, int rowStart, int rowEnd, JdbcTemplate jdbcTemplate);

    //根据基站编号查询频率
    List<RsbtLicensePdfChildrenDTO> findPdfChildrenDto(String stationGuid, JdbcTemplate jdbcTemplate);

    //根据基站编号查询频率
    List<ISectionDTO> findPdfSectionDto(String stationGuid, JdbcTemplate jdbcTemplate);

    //根据基站guid查询单个执照信息
    LicensePdfDTO findOnePdfDto(String stationGuid, JdbcTemplate jdbcTemplate);
}
