package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtStationTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtStationT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtStationTSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtStationTSyncRepository stationTSyncRepository;

    public List<RsbtStationT> findByAppCode(String appCode) {
        return stationTSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public RsbtStationT findByStatGuid(String statGuid) {
        return stationTSyncRepository.findByStatGuid(statGuid, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtStationT> rsbtStationTs) {
        return stationTSyncRepository.batchInsert(rsbtStationTs, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByGuid(List<RsbtStationT> rsbtStationTs) {
        return stationTSyncRepository.batchUpdateByGuid(rsbtStationTs, oracleStandardJdbcTemplate);
    }
}
