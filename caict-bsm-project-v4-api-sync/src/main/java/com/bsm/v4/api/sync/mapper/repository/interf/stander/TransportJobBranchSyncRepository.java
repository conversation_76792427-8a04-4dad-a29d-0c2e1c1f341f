package com.bsm.v4.api.sync.mapper.repository.interf.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/9.
 */
public interface TransportJobBranchSyncRepository extends BasicRepository<TransportJobBranch, JdbcTemplate> {

    int updateByNewAppCode(String appCode, String status, JdbcTemplate jdbcTemplate);

    List<TransportJobBranch> findAllComplete(String appCode, JdbcTemplate jdbcTemplate);
}
