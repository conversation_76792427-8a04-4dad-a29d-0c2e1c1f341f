package com.bsm.v4.api.sync.controller.sync;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.sync.business.sync.zy.SyncZyDataService;
import com.bsm.v4.system.model.dto.business.transfer.SyncZyDataDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * SyncDataRawBtsDataController. 同步数据到总院
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 7/8/21 2:29 PM
 **/
@RestController
@RequestMapping(value = "/apiSync/transfer/syncToZy")
@Api(value = "sync同步数据至总院接口", tags = "sync同步数据至总院接口")
public class SyncZyDataController {

    private static final Logger LOG = LoggerFactory.getLogger(SyncZyDataController.class);

    @Autowired
    private SyncZyDataService service;

//    /**
//     * 自动同步数据到总院
//     */
//    @ApiOperation(value = "自动同步数据到总院", notes = "自动同步数据到总院接口")
//    @RequestMapping(value = "/AutoSyncToZy", method = RequestMethod.POST)
//    public void autoSyncToZy() {
//        service.autoSyncToZy();
//    }

    /**
     * 同步数据到总院
     *
     * @param request request
     * @return json
     */
    @ApiOperation(value = "同步数据到总院", notes = "同步数据到总院接口")
    @RequestMapping(value = "/syncZy", method = RequestMethod.POST, produces = {"application/json;charset=utf-8"})
    public String syncToZy(HttpServletRequest request) {
        StringBuilder str = getInfoFromRequest(request);
        if (str != null && str.length() > 10) {
            SyncZyDataDTO syncZyDataDTO = JSONObject.parseObject(str.toString(), SyncZyDataDTO.class);
            return service.syncToZy(syncZyDataDTO);
        }
        return "解析post数据失败";
    }

    /**
     * 获取post数据.
     *
     * @param request request
     * @return java.lang.StringBuilder
     */
    private StringBuilder getInfoFromRequest(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();

        try {
            InputStream in = request.getInputStream();
            InputStreamReader reader = new InputStreamReader(in, StandardCharsets.UTF_8);
            BufferedReader bd = new BufferedReader(reader);
            String temp;
            while ((temp = bd.readLine()) != null) {
                sb.append(temp);
            }
        } catch (IOException e) {
            e.printStackTrace();
            LOG.error(e.getMessage(), e);
            return null;
        }
        return sb;
    }

}
