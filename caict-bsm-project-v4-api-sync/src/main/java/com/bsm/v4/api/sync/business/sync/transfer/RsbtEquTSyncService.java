package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtEquTSyncRepository;
import com.bsm.v4.system.model.entity.business.station.RsbtEquT;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtEquTSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private RsbtEquTSyncRepository equTSyncRepository;

    public List<RsbtEquT> findByAppCode(String appCode) {
        return equTSyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public List<RsbtEquT> findCCode(String statGuid) {
        return equTSyncRepository.findCCode(statGuid, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtEquT> equTs) {
        return equTSyncRepository.batchInsert(equTs, oracleStandardJdbcTemplate);
    }

    public int batchUpdateByGuid(List<RsbtEquT> equTs) {
        return equTSyncRepository.batchUpdateByGuid(equTs, oracleStandardJdbcTemplate);
    }
}
