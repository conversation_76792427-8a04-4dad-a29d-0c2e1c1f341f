package com.bsm.v4.api.sync.business.sync.stander;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.sync.business.sync.transfer.*;
import com.bsm.v4.api.sync.mapper.repository.interf.stander.StanderRepository;
import com.bsm.v4.system.model.contrust.transfer.ApplyLinkStateConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobInBranchConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobInConst;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.AntFeedAllDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.EquAllDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.FreqAllDTO;
import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.bsm.v4.system.model.entity.business.station.*;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import com.caictframework.utils.util.JSONResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/2.
 */
@Service
public class StanderSyncWebService {

    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    private StanderRepository standerRepository;
    @Autowired
    private ApplyLinkSyncService applyLinkSyncService;
    @Autowired
    private ApprovalTransportJobSyncService approvalTransportJobSyncService;
    @Autowired
    private RsbtStationSyncService stationSyncService;
    @Autowired
    private RsbtStationTSyncService stationTSyncService;
    @Autowired
    private RsbtApplySyncService applySyncService;
    @Autowired
    private RsbtLicenseSyncService licenseSyncService;
    @Autowired
    private RsbtNetSyncService netSyncService;
    @Autowired
    private RsbtEafSyncService eafSyncService;
    @Autowired
    private RsbtEquSyncService equSyncService;
    @Autowired
    private RsbtEquTSyncService equTSyncService;
    @Autowired
    private RsbtFreqSyncService freqSyncService;
    @Autowired
    private RsbtFreqTSyncService freqTSyncService;
    @Autowired
    private RsbtAntFeedSyncService antFeedSyncService;
    @Autowired
    private RsbtAntFeedTSyncService antFeedTSyncService;
    @Autowired
    private RsbtStationBakSyncService stationBakSyncService;
    @Autowired
    private ApplyJobInSyncService applyJobInSyncService;

    @Autowired
    private TransportJobBranchSyncService transportJobBranchSyncService;
    @Autowired
    private TransportJobSyncService transportJobSyncService;


    public String getAppCode() {
        return standerRepository.getAppCode(oracleStandardJdbcTemplate);
    }

    public JSONObject checkAppCode(String appCode) {
        List<RsbtApplyDTO> rsbtApplyDTOS = standerRepository.checkAppCode(appCode, oracleStandardJdbcTemplate);
        if (rsbtApplyDTOS.size() == 0) {
            return JSONResult.getSuccessJson("未发现此申请表编号");
        }
        return JSONResult.getFailureJson("此申请表编号重复");
    }

    public void commit(String appCode, String appGuid, String dataType) {
        try {
            // 推送数据
//            pushData(appCode, dataType);
            //修改申请表状态为推送成功
            applyLinkSyncService.updateByNewAppCode(appGuid, appCode, "2");
            //修改任务为推送成功
            applyJobInSyncService.updateByAppCode(appGuid, appCode, ApplyLinkStateConst.APPLY_SYNC_SUCCESS);
            //查询任务下申请表是否全部推送成功
            List<ApplyJobInDTO> countByAppGuid = stationBakSyncService.findCountByAppGuid(appGuid);
            int unCheckStationCount = stationBakSyncService.findStationCountByAppGuid(appGuid);
            if (countByAppGuid.size() == 0 && unCheckStationCount == 0) {
                stationBakSyncService.updateByAppGuid(appGuid, TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
                ApprovalTransportJob approvalTransportJob = approvalTransportJobSyncService.findByGuid(appGuid);
                approvalTransportJobSyncService.updateByJobUserTypeGen(approvalTransportJob.getJobGuid(), approvalTransportJob.getTechType(), approvalTransportJob.getDataType(),
                        approvalTransportJob.getGenNum(), approvalTransportJob.getRegionCode(), TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
            }
            List<ApprovalTransportJob> approvalTransportJobs = approvalTransportJobSyncService.findByAppGuidAndIsCompare(appGuid);
            if (approvalTransportJobs.size() == 0) {
                // 更新主任务状态
                TransportJob transportJob = transportJobSyncService.findByAppGuid(appGuid);
                transportJobSyncService.updateIsCompareByJobGuid(String.valueOf(transportJob.getGuid()), TransportJobInConst.JOBIN_COMPLETE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //transportJobBranchSyncService.updateByNewAppCode(appCode, TransportJobInBranchConst.BRANCHIN_CHECK_FAIL);
            applyJobInSyncService.updateByAppCode(appGuid, appCode, ApplyLinkStateConst.APPLY_SYNC_FAIL);
            applyLinkSyncService.updateByNewAppCode(appGuid, appCode, ApplyLinkStateConst.APPLY_SYNC_FAIL);
        }
    }

    /**
     * 推送数据到国家库
     *
     * @param appCode  appCode
     * @param dataType dataType
     */
    private void pushData(String appCode, String dataType) {
        if (dataType == null) {
            //本批数据推送完成，将任务状态改为已完成
            transportJobBranchSyncService.updateByNewAppCode(appCode, TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
            //检查分任务是否完成，完成后修改主任务状态为已完成
            List<TransportJobBranch> allComplete = transportJobBranchSyncService.findAllComplete(appCode);
            if (allComplete.size() == 0) {
                transportJobSyncService.updateComplete(appCode, TransportJobInConst.JOBIN_COMPLETE);
            }
        } else {
            //查询需要处理的数据
            List<ApplyLink> applyLinks = applyLinkSyncService.findByNewAppCode(appCode);
            switch (dataType) {
                case "1":
                    //处理新增
                    applyLinks.forEach(applyLink -> {
                        //共性表net
                        List<RsbtNet> nets = netSyncService.findByAppCode(applyLink.getAppCodeIn());
                        netSyncService.batchInsert(nets);
                        //基站
                        List<RsbtStation> rsbtStations = stationSyncService.findByAppCode(applyLink.getAppCodeIn());
                        stationSyncService.batchInsert(rsbtStations);
                        //基站关联表
                        List<RsbtStationT> rsbtStationTs = stationTSyncService.findByAppCode(applyLink.getAppCodeIn());
                        stationTSyncService.batchInsert(rsbtStationTs);
                        //执照
                        List<RsbtLicense> rsbtLicenses = licenseSyncService.findByAppCode(applyLink.getAppCodeIn());
                        licenseSyncService.batchInsert(rsbtLicenses);
                        //申请表
                        List<RsbtApply> rsbtApplyList = applySyncService.findByAppCode(applyLink.getAppCodeIn());
                        applySyncService.batchInsert(rsbtApplyList);
                        //设备
                        List<RsbtEqu> equs = equSyncService.findByAppCode(applyLink.getAppCodeIn());
                        equSyncService.batchInsert(equs);
                        List<RsbtEquT> equTs = equTSyncService.findByAppCode(applyLink.getAppCodeIn());
                        equTSyncService.batchInsert(equTs);
                        //频率
                        List<RsbtFreq> freqs = freqSyncService.findByAppCode(applyLink.getAppCodeIn());
                        freqSyncService.batchInsert(freqs);
                        List<RsbtFreqT> freqTs = freqTSyncService.findByAppCode(applyLink.getAppCodeIn());
                        freqTSyncService.batchInsert(freqTs);
                        //天线
                        List<RsbtAntfeed> antfeeds = antFeedSyncService.findByAppCode(applyLink.getAppCodeIn());
                        antFeedSyncService.batchInsert(antfeeds);
                        List<RsbtAntfeedT> antfeedTs = antFeedTSyncService.findByAppCode(applyLink.getAppCodeIn());
                        antFeedTSyncService.batchInsert(antfeedTs);
                        //eaf表
                        List<RsbtEaf> eafs = eafSyncService.findByAppCode(applyLink.getAppCodeIn());
                        eafSyncService.batchInsert(eafs);
                    });
                    break;
                case "2":
                    //处理变更
                    applyLinks.parallelStream().forEach(applyLink -> {
                        //申请表
                        List<RsbtApply> rsbtApplyList = applySyncService.findByAppCode(applyLink.getAppCodeIn());
                        applySyncService.batchInsert(rsbtApplyList);
                        List<RsbtStation> stations = new ArrayList<>();
                        List<RsbtStationT> stationTs = new ArrayList<>();
                        List<RsbtLicense> licenses = new ArrayList<>();
                        //基站
                        List<RsbtStation> rsbtStations = stationSyncService.findByAppCode(applyLink.getAppCodeIn());
                        for (RsbtStation station : rsbtStations) {
                            RsbtStation statOne = stationSyncService.findOneByStatName(station.getStatName());
                            if (statOne != null) {
                                String statGuid = statOne.getGuid();
                                //基站
                                station.setGuid(statGuid);
                                RsbtStationT statT = stationTSyncService.findByStatGuid(station.getGuid());
                                statT.setGuid(statGuid);
                                stations.add(station);
                                stationTs.add(statT);
                                //执照
                                RsbtLicense licenseOwn = licenseSyncService.findByStatGuid(station.getGuid());
                                licenseOwn.setStationGuid(statGuid);
                                licenses.add(licenseOwn);
                            }
                        }
                        //基站
                        stationSyncService.batchUpdateByGuid(stations);
                        stationTSyncService.batchUpdateByGuid(stationTs);
                        //执照
                        licenseSyncService.batchUpdateByStatGuid(licenses);
                        //设备
                        List<EquAllDTO> equAll = equSyncService.findAllByAppCode(applyLink.getAppCodeIn());
                        equSyncService.batchUpdateByEtCode(equAll);
                        //频率
                        List<FreqAllDTO> freqAll = freqSyncService.findAllByAppCode(applyLink.getAppCodeIn());
                        freqSyncService.batchUpdateByFtCode(freqAll);
                        //天线
                        List<AntFeedAllDTO> antfeedAll = antFeedSyncService.findAllByAppCode(applyLink.getAppCodeIn());
                        antFeedSyncService.batchUpdateByAtCode(antfeedAll);
                    });
                    break;
                case "3":
                    //处理注销
                    System.out.println("处理注销");
                    break;
                case "4":
                    //处理延续
                    System.out.println("处理延续");
                    break;
                default:
                    break;
            }
        }
    }
}
