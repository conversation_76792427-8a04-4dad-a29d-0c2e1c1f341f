package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.ApprovalTransportJobSyncRepository;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * ApprovalTransportJobSyncService.
 *
 * <AUTHOR>
 * @version jdk_1.8.0_144
 * @since 2022/1/12 15:07
 */
@Service
public class ApprovalTransportJobSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;
    @Autowired
    private ApprovalTransportJobSyncRepository approvalTransportJobSyncRepository;

    /**
     * 根据job_guid和is_compare 查询数据
     *
     * @param appGuid appGuid
     * @return list
     */
    public List<ApprovalTransportJob> findByAppGuidAndIsCompare(String appGuid) {
        return approvalTransportJobSyncRepository.findByAppGuidAndIsCompare(appGuid, oracleJdbcTemplate);
    }

    /**
     * 根据ID查询
     *
     * @param appGuid appGuid
     * @return T
     */
    public ApprovalTransportJob findByGuid(String appGuid) {
        return approvalTransportJobSyncRepository.findByGuid(appGuid, oracleJdbcTemplate);
    }

    /**
     * 根据ID更新
     *
     * @param jobGuid    appGuid
     * @param dataType   dataType
     * @param genNum     genNum
     * @param regionCode regionCode
     */
    public void updateByJobUserTypeGen(String jobGuid, String techType, String dataType, String genNum, String regionCode, String isCompare) {
        approvalTransportJobSyncRepository.updateByJobUserTypeGen(jobGuid, techType, dataType, genNum, regionCode, isCompare, oracleJdbcTemplate);
    }

}
