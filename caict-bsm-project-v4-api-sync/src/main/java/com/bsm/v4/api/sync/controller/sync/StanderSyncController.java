package com.bsm.v4.api.sync.controller.sync;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.sync.business.sync.stander.StanderSyncWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by yanchengpeng on 2020/12/2.
 */

@RestController
@RequestMapping(value = "/apiSync/transfer/stander")
@Api(value = "sync对接标准数据库", tags = "sync对接标准数据库接口")
public class StanderSyncController extends BasicController {

    @Autowired
    private StanderSyncWebService webService;

    @ApiOperation(value = "获取频率台站库申请表编号", notes = "获取频率台站库申请表编号接口")
    @GetMapping("/getAppCode")
    public String getAppCode() {
        return webService.getAppCode();
    }

    @ApiOperation(value = "检验频率台站库申请表编号是否存在", notes = "检验频率台站库申请表编号是否存在接口")
    @GetMapping("/checkAppCode/{appCode}")
    public JSONObject checkAppCode(@PathVariable("appCode") String appCode) {
        return webService.checkAppCode(appCode);
    }
}
