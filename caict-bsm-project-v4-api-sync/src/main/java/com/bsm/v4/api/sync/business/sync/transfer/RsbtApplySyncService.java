package com.bsm.v4.api.sync.business.sync.transfer;

import com.bsm.v4.api.sync.mapper.repository.interf.transfer.RsbtApplySyncRepository;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtApplySyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;

    @Autowired
    private RsbtApplySyncRepository applySyncRepository;

    public List<RsbtApply> findByAppCode(String appCode) {
        return applySyncRepository.findByAppCode(appCode, oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtApply> rsbtApplyList) {
        return applySyncRepository.batchInsert(rsbtApplyList, oracleStandardJdbcTemplate);
    }

    /**
     * 查询波尔频率台站数据库最大申请表编号
     */
    public String findMaxAppCode() {
        return applySyncRepository.findMaxAppCode(oracleStandardJdbcTemplate);
    }
}
