package com.bsm.v4.api.sync.mapper.repository.interf.stander;

import com.bsm.v4.api.sync.mapper.repository.basic.BasicRepository;
import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
public interface ApplyLinkSyncRepository extends BasicRepository<ApplyLink, JdbcTemplate> {

    List<ApplyLink> findByNewAppCode(String appCode, JdbcTemplate jdbcTemplate);

    int updateByNewAppCode(String appGuid, String appCode, String status, JdbcTemplate jdbcTemplate);
}
