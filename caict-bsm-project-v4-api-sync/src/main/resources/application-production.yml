server:
  port: 8011
  tomcat:
    basedir: /home/<USER>/bsm/tomcat

caict:
  myFilePath: /home/<USER>/bsm/4.0/file/
  myFilePathTmp: /home/<USER>/bsm/4.0/tmp/
  generalTemplate: general_template.pdf
  generalTemplateForm: general_template_form.pdf
  myFileExportPath: /home/<USER>/bsm/4.0/file/export/
  myZipExportPath: /home/<USER>/bsm/4.0/file/zipTemp/
  myWordTemplateFile: /home/<USER>/bsm/4.0/file/export/
  myWordExportFile: /home/<USER>/bsm/4.0/file/export/report/
  mySqlInsertsNumber: 50
  mySqlDeleteNumber: 50
  myPasswordKey: caict
  zipPath: /home/<USER>/bsm/4.0/file/zip/

spring:
  application:
    name: caict-bsm-4.0-sync
  mvc:
    static-path-pattern: /**
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    oracle:
      driver-class-name: oracle.jdbc.driver.OracleDriver
      jdbc-url: *****************************************
      username: BSM_CQ
      password: caict
      max-idle: 10
      min-idle: 5
      initial-size: 5
      validation-query: select 1 from dual
      max-wait: 10000
    oraclestandard:
      driver-class-name: oracle.jdbc.driver.OracleDriver
      jdbc-url: *****************************************
      username: 6500_STATDB
      password: 6500_STATDB
      max-idle: 10
      min-idle: 5
      initial-size: 5
      validation-query: select 1 from dual
      max-wait: 10000

  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password: iiiav2022.
    timeout: 10000
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${caict.myFilePath}
  servlet:
    multipart:
      enabled: true
      max-file-size: 10240000000
      max-request-size: 102400000000
  main:
    allow-circular-references: true

mybatis:
  configuration:
    map-underscore-to-camel-case: true