# TRANSPORT_FILE_NEW MySQL兼容性说明

## 修复内容

已将TransportFileNewMapper中的SQL语句调整为MySQL规范，主要修改如下：

### 1. 时间函数修改

**修复前（Oracle语法）**：
```java
@Update("UPDATE TRANSPORT_FILE_NEW SET file_state = #{fileState}, updated_at = SYSDATE WHERE id = #{id}")
@Update("UPDATE TRANSPORT_FILE_NEW SET is_deleted = 1, updated_at = SYSDATE WHERE id = #{id}")
```

**修复后（MySQL语法）**：
```java
@Update("UPDATE TRANSPORT_FILE_NEW SET file_state = #{fileState}, updated_at = NOW() WHERE id = #{id}")
@Update("UPDATE TRANSPORT_FILE_NEW SET is_deleted = 1, updated_at = NOW() WHERE id = #{id}")
```

### 2. 分页语法修改

**修复前（Oracle语法）**：
```java
@Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE file_md5 = #{fileMd5} AND is_deleted = 0 AND ROWNUM = 1")
```

**修复后（MySQL语法）**：
```java
@Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE file_md5 = #{fileMd5} AND is_deleted = 0 LIMIT 1")
```

## MySQL建表语句

### 完整的建表SQL

```sql
-- 创建TRANSPORT_FILE_NEW表（MySQL版本）
CREATE TABLE TRANSPORT_FILE_NEW (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT,
    file_local_name VARCHAR(255) NOT NULL,
    file_renamed VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_state TINYINT DEFAULT 0 NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    data_type VARCHAR(50),
    file_extension VARCHAR(20),
    mime_type VARCHAR(100),
    file_md5 VARCHAR(32),
    is_deleted TINYINT DEFAULT 0 NOT NULL,
    uploaded_by VARCHAR(100),
    file_description VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### MySQL与Oracle的主要差异

| 特性 | Oracle | MySQL |
|------|--------|-------|
| 主键自增 | NUMBER(19) + SEQUENCE | BIGINT AUTO_INCREMENT |
| 字符串类型 | VARCHAR2 | VARCHAR |
| 数字类型 | NUMBER | BIGINT/TINYINT |
| 当前时间 | SYSDATE | NOW() 或 CURRENT_TIMESTAMP |
| 分页语法 | ROWNUM | LIMIT |
| 自动更新时间 | 触发器 | ON UPDATE CURRENT_TIMESTAMP |
| 字符集 | 数据库级别设置 | utf8mb4 |

### 索引创建

```sql
-- 创建索引
CREATE INDEX IDX_TRANSPORT_FILE_NEW_JOB_ID ON TRANSPORT_FILE_NEW(job_id);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_FILE_TYPE ON TRANSPORT_FILE_NEW(file_type);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_FILE_STATE ON TRANSPORT_FILE_NEW(file_state);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_MD5 ON TRANSPORT_FILE_NEW(file_md5);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_CREATED_AT ON TRANSPORT_FILE_NEW(created_at);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_IS_DELETED ON TRANSPORT_FILE_NEW(is_deleted);

-- 创建复合索引
CREATE INDEX IDX_TRANSPORT_FILE_NEW_JOB_TYPE ON TRANSPORT_FILE_NEW(job_id, file_type, is_deleted);
```

## 字段类型说明

### MySQL字段类型选择

| 字段名 | MySQL类型 | 说明 |
|--------|-----------|------|
| id | BIGINT AUTO_INCREMENT | 主键，自动递增 |
| job_id | BIGINT | 外键，可为NULL |
| file_local_name | VARCHAR(255) | 原始文件名 |
| file_renamed | VARCHAR(255) | 重命名后文件名 |
| file_path | VARCHAR(500) | 文件路径 |
| file_size | BIGINT | 文件大小（字节） |
| file_state | TINYINT | 文件状态（0-3） |
| file_type | VARCHAR(10) | 文件类型 |
| data_type | VARCHAR(50) | 数据类型 |
| file_extension | VARCHAR(20) | 文件扩展名 |
| mime_type | VARCHAR(100) | MIME类型 |
| file_md5 | VARCHAR(32) | MD5值 |
| is_deleted | TINYINT | 删除标志（0/1） |
| uploaded_by | VARCHAR(100) | 上传者 |
| file_description | VARCHAR(500) | 文件描述 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 自动时间戳特性

MySQL的`updated_at`字段使用了`ON UPDATE CURRENT_TIMESTAMP`特性，这意味着：
- 插入记录时，自动设置为当前时间
- 更新记录时，自动更新为当前时间
- 不需要在代码中手动设置更新时间

## 配置建议

### 1. 数据库连接配置

```yaml
spring:
  datasource:
    url: **********************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 2. MyBatis配置

```yaml
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
    default-fetch-size: 100
    default-statement-timeout: 30
```

### 3. 文件上传配置

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
```

## 测试验证

### 1. 启动验证
确认应用启动时没有SQL语法错误：
```bash
# 查看启动日志
tail -f logs/application.log | grep -i "transport_file_new"
```

### 2. 功能测试
```bash
# 测试文件上传
curl -X POST "http://localhost:8011/apiWeb/transferNew/transportFile/upload" \
  -F "file=@test.csv" \
  -F "jobId=123" \
  -F "fileType=1"

# 测试文件查询
curl -X GET "http://localhost:8011/apiWeb/transferNew/transportFile/findByJobId?jobId=123"
```

### 3. 数据库验证
```sql
-- 检查表结构
DESCRIBE TRANSPORT_FILE_NEW;

-- 检查索引
SHOW INDEX FROM TRANSPORT_FILE_NEW;

-- 测试插入数据
INSERT INTO TRANSPORT_FILE_NEW (job_id, file_local_name, file_renamed, file_path, file_size, file_state, file_type) 
VALUES (123, 'test.csv', 'test_20240101.csv', '/path/to/test_20240101.csv', 1024, 1, '1');
```

## 注意事项

1. **字符集**：确保数据库和表都使用utf8mb4字符集
2. **时区**：配置正确的时区，避免时间显示问题
3. **索引优化**：根据实际查询需求调整索引
4. **存储引擎**：使用InnoDB引擎支持事务和外键
5. **备份策略**：制定合适的数据备份策略

## 性能优化建议

1. **分页查询**：对于大量数据，使用LIMIT进行分页
2. **索引使用**：确保查询条件使用了合适的索引
3. **文件存储**：考虑使用云存储服务存储大文件
4. **缓存策略**：对频繁查询的数据使用Redis缓存
5. **连接池**：配置合适的数据库连接池参数

修复完成后，TRANSPORT_FILE_NEW表和相关服务应该能够在MySQL环境下正常工作。
