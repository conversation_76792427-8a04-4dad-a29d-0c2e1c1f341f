# CSV数据处理服务使用说明

## 功能概述

实现了一个完整的CSV数据处理服务方法 `processCsvDataAndSave`，用于将 `parseAndValidateCsv` 方法解析后的数据插入到相应的数据库表中。

## 服务方法

### `processCsvDataAndSave`

**功能**：处理CSV解析后的数据并插入数据库

**入参**：
- `validRows`: List<Map<String, Object>> - 有效数据行
- `invalidRows`: List<Map<String, Object>> - 无效数据行  
- `csvFilePath`: String - CSV文件路径

**处理逻辑**：
1. 创建新的传输任务记录（TRANSPORT_JOB_NEW表）
2. 将有效数据插入 TRANSPORT_RAW_BTS_DEAL_NEW 表
3. 将无效数据插入 TRANSPORT_RAW_BTS_DEAL_LOG_NEW 表
4. 返回处理结果统计

## 数据库表结构

### TRANSPORT_JOB_NEW（传输任务表）
- `operator_code` = "CMCC"
- `apply_type` = "1" 
- `status` = "1"
- `csv_file_path` = 上传文件路径
- `created_by` = "admin"
- `created_at` = 当前时间
- `updated_at` = 当前时间

### TRANSPORT_RAW_BTS_DEAL_NEW（有效数据表）
包含所有CSV字段映射的业务数据，如：
- cellName（扇区名称）
- statName（台站名称）
- statLg（经度）
- statLa（纬度）
- 等等...

### TRANSPORT_RAW_BTS_DEAL_LOG_NEW（错误日志表）
- `job_id` - 关联的任务ID
- `field_name` - 出错字段名
- `error_message` - 错误信息
- `original_value` - 原始错误值
- `deal_data` - 整行数据JSON
- `error_type` = "VALIDATION_ERROR"

## API接口

### 接口地址
```
POST /apiWeb/transferNew/zhuanTransportJob/processCsvDataAndSave
```

### 请求参数
```json
{
  "validRows": [
    {
      "jobId": 123,
      "jobBranchId": 456,
      "cellName": "扇区名称",
      "statName": "台站名称",
      "statLg": 116.123456,
      "statLa": 39.123456,
      // ... 其他字段
    }
  ],
  "invalidRows": [
    {
      "jobId": 123,
      "jobBranchId": 456,
      "cellName": "错误数据",
      "errors": [
        "字段'LONGITUDE'格式错误",
        "字段'LATITUDE'不能为空"
      ],
      // ... 其他字段
    }
  ],
  "csvFilePath": "/path/to/uploaded/file.csv"
}
```

### 返回结果
```json
{
  "success": true,
  "data": {
    "jobId": 789,
    "validRowsCount": 100,
    "invalidRowsCount": 5,
    "totalRowsCount": 105,
    "csvFilePath": "/path/to/uploaded/file.csv",
    "processTime": "2024-01-01T12:00:00"
  },
  "message": "操作成功"
}
```

## 使用流程

### 1. 完整的CSV处理流程

```java
// 1. 上传CSV文件
Map<String, Object> uploadResult = transportJobNewWebService.uploadCsvFile(file);
String filePath = (String) ((Map) uploadResult.get("data")).get("savePath");

// 2. 解析并校验CSV
JobMetaVO jobMeta = new JobMetaVO();
jobMeta.setJobId(123L);
jobMeta.setJobBranchId(456L);

Map<String, Object> parseResult = transportJobNewWebService.parseAndValidateCsv(filePath, jobMeta);
CsvValidationResultVO validationResult = (CsvValidationResultVO) parseResult.get("data");

// 3. 处理数据并入库
Map<String, Object> saveResult = transportJobNewWebService.processCsvDataAndSave(
    validationResult.getValidRows(),
    validationResult.getInvalidRows(),
    filePath
);
```

### 2. 通过API调用

```bash
# 1. 上传文件
curl -X POST "http://localhost:8011/apiWeb/transferNew/zhuanTransportJob/uploadCsv" \
  -F "file=@data.csv"

# 2. 解析校验
curl -X POST "http://localhost:8011/apiWeb/transferNew/zhuanTransportJob/parseAndValidateCsv" \
  -H "Content-Type: application/json" \
  -d '{
    "filePath": "/path/to/file.csv",
    "jobMeta": {
      "jobId": 123,
      "jobBranchId": 456
    }
  }'

# 3. 处理入库
curl -X POST "http://localhost:8011/apiWeb/transferNew/zhuanTransportJob/processCsvDataAndSave" \
  -H "Content-Type: application/json" \
  -d '{
    "validRows": [...],
    "invalidRows": [...],
    "csvFilePath": "/path/to/file.csv"
  }'
```

## 特性说明

### 1. 事务管理
- 使用 `@Transactional` 注解确保数据一致性
- 如果任何步骤失败，整个操作会回滚

### 2. 错误处理
- 单行数据插入失败不会影响其他行的处理
- 详细的错误日志记录
- 返回处理统计信息

### 3. 数据类型转换
- 安全的类型转换方法
- 支持 String、Long、Integer、BigDecimal 类型
- 空值和格式错误的容错处理

### 4. 字段映射
- 基于预定义的 CSV_FIELD_MAPPING 进行字段映射
- 支持CSV表头到实体类字段的自动转换

### 5. 错误信息解析
- 智能提取错误信息中的字段名
- 记录原始错误值用于问题排查
- 将整行数据序列化为JSON存储

## 注意事项

1. **数据量限制**：大量数据建议分批处理
2. **内存使用**：注意validRows和invalidRows的内存占用
3. **事务超时**：长时间运行的事务可能需要调整超时设置
4. **并发处理**：多个请求同时处理时注意数据库连接池配置
5. **日志监控**：关注控制台输出的处理日志和错误信息

## 扩展建议

1. **批量插入优化**：对于大量数据，可以考虑使用批量插入
2. **异步处理**：对于大文件，可以考虑异步处理机制
3. **进度反馈**：添加处理进度查询接口
4. **数据校验增强**：添加更多业务规则校验
5. **性能监控**：添加处理时间和性能指标统计
