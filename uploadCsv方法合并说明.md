# uploadCsv方法合并到uploadFile方法说明

## 合并目标

将`TransportJobNewWebService`中的`uploadCsvFile`方法合并到`TransportFileNewWebService`的`uploadFile`方法中，当文件类型为"1"（CSV数据文件）时，自动校验文件后缀名必须是.csv。

## 主要变更

### 1. TransportFileNewWebService修改

#### 1.1 修改uploadFile方法签名
```java
// 修改前
private Map<String, Object> validateFile(MultipartFile file)

// 修改后  
private Map<String, Object> validateFile(MultipartFile file, String fileType)
```

#### 1.2 添加CSV文件类型校验
在`validateFile`方法中添加了特殊校验逻辑：

```java
// 如果是CSV数据文件，校验文件后缀名必须是.csv
if ("1".equals(fileType)) {
    if (!"csv".equalsIgnoreCase(fileExtension)) {
        result.put("valid", false);
        result.put("message", "CSV数据文件的后缀名必须是.csv");
        return result;
    }
}
```

### 2. TransportJobNewWebService修改

#### 2.1 删除uploadCsvFile方法
完全移除了原来的`uploadCsvFile`方法，避免代码重复。

### 3. TransportJobNewController修改

#### 3.1 添加依赖注入
```java
@Autowired
private TransportFileNewWebService transportFileNewWebService;
```

#### 3.2 修改uploadCsv接口实现
```java
// 修改前
return this.basicReturnJson(file, TransportJobNewWebService.class,
        (fileParam, service) -> service.uploadCsvFile(fileParam));

// 修改后
return this.basicReturnJson(file, TransportFileNewWebService.class,
        (fileParam, service) -> service.uploadFile(fileParam, jobId, "1", dataType, uploadedBy, fileDescription));
```

#### 3.3 增加可选参数
为了保持向后兼容性，添加了可选参数：
- `jobId`: 关联的任务ID（可选）
- `dataType`: 数据类型（默认"CSV"）
- `uploadedBy`: 上传者（默认"admin"）
- `fileDescription`: 文件描述（默认"CSV数据文件"）

## 功能特性

### 1. 统一的文件上传入口
- 所有文件上传都通过`TransportFileNewWebService.uploadFile`方法处理
- 根据`fileType`参数进行不同的校验逻辑

### 2. CSV文件特殊校验
- 当`fileType = "1"`时，强制校验文件扩展名必须是`.csv`
- 其他文件类型按照原有逻辑校验

### 3. 完整的文件管理
- 自动生成唯一文件名
- 计算文件MD5防重复
- 保存文件记录到TRANSPORT_FILE_NEW表
- 支持事务回滚

### 4. 向后兼容
- 保持原有`/uploadCsv`接口不变
- 新增可选参数，不影响现有调用

## 文件类型说明

| fileType | 说明 | 校验规则 |
|----------|------|----------|
| "0" | 附件 | 支持所有允许的文件格式 |
| "1" | CSV数据文件 | 必须是.csv格式 |
| "2" | 材料附件 | 支持所有允许的文件格式 |

## 支持的文件格式

- **文档类型**: txt, doc, docx, pdf
- **表格类型**: xls, xlsx, csv
- **图片类型**: jpg, jpeg, png

## API接口变更

### 原有接口（保持兼容）
```
POST /apiWeb/transferNew/zhuanTransportJob/uploadCsv
```

**新增参数**：
- `jobId` (可选): 关联的任务ID
- `dataType` (可选): 数据类型，默认"CSV"
- `uploadedBy` (可选): 上传者，默认"admin"  
- `fileDescription` (可选): 文件描述，默认"CSV数据文件"

### 统一文件上传接口
```
POST /apiWeb/transferNew/transportFile/upload
```

**参数**：
- `file` (必填): 上传的文件
- `jobId` (必填): 关联的任务ID
- `fileType` (必填): 文件类型（0-附件，1-CSV数据文件，2-材料附件）
- `dataType` (可选): 数据类型
- `uploadedBy` (可选): 上传者，默认"admin"
- `fileDescription` (可选): 文件描述

## 使用示例

### 1. 上传CSV文件（原有接口）
```bash
curl -X POST "http://localhost:8011/apiWeb/transferNew/zhuanTransportJob/uploadCsv" \
  -F "file=@data.csv" \
  -F "jobId=123" \
  -F "dataType=全量数据"
```

### 2. 上传CSV文件（统一接口）
```bash
curl -X POST "http://localhost:8011/apiWeb/transferNew/transportFile/upload" \
  -F "file=@data.csv" \
  -F "jobId=123" \
  -F "fileType=1" \
  -F "dataType=全量数据"
```

### 3. 上传材料附件
```bash
curl -X POST "http://localhost:8011/apiWeb/transferNew/transportFile/upload" \
  -F "file=@document.pdf" \
  -F "jobId=123" \
  -F "fileType=2" \
  -F "dataType=申请材料"
```

## 错误处理

### CSV文件校验错误
```json
{
  "success": false,
  "message": "CSV数据文件的后缀名必须是.csv"
}
```

### 文件重复错误
```json
{
  "success": false,
  "message": "文件已存在，MD5: D41D8CD98F00B204E9800998ECF8427E"
}
```

## 优势

1. **代码统一**: 所有文件上传逻辑集中在一个方法中
2. **功能增强**: 支持MD5校验、文件记录管理等高级功能
3. **类型安全**: 根据文件类型进行针对性校验
4. **向后兼容**: 不影响现有接口调用
5. **易于维护**: 减少代码重复，便于后续功能扩展

## 注意事项

1. **文件类型参数**: 确保传入正确的fileType值
2. **CSV校验**: fileType="1"时必须上传.csv文件
3. **事务处理**: 文件上传失败会自动回滚数据库操作
4. **文件大小**: 默认限制100MB，可通过配置调整
5. **存储路径**: 确保配置的文件存储目录有足够权限和空间

合并完成后，系统拥有了更加统一和强大的文件上传管理功能。
