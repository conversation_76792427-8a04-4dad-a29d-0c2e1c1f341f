package com.bsm.v4.system.model.dto.business.license;


import com.bsm.v4.system.model.dto.business.station.ISectionDTO;


import java.util.Date;
import java.util.List;

/**
 * 执照编号
 * 台站名称
 * 台站设置使用人
 * 台址（经度，纬度）
 * 无线电台识别码/基站识别码  台站类别/技术体制
 * 对应频率许可证编号  起止日期
 * 发射参数信息（发射频率/频段  接收频率/频段  发射功率 占用带宽/波道间隔  其他必要信息（小区识别码））
 * 发射设备（信息设备型号  天线类型（尺寸） 天线增益  极化方式  天线距地高度）
 * 其他特别约定事项
 */
public class LicensePdfDTO {
    private String licenseCode; //执照编号
    private Date licenseStartDate;   //起始日期
    private Date licenseEndDate;     //结束日期
    private String stationName; //台站名称
    private String stationCode;   //无线电台识别码/基站识别码
    private String licensee;    //台站设置使用人
    private String licenseeNo;  //统一代码
    private String statAddr;    //无线电台地址
    private String longitude;   //经度
    private String latitude;     //纬度
    private String licenceNo;    //对应频率许可证编号

    private String stationType;   //台站类别/技术体制

    private String countyName;   //台站类别/技术体制


    private List<ISectionDTO> iSectionDTOList; //设备，参数集合
    private String specialCase;   //其他特别约定事项

    private String stationGuid; //基站id

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public Date getLicenseStartDate() {
        return licenseStartDate;
    }

    public void setLicenseStartDate(Date licenseStartDate) {
        this.licenseStartDate = licenseStartDate;
    }

    public Date getLicenseEndDate() {
        return licenseEndDate;
    }

    public void setLicenseEndDate(Date licenseEndDate) {
        this.licenseEndDate = licenseEndDate;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getLicensee() {
        return licensee;
    }

    public void setLicensee(String licensee) {
        this.licensee = licensee;
    }

    public String getLicenseeNo() {
        return licenseeNo;
    }

    public void setLicenseeNo(String licenseeNo) {
        this.licenseeNo = licenseeNo;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLicenceNo() {
        return licenceNo;
    }

    public void setLicenceNo(String licenceNo) {
        this.licenceNo = licenceNo;
    }

    public String getStationType() {
        return stationType;
    }

    public void setStationType(String stationType) {
        this.stationType = stationType;
    }

    public String getCountyName() {
        return countyName;
    }

    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }

    public List<ISectionDTO> getiSectionDTOList() {
        return iSectionDTOList;
    }

    public void setiSectionDTOList(List<ISectionDTO> iSectionDTOList) {
        this.iSectionDTOList = iSectionDTOList;
    }

    public String getSpecialCase() {
        return specialCase;
    }

    public void setSpecialCase(String specialCase) {
        this.specialCase = specialCase;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }
}
