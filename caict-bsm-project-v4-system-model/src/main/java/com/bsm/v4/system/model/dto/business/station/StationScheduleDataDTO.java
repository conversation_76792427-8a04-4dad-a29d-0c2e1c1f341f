package com.bsm.v4.system.model.dto.business.station;

import com.bsm.v4.system.model.entity.business.transfer.TransportSchedule;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;


import java.util.List;

/**
 * 待办基站数据
 */

public class StationScheduleDataDTO {

    private String stationGuid;
    private String btsId;
    private String btsName;
    private String techType;
    private String location;
    private String county;
    private String regionCode;
    private String altitude;
    private Double latitude;
    private Double longitude;
    private String dataType;
    private String cellDataType;
    private String stScene;

    private String isHandle;
    private String userGuid;
    private String jobGuid;
    private String genNum;
    private String isValid;
    //1.宏站2.直放站
    private String expandStation;
    //1.室内站2.室外站
    private String attributeStation;

    //收倾角
    private String atRang;

    //发倾角
    private String atEang;

    //服务半径
    private String stServR;

    //1-5G干扰分析通过；2-5G干扰分析不通过
    private String analysisStatus;

    //1-fast范围外2-fast范围内
    private String fastStatus;

    List<TransportSchedule> transportScheduleList;

    //分页
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int pageNum;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int pageSize;

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCellDataType() {
        return cellDataType;
    }

    public void setCellDataType(String cellDataType) {
        this.cellDataType = cellDataType;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }

    public String getIsHandle() {
        return isHandle;
    }

    public void setIsHandle(String isHandle) {
        this.isHandle = isHandle;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public String getAtRang() {
        return atRang;
    }

    public void setAtRang(String atRang) {
        this.atRang = atRang;
    }

    public String getAtEang() {
        return atEang;
    }

    public void setAtEang(String atEang) {
        this.atEang = atEang;
    }

    public String getStServR() {
        return stServR;
    }

    public void setStServR(String stServR) {
        this.stServR = stServR;
    }

    public String getAnalysisStatus() {
        return analysisStatus;
    }

    public void setAnalysisStatus(String analysisStatus) {
        this.analysisStatus = analysisStatus;
    }

    public String getFastStatus() {
        return fastStatus;
    }

    public void setFastStatus(String fastStatus) {
        this.fastStatus = fastStatus;
    }

    public List<TransportSchedule> getTransportScheduleList() {
        return transportScheduleList;
    }

    public void setTransportScheduleList(List<TransportSchedule> transportScheduleList) {
        this.transportScheduleList = transportScheduleList;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
