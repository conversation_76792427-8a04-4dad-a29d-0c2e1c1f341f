package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_EAF")
public class RsbtEaf {

    /**
     * GUID
     */
    @TableId("GUID")
    private String guid;

    /**
     * 台站GUID
     */
    @TableFieId("STATION_GUID")
    private String stationGuid;

    /**
     * 设备
     */
    @TableFieId("EQU_GUID")
    private String equGuid;

    /**
     * 天线
     */
    @TableFieId("ANT_GUID")
    private String antGuid;

    /**
     * 频率
     */
    @TableFieId("FREQ_GUID")
    private String freqGuid;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getEquGuid() {
        return equGuid;
    }

    public void setEquGuid(String equGuid) {
        this.equGuid = equGuid;
    }

    public String getAntGuid() {
        return antGuid;
    }

    public void setAntGuid(String antGuid) {
        this.antGuid = antGuid;
    }

    public String getFreqGuid() {
        return freqGuid;
    }

    public void setFreqGuid(String freqGuid) {
        this.freqGuid = freqGuid;
    }
}
