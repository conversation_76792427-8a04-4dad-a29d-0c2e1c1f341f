package com.bsm.v4.system.model.entity.business.applytable;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-03
 */

@TableName("RSBT_APPLY")
public class RsbtApply {

    /**
     * 系统ID
     */
    @TableId("GUID")
    private String guid;
    /**
     * 申请组织机构ID
     */
    @TableFieId("ORG_GUID")
    private String orgGuid;

    /**
     *
     */
    @TableFieId("NET_GUID")
    private String netGuid;

    /**
     * 管理机构 GUID
     */
    @TableFieId("ORG_MANAGER_GUID")
    private String orgManagerGuid;
    /**
     * 申请表编号
     */
    @TableFieId("APP_CODE")
    private String appCode;

    /**
     * 申请表类别
     */
    @TableFieId("APP_TYPE")
    private String appType;

    /**
     * 申请类型
     */
    @TableFieId("APP_SUB_TYPE")
    private String appSubType;

    /**
     * 申请对象类型
     */
    @TableFieId("APP_OBJECT_TYPE")
    private String appObjectType;

    /**
     * 申请日期
     */
    @TableFieId("APP_DATE")
    private Date appDate;

    /**
     * 申请频率使用期限
     * （开始日期）
     */
    @TableFieId("APP_FTLB")
    private Date appFtlb;

    /**
     * 申请频率使用期限
     * （截止日期）
     */
    @TableFieId("APP_FTLE")
    private Date appFtle;

    /**
     * 备注
     */
    @TableFieId("MEMO")
    private String memo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOrgGuid() {
        return orgGuid;
    }

    public void setOrgGuid(String orgGuid) {
        this.orgGuid = orgGuid;
    }

    public String getNetGuid() {
        return netGuid;
    }

    public void setNetGuid(String netGuid) {
        this.netGuid = netGuid;
    }

    public String getOrgManagerGuid() {
        return orgManagerGuid;
    }

    public void setOrgManagerGuid(String orgManagerGuid) {
        this.orgManagerGuid = orgManagerGuid;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppSubType() {
        return appSubType;
    }

    public void setAppSubType(String appSubType) {
        this.appSubType = appSubType;
    }

    public String getAppObjectType() {
        return appObjectType;
    }

    public void setAppObjectType(String appObjectType) {
        this.appObjectType = appObjectType;
    }

    public Date getAppDate() {
        return appDate;
    }

    public void setAppDate(Date appDate) {
        this.appDate = appDate;
    }

    public Date getAppFtlb() {
        return appFtlb;
    }

    public void setAppFtlb(Date appFtlb) {
        this.appFtlb = appFtlb;
    }

    public Date getAppFtle() {
        return appFtle;
    }

    public void setAppFtle(Date appFtle) {
        this.appFtle = appFtle;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
