package com.bsm.v4.system.model.entity.business.stationbak;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("RSBT_ANTFEED_APPENDIX_BAK")
public class RsbtAntfeedAppendixBak {

    /**
     * 系统ID
     */
    @TableId("GUID")
    private String guid;
    @TableFieId("ANT_GUID")
    private String antGuid;
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getAntGuid() {
        return antGuid;
    }

    public void setAntGuid(String antGuid) {
        this.antGuid = antGuid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }
}
