package com.bsm.v4.system.model.vo.business.freqevaluation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @description 忙时激活用户数/用户流量统计表
 * @date 2023年8月24日 11点13分
 */

@ApiModel(value = "operUserStatisVO", description = "忙时激活用户数/用户流量统计表对象")
public class OperUserStatisVO {

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "任务id")
    private String jobGuid;

    @ApiModelProperty(value = "企业类型")
    private String orgType;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }
}
