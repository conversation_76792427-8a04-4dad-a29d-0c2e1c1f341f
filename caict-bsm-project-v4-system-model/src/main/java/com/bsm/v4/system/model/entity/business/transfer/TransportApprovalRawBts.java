package com.bsm.v4.system.model.entity.business.transfer;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * 无委上传excel表
 */

@TableName("TRANSPORT_APPROVAL_RAW_BTS")
public class TransportApprovalRawBts {

    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;

    /**
     * 中转任务id
     */
    @TableFieId("JOB_GUID")
    private String jobGuid;

    /**
     * 中转任务id
     */
    @TableFieId("APP_GUID")
    private String appGuid;

    /**
     * 基站识别码
     */
    @TableFieId("BTS_ID")
    private String btsId;

    /**
     * 小区识别码
     */
    @TableFieId("CELL_ID")
    private String cellId;

    /**
     * 文件存放guid
     */
    @TableFieId("FILE_GUID")
    private String fileGuid;

    /**
     * 操作用户GUID
     */
    @TableFieId("USER_GUID")
    private String userGuid;

    /**
     * 上传时间
     */
    @TableFieId("UPLOAD_DATE")
    private Date uploadDate;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }
}
