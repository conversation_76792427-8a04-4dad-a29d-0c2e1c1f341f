package com.bsm.v4.system.model.entity.business.applytable;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("RSBT_APPLY_APPENDIX")
public class RsbtApplyAppendix {

    @TableId("GUID")
    private String guid;

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;

    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;

    @TableFieId("IS_DELETED")
    private Long isDeleted;

    /**
     * 申请表名称
     */
    @TableFieId("APP_NAME")
    private String appName;


    /**
     * 申请操作人
     */
    @TableFieId("APP_USER_GUID")
    private String apppUserGuid;

    /**
     * 网络制式
     */
    @TableFieId("NET_TYPE")
    private String netType;
    /**
     * 基站个数（已占用）
     */
    @TableFieId("STATION_COUNT")
    private Long stationCount;

    /**
     * 是否同步
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getApppUserGuid() {
        return apppUserGuid;
    }

    public void setApppUserGuid(String apppUserGuid) {
        this.apppUserGuid = apppUserGuid;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public Long getStationCount() {
        return stationCount;
    }

    public void setStationCount(Long stationCount) {
        this.stationCount = stationCount;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }
}
