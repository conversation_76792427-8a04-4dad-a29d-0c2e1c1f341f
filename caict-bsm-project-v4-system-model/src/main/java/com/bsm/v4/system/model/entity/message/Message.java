package com.bsm.v4.system.model.entity.message;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;

@TableName("BSM_MESSAGE")
public class Message {

    @TableId(value = "GUID")
    private String guid;                    //主键
    @TableFieId("BSM_TYPE")
    private String bsmType;                 //消息类型  1：待办任务，2：执照到期，3：执照即将到期，4：数据库同步提示
    @TableFieId("BSM_TITLE")
    private String bsmTitle;                //标题
    @TableFieId("BSM_NAME")
    private String bsmName;                 //名称
    @TableFieId("BSM_CONTENT")
    private String bsmContent;              //内容
    @TableFieId("BSM_FROM")
    private String bsmFrom;                 //来源
    @TableFieId("UPDATE_TIME")
    private String updateTime;              //更新时间
    @TableFieId("SEND_TIME")
    private String sendTime;                //发送时间
    @TableFieId("REMARK")
    private String remark;                  //备注
    @TableFieId("BSM_FROM_GUID")
    private String bsmFromGuid;             //消息来源id
    @TableFieId("BSM_FROM_USER")
    private String bsmFromUser;             //消息所属对象

    public Message(){}

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getBsmTitle() {
        return bsmTitle;
    }

    public void setBsmTitle(String bsmTitle) {
        this.bsmTitle = bsmTitle;
    }

    public String getBsmName() {
        return bsmName;
    }

    public void setBsmName(String bsmName) {
        this.bsmName = bsmName;
    }

    public String getBsmContent() {
        return bsmContent;
    }

    public void setBsmContent(String bsmContent) {
        this.bsmContent = bsmContent;
    }

    public String getBsmFrom() {
        return bsmFrom;
    }

    public void setBsmFrom(String bsmFrom) {
        this.bsmFrom = bsmFrom;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBsmType() {
        return bsmType;
    }

    public void setBsmType(String bsmType) {
        this.bsmType = bsmType;
    }

    public String getBsmFromGuid() {
        return bsmFromGuid;
    }

    public void setBsmFromGuid(String bsmFromGuid) {
        this.bsmFromGuid = bsmFromGuid;
    }

    public String getBsmFromUser() {
        return bsmFromUser;
    }

    public void setBsmFromUser(String bsmFromUser) {
        this.bsmFromUser = bsmFromUser;
    }
}
