package com.bsm.v4.system.model.dto.business.license;



public class LicenseStatisticDTO {
    //已发
    private Long alreadyGrant;
    //未发
    private Long notGrant;
    //过期
    private Long overDue;
    //停用
    private Long stopUse;

    public Long getAlreadyGrant() {
        return alreadyGrant;
    }

    public void setAlreadyGrant(Long alreadyGrant) {
        this.alreadyGrant = alreadyGrant;
    }

    public Long getNotGrant() {
        return notGrant;
    }

    public void setNotGrant(Long notGrant) {
        this.notGrant = notGrant;
    }

    public Long getOverDue() {
        return overDue;
    }

    public void setOverDue(Long overDue) {
        this.overDue = overDue;
    }

    public Long getStopUse() {
        return stopUse;
    }

    public void setStopUse(Long stopUse) {
        this.stopUse = stopUse;
    }
}
