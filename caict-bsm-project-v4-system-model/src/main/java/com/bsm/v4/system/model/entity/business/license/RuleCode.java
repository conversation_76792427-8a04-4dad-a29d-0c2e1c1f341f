package com.bsm.v4.system.model.entity.business.license;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


/**
 * <AUTHOR>
 * @date 2019/12/13
 */

@TableName("BSM_RULE_CODE")
public class RuleCode {

    @TableId("GUID")
    private String guid;

    @TableFieId("RULE_TYPE")
    private String ruleType;

    @TableFieId("YEAR")
    private Integer year;

    @TableFieId("NEXT_NUM")
    private Long nextNum;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Long getNextNum() {
        return nextNum;
    }

    public void setNextNum(Long nextNum) {
        this.nextNum = nextNum;
    }
}
