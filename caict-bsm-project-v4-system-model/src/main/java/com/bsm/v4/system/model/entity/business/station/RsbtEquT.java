package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_EQU_T")
public class RsbtEquT {

    /**
     * RSBT_EQU  GUID
     */
    @TableId("GUID")
    private String guid;

    /**
     * 设备序号
     */
    @TableFieId("ET_EQU_NO")
    private String etEquNo;

    /**
     * 是否自动选频
     */
    @TableFieId("ET_AF_TYPE")
    private String etAfType;

    /**
     * 功率单位
     */
    @TableFieId("ET_POW_U")
    private String etPowU;

    /**
     * 呼号
     */
    @TableFieId("ET_EQU_CL")
    private String etEquCl;

    /**
     * 门限电平
     */
    @TableFieId("ET_EQU_TL")
    private Double etEquTl;

    /**
     * 噪声系数（dB）
     */
    @TableFieId("ET_EQU_RNQ")
    private Double etEquRnq;

    /**
     * 设备类型
     */
    @TableFieId("ET_EQU_TYPE_ID")
    private String etEquTypeId;

    /**
     * 设备名称
     */
    @TableFieId("ET_EQU_NAME")
    private String etEquName;

    /**
     * 型式认可代码
     */
    @TableFieId("ET_EQU_CODE")
    private String etEquCode;

    /**
     * 发射标识
     */
    @TableFieId("ET_EQU_ESGN")
    private String etEquEsgn;

    /**
     * 设备电台安装位置
     */
    @TableFieId("ET_EQU_EPOS")
    private String etEquEpos;

    /**
     * 设备天线类型
     */
    @TableFieId("ET_EQU_ATYPE")
    private String etEquAtype;

    /**
     * 天线安装位置
     */
    @TableFieId("ET_EQU_APOS")
    private String etEquApos;

    /**
     * 设备数量
     */
    @TableFieId("ET_EQU_SUM")
    private int etEquSum;

    /**
     * 电池有效期
     */
    @TableFieId("ET_EQU_EUSE")
    private String etEquEuse;

    /**
     * 频率间隔
     */
    @TableFieId("ET_EQU_FNTVAL")
    private Double etEquFntval;

    /**
     * 脉冲上升时间
     */
    @TableFieId("ET_EQU_PUP")
    private Double etEquPup;

    /**
     * 脉冲下降时间
     */
    @TableFieId("ET_EQU_PDN")
    private Double etEquPdn;

    /**
     * Chirp宽度
     */
    @TableFieId("ET_EQU_CHRIP")
    private Double etEquChrip;

    /**
     * 脉冲宽度1
     */
    @TableFieId("ET_EQU_PWID1")
    private String etEquPwid1;

    /**
     * 脉冲宽度2
     */
    @TableFieId("ET_EQU_PWID2")
    private String etEquPwid2;

    /**
     * 脉冲重复周期
     */
    @TableFieId("ET_EQU_PR")
    private Double etEquPr;

    /**
     * 脉冲重复频率
     */
    @TableFieId("ET_EQU_PF")
    private Double etEquPf;

    /**
     * 应答器发射频率
     */
    @TableFieId("ET_EQU_RF")
    private Double etEquRf;

    /**
     * 应答器发射的带宽
     */
    @TableFieId("ET_EQU_RF_BAND")
    private Double etEquRfBand;

    /**
     * 接收机灵敏度
     */
    @TableFieId("ET_EQU_SEN")
    private Double etEquSen;

    /**
     * 接收机灵敏度单位
     */
    @TableFieId("ET_EQU_SENU")
    private String etEquSenu;

    /**
     * 接收机灵敏度对应的误码率指标
     */
    @TableFieId("ET_EQU_SENERR")
    private String etEquSenerr;

    /**
     * 接收机中频带宽
     */
    @TableFieId("ET_EQU_RWID")
    private Double etEquRwid;

    /**
     * 调制方式
     */
    @TableFieId("ET_EQU_MT")
    private String etEquMt;

    /**
     * 扇区号
     */
    @TableFieId("ET_EQU_CCODE")
    private String etEquCcode;

    /**
     * 上行发射功率/信道的单位
     */
    @TableFieId("ET_EQU_UPU")
    private String etEquUpu;

    /**
     * 下行发射功率/信道的单位
     */
    @TableFieId("ET_EQU_DNU")
    private String etEquDnu;

    /**
     * 设备类别
     */
    @TableFieId("ET_EQU_TYPE")
    private String etEquType;

    /**
     * 使用方式
     */
    @TableFieId("ET_EQU_USE")
    private String etEquUse;

    /**
     * 峰值功率
     */
    @TableFieId("ET_EQU_POW_MAX")
    private Double etEquPowMax;

    /**
     * 平均功率
     */
    @TableFieId("ET_EQU_POW_AVG")
    private Double etEquPowAvg;

    /**
     * 上行发射功率
     */
    @TableFieId("ET_EQU_UPOW")
    private Double etEquUpow;

    /**
     * 下行发射功率
     */
    @TableFieId("ET_EQU_DPOW")
    private Double etEquDpow;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getEtEquNo() {
        return etEquNo;
    }

    public void setEtEquNo(String etEquNo) {
        this.etEquNo = etEquNo;
    }

    public String getEtAfType() {
        return etAfType;
    }

    public void setEtAfType(String etAfType) {
        this.etAfType = etAfType;
    }

    public String getEtPowU() {
        return etPowU;
    }

    public void setEtPowU(String etPowU) {
        this.etPowU = etPowU;
    }

    public String getEtEquCl() {
        return etEquCl;
    }

    public void setEtEquCl(String etEquCl) {
        this.etEquCl = etEquCl;
    }

    public Double getEtEquTl() {
        return etEquTl;
    }

    public void setEtEquTl(Double etEquTl) {
        this.etEquTl = etEquTl;
    }

    public Double getEtEquRnq() {
        return etEquRnq;
    }

    public void setEtEquRnq(Double etEquRnq) {
        this.etEquRnq = etEquRnq;
    }

    public String getEtEquTypeId() {
        return etEquTypeId;
    }

    public void setEtEquTypeId(String etEquTypeId) {
        this.etEquTypeId = etEquTypeId;
    }

    public String getEtEquName() {
        return etEquName;
    }

    public void setEtEquName(String etEquName) {
        this.etEquName = etEquName;
    }

    public String getEtEquCode() {
        return etEquCode;
    }

    public void setEtEquCode(String etEquCode) {
        this.etEquCode = etEquCode;
    }

    public String getEtEquEsgn() {
        return etEquEsgn;
    }

    public void setEtEquEsgn(String etEquEsgn) {
        this.etEquEsgn = etEquEsgn;
    }

    public String getEtEquEpos() {
        return etEquEpos;
    }

    public void setEtEquEpos(String etEquEpos) {
        this.etEquEpos = etEquEpos;
    }

    public String getEtEquAtype() {
        return etEquAtype;
    }

    public void setEtEquAtype(String etEquAtype) {
        this.etEquAtype = etEquAtype;
    }

    public String getEtEquApos() {
        return etEquApos;
    }

    public void setEtEquApos(String etEquApos) {
        this.etEquApos = etEquApos;
    }

    public int getEtEquSum() {
        return etEquSum;
    }

    public void setEtEquSum(int etEquSum) {
        this.etEquSum = etEquSum;
    }

    public String getEtEquEuse() {
        return etEquEuse;
    }

    public void setEtEquEuse(String etEquEuse) {
        this.etEquEuse = etEquEuse;
    }

    public Double getEtEquFntval() {
        return etEquFntval;
    }

    public void setEtEquFntval(Double etEquFntval) {
        this.etEquFntval = etEquFntval;
    }

    public Double getEtEquPup() {
        return etEquPup;
    }

    public void setEtEquPup(Double etEquPup) {
        this.etEquPup = etEquPup;
    }

    public Double getEtEquPdn() {
        return etEquPdn;
    }

    public void setEtEquPdn(Double etEquPdn) {
        this.etEquPdn = etEquPdn;
    }

    public Double getEtEquChrip() {
        return etEquChrip;
    }

    public void setEtEquChrip(Double etEquChrip) {
        this.etEquChrip = etEquChrip;
    }

    public String getEtEquPwid1() {
        return etEquPwid1;
    }

    public void setEtEquPwid1(String etEquPwid1) {
        this.etEquPwid1 = etEquPwid1;
    }

    public String getEtEquPwid2() {
        return etEquPwid2;
    }

    public void setEtEquPwid2(String etEquPwid2) {
        this.etEquPwid2 = etEquPwid2;
    }

    public Double getEtEquPr() {
        return etEquPr;
    }

    public void setEtEquPr(Double etEquPr) {
        this.etEquPr = etEquPr;
    }

    public Double getEtEquPf() {
        return etEquPf;
    }

    public void setEtEquPf(Double etEquPf) {
        this.etEquPf = etEquPf;
    }

    public Double getEtEquRf() {
        return etEquRf;
    }

    public void setEtEquRf(Double etEquRf) {
        this.etEquRf = etEquRf;
    }

    public Double getEtEquRfBand() {
        return etEquRfBand;
    }

    public void setEtEquRfBand(Double etEquRfBand) {
        this.etEquRfBand = etEquRfBand;
    }

    public Double getEtEquSen() {
        return etEquSen;
    }

    public void setEtEquSen(Double etEquSen) {
        this.etEquSen = etEquSen;
    }

    public String getEtEquSenu() {
        return etEquSenu;
    }

    public void setEtEquSenu(String etEquSenu) {
        this.etEquSenu = etEquSenu;
    }

    public String getEtEquSenerr() {
        return etEquSenerr;
    }

    public void setEtEquSenerr(String etEquSenerr) {
        this.etEquSenerr = etEquSenerr;
    }

    public Double getEtEquRwid() {
        return etEquRwid;
    }

    public void setEtEquRwid(Double etEquRwid) {
        this.etEquRwid = etEquRwid;
    }

    public String getEtEquMt() {
        return etEquMt;
    }

    public void setEtEquMt(String etEquMt) {
        this.etEquMt = etEquMt;
    }

    public String getEtEquCcode() {
        return etEquCcode;
    }

    public void setEtEquCcode(String etEquCcode) {
        this.etEquCcode = etEquCcode;
    }

    public String getEtEquUpu() {
        return etEquUpu;
    }

    public void setEtEquUpu(String etEquUpu) {
        this.etEquUpu = etEquUpu;
    }

    public String getEtEquDnu() {
        return etEquDnu;
    }

    public void setEtEquDnu(String etEquDnu) {
        this.etEquDnu = etEquDnu;
    }

    public String getEtEquType() {
        return etEquType;
    }

    public void setEtEquType(String etEquType) {
        this.etEquType = etEquType;
    }

    public String getEtEquUse() {
        return etEquUse;
    }

    public void setEtEquUse(String etEquUse) {
        this.etEquUse = etEquUse;
    }

    public Double getEtEquPowMax() {
        return etEquPowMax;
    }

    public void setEtEquPowMax(Double etEquPowMax) {
        this.etEquPowMax = etEquPowMax;
    }

    public Double getEtEquPowAvg() {
        return etEquPowAvg;
    }

    public void setEtEquPowAvg(Double etEquPowAvg) {
        this.etEquPowAvg = etEquPowAvg;
    }

    public Double getEtEquUpow() {
        return etEquUpow;
    }

    public void setEtEquUpow(Double etEquUpow) {
        this.etEquUpow = etEquUpow;
    }

    public Double getEtEquDpow() {
        return etEquDpow;
    }

    public void setEtEquDpow(Double etEquDpow) {
        this.etEquDpow = etEquDpow;
    }
}
