package com.bsm.v4.system.model.contrust.transfer;

/**
 * Created by dengsy on 2020-04-22.
 * 运营商申请表状态
 * <p>
 * 12:分任务待生成申请表
 * 13:分任务生成申请表中
 * 14:分任务生成申请表失败
 * 15:分任务生成申请表成功待入库
 * 16:分任务提交审核中（入库中）
 * 17:分任务审核失败
 * 18:分任务审核入库成功
 * 19:放弃
 */
public class TransportJobInBranchConst {

    public static final String BRANCHIN_APPLY_WAIT = "12";

    public static final String BRANCHIN_APPLYING = "13";

    public static final String BRANCHIN_APPLY_FAIL = "14";

    public static final String BRANCHIN_CHECK_WAIT = "15";

    public static final String BRANCHIN_CHECKING = "16";

    public static final String BRANCHIN_CHECK_FAIL = "17";

    public static final String BRANCHIN_CHECK_SUCCESS = "18";

    public static final String BRANCHIN_GIVEUP = "19";
}
