package com.bsm.v4.system.model.vo.security;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * @Title: OrgSearchVO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.vo.security
 * @Date 2023/9/13 11:56
 * @description: 组织机构搜索对象
 */

@ApiModel(value = "orgSearchVO", description = "组织机构搜索对象")
public class OrgSearchVO {
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
