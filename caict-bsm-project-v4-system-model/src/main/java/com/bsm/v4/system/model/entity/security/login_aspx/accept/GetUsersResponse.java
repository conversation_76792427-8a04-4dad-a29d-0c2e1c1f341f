package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/13
 */

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "responseBody",namespace = NameSpaceUrlConst.aNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "userInfo"
})
public class GetUsersResponse {
    @XmlElement(name = "userInfo",namespace = NameSpaceUrlConst.aNamespaceURI)
    private List<UserInfo> userInfo;

    public GetUsersResponse() {
    }

    public List<UserInfo> getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(List<UserInfo> userInfo) {
        this.userInfo = userInfo;
    }
}
