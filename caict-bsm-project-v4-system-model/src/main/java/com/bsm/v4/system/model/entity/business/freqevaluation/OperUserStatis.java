package com.bsm.v4.system.model.entity.business.freqevaluation;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * <AUTHOR>
 * @description 忙时激活用户数/用户流量统计表
 * @date 2023年8月24日 11点13分
 */

@ApiModel(value = "operUserStatis", description = "忙时激活用户数/用户流量统计表对象")
@TableName("BSM_OPER_USER_STATIS")
public class OperUserStatis {

    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "企业代码")
    @TableFieId("ORG_CODE")
    private String orgCode;

    @ApiModelProperty(value = "企业名称")
    @TableFieId("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "企业类型")
    @TableFieId("ORG_TYPE")
    private String orgType;

    @ApiModelProperty(value = "企业等级")
    @TableFieId("ORG_LEVEL")
    private String orgLevel;

    @ApiModelProperty(value = "地区代码")
    @TableFieId("ORG_AREA_CODE")
    private String orgAreaCode;

    @ApiModelProperty(value = "2G （用户数，户）")
    @TableFieId("USER_2G")
    private Double user2g;

    @ApiModelProperty(value = "3G （用户数，户）")
    @TableFieId("USER_3G")
    private Double user3g;

    @ApiModelProperty(value = "4G （用户流量，Gb ）")
    @TableFieId("USER_TRAFFIC_4G")
    private Double userTraffic4g;

    @ApiModelProperty(value = "4G （用户流量，Gb ）")
    @TableFieId("USER_TRAFFIC_5G")
    private Double userTraffic5g;

    @ApiModelProperty(value = "创建日期")
    @TableFieId("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "备注")
    @TableFieId("MEMO")
    private String memo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getOrgAreaCode() {
        return orgAreaCode;
    }

    public void setOrgAreaCode(String orgAreaCode) {
        this.orgAreaCode = orgAreaCode;
    }

    public Double getUser2g() {
        return user2g;
    }

    public void setUser2g(Double user2g) {
        this.user2g = user2g;
    }

    public Double getUser3g() {
        return user3g;
    }

    public void setUser3g(Double user3g) {
        this.user3g = user3g;
    }

    public Double getUserTraffic4g() {
        return userTraffic4g;
    }

    public void setUserTraffic4g(Double userTraffic4g) {
        this.userTraffic4g = userTraffic4g;
    }

    public Double getUserTraffic5g() {
        return userTraffic5g;
    }

    public void setUserTraffic5g(Double userTraffic5g) {
        this.userTraffic5g = userTraffic5g;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
