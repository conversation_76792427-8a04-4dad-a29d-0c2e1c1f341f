package com.bsm.v4.system.model.entity.business.stationbak;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_ANTFEED_BAK")
public class RsbtAntfeedBak {

    /**
     * 方位角范围起
     */
    @TableFieId("AT_ANG_B")
    private double atAngB;
    /**
     * 馈线系统总损耗
     */
    @TableFieId("FEED_LOSE")
    private double feedLose;
    /**
     * 天线增益
     */
    @TableFieId("ANT_GAIN")
    private double antGain;
    /**
     * 天线据地高度
     */
    @TableFieId("ANT_HIGHT")
    private double antHight;
    /**
     * 天线生产厂家
     */
    @TableFieId("ANT_MENU")
    private String antMenu;
    @TableFieId("ANT_MODEL")
    private String antModel;
    /**
     * 天线类型
     */
    @TableFieId("ANT_TYPE")
    private String antType;
    /**
     * 发极化方式
     */
    @TableFieId("ANT_EPOLE")
    private String antEpole;
    /**
     * 收极化方式
     */
    @TableFieId("ANT_RPOLE ")
    private String antRpole;
    @TableFieId("ANT_POLE")
    private String antPole;
    /**
     * 扇区标识码
     */
    @TableFieId("SECTION_CODE")
    private String sectionCode;
    @TableFieId("STATION_GUID")
    private String stationGuid;
    @TableId("ANT_GUID")
    private String antGuid;
    /**
     * 3dB角宽(收)
     */
    @TableFieId("AT_3DBR")
    private double at3dbr;
    /**
     * 3dB角宽(发)
     */
    @TableFieId("AT_3DBE")
    private double at3dbe;
    /**
     * 方位角范围止
     */
    @TableFieId("AT_ANG_E")
    private double atAngE;
    /**
     * 最大辐射方位角
     */
    @TableFieId("ANT_ANGLE")
    private double atAngle;
    /**
     * 海拔高度
     */
    @TableFieId("ALTITUDE")
    private double altitude;

    /**
     * 是否同步
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    /**
     * 数据类型（1-新增；2-变更；3-删除）
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    public double getAtAngB() {
        return atAngB;
    }

    public void setAtAngB(double atAngB) {
        this.atAngB = atAngB;
    }

    public double getFeedLose() {
        return feedLose;
    }

    public void setFeedLose(double feedLose) {
        this.feedLose = feedLose;
    }

    public double getAntGain() {
        return antGain;
    }

    public void setAntGain(double antGain) {
        this.antGain = antGain;
    }

    public double getAntHight() {
        return antHight;
    }

    public void setAntHight(double antHight) {
        this.antHight = antHight;
    }

    public String getAntMenu() {
        return antMenu;
    }

    public void setAntMenu(String antMenu) {
        this.antMenu = antMenu;
    }

    public String getAntModel() {
        return antModel;
    }

    public void setAntModel(String antModel) {
        this.antModel = antModel;
    }

    public String getAntType() {
        return antType;
    }

    public void setAntType(String antType) {
        this.antType = antType;
    }

    public String getAntEpole() {
        return antEpole;
    }

    public void setAntEpole(String antEpole) {
        this.antEpole = antEpole;
    }

    public String getAntRpole() {
        return antRpole;
    }

    public void setAntRpole(String antRpole) {
        this.antRpole = antRpole;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public String getSectionCode() {
        return sectionCode;
    }

    public void setSectionCode(String sectionCode) {
        this.sectionCode = sectionCode;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getAntGuid() {
        return antGuid;
    }

    public void setAntGuid(String antGuid) {
        this.antGuid = antGuid;
    }

    public double getAt3dbr() {
        return at3dbr;
    }

    public void setAt3dbr(double at3dbr) {
        this.at3dbr = at3dbr;
    }

    public double getAt3dbe() {
        return at3dbe;
    }

    public void setAt3dbe(double at3dbe) {
        this.at3dbe = at3dbe;
    }

    public double getAtAngE() {
        return atAngE;
    }

    public void setAtAngE(double atAngE) {
        this.atAngE = atAngE;
    }

    public double getAtAngle() {
        return atAngle;
    }

    public void setAtAngle(double atAngle) {
        this.atAngle = atAngle;
    }

    public double getAltitude() {
        return altitude;
    }

    public void setAltitude(double altitude) {
        this.altitude = altitude;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}
