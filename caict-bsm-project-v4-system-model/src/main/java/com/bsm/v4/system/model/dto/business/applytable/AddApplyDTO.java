package com.bsm.v4.system.model.dto.business.applytable;



/**
 * <AUTHOR>
 * @date 2020/1/20
 */
public class AddApplyDTO {
    //guid
    private String guid;
    //申请表类型
    private String appType;
    //申请表编号
    private String appCode;
    //表号
    private String tableNo;
    //备注
    private String memo;
    //共性表数据
    private AppNetDetailDTO appNetDetailDTO;
    //设台机构数据
    private AppOrgDetailDTO appOrgDetailDTO;
    //申请频率数据
    private AppFreqDetailDTO appFreqDetailDTO;
    //申请表数据
    private AppDataDTO appDataDTO;
    //缴费单位数据
    private AppFeeOrgDetailDTO appFeeOrgDetailDTO;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getTableNo() {
        return tableNo;
    }

    public void setTableNo(String tableNo) {
        this.tableNo = tableNo;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public AppNetDetailDTO getAppNetDetailDTO() {
        return appNetDetailDTO;
    }

    public void setAppNetDetailDTO(AppNetDetailDTO appNetDetailDTO) {
        this.appNetDetailDTO = appNetDetailDTO;
    }

    public AppOrgDetailDTO getAppOrgDetailDTO() {
        return appOrgDetailDTO;
    }

    public void setAppOrgDetailDTO(AppOrgDetailDTO appOrgDetailDTO) {
        this.appOrgDetailDTO = appOrgDetailDTO;
    }

    public AppFreqDetailDTO getAppFreqDetailDTO() {
        return appFreqDetailDTO;
    }

    public void setAppFreqDetailDTO(AppFreqDetailDTO appFreqDetailDTO) {
        this.appFreqDetailDTO = appFreqDetailDTO;
    }

    public AppDataDTO getAppDataDTO() {
        return appDataDTO;
    }

    public void setAppDataDTO(AppDataDTO appDataDTO) {
        this.appDataDTO = appDataDTO;
    }

    public AppFeeOrgDetailDTO getAppFeeOrgDetailDTO() {
        return appFeeOrgDetailDTO;
    }

    public void setAppFeeOrgDetailDTO(AppFeeOrgDetailDTO appFeeOrgDetailDTO) {
        this.appFeeOrgDetailDTO = appFeeOrgDetailDTO;
    }
}
