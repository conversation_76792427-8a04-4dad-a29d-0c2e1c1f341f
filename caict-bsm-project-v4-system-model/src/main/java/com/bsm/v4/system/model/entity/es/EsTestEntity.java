package com.bsm.v4.system.model.entity.es;

import io.swagger.annotations.ApiModelProperty;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @author: ycp
 * @createTime: 2023/08/10 10:01
 * @company: 成渝（成都）信息通信研究院
 * @description:
 */
@Document(indexName = "test_es")

public class EsTestEntity {

    @ApiModelProperty(value = "主键ID")
    @Id
    @Field(type = FieldType.Text)
    private String id;

    @ApiModelProperty(value = "类型（SKBS：时空伴随；ZDQY_ZDCS：重点场所；ZDQY_HIGH：高风险地区；ZDQY_MID：中风险地区；SM_PROVINCE：省际漫游；SM_CITY：省内漫游；TSRW：特殊任务；")
    @Field(name = "type", type = FieldType.Text)
    private String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
