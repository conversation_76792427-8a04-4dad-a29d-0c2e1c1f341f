package com.bsm.v4.system.model.vo.business.transfer;



import java.util.Date;


public class TransportFileVO {

    /**
     * 主键
     */
    private String guid;
    private String fileId;
    private Date gmtCreate;
    private Date gmtModified;
    private Long isDeleted;
    /**
     * JOB GUID
     */
    /**
     * 文件相对地址
     */
    private String filePath;
    /**
     * 大小kb
     */
    private Double fileSize;
    /**
     * 0创建1文件接收成功2文件处理结束3文件处理异常
     */
    private Long fileState;
    /**
     * 上传文件本地名
     */
    private String fileLocalName;
    private Long fileChunks;
    /**
     * 文件类型(0 附件;1 运营商上传申请文件;2 无委上传审核文件;3 无委上传协调文件)
     */
    private String fileType;

    private String dataType;

    private String genNum;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Double getFileSize() {
        return fileSize;
    }

    public void setFileSize(Double fileSize) {
        this.fileSize = fileSize;
    }

    public Long getFileState() {
        return fileState;
    }

    public void setFileState(Long fileState) {
        this.fileState = fileState;
    }

    public String getFileLocalName() {
        return fileLocalName;
    }

    public void setFileLocalName(String fileLocalName) {
        this.fileLocalName = fileLocalName;
    }

    public Long getFileChunks() {
        return fileChunks;
    }

    public void setFileChunks(Long fileChunks) {
        this.fileChunks = fileChunks;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }
}
