package com.bsm.v4.system.model.entity.business.transfer;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018-07-23
 */

@TableName("LOG_TRANSPORT_JOB")
public class LogTransportJob {
    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;

    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;

    @TableFieId("IS_DELETED")
    private Long isDeleted;
    /**
     * JOB GUID
     */
    @TableFieId("JOB_GUID")
    private String jobGuid;
    /**
     * 类别   0：正常；1：异常
     */
    @TableFieId("LOG_TYPE")
    private Long logType;
    /**
     * 具体信息
     */
    @TableFieId("LOG_DETAIL")
    private String logDetail;
    /**
     * 日志缩写
     */
    @TableFieId("LOG_SHORT")
    private String logShort;

    /**
     * 文件guid
     */
    @TableFieId("FILE_GUID")
    private String fileGuid;

    /**
     * 基站识别码
     */
    @TableFieId("BTS_ID")
    private String btsId;

    /**
     * CELL_ID
     */
    @TableFieId("CELL_ID")
    private String cellId;

    @TableFieId("IS_VALID")
    private String isValid;

    @TableFieId("CELL_NAME")
    private String cellName;

    @TableFieId("BTS_NAME")
    private String btsName;

    @TableFieId("TECH_TYPE")
    private String techType;

    @TableFieId("LOCATION")
    private String location;

    @TableFieId("LONGITUDE")
    private String longItude;

    @TableFieId("LATITUDE")
    private String latitude;

    @TableFieId("SEND_START_FREQ")
    private String sendStartFreq;

    @TableFieId("SEND_END_FREQ")
    private String sendEndFreq;
    @TableFieId("ACC_START_FREQ")
    private String accStartFreq;

    @TableFieId("ACC_END_FREQ")
    private String accEndFreq;

    @TableFieId("MAX_EMISSIVE_POWER")
    private String maxEmissivePower;

    @TableFieId("HEIGHT")
    private String height;

    @TableFieId("DATA_TYPE")
    private String dataType;

    @TableFieId("COUNTY")
    private String county;

    @TableFieId("IS_HANDLE")
    private String isHandle;

    @TableFieId("UPLOAD_DATE")
    private String uploadDate;

    @TableFieId("IS_DOWNLOAD")
    private String isDownload;

    @TableFieId("USER_GUID")
    private String userGuid;

    @TableFieId("DEAL_COUNT")
    private String dealCount;

    @TableFieId("UPLOAD_FLAG")
    private String uploadFlag;

    @TableFieId("VENDOR_NAME")
    private String vendorName;

    @TableFieId("DEVICE_MODEL")
    private String deviceModel;

    @TableFieId("MODEL_CODE")
    private String modelCode;

    @TableFieId("ANTENNA_GAIN")
    private String antennaGain;

    @TableFieId("ANTENNA_MODEL")
    private String antennaModel;

    @TableFieId("ANTENNA_FACTORY")
    private String antennaFactory;

    @TableFieId("POLARIZATION_MODE")
    private String polarizationMode;

    @TableFieId("ANTENNA_AZIMUTH")
    private String antennaAzimuth;

    @TableFieId("FEEDER_LOSS")
    private String feederLoss;

    @TableFieId("ALTITUDE")
    private String altitude;

    @TableFieId("SET_YEAR")
    private Long setYear;

    @TableFieId("SET_MONTH")
    private Long setMonth;

    @TableFieId("GEN_NUM")
    private String genNum;

    @TableFieId("ORG_TYPE")
    private String orgType;

    @TableFieId("AT_RANG")
    private String atRang;

    @TableFieId("AT_EANG")
    private String atEang;

    @TableFieId("ST_SERV_R")
    private String stServR;

    @TableFieId("EXPAND_STATION")
    private String expandStation;

    @TableFieId("ATTRIBUTE_STATION")
    private String attributeStation;

    @TableFieId("TRF_USER")
    private Double trfUser;

    @TableFieId("TRF_DATA")
    private Double trfData;

    @TableFieId("TRF_DATE")
    private Date trfDate;

    @TableFieId("ST_SCENE")
    private String stScene;

    @TableFieId("DEVICE_FACTORY")
    private String deviceFactory;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public Long getLogType() {
        return logType;
    }

    public void setLogType(Long logType) {
        this.logType = logType;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }

    public String getLogShort() {
        return logShort;
    }

    public void setLogShort(String logShort) {
        this.logShort = logShort;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongItude() {
        return longItude;
    }

    public void setLongItude(String longItude) {
        this.longItude = longItude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendStartFreq() {
        return sendStartFreq;
    }

    public void setSendStartFreq(String sendStartFreq) {
        this.sendStartFreq = sendStartFreq;
    }

    public String getSendEndFreq() {
        return sendEndFreq;
    }

    public void setSendEndFreq(String sendEndFreq) {
        this.sendEndFreq = sendEndFreq;
    }

    public String getAccStartFreq() {
        return accStartFreq;
    }

    public void setAccStartFreq(String accStartFreq) {
        this.accStartFreq = accStartFreq;
    }

    public String getAccEndFreq() {
        return accEndFreq;
    }

    public void setAccEndFreq(String accEndFreq) {
        this.accEndFreq = accEndFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getIsHandle() {
        return isHandle;
    }

    public void setIsHandle(String isHandle) {
        this.isHandle = isHandle;
    }

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getIsDownload() {
        return isDownload;
    }

    public void setIsDownload(String isDownload) {
        this.isDownload = isDownload;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getDealCount() {
        return dealCount;
    }

    public void setDealCount(String dealCount) {
        this.dealCount = dealCount;
    }

    public String getUploadFlag() {
        return uploadFlag;
    }

    public void setUploadFlag(String uploadFlag) {
        this.uploadFlag = uploadFlag;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAntennaGain() {
        return antennaGain;
    }

    public void setAntennaGain(String antennaGain) {
        this.antennaGain = antennaGain;
    }

    public String getAntennaModel() {
        return antennaModel;
    }

    public void setAntennaModel(String antennaModel) {
        this.antennaModel = antennaModel;
    }

    public String getAntennaFactory() {
        return antennaFactory;
    }

    public void setAntennaFactory(String antennaFactory) {
        this.antennaFactory = antennaFactory;
    }

    public String getPolarizationMode() {
        return polarizationMode;
    }

    public void setPolarizationMode(String polarizationMode) {
        this.polarizationMode = polarizationMode;
    }

    public String getAntennaAzimuth() {
        return antennaAzimuth;
    }

    public void setAntennaAzimuth(String antennaAzimuth) {
        this.antennaAzimuth = antennaAzimuth;
    }

    public String getFeederLoss() {
        return feederLoss;
    }

    public void setFeederLoss(String feederLoss) {
        this.feederLoss = feederLoss;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public Long getSetYear() {
        return setYear;
    }

    public void setSetYear(Long setYear) {
        this.setYear = setYear;
    }

    public Long getSetMonth() {
        return setMonth;
    }

    public void setSetMonth(Long setMonth) {
        this.setMonth = setMonth;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getAtRang() {
        return atRang;
    }

    public void setAtRang(String atRang) {
        this.atRang = atRang;
    }

    public String getAtEang() {
        return atEang;
    }

    public void setAtEang(String atEang) {
        this.atEang = atEang;
    }

    public String getStServR() {
        return stServR;
    }

    public void setStServR(String stServR) {
        this.stServR = stServR;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public Double getTrfUser() {
        return trfUser;
    }

    public void setTrfUser(Double trfUser) {
        this.trfUser = trfUser;
    }

    public Double getTrfData() {
        return trfData;
    }

    public void setTrfData(Double trfData) {
        this.trfData = trfData;
    }

    public Date getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(Date trfDate) {
        this.trfDate = trfDate;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }

    public String getDeviceFactory() {
        return deviceFactory;
    }

    public void setDeviceFactory(String deviceFactory) {
        this.deviceFactory = deviceFactory;
    }
}