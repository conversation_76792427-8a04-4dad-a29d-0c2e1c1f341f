package com.bsm.v4.system.model.vo.security;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dengsy on 2023-02-10.
 * <p>
 * 菜单搜索DTO
 */

@ApiModel(value = "menuSearchVO", description = "菜单搜索对象")
public class MenuSearchVO {

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
