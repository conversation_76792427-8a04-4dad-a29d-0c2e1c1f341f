package com.bsm.v4.system.model.dto.business.applytable;

import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


import java.util.Date;
import java.util.List;

/**
 * Created by dsy62 on 2020-04-28.
 * <p>
 * appDateStart：查询开始时间
 * appDateEnd：查询结束时间
 * orgName：组织机构名称
 * orgCode：组织机构代码
 * orgAddr：组织机构地址
 * <p>
 * orgDTO：组织机构的对象
 */
public class RsbtApplyDTO extends RsbtApply {

    private Date appDateStart;
    private Date appDateEnd;
    private String orgName;
    private String orgCode;
    private String orgAddr;
    private String applyTableName;
    //技术资料表号
    private String techNum;
    //是否已推送
    private String isSync;

    //地区
    private String regionId;

    //分页信息
    @TablePageNum
    private int page;

    @TablePageSize
    private int rows;

    private OrgDTO orgDTO;

    private List<String> techTableList;

    public Date getAppDateStart() {
        return appDateStart;
    }

    public void setAppDateStart(Date appDateStart) {
        this.appDateStart = appDateStart;
    }

    public Date getAppDateEnd() {
        return appDateEnd;
    }

    public void setAppDateEnd(Date appDateEnd) {
        this.appDateEnd = appDateEnd;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    public String getApplyTableName() {
        return applyTableName;
    }

    public void setApplyTableName(String applyTableName) {
        this.applyTableName = applyTableName;
    }

    public String getTechNum() {
        return techNum;
    }

    public void setTechNum(String techNum) {
        this.techNum = techNum;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public OrgDTO getOrgDTO() {
        return orgDTO;
    }

    public void setOrgDTO(OrgDTO orgDTO) {
        this.orgDTO = orgDTO;
    }

    public List<String> getTechTableList() {
        return techTableList;
    }

    public void setTechTableList(List<String> techTableList) {
        this.techTableList = techTableList;
    }
}
