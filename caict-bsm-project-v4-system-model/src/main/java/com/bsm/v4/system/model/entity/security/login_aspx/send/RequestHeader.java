package com.bsm.v4.system.model.entity.security.login_aspx.send;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/13
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "soapenv",namespace = NameSpaceUrlConst.sendNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "monitorHeader"
})
public class RequestHeader {
    @XmlElement(name = "MonitorHeader",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private MonitorHeader monitorHeader;

    public RequestHeader() {
    }

    public MonitorHeader getMonitorHeader() {
        return monitorHeader;
    }

    public void setMonitorHeader(MonitorHeader monitorHeader) {
        this.monitorHeader = monitorHeader;
    }
}
