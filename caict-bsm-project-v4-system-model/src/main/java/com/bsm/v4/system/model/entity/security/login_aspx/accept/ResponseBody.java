package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/13
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Body",namespace = NameSpaceUrlConst.mainNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "getUsersResponse"
})
public class ResponseBody {
    @XmlElement(name = "responseBody",namespace = NameSpaceUrlConst.aNamespaceURI)
    private GetUsersResponse getUsersResponse;

    public ResponseBody() {
    }

    public ResponseBody(GetUsersResponse getUserResponse) {
        this.getUsersResponse = getUserResponse;
    }

    public GetUsersResponse getGetUsersResponse() {
        return getUsersResponse;
    }

    public void setGetUserResponse(GetUsersResponse getUserResponse) {
        this.getUsersResponse = getUserResponse;
    }
}
