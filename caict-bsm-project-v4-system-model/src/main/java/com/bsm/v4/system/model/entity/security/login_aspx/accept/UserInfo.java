package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "userInfo",namespace = NameSpaceUrlConst.aNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "birthday",
        "countryEmail",
        "education",
        "ethnicity",
        "invalidDate",
        "localEmail",
        "mobile",
        "orgInfo",
        "phone",
        "politic",
        "position",
        "sex",
        "technical",
        "userCode",
        "userName",
        "userType"
})
public class UserInfo {
    @XmlElement(name = "birthday",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String birthday;
    @XmlElement(name = "countryEmail",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String countryEmail;
    @XmlElement(name = "education",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String education;
    @XmlElement(name = "ethnicity",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String ethnicity;
    @XmlElement(name = "invalidDate",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String invalidDate;
    @XmlElement(name = "localEmail",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String localEmail;
    @XmlElement(name = "mobile",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String mobile;
    @XmlElement(name = "orgInfo",namespace = NameSpaceUrlConst.aNamespaceURI)
    private ResOrg orgInfo;
    @XmlElement(name = "phone",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String phone;
    @XmlElement(name = "politic",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String politic;
    @XmlElement(name = "position",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String position;
    @XmlElement(name = "sex",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String sex;
    @XmlElement(name = "technical",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String technical;
    @XmlElement(name = "userCode",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String userCode;
    @XmlElement(name = "userName",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String userName;
    @XmlElement(name = "userType",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String userType;

    public UserInfo() {
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getCountryEmail() {
        return countryEmail;
    }

    public void setCountryEmail(String countryEmail) {
        this.countryEmail = countryEmail;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getEthnicity() {
        return ethnicity;
    }

    public void setEthnicity(String ethnicity) {
        this.ethnicity = ethnicity;
    }

    public String getInvalidDate() {
        return invalidDate;
    }

    public void setInvalidDate(String invalidDate) {
        this.invalidDate = invalidDate;
    }

    public String getLocalEmail() {
        return localEmail;
    }

    public void setLocalEmail(String localEmail) {
        this.localEmail = localEmail;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public ResOrg getOrgInfo() {
        return orgInfo;
    }

    public void setOrgInfo(ResOrg orgInfo) {
        this.orgInfo = orgInfo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPolitic() {
        return politic;
    }

    public void setPolitic(String politic) {
        this.politic = politic;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getTechnical() {
        return technical;
    }

    public void setTechnical(String technical) {
        this.technical = technical;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }
}
