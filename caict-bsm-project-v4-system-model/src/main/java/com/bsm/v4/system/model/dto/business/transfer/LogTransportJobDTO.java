package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.LogTransportJob;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * fileName：附件名称
 */

public class LogTransportJobDTO extends LogTransportJob {

    private String fileName;
    private String fileGuid;
    private String fileDataNum; //数据条数
    private String isCompare; //任务状态
    private String fileState; //文件校验结果
    private String message;//消息
    private String downType;//数据正常/异常下载
    private String jobId;
    private String jobName;
    private String userType;//用户类型
    private String dataType;//类型：新增更新等
    private String genNum;//制式

    /*数据导出*/
    private String cellName;
    private String cellId;
    private String btsName;
    private String btsId;
    private String techType;
    private String location;
    private String county;
    private String longitude;
    private String latitude;
    private String sendStartFreq;
    private String sendEndFreq;
    private String accStartFreq;
    private String accEndFreq;
    private String maxEmissivePower;
    private String height;
    private String deviceFactory;
    private String deviceModel;
    private String modelCode;
    private String antennaModel;
    private String antennaFactory;
    private String polarizationModel;
    private String antennaAzimuth;
    private String feederLoss;
    private String antennaGain;
    private String altitude;
    private String expandStation;
    private String stScene;
    private Date trfDate;
    //private String trfUser;
    //private String trfData;
    private String logDetail;
    private String logShort;

    //分页信息
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    private String queryCode;
    private String queryName;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getFileDataNum() {
        return fileDataNum;
    }

    public void setFileDataNum(String fileDataNum) {
        this.fileDataNum = fileDataNum;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDownType() {
        return downType;
    }

    public void setDownType(String downType) {
        this.downType = downType;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendStartFreq() {
        return sendStartFreq;
    }

    public void setSendStartFreq(String sendStartFreq) {
        this.sendStartFreq = sendStartFreq;
    }

    public String getSendEndFreq() {
        return sendEndFreq;
    }

    public void setSendEndFreq(String sendEndFreq) {
        this.sendEndFreq = sendEndFreq;
    }

    public String getAccStartFreq() {
        return accStartFreq;
    }

    public void setAccStartFreq(String accStartFreq) {
        this.accStartFreq = accStartFreq;
    }

    public String getAccEndFreq() {
        return accEndFreq;
    }

    public void setAccEndFreq(String accEndFreq) {
        this.accEndFreq = accEndFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getDeviceFactory() {
        return deviceFactory;
    }

    public void setDeviceFactory(String deviceFactory) {
        this.deviceFactory = deviceFactory;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAntennaModel() {
        return antennaModel;
    }

    public void setAntennaModel(String antennaModel) {
        this.antennaModel = antennaModel;
    }

    public String getAntennaFactory() {
        return antennaFactory;
    }

    public void setAntennaFactory(String antennaFactory) {
        this.antennaFactory = antennaFactory;
    }

    public String getPolarizationModel() {
        return polarizationModel;
    }

    public void setPolarizationModel(String polarizationModel) {
        this.polarizationModel = polarizationModel;
    }

    public String getAntennaAzimuth() {
        return antennaAzimuth;
    }

    public void setAntennaAzimuth(String antennaAzimuth) {
        this.antennaAzimuth = antennaAzimuth;
    }

    public String getFeederLoss() {
        return feederLoss;
    }

    public void setFeederLoss(String feederLoss) {
        this.feederLoss = feederLoss;
    }

    public String getAntennaGain() {
        return antennaGain;
    }

    public void setAntennaGain(String antennaGain) {
        this.antennaGain = antennaGain;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }

    public Date getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(Date trfDate) {
        this.trfDate = trfDate;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }

    public String getLogShort() {
        return logShort;
    }

    public void setLogShort(String logShort) {
        this.logShort = logShort;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getQueryCode() {
        return queryCode;
    }

    public void setQueryCode(String queryCode) {
        this.queryCode = queryCode;
    }

    public String getQueryName() {
        return queryName;
    }

    public void setQueryName(String queryName) {
        this.queryName = queryName;
    }
}
