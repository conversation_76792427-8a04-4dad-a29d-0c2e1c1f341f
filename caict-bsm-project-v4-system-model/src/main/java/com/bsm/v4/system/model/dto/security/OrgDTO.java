package com.bsm.v4.system.model.dto.security;

import com.caictframework.utils.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Title: OrgDTO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.dto.security
 * @Date 2023/8/15 10:16
 * @description:
 */

@ApiModel(value = "orgDTO", description = "组织机构对象")
public class OrgDTO {

    //分页信息
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    @ApiModelProperty(value = "主键ID")
    private String guid;

    @ApiModelProperty(value = "用户id")
    private String usersId;

    @ApiModelProperty(value = "统一社会信用码")
    private String orgCode;

    @ApiModelProperty(value = "组织机构名称,频率申请单位、设台单位、无线电管理机构、缴费单位名称")
    private String orgName;

    @ApiModelProperty(value = "地区代码")
    private String orgAreaCode;

    @ApiModelProperty(value = "系统/行业代码")
    private String orgSysCode;

    @ApiModelProperty(value = "单位类型")
    private String orgType;

    @ApiModelProperty(value = "单位联系人")
    private String orgLinkPerson;

    @ApiModelProperty(value = "联系人身份证号码")
    private String orgPersonId;

    @ApiModelProperty(value = "上级组织机构代码")
    private String orgSupCode;

    @ApiModelProperty(value = "组织机构代码")
    private String supCode;

    @ApiModelProperty(value = "组织机构地址")
    private String orgAddr;

    @ApiModelProperty(value = "组织机构邮编")
    private String orgPost;

    @ApiModelProperty(value = "联系电话")
    private String orgPhone;

    @ApiModelProperty(value = "手机号码")
    private String orgMobPhone;

    @ApiModelProperty(value = "组织机构传真")
    private String orgFax;

    @ApiModelProperty(value = "开户银行")
    private String orgBank;

    @ApiModelProperty(value = "账户名称")
    private String orgAccountName;

    @ApiModelProperty(value = "银行帐号")
    private String orgAccount;

    @ApiModelProperty(value = "设台单位性质")
    private Integer orgHostility;

    @ApiModelProperty(value = "网址")
    private String orgWebSite;

    @ApiModelProperty(value = "电子邮箱")
    private String orgMail;

    @ApiModelProperty(value = "父级id")
    @TableParentId
    private String parentId;

    @ApiModelProperty(value = "区域详情")
    private String regionDetails;

    @ApiModelProperty(value = "所属用户集合", hidden = true)
    private List<UsersDTO> usersDTOs;

    @ApiModelProperty(value = "所属省市区", hidden = true)
    private RegionDTO regionDTO;

    @ApiModelProperty(value = "每页显示数量", hidden = true)
    @TableChildren
    private List<OrgDTO> childrenList = new ArrayList<>();

    @ApiModelProperty(value = "类型")
    private String userType;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "创建时间")
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;

    @ApiModelProperty(value = "状态 1：启用；2：停用")
    private Integer status;

    @ApiModelProperty(value = "是否同步")
    private String isSync;

    @ApiModelProperty(value = "社会信用代码")
    private String sucCode;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getUsersId() {
        return usersId;
    }

    public void setUsersId(String usersId) {
        this.usersId = usersId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAreaCode() {
        return orgAreaCode;
    }

    public void setOrgAreaCode(String orgAreaCode) {
        this.orgAreaCode = orgAreaCode;
    }

    public String getOrgSysCode() {
        return orgSysCode;
    }

    public void setOrgSysCode(String orgSysCode) {
        this.orgSysCode = orgSysCode;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgLinkPerson() {
        return orgLinkPerson;
    }

    public void setOrgLinkPerson(String orgLinkPerson) {
        this.orgLinkPerson = orgLinkPerson;
    }

    public String getOrgPersonId() {
        return orgPersonId;
    }

    public void setOrgPersonId(String orgPersonId) {
        this.orgPersonId = orgPersonId;
    }

    public String getOrgSupCode() {
        return orgSupCode;
    }

    public void setOrgSupCode(String orgSupCode) {
        this.orgSupCode = orgSupCode;
    }

    public String getSupCode() {
        return supCode;
    }

    public void setSupCode(String supCode) {
        this.supCode = supCode;
    }

    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    public String getOrgPost() {
        return orgPost;
    }

    public void setOrgPost(String orgPost) {
        this.orgPost = orgPost;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgMobPhone() {
        return orgMobPhone;
    }

    public void setOrgMobPhone(String orgMobPhone) {
        this.orgMobPhone = orgMobPhone;
    }

    public String getOrgFax() {
        return orgFax;
    }

    public void setOrgFax(String orgFax) {
        this.orgFax = orgFax;
    }

    public String getOrgBank() {
        return orgBank;
    }

    public void setOrgBank(String orgBank) {
        this.orgBank = orgBank;
    }

    public String getOrgAccountName() {
        return orgAccountName;
    }

    public void setOrgAccountName(String orgAccountName) {
        this.orgAccountName = orgAccountName;
    }

    public String getOrgAccount() {
        return orgAccount;
    }

    public void setOrgAccount(String orgAccount) {
        this.orgAccount = orgAccount;
    }

    public Integer getOrgHostility() {
        return orgHostility;
    }

    public void setOrgHostility(Integer orgHostility) {
        this.orgHostility = orgHostility;
    }

    public String getOrgWebSite() {
        return orgWebSite;
    }

    public void setOrgWebSite(String orgWebSite) {
        this.orgWebSite = orgWebSite;
    }

    public String getOrgMail() {
        return orgMail;
    }

    public void setOrgMail(String orgMail) {
        this.orgMail = orgMail;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getRegionDetails() {
        return regionDetails;
    }

    public void setRegionDetails(String regionDetails) {
        this.regionDetails = regionDetails;
    }

    public List<UsersDTO> getUsersDTOs() {
        return usersDTOs;
    }

    public void setUsersDTOs(List<UsersDTO> usersDTOs) {
        this.usersDTOs = usersDTOs;
    }

    public RegionDTO getRegionDTO() {
        return regionDTO;
    }

    public void setRegionDTO(RegionDTO regionDTO) {
        this.regionDTO = regionDTO;
    }

    public List<OrgDTO> getChildrenList() {
        return childrenList;
    }

    public void setChildrenList(List<OrgDTO> childrenList) {
        this.childrenList = childrenList;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getSucCode() {
        return sucCode;
    }

    public void setSucCode(String sucCode) {
        this.sucCode = sucCode;
    }
}

