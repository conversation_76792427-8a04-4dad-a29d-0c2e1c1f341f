package com.bsm.v4.system.model.entity.business.applytable;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * Created by yanchengpeng on 2020/12/3.
 */

@TableName("APPLY_LINK")
public class ApplyLink {
    @TableId("GUID")
    private String guid;
    @TableFieId("JOB_GUID")
    private String jobGuid;
    @TableFieId("APP_CODE_OUT")
    private String appCodeOut;
    @TableFieId("APP_CODE_IN")
    private String appCodeIn;
    @TableFieId("SYNC_DATE")
    private Date syncDate;
    @TableFieId("SYNC_STATUS")
    private String syncStatus;
    @TableFieId("APPLY_STATUS")
    private String applyStatus;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getAppCodeOut() {
        return appCodeOut;
    }

    public void setAppCodeOut(String appCodeOut) {
        this.appCodeOut = appCodeOut;
    }

    public String getAppCodeIn() {
        return appCodeIn;
    }

    public void setAppCodeIn(String appCodeIn) {
        this.appCodeIn = appCodeIn;
    }

    public Date getSyncDate() {
        return syncDate;
    }

    public void setSyncDate(Date syncDate) {
        this.syncDate = syncDate;
    }

    public String getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }
}
