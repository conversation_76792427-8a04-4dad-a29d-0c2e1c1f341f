package com.bsm.v4.system.model.dto.business.techtable;



/**
 * Created by yanchengpeng on 2020/2/25.
 * <p>
 * 收发机设备DTO
 */

public class Table11EquDataDTO {
    //设备型号
    private String equModel;
    //型号核准代码
    private String equAuth;
    //设备生产厂家
    private String equMenu;
    //设备数量
    private Integer etEquSum;
    //发射功率
    private Double equPow;
    //功率单位
    private String equPowU;

    private String btsId;

    private String appGuid;

    public String getEquModel() {
        return equModel;
    }

    public void setEquModel(String equModel) {
        this.equModel = equModel;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public String getEquMenu() {
        return equMenu;
    }

    public void setEquMenu(String equMenu) {
        this.equMenu = equMenu;
    }

    public Integer getEtEquSum() {
        return etEquSum;
    }

    public void setEtEquSum(Integer etEquSum) {
        this.etEquSum = etEquSum;
    }

    public Double getEquPow() {
        return equPow;
    }

    public void setEquPow(Double equPow) {
        this.equPow = equPow;
    }

    public String getEquPowU() {
        return equPowU;
    }

    public void setEquPowU(String equPowU) {
        this.equPowU = equPowU;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }
}
