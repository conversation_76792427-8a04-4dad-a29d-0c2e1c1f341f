package com.bsm.v4.system.model.dto.business.station;

import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


import java.util.List;

/**
 * 基站名称，基站识别码，技术体制，台址，经度，纬度
 * <p>
 * radius:半径
 * freqEfb:发射频率下限
 * freqEfe:发射频率上限
 * freqRfb:接收频率下限
 * freqRfe:接收频率上限
 * isDeleted:状态
 * <p>
 * sectionDTOList:扇区集合
 */

public class StationDTO extends RsbtStation {

    private String stationId;
    private String stationName;
    private String stationCode;
    private String stationState;
    private String location;
    private String longitude;
    private String latitude;
    private String appGuid;
    private String stCCode;
    private String orgName;
    private String county;
    private String token;

    private String applyTableGuid;
    private String isArea;
    private String isSync;

    //所属附件集合
    private List<TransportFileDTO> transportFileAttachedDTOList;

    //组织机构Id
    private String orgId;
    //组织机构编号
    private String orgCode;
    //技术制式
    private String netType;
    //基站集合
    private List<RsbtStation> rsbtStations;
    //    //扇区集合
    private List<SectionDTO> sectionDTOList;

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    private Double radius;
    private Double minlat;
    private Double maxlat;
    private Double minlng;
    private Double maxlng;
    private String freqEfb;
    private String freqEfe;
    private String freqRfb;
    private String freqRfe;
    private String genNum;
    private String isDeleted;
    private String isLicense;
    private String userGuid;
    private String orgType;
    private String areaCode;

    //1.宏站2.直放站
    private String expandStation;

    //1.室内站2.室外站
    private String attributeStation;

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationState() {
        return stationState;
    }

    public void setStationState(String stationState) {
        this.stationState = stationState;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getStCCode() {
        return stCCode;
    }

    public void setStCCode(String stCCode) {
        this.stCCode = stCCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getApplyTableGuid() {
        return applyTableGuid;
    }

    public void setApplyTableGuid(String applyTableGuid) {
        this.applyTableGuid = applyTableGuid;
    }

    public String getIsArea() {
        return isArea;
    }

    public void setIsArea(String isArea) {
        this.isArea = isArea;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public List<TransportFileDTO> getTransportFileAttachedDTOList() {
        return transportFileAttachedDTOList;
    }

    public void setTransportFileAttachedDTOList(List<TransportFileDTO> transportFileAttachedDTOList) {
        this.transportFileAttachedDTOList = transportFileAttachedDTOList;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public List<RsbtStation> getRsbtStations() {
        return rsbtStations;
    }

    public void setRsbtStations(List<RsbtStation> rsbtStations) {
        this.rsbtStations = rsbtStations;
    }

    public List<SectionDTO> getSectionDTOList() {
        return sectionDTOList;
    }

    public void setSectionDTOList(List<SectionDTO> sectionDTOList) {
        this.sectionDTOList = sectionDTOList;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    public Double getMinlat() {
        return minlat;
    }

    public void setMinlat(Double minlat) {
        this.minlat = minlat;
    }

    public Double getMaxlat() {
        return maxlat;
    }

    public void setMaxlat(Double maxlat) {
        this.maxlat = maxlat;
    }

    public Double getMinlng() {
        return minlng;
    }

    public void setMinlng(Double minlng) {
        this.minlng = minlng;
    }

    public Double getMaxlng() {
        return maxlng;
    }

    public void setMaxlng(Double maxlng) {
        this.maxlng = maxlng;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getIsLicense() {
        return isLicense;
    }

    public void setIsLicense(String isLicense) {
        this.isLicense = isLicense;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }
}
