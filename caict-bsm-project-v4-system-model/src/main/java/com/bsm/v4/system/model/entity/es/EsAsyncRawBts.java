package com.bsm.v4.system.model.entity.es;

import io.swagger.annotations.ApiModelProperty;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

/**
 * es 数据表.
 *
 * <AUTHOR>
 * @since 2023年8月10日 11点33分
 **/
@Document(indexName = "es_async_raw_bts")

public class EsAsyncRawBts {

    @ApiModelProperty(value = "主键ID")
    @Id
    @Field(name = "GUID", type = FieldType.Text)
    private String guid;

    @ApiModelProperty(value = "任务编号")
    @Field(name = "JOB_GUID", type = FieldType.Text)
    private String jobGuid;

    @ApiModelProperty(value = "审核任务id")
    @Field(name = "APP_GUID", type = FieldType.Text)
    private String appGuid;

    @ApiModelProperty(value = "审核时间")
    @Field(name = "UPDATE_TIME", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "小区名称")
    @Field(name = "CELL_NAME", type = FieldType.Text)
    private String cellName;

    @ApiModelProperty(value = "小区识别码")
    @Field(name = "CELL_ID", type = FieldType.Text)
    private String cellId;

    @ApiModelProperty(value = "基站名称")
    @Field(name = "BTS_NAME", type = FieldType.Text)
    private String btsName;

    @ApiModelProperty(value = "基站识别码")
    @Field(name = "BTS_ID", type = FieldType.Text)
    private String btsId;

    @ApiModelProperty(value = "技术体制")
    @Field(name = "TECH_TYPE", type = FieldType.Text)
    private String techType;

    @ApiModelProperty(value = "台址")
    @Field(name = "LOCATION", type = FieldType.Text)
    private String location;

    @ApiModelProperty(value = "经度")
    @Field(name = "LONGITUDE", type = FieldType.Text)
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @Field(name = "LATITUDE", type = FieldType.Text)
    private String latitude;

    @ApiModelProperty(value = "发射起始频率")
    @Field(name = "SEND_START_FREQ", type = FieldType.Text)
    private String sendStartFreq;

    @ApiModelProperty(value = "发射终止频率")
    @Field(name = "SEND_END_FREQ", type = FieldType.Text)
    private String sendEndFreq;

    @ApiModelProperty(value = "接收起始频率")
    @Field(name = "ACC_START_FREQ", type = FieldType.Text)
    private String accStartFreq;

    @ApiModelProperty(value = "接收终止频率")
    @Field(name = "ACC_END_FREQ", type = FieldType.Text)
    private String accEndFreq;

    @ApiModelProperty(value = "最大发射功率")
    @Field(name = "MAX_EMISSIVE_POWER", type = FieldType.Text)
    private String maxEmissivePower;

    @ApiModelProperty(value = "天线距地高度")
    @Field(name = "HEIGHT", type = FieldType.Text)
    private String height;

    @ApiModelProperty(value = "数据变更类型(1：新增；2：变更；3：删除。默认首次传送值为1)")
    @Field(name = "DATA_TYPE", type = FieldType.Text)
    private String dataType;

    @ApiModelProperty(value = "地区")
    @Field(name = "COUNTY", type = FieldType.Text)
    private String county;

    @ApiModelProperty(value = "是否已处理 0-未处理 1-处理中 2-已处理")
    @Field(name = "IS_HANDLE", type = FieldType.Text)
    private String isHandle;

    @ApiModelProperty(value = "用户id")
    @Field(name = "USER_GUID", type = FieldType.Text)
    private String userId;

    @ApiModelProperty(value = "设备生产厂家")
    @Field(name = "VENDOR_NAME", type = FieldType.Text)
    private String vendorName;

    @ApiModelProperty(value = "设备型号")
    @Field(name = "DEVICE_MODEL", type = FieldType.Text)
    private String deviceModel;

    @ApiModelProperty(value = "型号核准代码")
    @Field(name = "MODEL_CODE", type = FieldType.Text)
    private String modelCode;

    @ApiModelProperty(value = "天线增益")
    @Field(name = "ANTENNA_GAIN", type = FieldType.Text)
    private String antennaGain;

    @ApiModelProperty(value = "制式")
    @Field(name = "GEN_NUM", type = FieldType.Text)
    private String genNum;

    @ApiModelProperty(value = "天线类型")
    @Field(name = "ANTENNA_MODEL", type = FieldType.Text)
    private String antennaModel;

    @ApiModelProperty(value = "天线生产厂家")
    @Field(name = "ANTENNA_FACTORY", type = FieldType.Text)
    private String antennaFactory;

    @ApiModelProperty(value = "极化方式")
    @Field(name = "POLARIZATION_MODE", type = FieldType.Text)
    private String polarizationMode;

    @ApiModelProperty(value = "天线方位角")
    @Field(name = "ANTENNA_AZIMUTH", type = FieldType.Text)
    private String antennaAzimuth;

    @ApiModelProperty(value = "馈线系统总损耗")
    @Field(name = "FEEDER_LOSS", type = FieldType.Text)
    private String feederLoss;

    @ApiModelProperty(value = "海拔高度")
    @Field(name = "ALTITUDE", type = FieldType.Text)
    private String altitude;

    @ApiModelProperty(value = "运营商类型")
    @Field(name = "ORG_TYPE", type = FieldType.Text)
    private String orgType;

    @ApiModelProperty(value = "运营商名称")
    @Field(name = "ORG_NAME", type = FieldType.Text)
    private String orgName;

    @ApiModelProperty(value = "1.宏站2.直放站")
    @Field(name = "EXPAND_STATION", type = FieldType.Text)
    private String expandStation;

    @ApiModelProperty(value = "设台月份")
    @Field(name = "SET_MONTH", type = FieldType.Text)
    private String setMonth;

    @ApiModelProperty(value = "设台年份")
    @Field(name = "SET_YEAR", type = FieldType.Text)
    private String setYear;

    @ApiModelProperty(value = "收倾角")
    @Field(name = "AT_RANG", type = FieldType.Text)
    private String atRang;

    @ApiModelProperty(value = "发倾角")
    @Field(name = "AT_EANG", type = FieldType.Text)
    private String atEang;

    @ApiModelProperty(value = "服务半径")
    @Field(name = "ST_SERV_R", type = FieldType.Text)
    private String stServR;

    @ApiModelProperty(value = "基站更新类型（1：新增；2：变更；3：删除；4：不变）")
    @Field(name = "BTS_DATA_TYPE", type = FieldType.Text)
    private String btsDataType;

    @ApiModelProperty(value = "基站覆盖场景")
    @Field(name = "ST_SCENE", type = FieldType.Text)
    private String stScene;

    @ApiModelProperty(value = "用户数或流量统计年份月份")
    @Field(name = "TRF_DATE", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime trfDate;

    @ApiModelProperty(value = "平均忙时激活用户数")
    @Field(name = "TRF_USER", type = FieldType.Double)
    private Double trfUser;

    @ApiModelProperty(value = "平均忙时激活用户流量")
    @Field(name = "TRF_DATA", type = FieldType.Double)
    private Double trfData;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendStartFreq() {
        return sendStartFreq;
    }

    public void setSendStartFreq(String sendStartFreq) {
        this.sendStartFreq = sendStartFreq;
    }

    public String getSendEndFreq() {
        return sendEndFreq;
    }

    public void setSendEndFreq(String sendEndFreq) {
        this.sendEndFreq = sendEndFreq;
    }

    public String getAccStartFreq() {
        return accStartFreq;
    }

    public void setAccStartFreq(String accStartFreq) {
        this.accStartFreq = accStartFreq;
    }

    public String getAccEndFreq() {
        return accEndFreq;
    }

    public void setAccEndFreq(String accEndFreq) {
        this.accEndFreq = accEndFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getIsHandle() {
        return isHandle;
    }

    public void setIsHandle(String isHandle) {
        this.isHandle = isHandle;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAntennaGain() {
        return antennaGain;
    }

    public void setAntennaGain(String antennaGain) {
        this.antennaGain = antennaGain;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getAntennaModel() {
        return antennaModel;
    }

    public void setAntennaModel(String antennaModel) {
        this.antennaModel = antennaModel;
    }

    public String getAntennaFactory() {
        return antennaFactory;
    }

    public void setAntennaFactory(String antennaFactory) {
        this.antennaFactory = antennaFactory;
    }

    public String getPolarizationMode() {
        return polarizationMode;
    }

    public void setPolarizationMode(String polarizationMode) {
        this.polarizationMode = polarizationMode;
    }

    public String getAntennaAzimuth() {
        return antennaAzimuth;
    }

    public void setAntennaAzimuth(String antennaAzimuth) {
        this.antennaAzimuth = antennaAzimuth;
    }

    public String getFeederLoss() {
        return feederLoss;
    }

    public void setFeederLoss(String feederLoss) {
        this.feederLoss = feederLoss;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getSetMonth() {
        return setMonth;
    }

    public void setSetMonth(String setMonth) {
        this.setMonth = setMonth;
    }

    public String getSetYear() {
        return setYear;
    }

    public void setSetYear(String setYear) {
        this.setYear = setYear;
    }

    public String getAtRang() {
        return atRang;
    }

    public void setAtRang(String atRang) {
        this.atRang = atRang;
    }

    public String getAtEang() {
        return atEang;
    }

    public void setAtEang(String atEang) {
        this.atEang = atEang;
    }

    public String getStServR() {
        return stServR;
    }

    public void setStServR(String stServR) {
        this.stServR = stServR;
    }

    public String getBtsDataType() {
        return btsDataType;
    }

    public void setBtsDataType(String btsDataType) {
        this.btsDataType = btsDataType;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }

    public LocalDateTime getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(LocalDateTime trfDate) {
        this.trfDate = trfDate;
    }

    public Double getTrfUser() {
        return trfUser;
    }

    public void setTrfUser(Double trfUser) {
        this.trfUser = trfUser;
    }

    public Double getTrfData() {
        return trfData;
    }

    public void setTrfData(Double trfData) {
        this.trfData = trfData;
    }
}
