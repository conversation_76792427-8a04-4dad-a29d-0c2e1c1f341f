package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-05-08.
 * <p>
 * appDateStart：查询开始时间
 * appDateEnd：查询结束时间
 * userType: 运行商类型
 * <p>
 * jobName:任务名称
 * <p>
 * transportFileDTOList:所属exl文件集合
 */

@ApiModel(value = "approvalTransportJobDTO", description = "菜单对象")
public class ApprovalTransportJobDTO extends ApprovalTransportJob {

    private Date appDateStart;
    private Date appDateEnd;
    private String userType;
    private String type;
    private String fileId;

    private String jobName;
    //    private String appGuid;
    private int sectionNum;

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    //附件信息
    private String fileGuid;
    private String token;
    private String fileName;
    private String fileState;

    //所属exl文件集合
    private List<TransportFileDTO> transportFileDTOList;

    public Date getAppDateStart() {
        return appDateStart;
    }

    public void setAppDateStart(Date appDateStart) {
        this.appDateStart = appDateStart;
    }

    public Date getAppDateEnd() {
        return appDateEnd;
    }

    public void setAppDateEnd(Date appDateEnd) {
        this.appDateEnd = appDateEnd;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public int getSectionNum() {
        return sectionNum;
    }

    public void setSectionNum(int sectionNum) {
        this.sectionNum = sectionNum;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }

    public List<TransportFileDTO> getTransportFileDTOList() {
        return transportFileDTOList;
    }

    public void setTransportFileDTOList(List<TransportFileDTO> transportFileDTOList) {
        this.transportFileDTOList = transportFileDTOList;
    }
}
