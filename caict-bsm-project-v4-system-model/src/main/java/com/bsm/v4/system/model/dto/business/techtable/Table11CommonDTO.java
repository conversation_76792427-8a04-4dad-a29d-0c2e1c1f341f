package com.bsm.v4.system.model.dto.business.techtable;



import java.util.List;

/**
 * Created by yanchengpeng on 2020/2/25.
 */

public class Table11CommonDTO {

    //申报表类型
    private String statAppType;

    //技术资料申报表编号
    private String statTdi;

    //备注
    private String memo;
    //表号
    private String tableNo;

    //台站数据
    private Table11StationDataDTO stationData;

    //扇区数据
    private List<Table11SectorDataDTO> sectorDatas;

    //天线数据
    private List<Table11AntFeedDataDTO> antFeedDatas;

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getTableNo() {
        return tableNo;
    }

    public void setTableNo(String tableNo) {
        this.tableNo = tableNo;
    }

    public Table11StationDataDTO getStationData() {
        return stationData;
    }

    public void setStationData(Table11StationDataDTO stationData) {
        this.stationData = stationData;
    }

    public List<Table11SectorDataDTO> getSectorDatas() {
        return sectorDatas;
    }

    public void setSectorDatas(List<Table11SectorDataDTO> sectorDatas) {
        this.sectorDatas = sectorDatas;
    }

    public List<Table11AntFeedDataDTO> getAntFeedDatas() {
        return antFeedDatas;
    }

    public void setAntFeedDatas(List<Table11AntFeedDataDTO> antFeedDatas) {
        this.antFeedDatas = antFeedDatas;
    }
}
