package com.bsm.v4.system.model.dto.business.techtable;



/**
 * Created by yanchengpeng on 2020/2/25.
 */

public class Table11SectorDataDTO {
    //扇区编号
    private String atCCode;
    //方位角
    private Double antAngle;
    //扇区标识码
    private String atCsgn;
    //收倾角
    private Double atRang;
    //发倾角
    private Double atEang;
    //发射频率下限
    private Double freqEfb;
    //发射频率上限
    private Double freqEfe;
    //接收频率下限
    private Double freqRfb;
    //接收频率上限
    private Double freqRfe;

    private String eafGuid;

    //设备数据
    private Table11EquDataDTO equData;

    private String btsId;
    private String appGuid;

    public String getAtCCode() {
        return atCCode;
    }

    public void setAtCCode(String atCCode) {
        this.atCCode = atCCode;
    }

    public Double getAntAngle() {
        return antAngle;
    }

    public void setAntAngle(Double antAngle) {
        this.antAngle = antAngle;
    }

    public String getAtCsgn() {
        return atCsgn;
    }

    public void setAtCsgn(String atCsgn) {
        this.atCsgn = atCsgn;
    }

    public Double getAtRang() {
        return atRang;
    }

    public void setAtRang(Double atRang) {
        this.atRang = atRang;
    }

    public Double getAtEang() {
        return atEang;
    }

    public void setAtEang(Double atEang) {
        this.atEang = atEang;
    }

    public Double getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(Double freqEfb) {
        this.freqEfb = freqEfb;
    }

    public Double getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(Double freqEfe) {
        this.freqEfe = freqEfe;
    }

    public Double getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(Double freqRfb) {
        this.freqRfb = freqRfb;
    }

    public Double getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(Double freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getEafGuid() {
        return eafGuid;
    }

    public void setEafGuid(String eafGuid) {
        this.eafGuid = eafGuid;
    }

    public Table11EquDataDTO getEquData() {
        return equData;
    }

    public void setEquData(Table11EquDataDTO equData) {
        this.equData = equData;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }
}
