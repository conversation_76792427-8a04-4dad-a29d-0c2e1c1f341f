package com.bsm.v4.system.model.dto.business.applytable;

import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dsy62 on 2020-12-8.
 * 外网端申请表技术资料信息打印
 */
public class BsmApplyTableTemplateDTO {

    @ApiModelProperty(value = "序号")
    private String id;

    @ApiModelProperty(value = "台站名称")
    private String btsName;

    @ApiModelProperty(value = "基站识别码")
    private String btsId;

    @ApiModelProperty(value = "技术体制")
    private String techType;

    @ApiModelProperty(value = "台址（编号）")
    private String county;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "发射频率")
    private String sendFreq;

    @ApiModelProperty(value = "接收频率")
    private String accFreq;

    @ApiModelProperty(value = "最大发射功率")
    private String maxEmissivePower;

    @ApiModelProperty(value = "天线距地面高度")
    private String height;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendFreq() {
        return sendFreq;
    }

    public void setSendFreq(String sendFreq) {
        this.sendFreq = sendFreq;
    }

    public String getAccFreq() {
        return accFreq;
    }

    public void setAccFreq(String accFreq) {
        this.accFreq = accFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }
}
