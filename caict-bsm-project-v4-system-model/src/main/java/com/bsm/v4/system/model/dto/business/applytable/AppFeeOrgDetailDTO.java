package com.bsm.v4.system.model.dto.business.applytable;



/**
 * Created by yanchengpeng on 2020/2/27.
 */
public class AppFeeOrgDetailDTO {
    //机构名称
    private String orgName;
    //机构代码
    private String orgCode;
    //机构地址
    private String orgAddr;
    //机构邮编
    private String orgPost;
    //联系人
    private String orgLinkPerson;
    //联系电话
    private String orgPhone;
    //移动电话
    private String orgMobPhone;
    //传真
    private String orgFax;
    //电子邮箱
    private String orgMail;
    //开户银行
    private String orgBank;
    //账户名称
    private String orgAccName;
    //银行账号
    private String orgAcc;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    public String getOrgPost() {
        return orgPost;
    }

    public void setOrgPost(String orgPost) {
        this.orgPost = orgPost;
    }

    public String getOrgLinkPerson() {
        return orgLinkPerson;
    }

    public void setOrgLinkPerson(String orgLinkPerson) {
        this.orgLinkPerson = orgLinkPerson;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgMobPhone() {
        return orgMobPhone;
    }

    public void setOrgMobPhone(String orgMobPhone) {
        this.orgMobPhone = orgMobPhone;
    }

    public String getOrgFax() {
        return orgFax;
    }

    public void setOrgFax(String orgFax) {
        this.orgFax = orgFax;
    }

    public String getOrgMail() {
        return orgMail;
    }

    public void setOrgMail(String orgMail) {
        this.orgMail = orgMail;
    }

    public String getOrgBank() {
        return orgBank;
    }

    public void setOrgBank(String orgBank) {
        this.orgBank = orgBank;
    }

    public String getOrgAccName() {
        return orgAccName;
    }

    public void setOrgAccName(String orgAccName) {
        this.orgAccName = orgAccName;
    }

    public String getOrgAcc() {
        return orgAcc;
    }

    public void setOrgAcc(String orgAcc) {
        this.orgAcc = orgAcc;
    }
}
