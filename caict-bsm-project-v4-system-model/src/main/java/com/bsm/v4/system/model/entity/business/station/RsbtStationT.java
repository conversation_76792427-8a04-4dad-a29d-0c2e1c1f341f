package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * 无线电台（站）台站冗余表
 */

@TableName("RSBT_STATION_T")
public class RsbtStationT {

    /**
     * RSBT_STATION
     */
    @TableId("GUID")
    private String guid;

    /**
     * 工作时间起
     */
    @TableFieId("ST_TIME_B")
    private Date stTimeB;

    /**
     * 工作时间止
     */
    @TableFieId("ST_TIME_E")
    private Date stTimeE;

    /**
     * 是否机车制式电台
     */
    @TableFieId("ST_LMMTR")
    private String stLmmtr;

    /**
     * 使用区域
     */
    @TableFieId("ST_USER_AREA")
    private String stUserArea;

    /**
     * 站代号
     */
    @TableFieId("ST_TF_CODE")
    private String stTfCode;

    /**
     * 传输容量单位
     */
    @TableFieId("ST_TF_TRANS_CA_PU")
    private String stTfTransCaPu;

    /**
     * 传输容量值
     */
    @TableFieId("ST_TF_TRANS_CA_P")
    private Double stTfTransCaP;

    /**
     * 网络编号
     */
    @TableFieId("ST_E_NET_CODE")
    private String stENetCode;

    /**
     * 多址方式
     */
    @TableFieId("ST_E_MULTI_ADDR")
    private String stEMultiAddr;

    /**
     * 通信范围
     */
    @TableFieId("ST_E_COM_AREA")
    private String stEComArea;

    /**
     * 位置类型
     */
    @TableFieId("ST_POS_TYPE")
    private String stPosType;

    /**
     * 位置类型（值）
     */
    @TableFieId("ST_E_POS")
    private String stEPos;

    /**
     * 空间电台（星座）名称
     */
    @TableFieId("ST_E_SAT")
    private String stESat;

    /**
     * 标称轨道经度
     */
    @TableFieId("ST_E_LG")
    private String stELg;

    /**
     * 台标
     */
    @TableFieId("ST_B_SGN")
    private String stBSgn;

    /**
     * 台站级别
     */
    @TableFieId("ST_B_LEVEL")
    private String stBLevel;

    /**
     * 广播制式
     */
    @TableFieId("ST_B_BM")
    private String stBBm;

    /**
     * 是否教育台
     */
    @TableFieId("ST_B_EDU")
    private String stBEdu;

    /**
     * 是否差转台
     */
    @TableFieId("ST_B_IC")
    private String stBIc;

    /**
     * 覆盖区域
     */
    @TableFieId("ST_B_COVER_AREA")
    private String stBCoverArea;

    /**
     * 船舶种类
     */
    @TableFieId("ST_SHIP_TYPE")
    private String stShipType;

    /**
     * 呼号
     */
    @TableFieId("ST_CALL_SIGN")
    private String stCallSign;

    /**
     * MMSI号
     */
    @TableFieId("ST_S_MMSI")
    private String stSMmsi;

    /**
     * 船舶登记号
     */
    @TableFieId("ST_S_CS")
    private String stSCs;

    /**
     * 船舶名称
     */
    @TableFieId("ST_SHIP_NAME")
    private String stShipName;

    /**
     * 船籍港名
     */
    @TableFieId("ST_S_PN")
    private String stSPn;

    /**
     * 帐务结算机构代码
     */
    @TableFieId("ST_S_AAIC")
    private String stSAaic;

    /**
     * 总吨位
     */
    @TableFieId("ST_S_T")
    private Double stST;

    /**
     * 总功率
     */
    @TableFieId("ST_S_P")
    private Double stSP;

    /**
     * 航空器型号
     */
    @TableFieId("ST_A_MODEL")
    private String stAModel;

    /**
     * 航空器类型
     */
    @TableFieId("ST_A_TYPE")
    private String stAType;

    /**
     * 选呼或报呼
     */
    @TableFieId("ST_A_CALL")
    private String stACall;

    /**
     * 呼号或其它标识
     */
    @TableFieId("ST_A_Call_OI")
    private String stACallOi;

    /**
     * 航空器识别码
     */
    @TableFieId("ST_A_SGN")
    private String stASgn;

    /**
     * 最大起飞重量
     */
    @TableFieId("ST_A_ST")
    private Double stASt;

    /**
     * 是否按地标飞行
     */
    @TableFieId("ST_A_PILOTING")
    private String stAPiloting;

    /**
     * 国籍和注册号码
     */
    @TableFieId("ST_A_NRM")
    private String stANrm;

    /**
     * 工作方位角最小值
     */
    @TableFieId("ST_R_WAMIN")
    private String stRWamin;

    /**
     * 工作方位角最大值
     */
    @TableFieId("ST_R_WAMAX")
    private String stRWamax;

    /**
     * 俯仰角最小值
     */
    @TableFieId("ST_R_EMIN")
    private String stREmin;

    /**
     * 俯仰角最大值
     */
    @TableFieId("ST_R_EMAX")
    private String stREmax;

    /**
     * 基站编号（唯一）
     */
    @TableFieId("ST_C_CODE")
    private String stCCode;

    /**
     * 扇区数量
     */
    @TableFieId("ST_C_SUM")
    private int stCSum;

    /**
     * 服务半径
     */
    @TableFieId("ST_SERV_R")
    private Double stServR;

    /**
     * 技术体制
     */
    @TableFieId("ST_D_TEC_TYPE")
    private String stDTecType;

    /**
     * 直放站类型
     */
    @TableFieId("ST_D_TYPE")
    private String stDType;

    /**
     * 卫星移动通信系统名称
     */
    @TableFieId("ST_ME_SAT_NAME")
    private String stMeSatName;

    /**
     * 关口站名称1
     */
    @TableFieId("ST_ME_STA1")
    private String stMeSta1;

    /**
     * 关口站名称2
     */
    @TableFieId("ST_ME_STA2")
    private String stMeSta2;

    /**
     * 关口站名称3
     */
    @TableFieId("ST_ME_STA3")
    private String stMeSta3;

    /**
     * 星座/卫星
     */
    @TableFieId("ST_ME_STYPE")
    private String stMeStype;

    /**
     * 空间电台（星座）名称1
     */
    @TableFieId("ST_ME_NAME1")
    private String stMeName1;

    /**
     * 空间电台（星座）名称2
     */
    @TableFieId("ST_ME_NAME2")
    private String stMeName2;

    /**
     * 标称轨道经度1
     */
    @TableFieId("ST_ME_LG1")
    private Double stMeLg1;

    /**
     * 标称轨道经度2
     */
    @TableFieId("ST_ME_LG2")
    private Double stMeLg2;

    /**
     * 使用总带宽
     */
    @TableFieId("ST_ME_FBAND")
    private Double stMeFband;

    /**
     * 广播电台台站类别1
     */
    @TableFieId("ST_B_TYPE1")
    private String stBType1;

    /**
     * 广播电台台站类别2
     */
    @TableFieId("ST_B_TYPE2")
    private String stBType2;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getStTimeB() {
        return stTimeB;
    }

    public void setStTimeB(Date stTimeB) {
        this.stTimeB = stTimeB;
    }

    public Date getStTimeE() {
        return stTimeE;
    }

    public void setStTimeE(Date stTimeE) {
        this.stTimeE = stTimeE;
    }

    public String getStLmmtr() {
        return stLmmtr;
    }

    public void setStLmmtr(String stLmmtr) {
        this.stLmmtr = stLmmtr;
    }

    public String getStUserArea() {
        return stUserArea;
    }

    public void setStUserArea(String stUserArea) {
        this.stUserArea = stUserArea;
    }

    public String getStTfCode() {
        return stTfCode;
    }

    public void setStTfCode(String stTfCode) {
        this.stTfCode = stTfCode;
    }

    public String getStTfTransCaPu() {
        return stTfTransCaPu;
    }

    public void setStTfTransCaPu(String stTfTransCaPu) {
        this.stTfTransCaPu = stTfTransCaPu;
    }

    public Double getStTfTransCaP() {
        return stTfTransCaP;
    }

    public void setStTfTransCaP(Double stTfTransCaP) {
        this.stTfTransCaP = stTfTransCaP;
    }

    public String getStENetCode() {
        return stENetCode;
    }

    public void setStENetCode(String stENetCode) {
        this.stENetCode = stENetCode;
    }

    public String getStEMultiAddr() {
        return stEMultiAddr;
    }

    public void setStEMultiAddr(String stEMultiAddr) {
        this.stEMultiAddr = stEMultiAddr;
    }

    public String getStEComArea() {
        return stEComArea;
    }

    public void setStEComArea(String stEComArea) {
        this.stEComArea = stEComArea;
    }

    public String getStPosType() {
        return stPosType;
    }

    public void setStPosType(String stPosType) {
        this.stPosType = stPosType;
    }

    public String getStEPos() {
        return stEPos;
    }

    public void setStEPos(String stEPos) {
        this.stEPos = stEPos;
    }

    public String getStESat() {
        return stESat;
    }

    public void setStESat(String stESat) {
        this.stESat = stESat;
    }

    public String getStELg() {
        return stELg;
    }

    public void setStELg(String stELg) {
        this.stELg = stELg;
    }

    public String getStBSgn() {
        return stBSgn;
    }

    public void setStBSgn(String stBSgn) {
        this.stBSgn = stBSgn;
    }

    public String getStBLevel() {
        return stBLevel;
    }

    public void setStBLevel(String stBLevel) {
        this.stBLevel = stBLevel;
    }

    public String getStBBm() {
        return stBBm;
    }

    public void setStBBm(String stBBm) {
        this.stBBm = stBBm;
    }

    public String getStBEdu() {
        return stBEdu;
    }

    public void setStBEdu(String stBEdu) {
        this.stBEdu = stBEdu;
    }

    public String getStBIc() {
        return stBIc;
    }

    public void setStBIc(String stBIc) {
        this.stBIc = stBIc;
    }

    public String getStBCoverArea() {
        return stBCoverArea;
    }

    public void setStBCoverArea(String stBCoverArea) {
        this.stBCoverArea = stBCoverArea;
    }

    public String getStShipType() {
        return stShipType;
    }

    public void setStShipType(String stShipType) {
        this.stShipType = stShipType;
    }

    public String getStCallSign() {
        return stCallSign;
    }

    public void setStCallSign(String stCallSign) {
        this.stCallSign = stCallSign;
    }

    public String getStSMmsi() {
        return stSMmsi;
    }

    public void setStSMmsi(String stSMmsi) {
        this.stSMmsi = stSMmsi;
    }

    public String getStSCs() {
        return stSCs;
    }

    public void setStSCs(String stSCs) {
        this.stSCs = stSCs;
    }

    public String getStShipName() {
        return stShipName;
    }

    public void setStShipName(String stShipName) {
        this.stShipName = stShipName;
    }

    public String getStSPn() {
        return stSPn;
    }

    public void setStSPn(String stSPn) {
        this.stSPn = stSPn;
    }

    public String getStSAaic() {
        return stSAaic;
    }

    public void setStSAaic(String stSAaic) {
        this.stSAaic = stSAaic;
    }

    public Double getStST() {
        return stST;
    }

    public void setStST(Double stST) {
        this.stST = stST;
    }

    public Double getStSP() {
        return stSP;
    }

    public void setStSP(Double stSP) {
        this.stSP = stSP;
    }

    public String getStAModel() {
        return stAModel;
    }

    public void setStAModel(String stAModel) {
        this.stAModel = stAModel;
    }

    public String getStAType() {
        return stAType;
    }

    public void setStAType(String stAType) {
        this.stAType = stAType;
    }

    public String getStACall() {
        return stACall;
    }

    public void setStACall(String stACall) {
        this.stACall = stACall;
    }

    public String getStACallOi() {
        return stACallOi;
    }

    public void setStACallOi(String stACallOi) {
        this.stACallOi = stACallOi;
    }

    public String getStASgn() {
        return stASgn;
    }

    public void setStASgn(String stASgn) {
        this.stASgn = stASgn;
    }

    public Double getStASt() {
        return stASt;
    }

    public void setStASt(Double stASt) {
        this.stASt = stASt;
    }

    public String getStAPiloting() {
        return stAPiloting;
    }

    public void setStAPiloting(String stAPiloting) {
        this.stAPiloting = stAPiloting;
    }

    public String getStANrm() {
        return stANrm;
    }

    public void setStANrm(String stANrm) {
        this.stANrm = stANrm;
    }

    public String getStRWamin() {
        return stRWamin;
    }

    public void setStRWamin(String stRWamin) {
        this.stRWamin = stRWamin;
    }

    public String getStRWamax() {
        return stRWamax;
    }

    public void setStRWamax(String stRWamax) {
        this.stRWamax = stRWamax;
    }

    public String getStREmin() {
        return stREmin;
    }

    public void setStREmin(String stREmin) {
        this.stREmin = stREmin;
    }

    public String getStREmax() {
        return stREmax;
    }

    public void setStREmax(String stREmax) {
        this.stREmax = stREmax;
    }

    public String getStCCode() {
        return stCCode;
    }

    public void setStCCode(String stCCode) {
        this.stCCode = stCCode;
    }

    public int getStCSum() {
        return stCSum;
    }

    public void setStCSum(int stCSum) {
        this.stCSum = stCSum;
    }

    public Double getStServR() {
        return stServR;
    }

    public void setStServR(Double stServR) {
        this.stServR = stServR;
    }

    public String getStDTecType() {
        return stDTecType;
    }

    public void setStDTecType(String stDTecType) {
        this.stDTecType = stDTecType;
    }

    public String getStDType() {
        return stDType;
    }

    public void setStDType(String stDType) {
        this.stDType = stDType;
    }

    public String getStMeSatName() {
        return stMeSatName;
    }

    public void setStMeSatName(String stMeSatName) {
        this.stMeSatName = stMeSatName;
    }

    public String getStMeSta1() {
        return stMeSta1;
    }

    public void setStMeSta1(String stMeSta1) {
        this.stMeSta1 = stMeSta1;
    }

    public String getStMeSta2() {
        return stMeSta2;
    }

    public void setStMeSta2(String stMeSta2) {
        this.stMeSta2 = stMeSta2;
    }

    public String getStMeSta3() {
        return stMeSta3;
    }

    public void setStMeSta3(String stMeSta3) {
        this.stMeSta3 = stMeSta3;
    }

    public String getStMeStype() {
        return stMeStype;
    }

    public void setStMeStype(String stMeStype) {
        this.stMeStype = stMeStype;
    }

    public String getStMeName1() {
        return stMeName1;
    }

    public void setStMeName1(String stMeName1) {
        this.stMeName1 = stMeName1;
    }

    public String getStMeName2() {
        return stMeName2;
    }

    public void setStMeName2(String stMeName2) {
        this.stMeName2 = stMeName2;
    }

    public Double getStMeLg1() {
        return stMeLg1;
    }

    public void setStMeLg1(Double stMeLg1) {
        this.stMeLg1 = stMeLg1;
    }

    public Double getStMeLg2() {
        return stMeLg2;
    }

    public void setStMeLg2(Double stMeLg2) {
        this.stMeLg2 = stMeLg2;
    }

    public Double getStMeFband() {
        return stMeFband;
    }

    public void setStMeFband(Double stMeFband) {
        this.stMeFband = stMeFband;
    }

    public String getStBType1() {
        return stBType1;
    }

    public void setStBType1(String stBType1) {
        this.stBType1 = stBType1;
    }

    public String getStBType2() {
        return stBType2;
    }

    public void setStBType2(String stBType2) {
        this.stBType2 = stBType2;
    }
}