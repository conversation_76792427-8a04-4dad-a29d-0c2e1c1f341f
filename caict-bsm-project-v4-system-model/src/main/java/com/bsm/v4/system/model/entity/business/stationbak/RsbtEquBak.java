package com.bsm.v4.system.model.entity.business.stationbak;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_EQU_BAK")
public class RsbtEquBak {

    /**
     * 上行发射功率
     */
    @TableFieId("EQU_UPOW")
    private double equUpow;
    /**
     * 设备生产厂家
     */
    @TableFieId("EQU_MENU")
    private String equMenu;
    /**
     * 型号核准代码
     */
    @TableFieId("EQU_AUTH")
    private String equAuth;
    /**
     * 设备型号
     */
    @TableFieId("EQU_MODEL")
    private String equModel;
    /**
     * 下行发射功率
     */
    @TableFieId("EQU_DPOW")
    private double equDpow;
    @TableFieId("SECTION_CODE")
    private String sectionCode;
    @TableFieId("STATION_GUID")
    private String stationGuid;
    @TableId("EQU_GUID")
    private String equGuid;

    /**
     * 是否同步
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    /**
     * 数据类型（1-新增；2-变更；3-删除）
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    public RsbtEquBak() {
        this.isSync = "0";
    }

    public double getEquUpow() {
        return equUpow;
    }

    public void setEquUpow(double equUpow) {
        this.equUpow = equUpow;
    }

    public String getEquMenu() {
        return equMenu;
    }

    public void setEquMenu(String equMenu) {
        this.equMenu = equMenu;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public String getEquModel() {
        return equModel;
    }

    public void setEquModel(String equModel) {
        this.equModel = equModel;
    }

    public double getEquDpow() {
        return equDpow;
    }

    public void setEquDpow(double equDpow) {
        this.equDpow = equDpow;
    }

    public String getSectionCode() {
        return sectionCode;
    }

    public void setSectionCode(String sectionCode) {
        this.sectionCode = sectionCode;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getEquGuid() {
        return equGuid;
    }

    public void setEquGuid(String equGuid) {
        this.equGuid = equGuid;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}
