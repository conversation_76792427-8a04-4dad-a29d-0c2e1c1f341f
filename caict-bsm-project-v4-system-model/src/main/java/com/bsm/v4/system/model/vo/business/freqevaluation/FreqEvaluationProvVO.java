package com.bsm.v4.system.model.vo.business.freqevaluation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @description 省级公众移动通信系统用户数及用户流量
 * @date 2023年8月24日 11点13分
 */

@ApiModel(value = "freqEvaluationProvVO", description = "省级公众移动通信系统用户数及用户流量对象")
public class FreqEvaluationProvVO {

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
