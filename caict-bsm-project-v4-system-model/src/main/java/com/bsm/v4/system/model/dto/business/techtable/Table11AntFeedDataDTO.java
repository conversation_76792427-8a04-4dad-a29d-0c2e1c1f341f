package com.bsm.v4.system.model.dto.business.techtable;



/**
 * Created by yanchen<PERSON><PERSON><PERSON> on 2020/2/25.
 */

public class Table11AntFeedDataDTO {
    //扇区
    private String atCCode;
    //天线类型
    private String antType;
    //天线型号
    private String antModel;
    //极化方式
    private String antPole;
    //3db角宽（发）
    private Double at3DBE;
    //3db角宽（收）
    private Double at3DBR;
    //发最大增益
    private Double antEGain;
    //收最大增益
    private Double antRGain;
    //天线生产厂家
    private String antMenu;
    //天线距地面高度
    private Double antHeight;
    //馈线系统总损耗
    private Double feedLose;

    public String getAtCCode() {
        return atCCode;
    }

    public void setAtCCode(String atCCode) {
        this.atCCode = atCCode;
    }

    public String getAntType() {
        return antType;
    }

    public void setAntType(String antType) {
        this.antType = antType;
    }

    public String getAntModel() {
        return antModel;
    }

    public void setAntModel(String antModel) {
        this.antModel = antModel;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public Double getAt3DBE() {
        return at3DBE;
    }

    public void setAt3DBE(Double at3DBE) {
        this.at3DBE = at3DBE;
    }

    public Double getAt3DBR() {
        return at3DBR;
    }

    public void setAt3DBR(Double at3DBR) {
        this.at3DBR = at3DBR;
    }

    public Double getAntEGain() {
        return antEGain;
    }

    public void setAntEGain(Double antEGain) {
        this.antEGain = antEGain;
    }

    public Double getAntRGain() {
        return antRGain;
    }

    public void setAntRGain(Double antRGain) {
        this.antRGain = antRGain;
    }

    public String getAntMenu() {
        return antMenu;
    }

    public void setAntMenu(String antMenu) {
        this.antMenu = antMenu;
    }

    public Double getAntHeight() {
        return antHeight;
    }

    public void setAntHeight(Double antHeight) {
        this.antHeight = antHeight;
    }

    public Double getFeedLose() {
        return feedLose;
    }

    public void setFeedLose(Double feedLose) {
        this.feedLose = feedLose;
    }
}
