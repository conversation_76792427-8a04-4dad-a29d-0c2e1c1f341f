package com.bsm.v4.system.model.entity.es;

import io.swagger.annotations.ApiModelProperty;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

@Document(indexName = "es_rsbt_station")

public class EsRsbtStation {

    @ApiModelProperty(value = "台站GUID")
    @Id
    @Field(name = "GUID", type = FieldType.Text)
    private String guid;

    @ApiModelProperty(value = "RSBT_NET外键")
    @Field(name = "NET_GUID", type = FieldType.Text)
    private String netGuid;

    @ApiModelProperty(value = "组织机构id")
    @Field(name = "ORG_CODE", type = FieldType.Text)
    private String orgCode;

    @ApiModelProperty(value = "无线电台（站）申请表编号")
    @Field(name = "APP_CODE", type = FieldType.Text)
    private String appCode;

    @ApiModelProperty(value = "技术资料申报表类型")
    @Field(name = "STAT_APP_TYPE", type = FieldType.Text)
    private String statAppType;

    @ApiModelProperty(value = "技术资料申报表编号")
    @Field(name = "STAT_TDI", type = FieldType.Text)
    private String statTdi;

    @ApiModelProperty(value = "无线电台站名称")
    @Field(name = "STAT_NAME", type = FieldType.Text)
    private String statName;

    @ApiModelProperty(value = "无线电台站地址")
    @Field(name = "STAT_ADDR", type = FieldType.Text)
    private String statAddr;

    @ApiModelProperty(value = "无线电台站所在地地区编码")
    @Field(name = "STAT_AREA_CODE", type = FieldType.Text)
    private String statAreaCode;

    @ApiModelProperty(value = "台站类别")
    @Field(name = "STAT_TYPE", type = FieldType.Text)
    private String statType;

    @ApiModelProperty(value = "工作方式")
    @Field(name = "STAT_WORK", type = FieldType.Text)
    private String statWork;

    @ApiModelProperty(value = "台站状态")
    @Field(name = "STAT_STATUS", type = FieldType.Text)
    private String statStatus;

    @ApiModelProperty(value = "(台站总)设备数量")
    @Field(name = "STAT_EQU_SUM", type = FieldType.Text)
    private Integer statEquSum;

    @ApiModelProperty(value = "台站经度(西经为负数)")
    @Field(name = "STAT_LG", type = FieldType.Text)
    private Double statLg;

    @ApiModelProperty(value = "台站纬度(南纬为负数)")
    @Field(name = "STAT_LA", type = FieldType.Text)
    private Double statLa;

    @ApiModelProperty(value = "海拔高度")
    @Field(name = "STAT_AT", type = FieldType.Text)
    private Double statAt;

    @ApiModelProperty(value = "启用日期")
    @Field(name = "STAT_DATE_START", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime statDateStart;

    @ApiModelProperty(value = "协调主管部门 国际协调和登记资料代码或名称")
    @Field(name = "MEMO", type = FieldType.Text)
    private String memo;

    @ApiModelProperty(value = "基站id")
    @Field(name = "STATION_ID", type = FieldType.Text)
    private String stationId;

    @ApiModelProperty(value = "基站名称")
    @Field(name = "STATION_NAME", type = FieldType.Text)
    private String stationName;

    @ApiModelProperty(value = "基站编号")
    @Field(name = "STATION_CODE", type = FieldType.Text)
    private String stationCode;

    @ApiModelProperty(value = "台站状态")
    @Field(name = "STATION_STATE", type = FieldType.Text)
    private String stationState;

    @ApiModelProperty(value = "台址")
    @Field(name = "LOCATION", type = FieldType.Text)
    private String location;

    @ApiModelProperty(value = "经度")
    @Field(name = "LONGITUDE", type = FieldType.Text)
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @Field(name = "LATITUDE", type = FieldType.Text)
    private String latitude;

    @ApiModelProperty(value = "审核任务id")
    @Field(name = "APP_GUID", type = FieldType.Text)
    private String appGuid;

    @ApiModelProperty(value = "运营商名")
    @Field(name = "ORG_NAME", type = FieldType.Text)
    private String orgName;

    @ApiModelProperty(value = "地区")
    @Field(name = "COUNTY", type = FieldType.Text)
    private String county;

    @ApiModelProperty(value = "申请表ID")
    @Field(name = "APPLY_TABLE_GUID", type = FieldType.Text)
    private String applyTableGuid;

    @ApiModelProperty(value = "接收频率上限")
    @Field(name = "FREQ_RFB", type = FieldType.Text)
    private String freqRfb;

    @ApiModelProperty(value = "接收频率下限")
    @Field(name = "FREQ_RFE", type = FieldType.Text)
    private String freqRfe;

    @ApiModelProperty(value = "发射频率上限")
    @Field(name = "FREQ_EFE", type = FieldType.Text)
    private String freqEfe;

    @ApiModelProperty(value = "发射频率下限")
    @Field(name = "FREQ_EFB", type = FieldType.Text)
    private String freqEfb;

    @ApiModelProperty(value = "基站是否在所属区域内；1：是；2：否")
    @Field(name = "IS_AREA", type = FieldType.Text)
    private String isArea;

    @ApiModelProperty(value = "1.宏站2.直放站")
    @Field(name = "EXPAND_STATION", type = FieldType.Text)
    private String expandStation;

    @ApiModelProperty(value = "1.室内站2.室外站")
    @Field(name = "ATTRIBUTE_STATION", type = FieldType.Text)
    private String attributeStation;

    @ApiModelProperty(value = "是否生成执照")
    @Field(name = "IS_LICENSE", type = FieldType.Text)
    private String isLicense;

    @ApiModelProperty(value = "组织机构Id")
    @Field(name = "ORG_ID", type = FieldType.Text)
    private String orgId;

    @ApiModelProperty(value = "技术制式")
    @Field(name = "NET_TYPE", type = FieldType.Text)
    private String netType;

    @ApiModelProperty(value = "用户id")
    @Field(name = "USER_GUID", type = FieldType.Text)
    private String userGuid;

    @ApiModelProperty(value = "运营商类型")
    @Field(name = "ORG_TYPE", type = FieldType.Text)
    private String orgType;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getNetGuid() {
        return netGuid;
    }

    public void setNetGuid(String netGuid) {
        this.netGuid = netGuid;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getStatName() {
        return statName;
    }

    public void setStatName(String statName) {
        this.statName = statName;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public String getStatAreaCode() {
        return statAreaCode;
    }

    public void setStatAreaCode(String statAreaCode) {
        this.statAreaCode = statAreaCode;
    }

    public String getStatType() {
        return statType;
    }

    public void setStatType(String statType) {
        this.statType = statType;
    }

    public String getStatWork() {
        return statWork;
    }

    public void setStatWork(String statWork) {
        this.statWork = statWork;
    }

    public String getStatStatus() {
        return statStatus;
    }

    public void setStatStatus(String statStatus) {
        this.statStatus = statStatus;
    }

    public Integer getStatEquSum() {
        return statEquSum;
    }

    public void setStatEquSum(Integer statEquSum) {
        this.statEquSum = statEquSum;
    }

    public Double getStatLg() {
        return statLg;
    }

    public void setStatLg(Double statLg) {
        this.statLg = statLg;
    }

    public Double getStatLa() {
        return statLa;
    }

    public void setStatLa(Double statLa) {
        this.statLa = statLa;
    }

    public Double getStatAt() {
        return statAt;
    }

    public void setStatAt(Double statAt) {
        this.statAt = statAt;
    }

    public LocalDateTime getStatDateStart() {
        return statDateStart;
    }

    public void setStatDateStart(LocalDateTime statDateStart) {
        this.statDateStart = statDateStart;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStationState() {
        return stationState;
    }

    public void setStationState(String stationState) {
        this.stationState = stationState;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getApplyTableGuid() {
        return applyTableGuid;
    }

    public void setApplyTableGuid(String applyTableGuid) {
        this.applyTableGuid = applyTableGuid;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getIsArea() {
        return isArea;
    }

    public void setIsArea(String isArea) {
        this.isArea = isArea;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public String getIsLicense() {
        return isLicense;
    }

    public void setIsLicense(String isLicense) {
        this.isLicense = isLicense;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }
}
