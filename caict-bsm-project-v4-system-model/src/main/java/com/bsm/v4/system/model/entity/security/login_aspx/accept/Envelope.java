package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Envelope",namespace = NameSpaceUrlConst.mainNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "responseHeader",
        "responseBody"
})
public class Envelope {
    @XmlElement(name="Header", namespace=NameSpaceUrlConst.mainNamespaceURI)
    private ResponseHeader responseHeader;
    @XmlElement(name="Body", namespace=NameSpaceUrlConst.mainNamespaceURI)
    private ResponseBody responseBody;

    public Envelope() {
    }

    public Envelope(ResponseBody responseBody) {
        this.responseBody = responseBody;
    }

    public ResponseBody getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(ResponseBody responseBody) {
        this.responseBody = responseBody;
    }

    public ResponseHeader getResponseHeader() {
        return responseHeader;
    }

    public void setResponseHeader(ResponseHeader responseHeader) {
        this.responseHeader = responseHeader;
    }
}
