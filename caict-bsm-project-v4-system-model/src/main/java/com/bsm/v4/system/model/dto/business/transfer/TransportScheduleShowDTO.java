package com.bsm.v4.system.model.dto.business.transfer;



/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * jobGuid: 任务
 * type: 类型
 * compare: 流程状态
 * scheduleNew: 新增数量
 * scheduleUpdate: 变更数量
 * scheduleDelete: 注销数量
 */

public class TransportScheduleShowDTO {

    private String jobGuid;
    private String type;
    private String compare;
    private int scheduleNew;
    private int scheduleUpdate;
    private int scheduleDelete;

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCompare() {
        return compare;
    }

    public void setCompare(String compare) {
        this.compare = compare;
    }

    public int getScheduleNew() {
        return scheduleNew;
    }

    public void setScheduleNew(int scheduleNew) {
        this.scheduleNew = scheduleNew;
    }

    public int getScheduleUpdate() {
        return scheduleUpdate;
    }

    public void setScheduleUpdate(int scheduleUpdate) {
        this.scheduleUpdate = scheduleUpdate;
    }

    public int getScheduleDelete() {
        return scheduleDelete;
    }

    public void setScheduleDelete(int scheduleDelete) {
        this.scheduleDelete = scheduleDelete;
    }
}
