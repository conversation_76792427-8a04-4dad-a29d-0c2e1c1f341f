package com.bsm.v4.system.model.dto.business.transfer;



import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/18
 */

public class TransportRawFileDTO {
    private String appGuid;
    private String jobStatus;

    private List<TransportRawBtsDealDTO> transportRawBtsDealDTOS;

    private List<TransportFileDTO> transportFileDTOS;

    private List<CoordinateScheduleTempDTO> coordinateScheduleTempDTOS;

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public List<TransportRawBtsDealDTO> getTransportRawBtsDealDTOS() {
        return transportRawBtsDealDTOS;
    }

    public void setTransportRawBtsDealDTOS(List<TransportRawBtsDealDTO> transportRawBtsDealDTOS) {
        this.transportRawBtsDealDTOS = transportRawBtsDealDTOS;
    }

    public List<TransportFileDTO> getTransportFileDTOS() {
        return transportFileDTOS;
    }

    public void setTransportFileDTOS(List<TransportFileDTO> transportFileDTOS) {
        this.transportFileDTOS = transportFileDTOS;
    }

    public List<CoordinateScheduleTempDTO> getCoordinateScheduleTempDTOS() {
        return coordinateScheduleTempDTOS;
    }

    public void setCoordinateScheduleTempDTOS(List<CoordinateScheduleTempDTO> coordinateScheduleTempDTOS) {
        this.coordinateScheduleTempDTOS = coordinateScheduleTempDTOS;
    }
}
