package com.bsm.v4.system.model.dto.business.transfer;


import java.util.List;


public class TransportInitTaskDetailDTO {
    private String userType; //用户类型
    List<LogTransportJobDTO> taskDetailList; //相关数据

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public List<LogTransportJobDTO> getTaskDetailList() {
        return taskDetailList;
    }

    public void setTaskDetailList(List<LogTransportJobDTO> taskDetailList) {
        this.taskDetailList = taskDetailList;
    }
}
