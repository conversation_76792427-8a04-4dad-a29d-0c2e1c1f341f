package com.bsm.v4.system.model.entity.business.license;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * <p>
 * <AUTHOR>
 * @since 2018-08-03
 */

@TableName("RSBT_LICENSE")
public class RsbtLicense {

    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "台站GUID")
    @TableFieId("STATION_GUID")
    private String stationGuid;

    @ApiModelProperty(value = "申请表编号")
    @TableFieId("APP_CODE")
    private String appCode;

    @ApiModelProperty(value = "技术资料申报表编号")
    @TableFieId("STAT_TDI")
    private String statTdi;

    @ApiModelProperty(value = "技术资料申报表类型")
    @TableFieId("STAT_APP_TYPE")
    private String statAppType;

    @ApiModelProperty(value = "执照类型")
    @TableFieId("LICENSE_TYPE")
    private String licenseType;

    @ApiModelProperty(value = "执照号")
    @TableFieId("LICENSE_CODE")
    private String licenseCode;

    @ApiModelProperty(value = "执照单位名称")
    @TableFieId("LICENSE_ORG_NAME")
    private String licenseOrgName;

    @ApiModelProperty(value = "核发单位名称")
    @TableFieId("LICENSE_MANAGER")
    private String licenseManager;

    @ApiModelProperty(value = "执照有效期起")
    @TableFieId("LICENSE_DATE_B")
    private Date licenseDateB;

    @ApiModelProperty(value = "执照有效期止")
    @TableFieId("LICENSE_DATE_E")
    private Date licenseDateE;

    @ApiModelProperty(value = "发证时间")
    @TableFieId("LICENSE_DATE")
    private Date licenseDate;

    @ApiModelProperty(value = "备注")
    @TableFieId("MEMO")
    private String memo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getLicenseOrgName() {
        return licenseOrgName;
    }

    public void setLicenseOrgName(String licenseOrgName) {
        this.licenseOrgName = licenseOrgName;
    }

    public String getLicenseManager() {
        return licenseManager;
    }

    public void setLicenseManager(String licenseManager) {
        this.licenseManager = licenseManager;
    }

    public Date getLicenseDateB() {
        return licenseDateB;
    }

    public void setLicenseDateB(Date licenseDateB) {
        this.licenseDateB = licenseDateB;
    }

    public Date getLicenseDateE() {
        return licenseDateE;
    }

    public void setLicenseDateE(Date licenseDateE) {
        this.licenseDateE = licenseDateE;
    }

    public Date getLicenseDate() {
        return licenseDate;
    }

    public void setLicenseDate(Date licenseDate) {
        this.licenseDate = licenseDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
