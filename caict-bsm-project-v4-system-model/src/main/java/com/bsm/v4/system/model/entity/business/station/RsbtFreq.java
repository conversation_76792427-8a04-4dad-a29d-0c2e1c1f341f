package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_FREQ")
public class RsbtFreq {

    @TableId("GUID")
    private String guid;

    @TableFieId("STATION_GUID")
    private String stationGuid;

    /**
     * 0：频点，1：频段
     */
    @TableFieId("FREQ_TYPE")
    private String freqType;

    /**
     * 中心频率（低频）
     */
    @TableFieId("FREQ_LC")
    private double freqLc;

    /**
     * 中心频率（高频）
     */
    @TableFieId("FREQ_UC")
    private double freqUc;

    /**
     * 发射频率下限
     */
    @TableFieId("FREQ_EFB")
    private double freqEfb;
    /**
     * 发射频率上限
     */
    @TableFieId("FREQ_EFE")
    private double freqEfe;

    /**
     * 发射（必要）带宽
     */
    @TableFieId("FREQ_E_BAND")
    private double freqEBand;

    /**
     * 接收频率上限
     */
    @TableFieId("FREQ_RFB")
    private double freqRfb;
    /**
     * 接收频率下限
     */
    @TableFieId("FREQ_RFE")
    private double freqRfe;

    /**
     * 接收（必要）带宽
     */
    @TableFieId("FREQ_R_BAND")
    private double freqRBand;

    /**
     * 调制方式
     */
    @TableFieId("FREQ_MOD")
    private String freqMod;

    /**
     * 主/备用频率标识
     */
    @TableFieId("FREQ_MB")
    private String freqMb;

    /**
     * 国家频率数据库对应码
     */
    @TableFieId("FREQ_CODE")
    private String freqCode;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getFreqType() {
        return freqType;
    }

    public void setFreqType(String freqType) {
        this.freqType = freqType;
    }

    public double getFreqLc() {
        return freqLc;
    }

    public void setFreqLc(double freqLc) {
        this.freqLc = freqLc;
    }

    public double getFreqUc() {
        return freqUc;
    }

    public void setFreqUc(double freqUc) {
        this.freqUc = freqUc;
    }

    public double getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(double freqEfb) {
        this.freqEfb = freqEfb;
    }

    public double getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(double freqEfe) {
        this.freqEfe = freqEfe;
    }

    public double getFreqEBand() {
        return freqEBand;
    }

    public void setFreqEBand(double freqEBand) {
        this.freqEBand = freqEBand;
    }

    public double getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(double freqRfb) {
        this.freqRfb = freqRfb;
    }

    public double getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(double freqRfe) {
        this.freqRfe = freqRfe;
    }

    public double getFreqRBand() {
        return freqRBand;
    }

    public void setFreqRBand(double freqRBand) {
        this.freqRBand = freqRBand;
    }

    public String getFreqMod() {
        return freqMod;
    }

    public void setFreqMod(String freqMod) {
        this.freqMod = freqMod;
    }

    public String getFreqMb() {
        return freqMb;
    }

    public void setFreqMb(String freqMb) {
        this.freqMb = freqMb;
    }

    public String getFreqCode() {
        return freqCode;
    }

    public void setFreqCode(String freqCode) {
        this.freqCode = freqCode;
    }
}