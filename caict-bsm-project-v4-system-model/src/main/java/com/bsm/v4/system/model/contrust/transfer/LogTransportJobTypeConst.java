package com.bsm.v4.system.model.contrust.transfer;

/**
 * 中转任务日志类别
 *
 * <AUTHOR>
 */
public class LogTransportJobTypeConst {

    /**
     * 正确
     */
    public static long RIGHT = 0;
    /**
     * 错误
     */
    public static long ERROR = 1;

    public static boolean isValidType(int type) {
        if (RIGHT == type) {
            return true;
        }
        if (ERROR == type) {
            return true;
        }
        return false;
    }

    public static boolean isValidType(Long type) {
        if (null == type) {
            return false;
        }
        if (RIGHT == type.intValue()) {
            return true;
        }
        if (ERROR == type.intValue()) {
            return true;
        }
        return false;
    }

}
