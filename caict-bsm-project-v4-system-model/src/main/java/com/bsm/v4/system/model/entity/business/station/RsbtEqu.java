package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_EQU")
public class RsbtEqu {

    @TableId("GUID")
    private String guid;

    /**
     * 基站GUID
     */
    @TableFieId("STATION_GUID")
    private String stationGuid;

    /**
     * 设备工作方式
     */
    @TableFieId("EQU_TYPE")
    private String equType;

    /**
     * 设备型号
     */
    @TableFieId("EQU_MODEL")
    private String equModel;

    /**
     * 型号核准代码
     */
    @TableFieId("EQU_AUTH")
    private String equAuth;

    /**
     * 设备生产厂家
     */
    @TableFieId("EQU_MENU")
    private String equMenu;

    /**
     * 设备出厂号
     */
    @TableFieId("EQU_CODE")
    private String equCode;

    /**
     * 功率标识
     */
    @TableFieId("EQU_PF")
    private String equPf;

    /**
     * 发射功率
     */
    @TableFieId("EQU_POW")
    private double equPow;

    /**
     * 主/备用标识
     */
    @TableFieId("EQU_MB")
    private String equMb;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getEquType() {
        return equType;
    }

    public void setEquType(String equType) {
        this.equType = equType;
    }

    public String getEquModel() {
        return equModel;
    }

    public void setEquModel(String equModel) {
        this.equModel = equModel;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public String getEquMenu() {
        return equMenu;
    }

    public void setEquMenu(String equMenu) {
        this.equMenu = equMenu;
    }

    public String getEquCode() {
        return equCode;
    }

    public void setEquCode(String equCode) {
        this.equCode = equCode;
    }

    public String getEquPf() {
        return equPf;
    }

    public void setEquPf(String equPf) {
        this.equPf = equPf;
    }

    public double getEquPow() {
        return equPow;
    }

    public void setEquPow(double equPow) {
        this.equPow = equPow;
    }

    public String getEquMb() {
        return equMb;
    }

    public void setEquMb(String equMb) {
        this.equMb = equMb;
    }
}
