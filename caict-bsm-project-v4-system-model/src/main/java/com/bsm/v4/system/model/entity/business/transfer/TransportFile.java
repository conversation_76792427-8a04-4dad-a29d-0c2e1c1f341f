package com.bsm.v4.system.model.entity.business.transfer;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("TRANSPORT_FILE")
public class TransportFile {
    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;
    /**
     * JOB GUID
     */
    @TableFieId("JOB_GUID")
    private String jobGuid;
    /**
     * 文件相对地址
     */
    @TableFieId("FILE_PATH")
    private String filePath;
    /**
     * 大小kb
     */
    @TableFieId("FILE_SIZE")
    private Double fileSize;
    /**
     * 0创建1文件接收成功2文件处理结束3文件处理异常
     */
    @TableFieId("FILE_STATE")
    private Long fileState;
    /**
     * 上传文件本地名
     */
    @TableFieId("FILE_LOCAL_NAME")
    private String fileLocalName;
    @TableFieId("FILE_CHUNKS")
    private Long fileChunks;
    /**
     * 文件类型(0 附件;1 运营商上传申请文件;2 无委上传审核文件;3 无委上传协调文件)
     */
    @TableFieId("FILE_TYPE")
    private String fileType;

    @TableFieId("DATA_TYPE")
    private String dataType;

    @TableFieId("GEN_NUM")
    private String genNum;

    /**
     * 数据数量
     */
    @TableFieId("DATA_NUM")
    private String dataNum;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Double getFileSize() {
        return fileSize;
    }

    public void setFileSize(Double fileSize) {
        this.fileSize = fileSize;
    }

    public Long getFileState() {
        return fileState;
    }

    public void setFileState(Long fileState) {
        this.fileState = fileState;
    }

    public String getFileLocalName() {
        return fileLocalName;
    }

    public void setFileLocalName(String fileLocalName) {
        this.fileLocalName = fileLocalName;
    }

    public Long getFileChunks() {
        return fileChunks;
    }

    public void setFileChunks(Long fileChunks) {
        this.fileChunks = fileChunks;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getDataNum() {
        return dataNum;
    }

    public void setDataNum(String dataNum) {
        this.dataNum = dataNum;
    }
}
