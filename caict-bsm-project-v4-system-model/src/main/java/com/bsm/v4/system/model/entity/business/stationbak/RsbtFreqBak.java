package com.bsm.v4.system.model.entity.business.stationbak;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_FREQ_BAK")
public class RsbtFreqBak {

    @TableId("FREQ_GUID")
    private String freqGuid;
    @TableFieId("STATION_GUID")
    private String stationGuid;
    @TableFieId("SECTION_CODE")
    private String sectionCode;
    /**
     * 发射频率下限
     */
    @TableFieId("FREQ_EFB")
    private String freqEfb;
    /**
     * 发射频率上限
     */
    @TableFieId("FREQ_EFE")
    private String freqEfe;
    /**
     * 接收频率上限
     */
    @TableFieId("FREQ_RFB")
    private String freqRfb;
    /**
     * 接收频率下限
     */
    @TableFieId("FREQ_RFE")
    private String freqRfe;
    /**
     * 0：频点，1：频段
     */
    @TableFieId("FREQ_TYPE")
    private String freqType;
    /**
     * 发射（必要）带宽
     */
    @TableFieId("FREQ_E_BAND")
    private String freqEBand;
    /**
     * 接收（必要）带宽
     */
    @TableFieId("FREQ_R_BAND")
    private String freqRBand;
    @TableFieId("FT_FREQ_CSGN")
    private String ftFreqCsgn;

    /**
     * 是否同步
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    /**
     * 数据类型（1-新增；2-变更；3-删除）
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    public RsbtFreqBak() {
        this.isSync = "0";
    }

    public String getFreqGuid() {
        return freqGuid;
    }

    public void setFreqGuid(String freqGuid) {
        this.freqGuid = freqGuid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getSectionCode() {
        return sectionCode;
    }

    public void setSectionCode(String sectionCode) {
        this.sectionCode = sectionCode;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getFreqType() {
        return freqType;
    }

    public void setFreqType(String freqType) {
        this.freqType = freqType;
    }

    public String getFreqEBand() {
        return freqEBand;
    }

    public void setFreqEBand(String freqEBand) {
        this.freqEBand = freqEBand;
    }

    public String getFreqRBand() {
        return freqRBand;
    }

    public void setFreqRBand(String freqRBand) {
        this.freqRBand = freqRBand;
    }

    public String getFtFreqCsgn() {
        return ftFreqCsgn;
    }

    public void setFtFreqCsgn(String ftFreqCsgn) {
        this.ftFreqCsgn = ftFreqCsgn;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}
