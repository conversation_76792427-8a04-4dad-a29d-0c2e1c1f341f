package com.bsm.v4.system.model.dto.business.license;


import io.swagger.annotations.ApiModelProperty;


/**
 * Created by qhy on 2018/8/3.
 */
public class LicenseOpDTO {
    @ApiModelProperty(value = "台站guid", required = true)
    private String stationGuId;
    @ApiModelProperty(value = "状态码（1换发，2停用，3恢复，4注销）", required = true)
    private Long stationOpType;
    @ApiModelProperty(value = "有效时间起（可为空）")
    private Long startDate;
    @ApiModelProperty(value = "有效时间止（可为空）")
    private Long endDate;

    public String getStationGuId() {
        return stationGuId;
    }

    public void setStationGuId(String stationGuId) {
        this.stationGuId = stationGuId;
    }

    public Long getStationOpType() {
        return stationOpType;
    }

    public void setStationOpType(Long stationOpType) {
        this.stationOpType = stationOpType;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }
}
