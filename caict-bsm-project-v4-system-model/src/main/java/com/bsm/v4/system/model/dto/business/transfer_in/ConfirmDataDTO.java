package com.bsm.v4.system.model.dto.business.transfer_in;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
public class ConfirmDataDTO {

    private String jobId;

    private String jobBranchId;

    private String sumStation;

    private String sumCell;

    private String dataType;

    private String genNum;

    private String area;

    private String techType;

    private String isCompare;

    private String fileState;

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getJobBranchId() {
        return jobBranchId;
    }

    public void setJobBranchId(String jobBranchId) {
        this.jobBranchId = jobBranchId;
    }

    public String getSumStation() {
        return sumStation;
    }

    public void setSumStation(String sumStation) {
        this.sumStation = sumStation;
    }

    public String getSumCell() {
        return sumCell;
    }

    public void setSumCell(String sumCell) {
        this.sumCell = sumCell;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }
}
