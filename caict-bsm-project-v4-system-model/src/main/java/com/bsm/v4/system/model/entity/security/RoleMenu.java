package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dengsy on 2019-10-24.
 * 角色关联菜单对象
 */

@TableName("sys_role_menu")
public class RoleMenu {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "角色id")
    @TableFieId("role_id")
    private String roleId;

    @ApiModelProperty(value = "菜单id")
    @TableFieId("menu_id")
    private String menuId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }
}
