package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "GetUsersResult",namespace = NameSpaceUrlConst.aNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "userInfo"
})
public class GetUsersResult {
    @XmlElement(name = "UserInfo",namespace = NameSpaceUrlConst.aNamespaceURI)
    private List<UserInfo> userInfo;

    public GetUsersResult() {
    }

    public List<UserInfo> getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(List<UserInfo> userInfo) {
        this.userInfo = userInfo;
    }
}
