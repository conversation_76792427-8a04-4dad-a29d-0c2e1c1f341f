package com.bsm.v4.system.model.dto.business.station;


public class SectionDTO {
    private String sectionCode;
    private String sectionName;
    private String antHight;//天线距地高度
    private String freqEfb;//发射起始频率
    private String freqEfe;//发射终止频率
    private String freqRfb;//接受起始频率
    private String freqRfe;//接受终止频率
    private String maxPower;//最大发射功率
    private String equMenu;//设备生产厂家
    private String equAuth;//型号核准代码

    private String antPole;//极化方式
    private String antType;//天线类型
    private String antMenu;//天线生产厂家
    private String feedLose;//馈线系统总损耗
    private String antAngle;//最大辐射方位角
    private String antGain;//天线增益
    private String analysisStatus;//5G干扰协调结果
    private String isValid;//频段是否合规

    public String getSectionCode() {
        return sectionCode;
    }

    public void setSectionCode(String sectionCode) {
        this.sectionCode = sectionCode;
    }

    public String getSectionName() {
        return sectionName;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public String getAntHight() {
        return antHight;
    }

    public void setAntHight(String antHight) {
        this.antHight = antHight;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getMaxPower() {
        return maxPower;
    }

    public void setMaxPower(String maxPower) {
        this.maxPower = maxPower;
    }

    public String getEquMenu() {
        return equMenu;
    }

    public void setEquMenu(String equMenu) {
        this.equMenu = equMenu;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public String getAntType() {
        return antType;
    }

    public void setAntType(String antType) {
        this.antType = antType;
    }

    public String getAntMenu() {
        return antMenu;
    }

    public void setAntMenu(String antMenu) {
        this.antMenu = antMenu;
    }

    public String getFeedLose() {
        return feedLose;
    }

    public void setFeedLose(String feedLose) {
        this.feedLose = feedLose;
    }

    public String getAntAngle() {
        return antAngle;
    }

    public void setAntAngle(String antAngle) {
        this.antAngle = antAngle;
    }

    public String getAntGain() {
        return antGain;
    }

    public void setAntGain(String antGain) {
        this.antGain = antGain;
    }

    public String getAnalysisStatus() {
        return analysisStatus;
    }

    public void setAnalysisStatus(String analysisStatus) {
        this.analysisStatus = analysisStatus;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }
}
