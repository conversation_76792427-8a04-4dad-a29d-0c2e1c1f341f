package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @since 2018-08-03
 */

@TableName("RSBT_ORG")
public class RsbtOrg {


    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "统一社会信用码")
    @TableFieId("ORG_CODE")
    private String orgCode;

    @ApiModelProperty(value = "组织机构名称,频率申请单位、设台单位、无线电管理机构、缴费单位名称")
    @TableFieId("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "地区代码")
    @TableFieId("ORG_AREA_CODE")
    private String orgAreaCode;

    @ApiModelProperty(value = "系统/行业代码")
    @TableFieId("ORG_SYS_CODE")
    private String orgSysCode;

    @ApiModelProperty(value = "单位类型")
    @TableFieId("ORG_TYPE")
    private String orgType;

    @ApiModelProperty(value = "单位联系人")
    @TableFieId("ORG_LINK_PERSON")
    private String orgLinkPerson;

    @ApiModelProperty(value = "联系人身份证号码")
    @TableFieId("ORG_PERSON_ID")
    private String orgPersonId;

    @ApiModelProperty(value = "上级组织机构代码")
    @TableFieId("ORG_SUP_CODE")
    private String orgSupCode;

    @ApiModelProperty(value = "组织机构地址")
    @TableFieId("ORG_ADDR")
    private String orgAddr;

    @ApiModelProperty(value = "组织机构邮编")
    @TableFieId("ORG_POST")
    private String orgPost;

    @ApiModelProperty(value = "联系电话")
    @TableFieId("ORG_PHONE")
    private String orgPhone;

    @ApiModelProperty(value = "手机号码")
    @TableFieId("ORG_MOB_PHONE")
    private String orgMobPhone;

    @ApiModelProperty(value = "组织机构传真")
    @TableFieId("ORG_FAX")
    private String orgFax;

    @ApiModelProperty(value = "开户银行")
    @TableFieId("ORG_BANK")
    private String orgBank;

    @ApiModelProperty(value = "账户名称")
    @TableFieId("ORG_ACCOUNT_NAME")
    private String orgAccountName;

    @ApiModelProperty(value = "银行帐号")
    @TableFieId("ORG_ACCOUNT")
    private String orgAccount;

    @ApiModelProperty(value = "设台单位性质")
    @TableFieId("ORG_HOSTILITY")
    private Integer orgHostility;

    @ApiModelProperty(value = "网址")
    @TableFieId("ORG_WEB_SITE")
    private String orgWebSite;

    @ApiModelProperty(value = "电子邮箱")
    @TableFieId("ORG_MAIL")
    private String orgMail;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAreaCode() {
        return orgAreaCode;
    }

    public void setOrgAreaCode(String orgAreaCode) {
        this.orgAreaCode = orgAreaCode;
    }

    public String getOrgSysCode() {
        return orgSysCode;
    }

    public void setOrgSysCode(String orgSysCode) {
        this.orgSysCode = orgSysCode;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgLinkPerson() {
        return orgLinkPerson;
    }

    public void setOrgLinkPerson(String orgLinkPerson) {
        this.orgLinkPerson = orgLinkPerson;
    }

    public String getOrgPersonId() {
        return orgPersonId;
    }

    public void setOrgPersonId(String orgPersonId) {
        this.orgPersonId = orgPersonId;
    }

    public String getOrgSupCode() {
        return orgSupCode;
    }

    public void setOrgSupCode(String orgSupCode) {
        this.orgSupCode = orgSupCode;
    }

    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    public String getOrgPost() {
        return orgPost;
    }

    public void setOrgPost(String orgPost) {
        this.orgPost = orgPost;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgMobPhone() {
        return orgMobPhone;
    }

    public void setOrgMobPhone(String orgMobPhone) {
        this.orgMobPhone = orgMobPhone;
    }

    public String getOrgFax() {
        return orgFax;
    }

    public void setOrgFax(String orgFax) {
        this.orgFax = orgFax;
    }

    public String getOrgBank() {
        return orgBank;
    }

    public void setOrgBank(String orgBank) {
        this.orgBank = orgBank;
    }

    public String getOrgAccountName() {
        return orgAccountName;
    }

    public void setOrgAccountName(String orgAccountName) {
        this.orgAccountName = orgAccountName;
    }

    public String getOrgAccount() {
        return orgAccount;
    }

    public void setOrgAccount(String orgAccount) {
        this.orgAccount = orgAccount;
    }

    public Integer getOrgHostility() {
        return orgHostility;
    }

    public void setOrgHostility(Integer orgHostility) {
        this.orgHostility = orgHostility;
    }

    public String getOrgWebSite() {
        return orgWebSite;
    }

    public void setOrgWebSite(String orgWebSite) {
        this.orgWebSite = orgWebSite;
    }

    public String getOrgMail() {
        return orgMail;
    }

    public void setOrgMail(String orgMail) {
        this.orgMail = orgMail;
    }
}
