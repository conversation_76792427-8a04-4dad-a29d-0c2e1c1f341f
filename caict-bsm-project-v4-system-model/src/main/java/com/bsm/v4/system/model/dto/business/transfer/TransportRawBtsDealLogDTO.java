package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDealLog;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dengsy on 2020-05-06.
 * <p>
 * 对比记录DTO
 */

public class TransportRawBtsDealLogDTO extends TransportRawBtsDealLog {

    @ApiModelProperty(value = "状态数量")
    private int isValidCount;

    @ApiModelProperty(value = "日志具体信息")
    private String logDetail;

    @ApiModelProperty(value = "日志缩写")
    private String logShort;

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    public int getIsValidCount() {
        return isValidCount;
    }

    public void setIsValidCount(int isValidCount) {
        this.isValidCount = isValidCount;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }

    public String getLogShort() {
        return logShort;
    }

    public void setLogShort(String logShort) {
        this.logShort = logShort;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
