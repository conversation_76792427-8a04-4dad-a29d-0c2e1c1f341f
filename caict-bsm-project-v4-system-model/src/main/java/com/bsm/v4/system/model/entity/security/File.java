package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2021-8-13.
 * 文件存储对象
 */

@TableName("sys_file")
public class File {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "编号")
    @TableFieId("code")
    private String code;

    @ApiModelProperty(value = "名称")
    @TableFieId("name")
    private String name;

    @ApiModelProperty(value = "类型")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "状态")
    @TableFieId("status")
    private Integer status;

    @ApiModelProperty(value = "文件地址")
    @TableFieId("file_path")
    private String filePath;

    @ApiModelProperty(value = "文件大小")
    @TableFieId("file_size")
    private Long fileSize;

    @ApiModelProperty(value = "保存时间")
    @TableFieId("update_date_time")
    private Date updateDateTime;

    @ApiModelProperty(value = "发送时间")
    @TableFieId("send_time")
    private Date sendTime;

    @ApiModelProperty(value = "备注")
    @TableFieId("remark")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
