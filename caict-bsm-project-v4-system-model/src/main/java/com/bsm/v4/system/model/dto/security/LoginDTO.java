package com.bsm.v4.system.model.dto.security;

import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * @Title: LoginDTO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.dto.security
 * @Date 2023/8/18 10:50
 * @description:
 */

public class LoginDTO {
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "登录名")
    private String loginName;

    @ApiModelProperty(value = "登录密码")
    private String password;

    @ApiModelProperty(value = "登录状态")
    private Integer status;

    @ApiModelProperty(value = "登录类型")
    private String type;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "最近登录时间")
    private Date lastDate;

    @ApiModelProperty(value = "最近登录方式")
    private String lastWay;

    @ApiModelProperty(value = "最近登录ip")
    private String lastIp;

    @ApiModelProperty(value = "平台信息登录名")
    private String srrcUserCode;

    @ApiModelProperty(value = "平台信息用户名")
    private String srrcUserName;

    @ApiModelProperty(value = "平台信息机构code")
    private String srrcOrgCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }

    public String getLastWay() {
        return lastWay;
    }

    public void setLastWay(String lastWay) {
        this.lastWay = lastWay;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getSrrcUserCode() {
        return srrcUserCode;
    }

    public void setSrrcUserCode(String srrcUserCode) {
        this.srrcUserCode = srrcUserCode;
    }

    public String getSrrcUserName() {
        return srrcUserName;
    }

    public void setSrrcUserName(String srrcUserName) {
        this.srrcUserName = srrcUserName;
    }

    public String getSrrcOrgCode() {
        return srrcOrgCode;
    }

    public void setSrrcOrgCode(String srrcOrgCode) {
        this.srrcOrgCode = srrcOrgCode;
    }
}
