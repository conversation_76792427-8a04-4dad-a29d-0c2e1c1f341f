package com.bsm.v4.system.model.vo.document;

import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dengsy on 2023-02-10.
 * <p>
 * FastFDS文件处理对象
 */

@ApiModel(value = "fastFdsVO", description = "FastFDS文件处理对象")
public class FastFdsVO extends TransportFile {

    @ApiModelProperty(value = "文件全路径")
    private String filePath;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件分组")
    private String group;

    @Override
    public String getFilePath() {
        return filePath;
    }

    @Override
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }
}
