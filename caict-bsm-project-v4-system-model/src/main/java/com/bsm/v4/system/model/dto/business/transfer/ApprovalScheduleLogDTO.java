package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


/**
 * Created by dengsy on 2020-05-08.
 */

public class ApprovalScheduleLogDTO extends ApprovalScheduleLog {

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
