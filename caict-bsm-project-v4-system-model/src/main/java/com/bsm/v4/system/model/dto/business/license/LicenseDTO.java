package com.bsm.v4.system.model.dto.business.license;


import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


import java.util.Date;

/**
 * Created by qhy on 2018/8/3.
 * <p>
 * userType:用户类型
 */
public class LicenseDTO extends RsbtLicense {
    private Date stationStartDate;
    // 换发、恢复 开始结束时间
    private long startDate;
    private long endDate;

    private String stationGuid;
    private String stationName;
    private String stationCode;
    private String netType;
    private String location;
    private Long licenseState;
    private Double longitude;
    private Double latitude;
//    private List<SectionDTO> sectionList;

    private String orgId;
    private OrgDTO org;
    private String orgName; //设台单位名称

    private String fequencyLicenseNo;//对应频率许可证编号

    private String applyTableCode; //申请表编号

    private String userType;

    private String licenseCounty;

    private String licenseCountyStr;

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    public Date getStationStartDate() {
        return stationStartDate;
    }

    public void setStationStartDate(Date stationStartDate) {
        this.stationStartDate = stationStartDate;
    }

    public long getStartDate() {
        return startDate;
    }

    public void setStartDate(long startDate) {
        this.startDate = startDate;
    }

    public long getEndDate() {
        return endDate;
    }

    public void setEndDate(long endDate) {
        this.endDate = endDate;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Long getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(Long licenseState) {
        this.licenseState = licenseState;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public OrgDTO getOrg() {
        return org;
    }

    public void setOrg(OrgDTO org) {
        this.org = org;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getFequencyLicenseNo() {
        return fequencyLicenseNo;
    }

    public void setFequencyLicenseNo(String fequencyLicenseNo) {
        this.fequencyLicenseNo = fequencyLicenseNo;
    }

    public String getApplyTableCode() {
        return applyTableCode;
    }

    public void setApplyTableCode(String applyTableCode) {
        this.applyTableCode = applyTableCode;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getLicenseCounty() {
        return licenseCounty;
    }

    public void setLicenseCounty(String licenseCounty) {
        this.licenseCounty = licenseCounty;
    }

    public String getLicenseCountyStr() {
        return licenseCountyStr;
    }

    public void setLicenseCountyStr(String licenseCountyStr) {
        this.licenseCountyStr = licenseCountyStr;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
