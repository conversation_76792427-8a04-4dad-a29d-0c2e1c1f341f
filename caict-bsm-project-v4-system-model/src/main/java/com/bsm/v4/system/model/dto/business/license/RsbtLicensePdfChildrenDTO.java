package com.bsm.v4.system.model.dto.business.license;

import io.swagger.annotations.ApiModelProperty;


/**
 * pdf下载子dto
 */
public class RsbtLicensePdfChildrenDTO {

    @ApiModelProperty(value = "对应频率使用许可证编号")
    private String freqPass;

    @ApiModelProperty(value = "发射频率")
    private String freqEf;

    @ApiModelProperty(value = "接收频率")
    private String freqRf;

    @ApiModelProperty(value = "发射功率")
    private String equPow;

    @ApiModelProperty(value = "（必要）带宽")
    private String freqBand;

    @ApiModelProperty(value = "型号核准代码")
    private String equAuth;

    @ApiModelProperty(value = "天线增益")
    private Long antGain;

    @ApiModelProperty(value = "极化方式")
    private String antEpole;

    @ApiModelProperty(value = "天线据地高度")
    private Long antHight;

    public String getFreqPass() {
        return freqPass;
    }

    public void setFreqPass(String freqPass) {
        this.freqPass = freqPass;
    }

    public String getFreqEf() {
        return freqEf;
    }

    public void setFreqEf(String freqEf) {
        this.freqEf = freqEf;
    }

    public String getFreqRf() {
        return freqRf;
    }

    public void setFreqRf(String freqRf) {
        this.freqRf = freqRf;
    }

    public String getEquPow() {
        return equPow;
    }

    public void setEquPow(String equPow) {
        this.equPow = equPow;
    }

    public String getFreqBand() {
        return freqBand;
    }

    public void setFreqBand(String freqBand) {
        this.freqBand = freqBand;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public Long getAntGain() {
        return antGain;
    }

    public void setAntGain(Long antGain) {
        this.antGain = antGain;
    }

    public String getAntEpole() {
        return antEpole;
    }

    public void setAntEpole(String antEpole) {
        this.antEpole = antEpole;
    }

    public Long getAntHight() {
        return antHight;
    }

    public void setAntHight(Long antHight) {
        this.antHight = antHight;
    }
}
