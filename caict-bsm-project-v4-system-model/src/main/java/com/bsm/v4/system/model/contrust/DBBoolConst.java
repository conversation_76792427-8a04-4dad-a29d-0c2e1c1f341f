package com.bsm.v4.system.model.contrust;

/**
 * 数据库布尔值
 * Created by ch<PERSON><PERSON> on 2017/5/12.
 */
public class DBBoolConst {

    public static long TURE = 1;
    public static long FALSE = 0;
    public static long DIS = 2;
    //可以入库的问题数据
    public static long DOUBT = 3;
    //不能入库的问题数据
    public static long WARNING = 4;


    public static String ISHANDLE = "1";
    public static String NOTHANDLE = "0";

    public static String ISDOWNLOAD = "1";
    public static String NOTDOWNLOAD = "0";

    public static String ISCOMPARING = "1";
    public static String COMPARED = "2";

    //数据变更类型(1：新增；2：变更；3：删除；4：不变)
    public static String NEW = "1";
    public static String UPDATE = "2";
    public static String DEL = "3";
    public static String STAY = "4";

    //数据处理类型：1-运营商上传数据处理 2-数据对比
    public static String DEAL = "1";
    public static String COMPARE = "2";

    //上传csv任务数据处理状态:1-等待处理，2-处理中，3-已处理，4-错误
    public static final long WAITDEAL = 1;
    public static final long DEALING = 2;
    public static final long DEALED = 3;
    public static final long ERROR = 4;

    //无委审核状态:0-未审核；1-审核中；2-已审核；3-审核中第二阶段（进入自动处理）
    public static String UNCHECK = "0";
    public static String CHECKING = "1";
    public static String CHECKED = "2";
    public static String CHECKDATA = "3";

}
