package com.bsm.v4.system.model.entity.security;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dsy62 on 2017-11-28.
 * 角色用户关联对象
 */

@TableName("sys_role_users")
public class RoleUsers {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "角色id")
    @TableFieId("role_id")
    private String roleId;

    @ApiModelProperty(value = "用户id")
    @TableFieId("users_id")
    private String usersId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getUsersId() {
        return usersId;
    }

    public void setUsersId(String usersId) {
        this.usersId = usersId;
    }
}
