package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @description ProviderResponse
 * @date 2022/6/17 17:39
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "ProviderResponse",namespace = NameSpaceUrlConst.aNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "bizResCd",
        "bizResText"
})
public class ProviderResponse {

    @XmlElement(name="bizResCd", namespace=NameSpaceUrlConst.aNamespaceURI)
    private String bizResCd;
    @XmlElement(name="bizResText", namespace=NameSpaceUrlConst.aNamespaceURI)
    private String bizResText;

    public ProviderResponse() {
    }

    public String getBizResCd() {
        return bizResCd;
    }

    public void setBizResCd(String bizResCd) {
        this.bizResCd = bizResCd;
    }

    public String getBizResText() {
        return bizResText;
    }

    public void setBizResText(String bizResText) {
        this.bizResText = bizResText;
    }
}
