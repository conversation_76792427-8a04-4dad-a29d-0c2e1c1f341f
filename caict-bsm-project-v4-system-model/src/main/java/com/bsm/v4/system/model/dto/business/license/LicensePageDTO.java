package com.bsm.v4.system.model.dto.business.license;


import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * 执照编号（可为空）
 * 基站名称（可为空）
 * 基站到期时间（可为空）
 * 申请表编号（可为空）
 * 基站识别码（可为空）
 * 基站执照状态（可为空）
 * Created by qhy on 2018/8/3.
 */
public class LicensePageDTO {
    @ApiModelProperty(value = "当前页", required = true)
    private Integer page;
    @ApiModelProperty(value = "每页展示条数", required = true)
    private Integer rows;
    @ApiModelProperty(value = "执照编号（可为空）")
    private String licenseCode;
    @ApiModelProperty(value = "基站名称（可为空）")
    private String stationName;
    @ApiModelProperty(value = "基站到期时间（可为空）")
    private Date expirationDate;
    @ApiModelProperty(value = "申请表编号（可为空）")
    private String applytableCode;
    @ApiModelProperty(value = "基站识别码（可为空）")
    private String stationCode;
    @ApiModelProperty(value = " 基站执照状态（可为空）有效0,过期1,停用2,注销3")
    private Long licenseState;
    @ApiModelProperty(value = " 组织机构名称/设台单位（可为空）")
    private String orgName;
    @ApiModelProperty(value = "技术制式")
    private String netType;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getApplytableCode() {
        return applytableCode;
    }

    public void setApplytableCode(String applytableCode) {
        this.applytableCode = applytableCode;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public Long getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(Long licenseState) {
        this.licenseState = licenseState;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }
}
