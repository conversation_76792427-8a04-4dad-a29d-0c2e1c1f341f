package com.bsm.v4.system.model.entity.security.login_aspx.send;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @description MonitorHeader
 * @date 2022/6/17 16:46
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "srrc",namespace = NameSpaceUrlConst.sendNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "transId","bizKey","psCode","bsCode","appCode","appName","platFormCode","platFormName","resCode1","resCode2"
})
public class MonitorHeader {

    @XmlElement(name = "TransId",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String transId;
    @XmlElement(name = "BizK<PERSON>",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String bizKey;
    @XmlElement(name = "PSCode",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String psCode;
    @XmlElement(name = "BSCode",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String bsCode;
    @XmlElement(name = "appCode",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String appCode;
    @XmlElement(name = "appName",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String appName;
    @XmlElement(name = "platFormCode",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String platFormCode;
    @XmlElement(name = "platFormName",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String platFormName;
    @XmlElement(name = "resCode1",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String resCode1;
    @XmlElement(name = "resCode2",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private String resCode2;

    public MonitorHeader() {
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public String getBizKey() {
        return bizKey;
    }

    public void setBizKey(String bizKey) {
        this.bizKey = bizKey;
    }

    public String getPsCode() {
        return psCode;
    }

    public void setPsCode(String psCode) {
        this.psCode = psCode;
    }

    public String getBsCode() {
        return bsCode;
    }

    public void setBsCode(String bsCode) {
        this.bsCode = bsCode;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPlatFormCode() {
        return platFormCode;
    }

    public void setPlatFormCode(String platFormCode) {
        this.platFormCode = platFormCode;
    }

    public String getPlatFormName() {
        return platFormName;
    }

    public void setPlatFormName(String platFormName) {
        this.platFormName = platFormName;
    }

    public String getResCode1() {
        return resCode1;
    }

    public void setResCode1(String resCode1) {
        this.resCode1 = resCode1;
    }

    public String getResCode2() {
        return resCode2;
    }

    public void setResCode2(String resCode2) {
        this.resCode2 = resCode2;
    }
}
