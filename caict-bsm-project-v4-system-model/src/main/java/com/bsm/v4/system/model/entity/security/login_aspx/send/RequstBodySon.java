package com.bsm.v4.system.model.entity.security.login_aspx.send;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 * @description RequstBodySon
 * @date 2022/6/17 16:56
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "srrc",namespace = NameSpaceUrlConst.sendNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
})
public class RequstBodySon {
}
