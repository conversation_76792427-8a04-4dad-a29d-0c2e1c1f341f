package com.bsm.v4.system.model.entity.business.freqevaluation;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * <AUTHOR>
 * @description 省级公众移动通信系统用户数及用户流量
 * @date 2023年8月24日 11点13分
 */

@ApiModel(value = "freqEvaluationProv", description = "省级公众移动通信系统用户数及用户流量对象")
@TableName("BSM_FREQ_EVALUATION_PROV")
public class FreqEvaluationProv {

    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "制式代数,2G,3G,4G,5G")
    @TableFieId("NET_TS_LEVEL")
    private String netTsLevel;

    @ApiModelProperty(value = "地区代码")
    @TableFieId("ORG_AREA_CODE")
    private String orgAreaCode;

    @ApiModelProperty(value = "2G,3G用户量（户）或 4G,5G用户流量（MHz）")
    @TableFieId("TRF_VALUE")
    private Double trfValue;

    @ApiModelProperty(value = "创建日期")
    @TableFieId("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "备注")
    @TableFieId("MEMO")
    private String memo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getNetTsLevel() {
        return netTsLevel;
    }

    public void setNetTsLevel(String netTsLevel) {
        this.netTsLevel = netTsLevel;
    }

    public String getOrgAreaCode() {
        return orgAreaCode;
    }

    public void setOrgAreaCode(String orgAreaCode) {
        this.orgAreaCode = orgAreaCode;
    }

    public Double getTrfValue() {
        return trfValue;
    }

    public void setTrfValue(Double trfValue) {
        this.trfValue = trfValue;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
