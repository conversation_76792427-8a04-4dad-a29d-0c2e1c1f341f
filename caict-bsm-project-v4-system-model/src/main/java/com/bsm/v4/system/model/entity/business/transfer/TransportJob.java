package com.bsm.v4.system.model.entity.business.transfer;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;


@TableName("TRANSPORT_JOB")
public class TransportJob {

    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;
    /**
     * 流水号
     */
    @TableFieId("JOB_CODE")
    private String jobCode;
    /**
     * 任务状态
     * 状态 （0：正常；1：异常；2：未提交）
     */
    @TableFieId("JOB_STATE")
    private Long jobState;
    /**
     * 创建用户id
     */
    @TableFieId("USER_GUID")
    private String userGuid;

    /**
     * 任务提交日期
     */
    @TableFieId("JOB_DATE")
    private Date jobDate;

    /**
     * 事项名称
     */
    @TableFieId("JOB_NAME")
    private String jobName;

    /**
     * 是否完成对比字段
     * 处理流程（详细见TransportJobStateConst）
     */
    @TableFieId("IS_COMPARE")
    private String isCompare;

    @ApiModelProperty(value = "审核通过数量")
    @TableFieId("APPROVAL_COUNT")
    private Integer approvalCount;

    @ApiModelProperty(value = "未审核通过数量")
    @TableFieId("DISAPPROVAL_COUNT")
    private Integer disapprovalCount;

    @ApiModelProperty(value = "全量、增量上传标志（为空则为全量上传；1-新增；2-变更；3-注销）")
    @TableFieId("DATA_TYPE")
    private String dataType;

    @ApiModelProperty(value = "任务下校验失败数")
    @TableFieId("CHECK_FAILED_NUM")
    private Integer checkFailedNum;

    @ApiModelProperty(value = "任务下校验成功数")
    @TableFieId("CHECK_SUCCESS_NUM")
    private Integer checkSuccessNum;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public Long getJobState() {
        return jobState;
    }

    public void setJobState(Long jobState) {
        this.jobState = jobState;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public Date getJobDate() {
        return jobDate;
    }

    public void setJobDate(Date jobDate) {
        this.jobDate = jobDate;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public Integer getApprovalCount() {
        return approvalCount;
    }

    public void setApprovalCount(Integer approvalCount) {
        this.approvalCount = approvalCount;
    }

    public Integer getDisapprovalCount() {
        return disapprovalCount;
    }

    public void setDisapprovalCount(Integer disapprovalCount) {
        this.disapprovalCount = disapprovalCount;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Integer getCheckFailedNum() {
        return checkFailedNum;
    }

    public void setCheckFailedNum(Integer checkFailedNum) {
        this.checkFailedNum = checkFailedNum;
    }

    public Integer getCheckSuccessNum() {
        return checkSuccessNum;
    }

    public void setCheckSuccessNum(Integer checkSuccessNum) {
        this.checkSuccessNum = checkSuccessNum;
    }
}
