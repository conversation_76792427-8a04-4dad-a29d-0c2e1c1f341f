package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("RSBT_NET")
public class RsbtNet {

    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;

    /**
     * 申请单位GUID
     */
    @TableFieId("ORG_GUID")
    private String orgGuid;

    /**
     * 缴费单位GUID
     */
    @TableFieId("FEE_GUID")
    private String feeGuid;

    /**
     * 无线电管理机构组织机构代码
     */
    @TableFieId("ORG_CODE")
    private String orgCode;

    /**
     * 无线电系统/网络名称
     */
    @TableFieId("NET_NAME")
    private String netName;

    /**
     * 通信业务/系统类型
     */
    @TableFieId("NET_SVN")
    private String netSvn;

    /**
     * 业务性质
     */
    @TableFieId("NET_SP")
    private String netSp;

    /**
     * 技术体制
     */
    @TableFieId("NET_TS")
    private String netTs;

    /**
     * 信道带宽/波道间隔
     */
    @TableFieId("NET_BAND")
    private double netBand;

    /**
     * 使用范围
     */
    @TableFieId("NET_AREA")
    private String netArea;

    /**
     * 网络用途
     */
    @TableFieId("NET_USE")
    private String netUse;

    /**
     * 卫星/星座名称
     */
    @TableFieId("NET_SAT_NAME")
    private String netStaName;

    /**
     * 标称轨道经度
     */
    @TableFieId("NET_LG")
    private String netLg;

    /**
     * 启用日期
     */
    @TableFieId("NET_START_DATE")
    private Date netStartDate;

    /**
     * 批准日期
     */
    @TableFieId("NET_CONFIRM_DATE")
    private Date netConfirmDate;

    /**
     * 报废日期
     */
    @TableFieId("NET_EXPIRED_DATE")
    private Date netExpiredDate;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOrgGuid() {
        return orgGuid;
    }

    public void setOrgGuid(String orgGuid) {
        this.orgGuid = orgGuid;
    }

    public String getFeeGuid() {
        return feeGuid;
    }

    public void setFeeGuid(String feeGuid) {
        this.feeGuid = feeGuid;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getNetName() {
        return netName;
    }

    public void setNetName(String netName) {
        this.netName = netName;
    }

    public String getNetSvn() {
        return netSvn;
    }

    public void setNetSvn(String netSvn) {
        this.netSvn = netSvn;
    }

    public String getNetSp() {
        return netSp;
    }

    public void setNetSp(String netSp) {
        this.netSp = netSp;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public double getNetBand() {
        return netBand;
    }

    public void setNetBand(double netBand) {
        this.netBand = netBand;
    }

    public String getNetArea() {
        return netArea;
    }

    public void setNetArea(String netArea) {
        this.netArea = netArea;
    }

    public String getNetUse() {
        return netUse;
    }

    public void setNetUse(String netUse) {
        this.netUse = netUse;
    }

    public String getNetStaName() {
        return netStaName;
    }

    public void setNetStaName(String netStaName) {
        this.netStaName = netStaName;
    }

    public String getNetLg() {
        return netLg;
    }

    public void setNetLg(String netLg) {
        this.netLg = netLg;
    }

    public Date getNetStartDate() {
        return netStartDate;
    }

    public void setNetStartDate(Date netStartDate) {
        this.netStartDate = netStartDate;
    }

    public Date getNetConfirmDate() {
        return netConfirmDate;
    }

    public void setNetConfirmDate(Date netConfirmDate) {
        this.netConfirmDate = netConfirmDate;
    }

    public Date getNetExpiredDate() {
        return netExpiredDate;
    }

    public void setNetExpiredDate(Date netExpiredDate) {
        this.netExpiredDate = netExpiredDate;
    }
}
