package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @description ResOrg
 * @date 2022/6/17 17:29
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "orgInfo",namespace = NameSpaceUrlConst.aNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "order",
        "orgCode"
})
public class ResOrg {
    @XmlElement(name = "order",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String order;
    @XmlElement(name = "orgCode",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String orgCode;

    public ResOrg() {
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
}
