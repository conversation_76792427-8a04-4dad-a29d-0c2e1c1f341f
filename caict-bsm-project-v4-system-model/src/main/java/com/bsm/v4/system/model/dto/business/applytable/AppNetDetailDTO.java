package com.bsm.v4.system.model.dto.business.applytable;




import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/1/2
 * <p>
 * netSvn: null,        // 通信业务/系统 类型
 * netName              //无线电网络系统名称
 * netBand              //信道带宽/波道间隔
 * netBandU             //信道带宽/波道间隔单位
 * netTS: null,         // 业务性质
 * netSP: null,         // 技术体制
 * netArea: null,       // 使用范围
 * netUse: null,        // 网络用途
 * appFileNo: null,     // 频率使用许可证号或批准文号
 * netConfirmDate: null //批准频率使用期限
 * netExpiredDate: null //批准频率使用期限
 */
public class AppNetDetailDTO {
    private String netSvn;
    private String netName;
    private Double netBand;
    private String netBandU;
    private String netTs;
    private String netSp;
    private String netArea;
    private String netUse;
    private List<String> netFileNo;
    private Date netConfirmDate;
    private Date netExpiredDate;

    public String getNetSvn() {
        return netSvn;
    }

    public void setNetSvn(String netSvn) {
        this.netSvn = netSvn;
    }

    public String getNetName() {
        return netName;
    }

    public void setNetName(String netName) {
        this.netName = netName;
    }

    public Double getNetBand() {
        return netBand;
    }

    public void setNetBand(Double netBand) {
        this.netBand = netBand;
    }

    public String getNetBandU() {
        return netBandU;
    }

    public void setNetBandU(String netBandU) {
        this.netBandU = netBandU;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getNetSp() {
        return netSp;
    }

    public void setNetSp(String netSp) {
        this.netSp = netSp;
    }

    public String getNetArea() {
        return netArea;
    }

    public void setNetArea(String netArea) {
        this.netArea = netArea;
    }

    public String getNetUse() {
        return netUse;
    }

    public void setNetUse(String netUse) {
        this.netUse = netUse;
    }

    public List<String> getNetFileNo() {
        return netFileNo;
    }

    public void setNetFileNo(List<String> netFileNo) {
        this.netFileNo = netFileNo;
    }

    public Date getNetConfirmDate() {
        return netConfirmDate;
    }

    public void setNetConfirmDate(Date netConfirmDate) {
        this.netConfirmDate = netConfirmDate;
    }

    public Date getNetExpiredDate() {
        return netExpiredDate;
    }

    public void setNetExpiredDate(Date netExpiredDate) {
        this.netExpiredDate = netExpiredDate;
    }
}
