package com.bsm.v4.system.model.entity.es;

import io.swagger.annotations.ApiModelProperty;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;

/**
 * es 执照表.
 *
 * <AUTHOR>
 * @since 2023年8月10日 11点33分
 **/
@Document(indexName = "es_rsbt_license")

public class EsRsbtLicense {

    @ApiModelProperty(value = "主键ID")
    @Id
    @Field(name = "GUID", type = FieldType.Text)
    private String guid;

    @ApiModelProperty(value = "台站GUID")
    @Field(name = "STATION_GUID", type = FieldType.Text)
    private String stationGuid;

    @ApiModelProperty(value = "申请表编号")
    @Field(name = "APP_CODE", type = FieldType.Text)
    private String appCode;

    @ApiModelProperty(value = "技术资料申报表编号")
    @Field(name = "STAT_TDI", type = FieldType.Text)
    private String statTdi;

    @ApiModelProperty(value = "技术资料申报表类型")
    @Field(name = "STAT_APP_TYPE", type = FieldType.Text)
    private String statAppType;

    @ApiModelProperty(value = "执照类型")
    @Field(name = "LICENSE_TYPE", type = FieldType.Text)
    private String licenseType;

    @ApiModelProperty(value = "执照号")
    @Field(name = "LICENSE_CODE", type = FieldType.Text)
    private String licenseCode;

    @ApiModelProperty(value = "执照单位名称")
    @Field(name = "LICENSE_ORG_NAME", type = FieldType.Text)
    private String licenseOrgName;

    @ApiModelProperty(value = "核发单位名称")
    @Field(name = "LICENSE_MANAGER", type = FieldType.Text)
    private String licenseManager;

    @ApiModelProperty(value = "执照有效期起")
    @Field(name = "LICENSE_DATE_B", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime licenseDateB;

    @ApiModelProperty(value = "执照有效期止")
    @Field(name = "LICENSE_DATE_E", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime licenseDateE;

    @ApiModelProperty(value = "发证时间")
    @Field(name = "LICENSE_DATE", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime licenseDate;

    @ApiModelProperty(value = "备注")
    @Field(name = "MEMO", type = FieldType.Text)
    private String memo;

    @ApiModelProperty(value = "所属区域")
    @Field(name = "LICENSE_COUNTY", type = FieldType.Text)
    private String licenseCounty;

    @ApiModelProperty(value = "许可证号")
    @Field(name = "LICENSE_NO", type = FieldType.Text)
    private String licenseNo;

    @ApiModelProperty(value = "制式")
    @Field(name = "TECH_TYPE", type = FieldType.Text)
    private String techType;

    @ApiModelProperty(value = "运营商类型")
    @Field(name = "ORG_TYPE", type = FieldType.Text)
    private String orgType;

    @ApiModelProperty(value = "基站名称")
    @Field(name = "STATION_NAME", type = FieldType.Text)
    private String stationName;

    @ApiModelProperty(value = "基站编号")
    @Field(name = "STATION_CODE", type = FieldType.Text)
    private String stationCode;

    @ApiModelProperty(value = "技术体制")
    @Field(name = "NET_TYPE", type = FieldType.Text)
    private String netType;

    @ApiModelProperty(value = "台址")
    @Field(name = "LOCATION", type = FieldType.Text)
    private String location;

    @ApiModelProperty(value = "执照状态")
    @Field(name = "LICENSE_STATE", type = FieldType.Long)
    private Long licenseState;

    @ApiModelProperty(value = "经度")
    @Field(name = "LONGITUDE", type = FieldType.Text)
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @Field(name = "LATITUDE", type = FieldType.Text)
    private String latitude;

    @ApiModelProperty(value = "设台单位id")
    @Field(name = "ORG_ID", type = FieldType.Text)
    private String orgId;

    @ApiModelProperty(value = "设台单位名称")
    @Field(name = "ORG_NAME", type = FieldType.Text)
    private String orgName;

    @ApiModelProperty(value = "申请表编号")
    @Field(name = "APPLY_TABLE_CODE", type = FieldType.Text)
    private String applyTableCode;

    @ApiModelProperty(value = "用户类型")
    @Field(name = "USER_TYPE", type = FieldType.Text)
    private String userType;

    @ApiModelProperty(value = "地区")
    @Field(name = "LICENSE_COUNTY_STR", type = FieldType.Text)
    private String licenseCountyStr;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(String licenseType) {
        this.licenseType = licenseType;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getLicenseOrgName() {
        return licenseOrgName;
    }

    public void setLicenseOrgName(String licenseOrgName) {
        this.licenseOrgName = licenseOrgName;
    }

    public String getLicenseManager() {
        return licenseManager;
    }

    public void setLicenseManager(String licenseManager) {
        this.licenseManager = licenseManager;
    }

    public LocalDateTime getLicenseDateB() {
        return licenseDateB;
    }

    public void setLicenseDateB(LocalDateTime licenseDateB) {
        this.licenseDateB = licenseDateB;
    }

    public LocalDateTime getLicenseDateE() {
        return licenseDateE;
    }

    public void setLicenseDateE(LocalDateTime licenseDateE) {
        this.licenseDateE = licenseDateE;
    }

    public LocalDateTime getLicenseDate() {
        return licenseDate;
    }

    public void setLicenseDate(LocalDateTime licenseDate) {
        this.licenseDate = licenseDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getLicenseCounty() {
        return licenseCounty;
    }

    public void setLicenseCounty(String licenseCounty) {
        this.licenseCounty = licenseCounty;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Long getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(Long licenseState) {
        this.licenseState = licenseState;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getApplyTableCode() {
        return applyTableCode;
    }

    public void setApplyTableCode(String applyTableCode) {
        this.applyTableCode = applyTableCode;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getLicenseCountyStr() {
        return licenseCountyStr;
    }

    public void setLicenseCountyStr(String licenseCountyStr) {
        this.licenseCountyStr = licenseCountyStr;
    }
}
