package com.bsm.v4.system.model.dto.business.station;



/**
 * 扇区列表（发射起始频率，发射终止频率，接受起始频率，接受终止频率，最大发射功率，极化方式，天线距地高度）
 */
public class ISectionDTO {
    private String freqEf;//发射频率
    private String freqRf;//接收频率
    private String equPow;//发射功率
    private String freqBand;//（必要）带宽
    private String equAuth;//型号核准代码
    private Long antGain;//天线增益
    private String antEpole;//极化方式
    private Long antHight;//天线据地高度
    private String freqPass;//频率使用许可证号或批准文号
    private String detail;//其他信息

    public String getFreqEf() {
        return freqEf;
    }

    public void setFreqEf(String freqEf) {
        this.freqEf = freqEf;
    }

    public String getFreqRf() {
        return freqRf;
    }

    public void setFreqRf(String freqRf) {
        this.freqRf = freqRf;
    }

    public String getEquPow() {
        return equPow;
    }

    public void setEquPow(String equPow) {
        this.equPow = equPow;
    }

    public String getFreqBand() {
        return freqBand;
    }

    public void setFreqBand(String freqBand) {
        this.freqBand = freqBand;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public Long getAntGain() {
        return antGain;
    }

    public void setAntGain(Long antGain) {
        this.antGain = antGain;
    }

    public String getAntEpole() {
        return antEpole;
    }

    public void setAntEpole(String antEpole) {
        this.antEpole = antEpole;
    }

    public Long getAntHight() {
        return antHight;
    }

    public void setAntHight(Long antHight) {
        this.antHight = antHight;
    }

    public String getFreqPass() {
        return freqPass;
    }

    public void setFreqPass(String freqPass) {
        this.freqPass = freqPass;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
