package com.bsm.v4.system.model.dto.business.license;


import io.swagger.annotations.ApiModelProperty;


import java.util.Date;
import java.util.List;

/**
 * pdf下载dto
 */
public class RsbtLicensePdfDTO {

    private int page;
    private int rows;

    @ApiModelProperty(value = "基站编号")
    private String stationGuid;

    @ApiModelProperty(value = "执照编号")
    private String linceseCode;

    @ApiModelProperty(value = "基站编号")
    private String stationCode;

    @ApiModelProperty(value = "开始日期")
    private Date linceseStartDate;

    @ApiModelProperty(value = "过期日期")
    private Date linceseEndDate;

    @ApiModelProperty(value = "台站名称")
    private String staName;

    @ApiModelProperty(value = "无线电台识别码")
    private String idenCode;

    @ApiModelProperty(value = "使用人")
    private String userName;

    @ApiModelProperty(value = "统一社会信用码")
    private String orgCode;

    @ApiModelProperty(value = "台址/使用区域")
    private String location;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "其他信息")
    private String detail;

    @ApiModelProperty(value = "发射/接收参数等")
    private List<RsbtLicensePdfChildrenDTO> staPdfChildrenDTOList;

    @ApiModelProperty(value = "特别规定事项")
    private String specialCase;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getLinceseCode() {
        return linceseCode;
    }

    public void setLinceseCode(String linceseCode) {
        this.linceseCode = linceseCode;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public Date getLinceseStartDate() {
        return linceseStartDate;
    }

    public void setLinceseStartDate(Date linceseStartDate) {
        this.linceseStartDate = linceseStartDate;
    }

    public Date getLinceseEndDate() {
        return linceseEndDate;
    }

    public void setLinceseEndDate(Date linceseEndDate) {
        this.linceseEndDate = linceseEndDate;
    }

    public String getStaName() {
        return staName;
    }

    public void setStaName(String staName) {
        this.staName = staName;
    }

    public String getIdenCode() {
        return idenCode;
    }

    public void setIdenCode(String idenCode) {
        this.idenCode = idenCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public List<RsbtLicensePdfChildrenDTO> getStaPdfChildrenDTOList() {
        return staPdfChildrenDTOList;
    }

    public void setStaPdfChildrenDTOList(List<RsbtLicensePdfChildrenDTO> staPdfChildrenDTOList) {
        this.staPdfChildrenDTOList = staPdfChildrenDTOList;
    }

    public String getSpecialCase() {
        return specialCase;
    }

    public void setSpecialCase(String specialCase) {
        this.specialCase = specialCase;
    }
}
