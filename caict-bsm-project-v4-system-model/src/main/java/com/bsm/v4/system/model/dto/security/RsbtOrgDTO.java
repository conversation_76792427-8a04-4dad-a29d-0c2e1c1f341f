package com.bsm.v4.system.model.dto.security;

import com.bsm.v4.system.model.entity.security.RsbtOrg;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * @Title: RsbtOrgDTO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.dto.security
 * @Date 2023/9/13 14:33
 * @description:
 */

@ApiModel(value = "rsbtOrgDTO", description = "组织机构对象")
public class RsbtOrgDTO extends RsbtOrg {
    //分页信息
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
