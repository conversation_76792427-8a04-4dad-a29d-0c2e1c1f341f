package com.bsm.v4.system.model.dto.security;

import com.caictframework.utils.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.ArrayList;
import java.util.List;

/**
 * Created by dengsy on 2019-10-24.
 * 中国省市区DTO
 * childrenList:子节点集合
 */

@ApiModel(value = "regionDTO", description = "省市区对象")
public class RegionDTO {

    //分页
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int pageNum;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int pageSize;

    @ApiModelProperty(value = "子类集合")
    @TableChildren
    private List<RegionDTO> regionDTOList = new ArrayList<RegionDTO>();

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "父级id")
    @TableParentId
    private String parentId;

    @ApiModelProperty(value = "权重")
    private Integer sort;

    @ApiModelProperty(value = "数据级别")
    private Integer dataLevel;

    @ApiModelProperty(value = "区域详情")
    private String regionDetails;

    @ApiModelProperty(value = "坐标集合")
    private String coordinate;

    @ApiModelProperty(value = "简短code(行政区号)")
    private String sCode;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<RegionDTO> getRegionDTOList() {
        return regionDTOList;
    }

    public void setRegionDTOList(List<RegionDTO> regionDTOList) {
        this.regionDTOList = regionDTOList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getDataLevel() {
        return dataLevel;
    }

    public void setDataLevel(Integer dataLevel) {
        this.dataLevel = dataLevel;
    }

    public String getRegionDetails() {
        return regionDetails;
    }

    public void setRegionDetails(String regionDetails) {
        this.regionDetails = regionDetails;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getsCode() {
        return sCode;
    }

    public void setsCode(String sCode) {
        this.sCode = sCode;
    }
}
