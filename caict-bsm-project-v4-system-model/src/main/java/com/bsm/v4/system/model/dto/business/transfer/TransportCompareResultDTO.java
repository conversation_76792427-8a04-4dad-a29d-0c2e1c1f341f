package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.TransportCompareResult;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


/**
 * Created by dengsy on 2020-05-08.
 * <p>
 * transportRawBtsDTO:  审核前数据详情
 * approvalScheduleDTO: 审核后数据详情
 * <p>
 * btsName:基站名称
 * btsId:基站id
 * cellName:扇区名称
 * cellId:扇区id
 */

public class TransportCompareResultDTO extends TransportCompareResult {

    private TransportRawBtsDTO transportRawBtsDTO;
    private ApprovalScheduleLogDTO approvalScheduleLogDTO;
    private ApprovalScheduleDTO approvalScheduleDTO;

    private String btsName;
    private String btsId;
    private String cellName;
    private String cellId;
    private String type;

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    public TransportRawBtsDTO getTransportRawBtsDTO() {
        return transportRawBtsDTO;
    }

    public void setTransportRawBtsDTO(TransportRawBtsDTO transportRawBtsDTO) {
        this.transportRawBtsDTO = transportRawBtsDTO;
    }

    public ApprovalScheduleLogDTO getApprovalScheduleLogDTO() {
        return approvalScheduleLogDTO;
    }

    public void setApprovalScheduleLogDTO(ApprovalScheduleLogDTO approvalScheduleLogDTO) {
        this.approvalScheduleLogDTO = approvalScheduleLogDTO;
    }

    public ApprovalScheduleDTO getApprovalScheduleDTO() {
        return approvalScheduleDTO;
    }

    public void setApprovalScheduleDTO(ApprovalScheduleDTO approvalScheduleDTO) {
        this.approvalScheduleDTO = approvalScheduleDTO;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
