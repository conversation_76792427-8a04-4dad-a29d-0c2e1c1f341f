package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * 无线电台（站）频率冗余表
 */

@TableName("RSBT_FREQ_T")
public class RsbtFreqT {

    /**
     * RSBT_FREQ GUID
     */
    @TableId("GUID")
    private String guid;

    /**
     * 频率序号
     */
    @TableFieId("FT_FREQ_NO")
    private String ftFreqNo;

    /**
     * 工作时间起
     */
    @TableFieId("FT_FREQ_TIMEB")
    private Date ftFreqTimeb;

    /**
     * 工作时间止
     */
    @TableFieId("FT_FREQ_TIMEE")
    private Date ftFreqTimee;

    /**
     * 发送信息类型
     */
    @TableFieId("FT_FREQ_INFO_TYPE")
    private String ftFreqInfoType;

    /**
     * 呼号
     */
    @TableFieId("FT_FREQ_HCL")
    private String ftFreqHcl;

    /**
     * 波道间隔/信道带宽
     */
    @TableFieId("FT_FREQ_TYPE")
    private String ftFreqType;

    /**
     * 调制特性类型
     */
    @TableFieId("FT_FREQ_MC")
    private String ftFreqMc;

    /**
     * 调制特性1
     */
    @TableFieId("FT_FREQ_MC1")
    private String ftFreqMc1;

    /**
     * 调制特性2
     */
    @TableFieId("FT_FREQ_MC2")
    private String ftFreqMc2;

    /**
     * 调制特性3
     */
    @TableFieId("FT_FREQ_MC3")
    private String ftFreqMc3;

    /**
     * 扇区号
     */
    @TableFieId("FT_FREQ_CCODE")
    private String ftFreqCcode;

    /**
     * 扇区标识码
     */
    @TableFieId("FT_FREQ_CSGN")
    private String ftFreqCsgn;

    /**
     * 直放站的上行/下行标识
     */
    @TableFieId("FT_FREQ_DUPDN")
    private String ftFreqDupdn;

    /**
     * 频率单位
     */
    @TableFieId("FT_FREQ_UNIT_TYPE")
    private String ftFreqUnitType;

    /**
     * 发射极化方式
     */
    @TableFieId("FT_FREQ_FEP")
    private String ftFreqFep;

    /**
     * 接收极化方式
     */
    @TableFieId("FT_FREQ_FRP")
    private String ftFreqFrp;

    /**
     * 发射功率W
     */
    @TableFieId("FT_FREQ_EPOW")
    private Double ftFreqEpow;

    /**
     * 功率标识
     */
    @TableFieId("FT_FREQ_POWFLAG")
    private String ftFreqPowflag;

    /**
     * EIRP值
     */
    @TableFieId("FT_FREQ_EIRP")
    private Double ftFreqEirp;

    /**
     * 峰值功率
     */
    @TableFieId("FT_FREQ_POW_MAX")
    private Double ftFreqPowMax;

    /**
     * 平均功率
     */
    @TableFieId("FT_FREQ_POW_AVG")
    private Double ftFreqPowAvg;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getFtFreqNo() {
        return ftFreqNo;
    }

    public void setFtFreqNo(String ftFreqNo) {
        this.ftFreqNo = ftFreqNo;
    }

    public Date getFtFreqTimeb() {
        return ftFreqTimeb;
    }

    public void setFtFreqTimeb(Date ftFreqTimeb) {
        this.ftFreqTimeb = ftFreqTimeb;
    }

    public Date getFtFreqTimee() {
        return ftFreqTimee;
    }

    public void setFtFreqTimee(Date ftFreqTimee) {
        this.ftFreqTimee = ftFreqTimee;
    }

    public String getFtFreqInfoType() {
        return ftFreqInfoType;
    }

    public void setFtFreqInfoType(String ftFreqInfoType) {
        this.ftFreqInfoType = ftFreqInfoType;
    }

    public String getFtFreqHcl() {
        return ftFreqHcl;
    }

    public void setFtFreqHcl(String ftFreqHcl) {
        this.ftFreqHcl = ftFreqHcl;
    }

    public String getFtFreqType() {
        return ftFreqType;
    }

    public void setFtFreqType(String ftFreqType) {
        this.ftFreqType = ftFreqType;
    }

    public String getFtFreqMc() {
        return ftFreqMc;
    }

    public void setFtFreqMc(String ftFreqMc) {
        this.ftFreqMc = ftFreqMc;
    }

    public String getFtFreqMc1() {
        return ftFreqMc1;
    }

    public void setFtFreqMc1(String ftFreqMc1) {
        this.ftFreqMc1 = ftFreqMc1;
    }

    public String getFtFreqMc2() {
        return ftFreqMc2;
    }

    public void setFtFreqMc2(String ftFreqMc2) {
        this.ftFreqMc2 = ftFreqMc2;
    }

    public String getFtFreqMc3() {
        return ftFreqMc3;
    }

    public void setFtFreqMc3(String ftFreqMc3) {
        this.ftFreqMc3 = ftFreqMc3;
    }

    public String getFtFreqCcode() {
        return ftFreqCcode;
    }

    public void setFtFreqCcode(String ftFreqCcode) {
        this.ftFreqCcode = ftFreqCcode;
    }

    public String getFtFreqCsgn() {
        return ftFreqCsgn;
    }

    public void setFtFreqCsgn(String ftFreqCsgn) {
        this.ftFreqCsgn = ftFreqCsgn;
    }

    public String getFtFreqDupdn() {
        return ftFreqDupdn;
    }

    public void setFtFreqDupdn(String ftFreqDupdn) {
        this.ftFreqDupdn = ftFreqDupdn;
    }

    public String getFtFreqUnitType() {
        return ftFreqUnitType;
    }

    public void setFtFreqUnitType(String ftFreqUnitType) {
        this.ftFreqUnitType = ftFreqUnitType;
    }

    public String getFtFreqFep() {
        return ftFreqFep;
    }

    public void setFtFreqFep(String ftFreqFep) {
        this.ftFreqFep = ftFreqFep;
    }

    public String getFtFreqFrp() {
        return ftFreqFrp;
    }

    public void setFtFreqFrp(String ftFreqFrp) {
        this.ftFreqFrp = ftFreqFrp;
    }

    public Double getFtFreqEpow() {
        return ftFreqEpow;
    }

    public void setFtFreqEpow(Double ftFreqEpow) {
        this.ftFreqEpow = ftFreqEpow;
    }

    public String getFtFreqPowflag() {
        return ftFreqPowflag;
    }

    public void setFtFreqPowflag(String ftFreqPowflag) {
        this.ftFreqPowflag = ftFreqPowflag;
    }

    public Double getFtFreqEirp() {
        return ftFreqEirp;
    }

    public void setFtFreqEirp(Double ftFreqEirp) {
        this.ftFreqEirp = ftFreqEirp;
    }

    public Double getFtFreqPowMax() {
        return ftFreqPowMax;
    }

    public void setFtFreqPowMax(Double ftFreqPowMax) {
        this.ftFreqPowMax = ftFreqPowMax;
    }

    public Double getFtFreqPowAvg() {
        return ftFreqPowAvg;
    }

    public void setFtFreqPowAvg(Double ftFreqPowAvg) {
        this.ftFreqPowAvg = ftFreqPowAvg;
    }
}