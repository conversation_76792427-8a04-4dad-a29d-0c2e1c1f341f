package com.bsm.v4.system.model.entity.business.freqevaluation;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */

@ApiModel(value = "freqEvaluation", description = "无线电频率使用率评价结果对象")
@TableName("BSM_FREQ_EVALUATION_RESULTS")
public class FreqEvaluation {

    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "企业代码")
    @TableFieId("ORG_CODE")
    private String orgCode;

    @ApiModelProperty(value = "企业名称")
    @TableFieId("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "企业类型")
    @TableFieId("ORG_TYPE")
    private String orgType;

    @ApiModelProperty(value = "企业等级")
    @TableFieId("ORG_LEVEL")
    private String orgLevel;

    @ApiModelProperty(value = "地区代码")
    @TableFieId("ORG_AREA_CODE")
    private String orgAreaCode;

    @ApiModelProperty(value = "基站起始发射频率（MHz）")
    @TableFieId("EMIT_START_FREQ")
    private Double emitStartFreq;

    @ApiModelProperty(value = "基站结束发射频率（MHz）")
    @TableFieId("EMIT_STOP_FREQ")
    private Double emitStopFreq;

    @ApiModelProperty(value = "技术体制")
    @TableFieId("NET_TS")
    private String netTs;

    @ApiModelProperty(value = "频段占用度（%）")
    @TableFieId("FREQUENCY_OCCUPANCY")
    private Double frequencyOccupancy;

    @ApiModelProperty(value = "区域覆盖率（%）")
    @TableFieId("REGIONAL_COVERAGE")
    private Double regionalCoverage;

    @ApiModelProperty(value = "用户承载率（%）")
    @TableFieId("USER_LOAD_CAPACITY")
    private Double userLoadCapacity;

    @ApiModelProperty(value = "人均基站拥有量（个/万人）")
    @TableFieId("BASE_STATION_OWNERSHIP")
    private Double baseStationOwnership;

    @ApiModelProperty(value = "用户量（户）")
    @TableFieId("TRF_USER")
    private Double trfUser;

    @ApiModelProperty(value = "用户流量（MHz）")
    @TableFieId("TRF_DATA")
    private Double trfData;

    @ApiModelProperty(value = "创建日期")
    @TableFieId("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "备注")
    @TableFieId("MEMO")
    private String memo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getOrgAreaCode() {
        return orgAreaCode;
    }

    public void setOrgAreaCode(String orgAreaCode) {
        this.orgAreaCode = orgAreaCode;
    }

    public Double getEmitStartFreq() {
        return emitStartFreq;
    }

    public void setEmitStartFreq(Double emitStartFreq) {
        this.emitStartFreq = emitStartFreq;
    }

    public Double getEmitStopFreq() {
        return emitStopFreq;
    }

    public void setEmitStopFreq(Double emitStopFreq) {
        this.emitStopFreq = emitStopFreq;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public Double getFrequencyOccupancy() {
        return frequencyOccupancy;
    }

    public void setFrequencyOccupancy(Double frequencyOccupancy) {
        this.frequencyOccupancy = frequencyOccupancy;
    }

    public Double getRegionalCoverage() {
        return regionalCoverage;
    }

    public void setRegionalCoverage(Double regionalCoverage) {
        this.regionalCoverage = regionalCoverage;
    }

    public Double getUserLoadCapacity() {
        return userLoadCapacity;
    }

    public void setUserLoadCapacity(Double userLoadCapacity) {
        this.userLoadCapacity = userLoadCapacity;
    }

    public Double getBaseStationOwnership() {
        return baseStationOwnership;
    }

    public void setBaseStationOwnership(Double baseStationOwnership) {
        this.baseStationOwnership = baseStationOwnership;
    }

    public Double getTrfUser() {
        return trfUser;
    }

    public void setTrfUser(Double trfUser) {
        this.trfUser = trfUser;
    }

    public Double getTrfData() {
        return trfData;
    }

    public void setTrfData(Double trfData) {
        this.trfData = trfData;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
