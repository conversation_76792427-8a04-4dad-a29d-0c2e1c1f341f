package com.bsm.v4.system.model.contrust.transfer;

/**
 * file状态
 *
 * <AUTHOR>
 * <p>
 * state
 * 0 文件上传中
 * 1 文件上传失败
 * 2 文件上传成功待处理
 * 3 文件处理中
 * 4 文件处理失败
 * 5 文件处理成功
 * <p>
 * type
 * 0 附件
 * 1 运营商上传申请文件
 * 2 无委上传审核文件
 * 3 无委上传协调文件
 * 4 数据迁移文件
 * 5 无委上传频率使用率文件
 */
public class TransportFileStateConst {

    public static final long UPLOAD_WAIT = 0;

    public static final long UPLOAD_FAILURE = 1;

    public static final long UPLOAD_SUCCESS = 2;

    public static final long CHECK_PROCESSING = 3;

    public static final long CHECK_FAIL = 4;

    public static final long CHECK_SUCCESS = 5;

    public static final String ENCLOSURE = "0";

    public static final String OPERATORS = "1";

    public static final String EXAMINE = "2";

    public static final String COORDINATE = "3";

    public static final String IMPORT = "4";

    public static final String FREQ = "5";
}
