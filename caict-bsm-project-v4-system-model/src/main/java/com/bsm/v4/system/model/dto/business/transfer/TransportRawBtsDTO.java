package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dengsy on 2020-04-22.
 */

public class TransportRawBtsDTO extends TransportRawBts {

    //分页
    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int pageNum;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int pageSize;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
