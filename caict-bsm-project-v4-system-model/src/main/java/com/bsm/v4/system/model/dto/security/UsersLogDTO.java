package com.bsm.v4.system.model.dto.security;

import com.bsm.v4.system.model.entity.security.UsersLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * @Title: UsersLogDTO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.dto.security
 * @Date 2023/8/17 14:13
 * @description:
 */

@ApiModel(value = "usersLogDTO", description = "用户日志对象")
public class UsersLogDTO extends UsersLog {
    @ApiModelProperty(value = "用户名")
    private String usersName;

    public String getUsersName() {
        return usersName;
    }

    public void setUsersName(String usersName) {
        this.usersName = usersName;
    }
}
