package com.bsm.v4.system.model.vo.business.transferNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * CSV数据处理请求VO
 */
@ApiModel(value = "csvDataProcessRequestVO", description = "CSV数据处理请求对象")
public class CsvDataProcessRequestVO {

    /**
     * 有效数据行
     */
    @ApiModelProperty(value = "有效数据行")
    private List<Map<String, Object>> validRows;

    /**
     * 无效数据行
     */
    @ApiModelProperty(value = "无效数据行")
    private List<Map<String, Object>> invalidRows;

    /**
     * CSV文件路径
     */
    @ApiModelProperty(value = "CSV文件路径")
    private String csvFilePath;

    public List<Map<String, Object>> getValidRows() {
        return validRows;
    }

    public void setValidRows(List<Map<String, Object>> validRows) {
        this.validRows = validRows;
    }

    public List<Map<String, Object>> getInvalidRows() {
        return invalidRows;
    }

    public void setInvalidRows(List<Map<String, Object>> invalidRows) {
        this.invalidRows = invalidRows;
    }

    public String getCsvFilePath() {
        return csvFilePath;
    }

    public void setCsvFilePath(String csvFilePath) {
        this.csvFilePath = csvFilePath;
    }
}
