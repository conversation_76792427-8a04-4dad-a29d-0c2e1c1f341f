package com.bsm.v4.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;
import java.util.Date;

/**
 * Created by yancheng<PERSON>g on 2020/11/27.
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "GetUserResult",namespace = NameSpaceUrlConst.aNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "RealName",
        "SafePlatformCode",
        "UniqueUserName",
        "UserBirthDay",
        "UserEducation",
        "UserEmail",
        "UserFax",
        "UserID",
        "UserJoinDate",
        "UserLeaveDate",
        "UserMobile",
        "UserPassword",
        "UserPhone",
        "UserPicture",
        "UserSex",
        "UserStatus"
})
public class GetUserResult {
    @XmlElement(name = "RealName",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String RealName;
    @XmlElement(name = "SafePlatformCode",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String SafePlatformCode;
    @XmlElement(name = "UniqueUserName",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UniqueUserName;
    @XmlElement(name = "UserBirthDay",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserBirthDay;
    @XmlElement(name = "UserEducation",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserEducation;
    @XmlElement(name = "UserEmail",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserEmail;
    @XmlElement(name = "UserFax",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserFax;
    @XmlElement(name = "UserID",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserID;
    @XmlElement(name = "UserJoinDate",namespace = NameSpaceUrlConst.aNamespaceURI)
    private Date UserJoinDate;
    @XmlElement(name = "UserLeaveDate",namespace = NameSpaceUrlConst.aNamespaceURI)
    private Date UserLeaveDate;
    @XmlElement(name = "UserMobile",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserMobile;
    @XmlElement(name = "UserPassword",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserPassword;
    @XmlElement(name = "UserPhone",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserPhone;
    @XmlElement(name = "UserPicture",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserPicture;
    @XmlElement(name = "UserSex",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserSex;
    @XmlElement(name = "UserStatus",namespace = NameSpaceUrlConst.aNamespaceURI)
    private String UserStatus;

    public GetUserResult() {
    }

    public GetUserResult(String realName, String safePlatformCode, String uniqueUserName, String userBirthDay, String userEducation, String userEmail, String userFax, String userID, Date userJoinDate, Date userLeaveDate, String userMobile, String userPassword, String userPhone, String userPicture, String userSex, String userStatus) {
        RealName = realName;
        SafePlatformCode = safePlatformCode;
        UniqueUserName = uniqueUserName;
        UserBirthDay = userBirthDay;
        UserEducation = userEducation;
        UserEmail = userEmail;
        UserFax = userFax;
        UserID = userID;
        UserJoinDate = userJoinDate;
        UserLeaveDate = userLeaveDate;
        UserMobile = userMobile;
        UserPassword = userPassword;
        UserPhone = userPhone;
        UserPicture = userPicture;
        UserSex = userSex;
        UserStatus = userStatus;
    }

    public String getRealName() {
        return RealName;
    }

    public void setRealName(String realName) {
        RealName = realName;
    }

    public String getSafePlatformCode() {
        return SafePlatformCode;
    }

    public void setSafePlatformCode(String safePlatformCode) {
        SafePlatformCode = safePlatformCode;
    }

    public String getUniqueUserName() {
        return UniqueUserName;
    }

    public void setUniqueUserName(String uniqueUserName) {
        UniqueUserName = uniqueUserName;
    }

    public String getUserBirthDay() {
        return UserBirthDay;
    }

    public void setUserBirthDay(String userBirthDay) {
        UserBirthDay = userBirthDay;
    }

    public String getUserEducation() {
        return UserEducation;
    }

    public void setUserEducation(String userEducation) {
        UserEducation = userEducation;
    }

    public String getUserEmail() {
        return UserEmail;
    }

    public void setUserEmail(String userEmail) {
        UserEmail = userEmail;
    }

    public String getUserFax() {
        return UserFax;
    }

    public void setUserFax(String userFax) {
        UserFax = userFax;
    }

    public String getUserID() {
        return UserID;
    }

    public void setUserID(String userID) {
        UserID = userID;
    }

    public Date getUserJoinDate() {
        return UserJoinDate;
    }

    public void setUserJoinDate(Date userJoinDate) {
        UserJoinDate = userJoinDate;
    }

    public Date getUserLeaveDate() {
        return UserLeaveDate;
    }

    public void setUserLeaveDate(Date userLeaveDate) {
        UserLeaveDate = userLeaveDate;
    }

    public String getUserMobile() {
        return UserMobile;
    }

    public void setUserMobile(String userMobile) {
        UserMobile = userMobile;
    }

    public String getUserPassword() {
        return UserPassword;
    }

    public void setUserPassword(String userPassword) {
        UserPassword = userPassword;
    }

    public String getUserPhone() {
        return UserPhone;
    }

    public void setUserPhone(String userPhone) {
        UserPhone = userPhone;
    }

    public String getUserPicture() {
        return UserPicture;
    }

    public void setUserPicture(String userPicture) {
        UserPicture = userPicture;
    }

    public String getUserSex() {
        return UserSex;
    }

    public void setUserSex(String userSex) {
        UserSex = userSex;
    }

    public String getUserStatus() {
        return UserStatus;
    }

    public void setUserStatus(String userStatus) {
        UserStatus = userStatus;
    }
}
