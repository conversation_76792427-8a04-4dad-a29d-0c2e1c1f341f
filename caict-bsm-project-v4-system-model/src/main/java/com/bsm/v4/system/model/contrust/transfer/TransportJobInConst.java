package com.bsm.v4.system.model.contrust.transfer;

/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * compare
 * 0：待提交
 * 1：提交失败
 * 2：提交成功（待对比）
 * 3：对比中
 * 4：对比失败
 * 5：对比成功（无委待确认）
 * 6：无委确认中
 * 7：无委确认不通过
 * 8：无委确认通过（待审核）
 * 81:申请表待提交
 * 82:申请报提交失败
 * 83:申请表提交成功（待审核）
 * 9：审核中
 * 10：完结
 * <p>
 * 内网端
 * 10：无委确认通过（待审核）
 * 11: 无委审核中（提交审核处理中）
 * 12: 审核处理数据失败
 * 13: 审核处理数据成功
 */
public class TransportJobInConst {

    public static final String JOBIN_CONFIRM_PASS = "10";
    public static final String JOBIN_CHECKING = "11";
    public static final String JOBIN_CHECK_FAIL = "12";
    public static final String JOBIN_COMPLETE = "13";
}
