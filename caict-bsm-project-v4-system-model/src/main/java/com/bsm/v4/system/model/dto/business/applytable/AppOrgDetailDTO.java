package com.bsm.v4.system.model.dto.business.applytable;



/**
 * <AUTHOR>
 * @date 2019/12/27
 */
public class AppOrgDetailDTO {
    //申请类型
    private String appSubType;
    //机构名称
    private String orgName;

    //组织机构代码
    private String orgCode;
    //系统代码
    private String orgSysCode;
    //机构地址
    private String orgAddr;
    //机构邮编
    private String orgPost;
    //联系人
    private String orgLinkPerson;
    //联系电话
    private String orgPhone;
    //机构传真
    private String orgFax;
    //移动电话
    private String orgMobPhone;
    //电子邮箱
    private String orgMail;

    public String getAppSubType() {
        return appSubType;
    }

    public void setAppSubType(String appSubType) {
        this.appSubType = appSubType;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgSysCode() {
        return orgSysCode;
    }

    public void setOrgSysCode(String orgSysCode) {
        this.orgSysCode = orgSysCode;
    }

    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    public String getOrgPost() {
        return orgPost;
    }

    public void setOrgPost(String orgPost) {
        this.orgPost = orgPost;
    }

    public String getOrgLinkPerson() {
        return orgLinkPerson;
    }

    public void setOrgLinkPerson(String orgLinkPerson) {
        this.orgLinkPerson = orgLinkPerson;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgFax() {
        return orgFax;
    }

    public void setOrgFax(String orgFax) {
        this.orgFax = orgFax;
    }

    public String getOrgMobPhone() {
        return orgMobPhone;
    }

    public void setOrgMobPhone(String orgMobPhone) {
        this.orgMobPhone = orgMobPhone;
    }

    public String getOrgMail() {
        return orgMail;
    }

    public void setOrgMail(String orgMail) {
        this.orgMail = orgMail;
    }
}
