package com.bsm.v4.system.model.entity.security.login_aspx.send;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/12
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Envelope",namespace = NameSpaceUrlConst.mainNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "requestHeader",
        "requestBody"
})
public class RespEnvelope {
    @XmlElement(name="Header", namespace=NameSpaceUrlConst.mainNamespaceURI)
    private RequestHeader requestHeader;
    @XmlElement(name="Body", namespace=NameSpaceUrlConst.mainNamespaceURI)
    private RequestBody requestBody;

    public RespEnvelope() {
    }

    public RespEnvelope(RequestHeader requestHeader, RequestBody requestBody) {
        this.requestHeader = requestHeader;
        this.requestBody = requestBody;
    }

    public RequestHeader getRequestHeader() {
        return requestHeader;
    }

    public void setRequestHeader(RequestHeader requestHeader) {
        this.requestHeader = requestHeader;
    }

    public RequestBody getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(RequestBody requestBody) {
        this.requestBody = requestBody;
    }
}
