package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2021-8-13.
 * 用户日志对象
 */

@TableName("sys_users_log")
public class UsersLog {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "内容")
    @TableFieId("content")
    private String content;

    @ApiModelProperty(value = "类型。system:系统日志；business：业务日志")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "用户主键")
    @TableFieId("users_id")
    private String usersId;

    @ApiModelProperty(value = "更新时间")
    @TableFieId("update_date_time")
    private Date updateDateTime;

    @ApiModelProperty(value = "备注")
    @TableFieId("remark")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUsersId() {
        return usersId;
    }

    public void setUsersId(String usersId) {
        this.usersId = usersId;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
