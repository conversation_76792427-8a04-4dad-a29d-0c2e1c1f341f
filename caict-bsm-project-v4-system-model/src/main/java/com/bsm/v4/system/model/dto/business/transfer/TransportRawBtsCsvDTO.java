package com.bsm.v4.system.model.dto.business.transfer;

import com.opencsv.bean.CsvBindByName;


/**
 * csv实体类
 */

public class TransportRawBtsCsvDTO {

    @CsvBindByName(column = "CELL_NAME")
//    @CsvBindByPosition(position=0)
    private String cellName;
    @CsvBindByName(column = "CELL_ID")
//    @CsvBindByPosition(position=1)
    private String cellId;
    @CsvBindByName(column = "BTS_NAME")
//    @CsvBindByPosition(position=2)
    private String btsName;
    @CsvBindByName(column = "BTS_ID")
//    @CsvBindByPosition(position=3)
    private String btsId;
    @CsvBindByName(column = "TECH_TYPE")
//    @CsvBindByPosition(position=4)
    private String techType;
    @CsvBindByName(column = "LOCATION")
//    @CsvBindByPosition(position=5)
    private String location;
    @CsvBindByName(column = "LONGITUDE")
//    @CsvBindByPosition(position=6)
    private String longitude;
    @CsvBindByName(column = "LATITUDE")
//    @CsvBindByPosition(position=7)
    private String latitude;
    @CsvBindByName(column = "SEND_START_FREQ")
//    @CsvBindByPosition(position=8)
    private String sendStartFreq;
    @CsvBindByName(column = "SEND_END_FREQ")
//    @CsvBindByPosition(position=9)
    private String sendEndFreq;
    @CsvBindByName(column = "ACC_START_FREQ")
//    @CsvBindByPosition(position=10)
    private String accStartFreq;
    @CsvBindByName(column = "ACC_END_FREQ")
//    @CsvBindByPosition(position=11)
    private String accEndFreq;
    @CsvBindByName(column = "MAX_EMISSIVE_POWER")
//    @CsvBindByPosition(position=12)
    private String maxEmissivePower;
    @CsvBindByName(column = "HEIGHT")
//    @CsvBindByPosition(position=13)
    private String height;
    @CsvBindByName(column = "COUNTY")
//    @CsvBindByPosition(position=14)
    private String county;
    @CsvBindByName(column = "DEVICE_FACTORY")
//    @CsvBindByPosition(position=15)
    private String vendorName;
    @CsvBindByName(column = "DEVICE_MODEL")
//    @CsvBindByPosition(position=16)
    private String deviceModel;
    @CsvBindByName(column = "MODEL_CODE")
//    @CsvBindByPosition(position=17)
    private String modelCode;
    @CsvBindByName(column = "ANTENNA_GAIN")
//    @CsvBindByPosition(position=18)
    private String antennaGain;
    //天线类型
    @CsvBindByName(column = "ANTENNA_MODEL")
    private String antennaModel;
    //天线生产厂家
    @CsvBindByName(column = "ANTENNA_FACTORY")
    private String antennaFactory;
    //极化方式
    @CsvBindByName(column = "POLARIZATION_MODE")
    private String polarizationMode;
    //天线方位角
    @CsvBindByName(column = "ANTENNA_AZIMUTH")
    private String antennaAzimuth;
    //馈线系统总损耗
    @CsvBindByName(column = "FEEDER_LOSS")
    private String feederLoss;
    //海拔高度
    @CsvBindByName(column = "ALTITUDE")
    private String altitude;
    //设台年份
    @CsvBindByName(column = "SET_YEAR")
    private String setYear;
    //设台月份
    @CsvBindByName(column = "SET_MONTH")
    private String setMonth;
    //1.宏站2.直放站
    @CsvBindByName(column = "EXPAND_STATION")
    private String expandStation;

    //1.室内站2.室外站
    @CsvBindByName(column = "ATTRIBUTE_STATION")
    private String attributeStation;

    //收倾角
    @CsvBindByName(column = "AT_RANG")
    private String atRang;

    //发倾角
    @CsvBindByName(column = "AT_EANG")
    private String atEang;

    //服务半径
    @CsvBindByName(column = "ST_SERV_R")
    private String stServR;

    //拓展csv文件字段
    @CsvBindByName(column = "ST_SCENE")
    private String stScene;

    @CsvBindByName(column = "TRF_DATE")
    private String trfDate;

    @CsvBindByName(column = "TRF_USER")
    private String trfUser;

    @CsvBindByName(column = "TRF_DATA")
    private String trfData;

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendStartFreq() {
        return sendStartFreq;
    }

    public void setSendStartFreq(String sendStartFreq) {
        this.sendStartFreq = sendStartFreq;
    }

    public String getSendEndFreq() {
        return sendEndFreq;
    }

    public void setSendEndFreq(String sendEndFreq) {
        this.sendEndFreq = sendEndFreq;
    }

    public String getAccStartFreq() {
        return accStartFreq;
    }

    public void setAccStartFreq(String accStartFreq) {
        this.accStartFreq = accStartFreq;
    }

    public String getAccEndFreq() {
        return accEndFreq;
    }

    public void setAccEndFreq(String accEndFreq) {
        this.accEndFreq = accEndFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAntennaGain() {
        return antennaGain;
    }

    public void setAntennaGain(String antennaGain) {
        this.antennaGain = antennaGain;
    }

    public String getAntennaModel() {
        return antennaModel;
    }

    public void setAntennaModel(String antennaModel) {
        this.antennaModel = antennaModel;
    }

    public String getAntennaFactory() {
        return antennaFactory;
    }

    public void setAntennaFactory(String antennaFactory) {
        this.antennaFactory = antennaFactory;
    }

    public String getPolarizationMode() {
        return polarizationMode;
    }

    public void setPolarizationMode(String polarizationMode) {
        this.polarizationMode = polarizationMode;
    }

    public String getAntennaAzimuth() {
        return antennaAzimuth;
    }

    public void setAntennaAzimuth(String antennaAzimuth) {
        this.antennaAzimuth = antennaAzimuth;
    }

    public String getFeederLoss() {
        return feederLoss;
    }

    public void setFeederLoss(String feederLoss) {
        this.feederLoss = feederLoss;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public String getSetYear() {
        return setYear;
    }

    public void setSetYear(String setYear) {
        this.setYear = setYear;
    }

    public String getSetMonth() {
        return setMonth;
    }

    public void setSetMonth(String setMonth) {
        this.setMonth = setMonth;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public String getAtRang() {
        return atRang;
    }

    public void setAtRang(String atRang) {
        this.atRang = atRang;
    }

    public String getAtEang() {
        return atEang;
    }

    public void setAtEang(String atEang) {
        this.atEang = atEang;
    }

    public String getStServR() {
        return stServR;
    }

    public void setStServR(String stServR) {
        this.stServR = stServR;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }

    public String getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(String trfDate) {
        this.trfDate = trfDate;
    }

    public String getTrfUser() {
        return trfUser;
    }

    public void setTrfUser(String trfUser) {
        this.trfUser = trfUser;
    }

    public String getTrfData() {
        return trfData;
    }

    public void setTrfData(String trfData) {
        this.trfData = trfData;
    }
}
