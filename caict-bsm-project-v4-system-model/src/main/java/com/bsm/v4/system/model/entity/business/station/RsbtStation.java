package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("RSBT_STATION")
public class RsbtStation {

    @TableId("GUID")
    private String guid;

    /**
     * RSBT_NET外键
     */
    @TableFieId("NET_GUID")
    private String netGuid;
    /**
     * 组织机构id
     */
    @TableFieId("ORG_CODE")
    private String orgCode;

    /**
     * 无线电台（站）申请表编号
     */
    @TableFieId("APP_CODE")
    private String appCode;

    /**
     * 技术资料申报表类型
     */
    @TableFieId("STAT_APP_TYPE")
    private String statAppType;

    /**
     * 技术资料申报表编号
     */
    @TableFieId("STAT_TDI")
    private String statTdi;


    /**
     * 无线电台站名称
     */
    @TableFieId("STAT_NAME")
    private String statName;

    /**
     * 无线电台站地址
     */
    @TableFieId("STAT_ADDR")
    private String statAddr;

    /**
     * 无线电台站所在地地区编码
     */
    @TableFieId("STAT_AREA_CODE")
    private String statAreaCode;

    /**
     * 台站类别
     */
    @TableFieId("STAT_TYPE")
    private String statType;

    /**
     * 工作方式
     */
    @TableFieId("STAT_WORK")
    private String statWork;

    /**
     * 台站状态
     */
    @TableFieId("STAT_STATUS")
    private String statStatus;

    /**
     * （台站总）设备数量
     */
    @TableFieId("STAT_EQU_SUM")
    private int statEquSum;

    /**
     * 台站经度(西经为负数)
     */
    @TableFieId("STAT_LG")
    private Double statLg;
    @TableFieId("STAT_LA")
    private Double statLa;
    /**
     * 海拔高度
     */
    @TableFieId("STAT_AT")
    private Double statAt;

    /**
     * 启用日期
     */
    @TableFieId("STAT_DATE_START")
    private Date statDateStart;

    /**
     * 协调主管部门；
     * 国际协调和登记资料代码或名称
     */
    @TableFieId("MEMO")
    private String memo;

    @TableFieId("ST_SCENE")
    private String stScene;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getNetGuid() {
        return netGuid;
    }

    public void setNetGuid(String netGuid) {
        this.netGuid = netGuid;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getStatName() {
        return statName;
    }

    public void setStatName(String statName) {
        this.statName = statName;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public String getStatAreaCode() {
        return statAreaCode;
    }

    public void setStatAreaCode(String statAreaCode) {
        this.statAreaCode = statAreaCode;
    }

    public String getStatType() {
        return statType;
    }

    public void setStatType(String statType) {
        this.statType = statType;
    }

    public String getStatWork() {
        return statWork;
    }

    public void setStatWork(String statWork) {
        this.statWork = statWork;
    }

    public String getStatStatus() {
        return statStatus;
    }

    public void setStatStatus(String statStatus) {
        this.statStatus = statStatus;
    }

    public int getStatEquSum() {
        return statEquSum;
    }

    public void setStatEquSum(int statEquSum) {
        this.statEquSum = statEquSum;
    }

    public Double getStatLg() {
        return statLg;
    }

    public void setStatLg(Double statLg) {
        this.statLg = statLg;
    }

    public Double getStatLa() {
        return statLa;
    }

    public void setStatLa(Double statLa) {
        this.statLa = statLa;
    }

    public Double getStatAt() {
        return statAt;
    }

    public void setStatAt(Double statAt) {
        this.statAt = statAt;
    }

    public Date getStatDateStart() {
        return statDateStart;
    }

    public void setStatDateStart(Date statDateStart) {
        this.statDateStart = statDateStart;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }
}
