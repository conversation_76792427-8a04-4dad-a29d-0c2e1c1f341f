package com.bsm.v4.system.model.dto.business.station;

import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/25
 */

public class StationBakDTO extends RsbtStationBak {
    private String stationCode;
    private String netType;
    private List<SectionDTO> sectionDTOS;

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public List<SectionDTO> getSectionDTOS() {
        return sectionDTOS;
    }

    public void setSectionDTOS(List<SectionDTO> sectionDTOS) {
        this.sectionDTOS = sectionDTOS;
    }
}
