package com.bsm.v4.system.model.entity.security.login_aspx.send;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/13
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "soapenv",namespace = NameSpaceUrlConst.mainNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "requstBodySon"
})
public class RequestBody {

    @XmlElement(name = "requestBody",namespace = NameSpaceUrlConst.sendNamespaceURI)
    private RequstBodySon requstBodySon;

    public RequestBody() {
    }

    public RequstBodySon getRequstBodySon() {
        return requstBodySon;
    }

    public void setRequstBodySon(RequstBodySon requstBodySon) {
        this.requstBodySon = requstBodySon;
    }
}
