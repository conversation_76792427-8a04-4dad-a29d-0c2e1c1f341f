package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.business.station.*;
import com.bsm.v4.system.model.entity.business.transfer.LogTransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;


import java.util.ArrayList;
import java.util.List;

/**
 * 入库参数
 */

public class ProcessingDataDTO {

    private String userId;
    private String jobId;
    private String appGuid;
    private String dateType;
    //批量处理基站
    private List<RsbtTraffic> rsbtTrafficList;
    private List<RsbtNet> rsbtNetList;

    //批量处理基站
    private List<RsbtStation> rsbtStationList;
    //批量处理基站状态
    private List<RsbtStationAppendix> rsbtStationAppendixList;

    private List<RsbtStationT> rsbtStationTList;

    private List<RsbtStationBak> rsbtStationBakList;

    //批量处理天线
    private List<RsbtAntfeed> rsbtAntfeedList;
    //批量处理天线状态
    private List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList;

    private List<RsbtAntfeedT> rsbtAntfeedTList;

    //批量处理设备
    private List<RsbtEqu> rsbtEquList;
    //批量处理设备状态
    private List<RsbtEquAppendix> rsbtEquAppendixList;

    private List<RsbtEquT> rsbtEquTList;

    //批量处理参数
    private List<RsbtFreq> rsbtFreqList;
    //批量处理参数状态
    private List<RsbtFreqAppendix> rsbtFreqAppendixList;

    private List<RsbtFreqT> rsbtFreqTList;

    private List<RsbtEaf> rsbtEafList;

    //csv文件数据
    List<TransportRawBts> transportRawBtsList;
    //日志数据
    List<LogTransportJob> logTransportJobList;

    //批量添加总表
    private List<AsyncRawBts> asyncRawBtsList;

    public ProcessingDataDTO() {
    }

    public ProcessingDataDTO(String userId, String jobId, String appGuid, String dateType, List<RsbtNet> rsbtNetList, List<RsbtStation> rsbtStationList,
                             List<RsbtStationAppendix> rsbtStationAppendixList, List<RsbtStationT> rsbtStationTList, List<RsbtStationBak> rsbtStationBakList,
                             List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList, List<RsbtAntfeedT> rsbtAntfeedTList,
                             List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList, List<RsbtTraffic> rsbtTrafficList,
                             List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList,
                             List<AsyncRawBts> asyncRawBtsList) {
        this.userId = userId;
        this.jobId = jobId;
        this.appGuid = appGuid;
        this.dateType = dateType;
        this.rsbtNetList = rsbtNetList;
        this.rsbtStationList = (rsbtStationList == null ? new ArrayList<>() : rsbtStationList);
        this.rsbtTrafficList = (rsbtTrafficList == null ? new ArrayList<>() : rsbtTrafficList);
        this.rsbtStationAppendixList = (rsbtStationAppendixList == null ? new ArrayList<>() : rsbtStationAppendixList);
        this.rsbtStationTList = (rsbtStationTList == null ? new ArrayList<>() : rsbtStationTList);
        this.rsbtStationBakList = (rsbtStationBakList == null ? new ArrayList<>() : rsbtStationBakList);

        this.rsbtAntfeedList = (rsbtAntfeedList == null ? new ArrayList<>() : rsbtAntfeedList);
        this.rsbtAntfeedAppendixList = (rsbtAntfeedAppendixList == null ? new ArrayList<>() : rsbtAntfeedAppendixList);
        this.rsbtAntfeedTList = (rsbtAntfeedTList == null ? new ArrayList<>() : rsbtAntfeedTList);

        this.rsbtEquList = (rsbtEquList == null ? new ArrayList<>() : rsbtEquList);
        this.rsbtEquAppendixList = (rsbtEquAppendixList == null ? new ArrayList<>() : rsbtEquAppendixList);
        this.rsbtEquTList = (rsbtEquTList == null ? new ArrayList<>() : rsbtEquTList);

        this.rsbtFreqList = (rsbtFreqList == null ? new ArrayList<>() : rsbtFreqList);
        this.rsbtFreqAppendixList = (rsbtFreqAppendixList == null ? new ArrayList<>() : rsbtFreqAppendixList);
        this.rsbtFreqTList = (rsbtFreqTList == null ? new ArrayList<>() : rsbtFreqTList);
        this.rsbtEafList = rsbtEafList;
        this.asyncRawBtsList = (asyncRawBtsList == null ? new ArrayList<>() : asyncRawBtsList);
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    public List<RsbtTraffic> getRsbtTrafficList() {
        return rsbtTrafficList;
    }

    public void setRsbtTrafficList(List<RsbtTraffic> rsbtTrafficList) {
        this.rsbtTrafficList = rsbtTrafficList;
    }

    public List<RsbtNet> getRsbtNetList() {
        return rsbtNetList;
    }

    public void setRsbtNetList(List<RsbtNet> rsbtNetList) {
        this.rsbtNetList = rsbtNetList;
    }

    public List<RsbtStation> getRsbtStationList() {
        return rsbtStationList;
    }

    public void setRsbtStationList(List<RsbtStation> rsbtStationList) {
        this.rsbtStationList = rsbtStationList;
    }

    public List<RsbtStationAppendix> getRsbtStationAppendixList() {
        return rsbtStationAppendixList;
    }

    public void setRsbtStationAppendixList(List<RsbtStationAppendix> rsbtStationAppendixList) {
        this.rsbtStationAppendixList = rsbtStationAppendixList;
    }

    public List<RsbtStationT> getRsbtStationTList() {
        return rsbtStationTList;
    }

    public void setRsbtStationTList(List<RsbtStationT> rsbtStationTList) {
        this.rsbtStationTList = rsbtStationTList;
    }

    public List<RsbtStationBak> getRsbtStationBakList() {
        return rsbtStationBakList;
    }

    public void setRsbtStationBakList(List<RsbtStationBak> rsbtStationBakList) {
        this.rsbtStationBakList = rsbtStationBakList;
    }

    public List<RsbtAntfeed> getRsbtAntfeedList() {
        return rsbtAntfeedList;
    }

    public void setRsbtAntfeedList(List<RsbtAntfeed> rsbtAntfeedList) {
        this.rsbtAntfeedList = rsbtAntfeedList;
    }

    public List<RsbtAntfeedAppendix> getRsbtAntfeedAppendixList() {
        return rsbtAntfeedAppendixList;
    }

    public void setRsbtAntfeedAppendixList(List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList) {
        this.rsbtAntfeedAppendixList = rsbtAntfeedAppendixList;
    }

    public List<RsbtAntfeedT> getRsbtAntfeedTList() {
        return rsbtAntfeedTList;
    }

    public void setRsbtAntfeedTList(List<RsbtAntfeedT> rsbtAntfeedTList) {
        this.rsbtAntfeedTList = rsbtAntfeedTList;
    }

    public List<RsbtEqu> getRsbtEquList() {
        return rsbtEquList;
    }

    public void setRsbtEquList(List<RsbtEqu> rsbtEquList) {
        this.rsbtEquList = rsbtEquList;
    }

    public List<RsbtEquAppendix> getRsbtEquAppendixList() {
        return rsbtEquAppendixList;
    }

    public void setRsbtEquAppendixList(List<RsbtEquAppendix> rsbtEquAppendixList) {
        this.rsbtEquAppendixList = rsbtEquAppendixList;
    }

    public List<RsbtEquT> getRsbtEquTList() {
        return rsbtEquTList;
    }

    public void setRsbtEquTList(List<RsbtEquT> rsbtEquTList) {
        this.rsbtEquTList = rsbtEquTList;
    }

    public List<RsbtFreq> getRsbtFreqList() {
        return rsbtFreqList;
    }

    public void setRsbtFreqList(List<RsbtFreq> rsbtFreqList) {
        this.rsbtFreqList = rsbtFreqList;
    }

    public List<RsbtFreqAppendix> getRsbtFreqAppendixList() {
        return rsbtFreqAppendixList;
    }

    public void setRsbtFreqAppendixList(List<RsbtFreqAppendix> rsbtFreqAppendixList) {
        this.rsbtFreqAppendixList = rsbtFreqAppendixList;
    }

    public List<RsbtFreqT> getRsbtFreqTList() {
        return rsbtFreqTList;
    }

    public void setRsbtFreqTList(List<RsbtFreqT> rsbtFreqTList) {
        this.rsbtFreqTList = rsbtFreqTList;
    }

    public List<RsbtEaf> getRsbtEafList() {
        return rsbtEafList;
    }

    public void setRsbtEafList(List<RsbtEaf> rsbtEafList) {
        this.rsbtEafList = rsbtEafList;
    }

    public List<TransportRawBts> getTransportRawBtsList() {
        return transportRawBtsList;
    }

    public void setTransportRawBtsList(List<TransportRawBts> transportRawBtsList) {
        this.transportRawBtsList = transportRawBtsList;
    }

    public List<LogTransportJob> getLogTransportJobList() {
        return logTransportJobList;
    }

    public void setLogTransportJobList(List<LogTransportJob> logTransportJobList) {
        this.logTransportJobList = logTransportJobList;
    }

    public List<AsyncRawBts> getAsyncRawBtsList() {
        return asyncRawBtsList;
    }

    public void setAsyncRawBtsList(List<AsyncRawBts> asyncRawBtsList) {
        this.asyncRawBtsList = asyncRawBtsList;
    }
}
