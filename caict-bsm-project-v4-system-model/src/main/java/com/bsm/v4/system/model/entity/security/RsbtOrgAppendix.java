package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018-08-03
 */

@TableName("RSBT_ORG_APPENDIX")
public class RsbtOrgAppendix {

    /**
     * 系统ID
     */
    @ApiModelProperty(value = "主键ID、与Org主键一致")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;

    @ApiModelProperty(value = "状态 1：启用；2：停用")
    @TableFieId("STATUS")
    private Integer status;

    @ApiModelProperty(value = "是否同步")
    @TableFieId("IS_SYNC")
    private String isSync;

    @ApiModelProperty(value = "类型")
    @TableFieId("TYPE")
    private String type;

    @ApiModelProperty(value = "社会信用代码")
    @TableFieId("SUC_CODE")
    private String sucCode;

    @ApiModelProperty(value = "父级id")
    @TableFieId("PARENT_ID")
    private String parentId;

    @ApiModelProperty(value = "区域详情")
    @TableFieId("REGION_DETAILS")
    private String regionDetails;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSucCode() {
        return sucCode;
    }

    public void setSucCode(String sucCode) {
        this.sucCode = sucCode;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getRegionDetails() {
        return regionDetails;
    }

    public void setRegionDetails(String regionDetails) {
        this.regionDetails = regionDetails;
    }
}
