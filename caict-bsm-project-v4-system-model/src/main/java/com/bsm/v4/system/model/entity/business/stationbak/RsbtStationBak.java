package com.bsm.v4.system.model.entity.business.stationbak;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("RSBT_STATION_BAK")
public class RsbtStationBak {

    @TableId("GUID")
    private String guid;
    /**
     * 无线电台站名称
     */
    @TableFieId("STAT_NAME")
    private String statName;
    /**
     * 技术体制
     */
    @TableFieId("NET_TS")
    private String netTs;
    /**
     * 无线电台站地址
     */
    @TableFieId("STAT_ADDR")
    private String statAddr;
    /**
     * 台站经度(西经为负数)
     */
    @TableFieId("STAT_LG")
    private Double statLg;
    @TableFieId("STAT_LA")
    private Double statLa;
    /**
     * 启用日期
     */
    @TableFieId("STAT_DATE_START")
    private Date statDateStart;
    /**
     * 基站编号
     */
    @TableFieId("ST_C_CODE")
    private String stCCode;

    /**
     * 制式（2-2G;3-3G;4-4G;5-5G）
     */
    @TableFieId("GEN_NUM")
    private String genNum;

    @TableFieId("COUNTY")
    private String county;

    /**
     * 基站guid（新疆临时字段）
     */
    @TableFieId("USER_GUID")
    private String userGuid;

    @TableFieId("ORG_TYPE")
    private String orgType;

    /**
     * 数据类型（1-新增；2-变更；3-删除）
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    /**
     * 1 宏站  2 拉远站
     */
    @TableFieId("EXPAND_STATION")
    private String expandStation;

    /**
     * 1 宏站  2 拉远站
     */
    @TableFieId("ATTRIBUTE_STATION")
    private String attributeStation;

    /**
     * 插入时间
     */
    @TableFieId("BAK_DATE")
    private Date bakDate;

    /**
     * 申请表编号
     */
    @TableFieId("APP_CODE")
    private String appCode;

    /**
     * 同步状态
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    /**
     * jobguid
     */
    @TableFieId("APP_GUID")
    private String appGuid;

    /**
     * 校验状态
     */
    @TableFieId("IS_VALID")
    private String isValid;

    /**
     * 技术资料表编号
     */
    @TableFieId("STAT_TDI")
    private String statTdi;

    /**
     * 1 展示  2 不展示 3 上次任务不通过的数据
     */
    @TableFieId("IS_SHOW")
    private String isShow;

    @TableFieId("STATION_GUID")
    private String stationGuid;

    @TableFieId("STAT_AT")
    private Double statAt;

    @TableFieId("ST_SCENE")
    private String stScene;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStatName() {
        return statName;
    }

    public void setStatName(String statName) {
        this.statName = statName;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public Double getStatLg() {
        return statLg;
    }

    public void setStatLg(Double statLg) {
        this.statLg = statLg;
    }

    public Double getStatLa() {
        return statLa;
    }

    public void setStatLa(Double statLa) {
        this.statLa = statLa;
    }

    public Date getStatDateStart() {
        return statDateStart;
    }

    public void setStatDateStart(Date statDateStart) {
        this.statDateStart = statDateStart;
    }

    public String getStCCode() {
        return stCCode;
    }

    public void setStCCode(String stCCode) {
        this.stCCode = stCCode;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public Date getBakDate() {
        return bakDate;
    }

    public void setBakDate(Date bakDate) {
        this.bakDate = bakDate;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public Double getStatAt() {
        return statAt;
    }

    public void setStatAt(Double statAt) {
        this.statAt = statAt;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }
}