package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;



@TableName("RSBT_ANTFEED")
public class RsbtAntfeed {

    /**
     * GUID
     */
    @TableId("GUID")
    private String guid;

    /**
     * 台站GUID
     */
    @TableFieId("STATION_GUID")
    private String stationGuid;

    /**
     * 工作方式
     */
    @TableFieId("ANT_WORK_TYPE")
    private String antWorkType;

    /**
     * 极化方式
     */
    @TableFieId("ANT_POLE")
    private String antPole;

    /**
     * 收极化方式
     */
    @TableFieId("ANT_RPOLE ")
    private String antRpole;

    /**
     * 发极化方式
     */
    @TableFieId("ANT_EPOLE")
    private String antEpole;

    /**
     * 天线类型
     */
    @TableFieId("ANT_TYPE")
    private String antType;

    /**
     * 天线型号
     */
    @TableFieId("ANT_MODEL")
    private String antModel;

    /**
     * 天线生产厂家
     */
    @TableFieId("ANT_MENU")
    private String antMenu;

    /**
     * 天线据地高度
     */
    @TableFieId("ANT_HIGHT")
    private double antHight;

    /**
     * 天线增益
     */
    @TableFieId("ANT_GAIN")
    private double antGain;

    /**
     * 天线发增益  E、V 单位：dBi
     */
    @TableFieId("ANT_EGAIN")
    private double antEgain;

    /**
     * 天线收增益 E、V 单位：dBi
     */
    @TableFieId("ANT_RGAIN")
    private double antRgain;

    /**
     * 最大辐射方位角
     */
    @TableFieId("ANT_ANGLE")
    private double antAngle;

    /**
     * 天线尺寸
     */
    @TableFieId("ANT_SIZE")
    private String antSize;

    /**
     * 馈线生产厂家
     */
    @TableFieId("FEED_MENU")
    private String feedMenu;

    /**
     * 馈线型号
     */
    @TableFieId("FEED_MODEL")
    private String feedModel;

    /**
     * 馈线长度
     */
    @TableFieId("FEED_LENGTH")
    private double feedLength;

    /**
     * 馈线系统总损耗
     */
    @TableFieId("FEED_LOSE")
    private double feedLose;

    /**
     * ADBMS_PK_ANTENO（天线序号）
     */
    @TableFieId("ANT_CODE")
    private long antCode;

    /**
     * ADBMS_PK_FEEDLINENO（馈线序号）
     */
    @TableFieId("FEED_CODE")
    private long feedCode;

    //频段是否合规(1-正常；3-不合规)
    @TableFieId("IS_VALID")
    private String isValid;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getAntWorkType() {
        return antWorkType;
    }

    public void setAntWorkType(String antWorkType) {
        this.antWorkType = antWorkType;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public String getAntRpole() {
        return antRpole;
    }

    public void setAntRpole(String antRpole) {
        this.antRpole = antRpole;
    }

    public String getAntEpole() {
        return antEpole;
    }

    public void setAntEpole(String antEpole) {
        this.antEpole = antEpole;
    }

    public String getAntType() {
        return antType;
    }

    public void setAntType(String antType) {
        this.antType = antType;
    }

    public String getAntModel() {
        return antModel;
    }

    public void setAntModel(String antModel) {
        this.antModel = antModel;
    }

    public String getAntMenu() {
        return antMenu;
    }

    public void setAntMenu(String antMenu) {
        this.antMenu = antMenu;
    }

    public double getAntHight() {
        return antHight;
    }

    public void setAntHight(double antHight) {
        this.antHight = antHight;
    }

    public double getAntGain() {
        return antGain;
    }

    public void setAntGain(double antGain) {
        this.antGain = antGain;
    }

    public double getAntEgain() {
        return antEgain;
    }

    public void setAntEgain(double antEgain) {
        this.antEgain = antEgain;
    }

    public double getAntRgain() {
        return antRgain;
    }

    public void setAntRgain(double antRgain) {
        this.antRgain = antRgain;
    }

    public double getAntAngle() {
        return antAngle;
    }

    public void setAntAngle(double antAngle) {
        this.antAngle = antAngle;
    }

    public String getAntSize() {
        return antSize;
    }

    public void setAntSize(String antSize) {
        this.antSize = antSize;
    }

    public String getFeedMenu() {
        return feedMenu;
    }

    public void setFeedMenu(String feedMenu) {
        this.feedMenu = feedMenu;
    }

    public String getFeedModel() {
        return feedModel;
    }

    public void setFeedModel(String feedModel) {
        this.feedModel = feedModel;
    }

    public double getFeedLength() {
        return feedLength;
    }

    public void setFeedLength(double feedLength) {
        this.feedLength = feedLength;
    }

    public double getFeedLose() {
        return feedLose;
    }

    public void setFeedLose(double feedLose) {
        this.feedLose = feedLose;
    }

    public long getAntCode() {
        return antCode;
    }

    public void setAntCode(long antCode) {
        this.antCode = antCode;
    }

    public long getFeedCode() {
        return feedCode;
    }

    public void setFeedCode(long feedCode) {
        this.feedCode = feedCode;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }
}
