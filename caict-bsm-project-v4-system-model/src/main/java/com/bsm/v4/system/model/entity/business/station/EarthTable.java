package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


/**
 * <AUTHOR>
 * @date 2020/12/24
 * <p>
 * 单A应受保护的台站清单-卫星地球站
 * <p>
 * 单B应受保护的台站清单-其他台站
 */

@TableName("FSA_EARTH_STATION")
public class EarthTable {

    @TableId("GUID")
    private String guid;

    /**
     * 保护清单识别号
     */
    @TableFieId("ID_CODE")
    private String idCode;

    /**
     * 用户单位名称
     */
    @TableFieId("COMP_NAME")
    private String compName;

    /**
     * 联系人
     */
    @TableFieId("LINK_PERSON")
    private String linkPerson;

    /**
     * 联系电话
     */
    @TableFieId("PHONE")
    private String phone;

    /**
     * 地球站类型(1-双向通信地球站;2-单收地球站;3-广播电视地球站;4-微波站;5-射电天文台;6-其他)
     */
    @TableFieId("STAT_TYPE")
    private String statType;

    /**
     * 地球站有效证
     */
    @TableFieId("STAT_DETAIL")
    private String statDetail;

    /**
     * 发射频率下限
     */
    @TableFieId("FREQ_EFB")
    private String freqEfb;

    /**
     * 发射频率上限
     */
    @TableFieId("FREQ_EFE")
    private String freqEfe;

    /**
     * 接收频率下限
     */
    @TableFieId("FREQ_RFB")
    private String freqRfb;

    /**
     * 接收频率上限
     */
    @TableFieId("FREQ_RFE")
    private String freqRfe;

    /**
     * 对应的空间无线电台名称
     */
    @TableFieId("SPACE_NAME")
    private String spaceName;

    /**
     * 位置
     */
    @TableFieId("LOCATION")
    private String location;

    /**
     * 经度
     */
    @TableFieId("LONGITUDE")
    private String longitude;

    /**
     * 纬度
     */
    @TableFieId("LATITUDE")
    private String latitude;

    /**
     * 台站信息确认
     */
    @TableFieId("STAT_CONFIRM")
    private String statConfirm;

    /**
     * 确认时间
     */
    @TableFieId("CONFIRM_DATE")
    private String confirmDate;

    /**
     * 核验人
     */
    @TableFieId("VERIFIER")
    private String verifier;

    /**
     * 备注
     */
    @TableFieId("DEMO")
    private String demo;

    /**
     * 表格类型
     */
    @TableFieId("TABLE_TYPE")
    private String tableType;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getCompName() {
        return compName;
    }

    public void setCompName(String compName) {
        this.compName = compName;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getStatType() {
        return statType;
    }

    public void setStatType(String statType) {
        this.statType = statType;
    }

    public String getStatDetail() {
        return statDetail;
    }

    public void setStatDetail(String statDetail) {
        this.statDetail = statDetail;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getStatConfirm() {
        return statConfirm;
    }

    public void setStatConfirm(String statConfirm) {
        this.statConfirm = statConfirm;
    }

    public String getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(String confirmDate) {
        this.confirmDate = confirmDate;
    }

    public String getVerifier() {
        return verifier;
    }

    public void setVerifier(String verifier) {
        this.verifier = verifier;
    }

    public String getDemo() {
        return demo;
    }

    public void setDemo(String demo) {
        this.demo = demo;
    }

    public String getTableType() {
        return tableType;
    }

    public void setTableType(String tableType) {
        this.tableType = tableType;
    }
}