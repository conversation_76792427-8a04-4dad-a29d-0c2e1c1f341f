package com.bsm.v4.system.model.dto.security;

import com.bsm.v4.system.model.entity.security.Users;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;



@ApiModel(value = "usersDTO", description = "用户对象")
public class UsersDTO extends Users {


    //登陆信息
    @ApiModelProperty(value = "登录名")
    private String loginName;

    @ApiModelProperty(value = "登录密码")
    private String password;

    @ApiModelProperty(value = "旧密码")
    private String passwordOld;

    @ApiModelProperty(value = "最近登录时间")
    private String lastDate;

    @ApiModelProperty(value = "最近登录方式")
    private String lastWay;

    @ApiModelProperty(value = "最近登录ip")
    private String lastIp;

    @ApiModelProperty(value = "登录id")
    private String loginId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "所属地区信息",hidden = true)
    private RegionDTO regionDTO;

    @ApiModelProperty(value = "所属角色名称")
    private String roleName;

    @ApiModelProperty(value = "所属角色信息",hidden = true)
    private RoleDTO roleDTO;

    @ApiModelProperty(value = "所属菜单",hidden = true)
    private List<MenuDTO> menuDTOs;

    @ApiModelProperty(value = "所属公司名称")
    private String orgName;

    @ApiModelProperty(value = "所属公司",hidden = true)
    private OrgDTO orgDTO;
    @ApiModelProperty(value = "平台信息登录名")
    private String srrcUserCode;

    @ApiModelProperty(value = "平台信息用户名")
    private String srrcUserName;

    @ApiModelProperty(value = "平台信息机构code")
    private String srrcOrgCode;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPasswordOld() {
        return passwordOld;
    }

    public void setPasswordOld(String passwordOld) {
        this.passwordOld = passwordOld;
    }

    public String getLastDate() {
        return lastDate;
    }

    public void setLastDate(String lastDate) {
        this.lastDate = lastDate;
    }

    public String getLastWay() {
        return lastWay;
    }

    public void setLastWay(String lastWay) {
        this.lastWay = lastWay;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public RegionDTO getRegionDTO() {
        return regionDTO;
    }

    public void setRegionDTO(RegionDTO regionDTO) {
        this.regionDTO = regionDTO;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public RoleDTO getRoleDTO() {
        return roleDTO;
    }

    public void setRoleDTO(RoleDTO roleDTO) {
        this.roleDTO = roleDTO;
    }

    public List<MenuDTO> getMenuDTOs() {
        return menuDTOs;
    }

    public void setMenuDTOs(List<MenuDTO> menuDTOs) {
        this.menuDTOs = menuDTOs;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public OrgDTO getOrgDTO() {
        return orgDTO;
    }

    public void setOrgDTO(OrgDTO orgDTO) {
        this.orgDTO = orgDTO;
    }

    public String getSrrcUserCode() {
        return srrcUserCode;
    }

    public void setSrrcUserCode(String srrcUserCode) {
        this.srrcUserCode = srrcUserCode;
    }

    public String getSrrcUserName() {
        return srrcUserName;
    }

    public void setSrrcUserName(String srrcUserName) {
        this.srrcUserName = srrcUserName;
    }

    public String getSrrcOrgCode() {
        return srrcOrgCode;
    }

    public void setSrrcOrgCode(String srrcOrgCode) {
        this.srrcOrgCode = srrcOrgCode;
    }
}
