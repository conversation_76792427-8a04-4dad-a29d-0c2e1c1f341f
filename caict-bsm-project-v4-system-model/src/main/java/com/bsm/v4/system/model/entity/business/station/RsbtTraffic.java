package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * 用户数及流量表
 */

@TableName("RSBT_TRAFFIC")
public class RsbtTraffic {
    // ID
    @TableId("TRAFFIC_GUID")
    private String trafficGuid;
    @TableFieId("STATION_GUID")
    private String stationGuid;
    // 扇区标识码
    @TableFieId("SECTION_CODE")
    private String sectionCode;
    // 技术体制
    @TableFieId("NET_TS")
    private String netTs;
    // 载波发射频率起单位MHz
    @TableFieId("FREQ_EFE_C")
    private Double freqEfeC;
    // 载波发射频率止单位MHz
    @TableFieId("FREQ_EFB_C")
    private Double freqEfbC;
    //用户数或流量统计年份
    @TableFieId("TRF_DATE")
    private Date trfDate;
    //平均忙时激活用户数
    @TableFieId("TRF_USER")
    private Double trfUser;
    //平均忙时激活用户流量
    @TableFieId("TRF_DATA")
    private Double trfData;

    public String getTrafficGuid() {
        return trafficGuid;
    }

    public void setTrafficGuid(String trafficGuid) {
        this.trafficGuid = trafficGuid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getSectionCode() {
        return sectionCode;
    }

    public void setSectionCode(String sectionCode) {
        this.sectionCode = sectionCode;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public Double getFreqEfeC() {
        return freqEfeC;
    }

    public void setFreqEfeC(Double freqEfeC) {
        this.freqEfeC = freqEfeC;
    }

    public Double getFreqEfbC() {
        return freqEfbC;
    }

    public void setFreqEfbC(Double freqEfbC) {
        this.freqEfbC = freqEfbC;
    }

    public Date getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(Date trfDate) {
        this.trfDate = trfDate;
    }

    public Double getTrfUser() {
        return trfUser;
    }

    public void setTrfUser(Double trfUser) {
        this.trfUser = trfUser;
    }

    public Double getTrfData() {
        return trfData;
    }

    public void setTrfData(Double trfData) {
        this.trfData = trfData;
    }
}
