package com.bsm.v4.system.model.vo.es;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * 数据es搜索对象.
 *
 * <AUTHOR>
 * @since 2023年8月10日 11点33分
 **/

@ApiModel(value = "esAsyncRawBtsVO", description = "数据es搜索对象")
public class EsAsyncRawBtsVO {

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    @ApiModelProperty(value = "技术体制")
    private String techType;

    @ApiModelProperty(value = "地区")
    private String county;

    @ApiModelProperty(value = "制式")
    private String genNum;

    @ApiModelProperty(value = "运营商类型")
    private String orgType;

    @ApiModelProperty(value = "用户数或流量统计年份月份")
    private String trfDateStr;

    @ApiModelProperty(value = "用户数或流量统计年份月份")
    private Date trfDate;

    @ApiModelProperty(value = "基站覆盖场景")
    private String stScene;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getTrfDateStr() {
        return trfDateStr;
    }

    public void setTrfDateStr(String trfDateStr) {
        this.trfDateStr = trfDateStr;
    }

    public Date getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(Date trfDate) {
        this.trfDate = trfDate;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }
}
