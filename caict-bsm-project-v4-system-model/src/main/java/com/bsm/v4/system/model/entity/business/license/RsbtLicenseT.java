package com.bsm.v4.system.model.entity.business.license;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @date 2020/9/11
 */

@TableName("RSBT_LICENSE_T")
public class RsbtLicenseT {
    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "执照状态")
    @TableFieId("LICENSE_STATE")
    private Long licenseState;

    @ApiModelProperty(value = "所属区域")
    @TableFieId("LICENSE_COUNTY")
    private String licenseCounty;

    @ApiModelProperty(value = "许可证号")
    @TableFieId("LICENSE_NO")
    private String licenseNo;

    @ApiModelProperty(value = "制式")
    @TableFieId("TECH_TYPE")
    private String techType;

    @ApiModelProperty(value = "运营商")
    @TableFieId("ORG_TYPE")
    private String orgType;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Long getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(Long licenseState) {
        this.licenseState = licenseState;
    }

    public String getLicenseCounty() {
        return licenseCounty;
    }

    public void setLicenseCounty(String licenseCounty) {
        this.licenseCounty = licenseCounty;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }
}
