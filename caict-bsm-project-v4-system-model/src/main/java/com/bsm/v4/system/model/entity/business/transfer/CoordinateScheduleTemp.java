package com.bsm.v4.system.model.entity.business.transfer;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


/**
 * <AUTHOR>
 * @date 2019/7/2
 */

@TableName("COORDINATE_SCHEDULE_TEMP")
public class CoordinateScheduleTemp {

    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;

    /**
     * 审核任务id
     */
    @TableFieId("APP_GUID")
    private String appGuid;

    /**
     * 中转任务id
     */
    @TableFieId("JOB_GUID")
    private String jobGuid;
//    /**
//     * 申请表id
//     */
//    @TableField("APPLICATION_CODE")
//    private String applicationCode;
    /**
     * 是否为有效数据
     */
    @TableFieId("IS_VALID")
    private Long isValid;
    /**
     * 小区名称
     */
    @TableFieId("CELL_NAME")
    private String cellName;
    /**
     * 小区识别码
     */
    @TableFieId("CELL_ID")
    private String cellId;
    /**
     * 基站名称
     */
    @TableFieId("BTS_NAME")
    private String btsName;
    /**
     * 基站识别码
     */
    @TableFieId("BTS_ID")
    private String btsId;
    /**
     * 技术体制
     */
    @TableFieId("TECH_TYPE")
    private String techType;
    /**
     * 台址
     */
    @TableFieId("LOCATION")
    private String location;
    /**
     * 经度
     */
    @TableFieId("LONGITUDE")
    private String longitude;
    /**
     * 纬度
     */
    @TableFieId("LATITUDE")
    private String latitude;
    /**
     * 发射起始频率
     */
    @TableFieId("SEND_START_FREQ")
    private String sendStartFreq;
    /**
     * 发射终止频率
     */
    @TableFieId("SEND_END_FREQ")
    private String sendEndFreq;
    /**
     * 接收起始频率
     */
    @TableFieId("ACC_START_FREQ")
    private String accStartFreq;
    /**
     * 接收终止频率
     */
    @TableFieId("ACC_END_FREQ")
    private String accEndFreq;
    /**
     * 最大发射功率
     */
    @TableFieId("MAX_EMISSIVE_POWER")
    private String maxEmissivePower;
    /**
     * 天线距地高度
     */
    @TableFieId("HEIGHT")
    private String height;
    /**
     * 数据变更类型(1：新增；2：变更；3：删除。默认首次传送值为1)
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    @TableFieId("COUNTY")
    private String county;

    /**
     * 是否已处理 0-未处理 1-处理中 2-已处理
     */
    @TableFieId("IS_HANDLE")
    private String isHandle;

    @TableFieId("USER_GUID")
    private String userId;

    @TableFieId("VENDOR_NAME")
    private String vendorName;
    @TableFieId("DEVICE_MODEL")
    private String deviceModel;
    @TableFieId("MODEL_CODE")
    private String modelCode;
    @TableFieId("ANTENNA_GAIN")
    private String antennaGain;

    //制式
    @TableFieId("GEN_NUM")
    private String genNum;

    //天线类型
    @TableFieId("ANTENNA_MODEL")
    private String antennaModel;
    //天线生产厂家
    @TableFieId("ANTENNA_FACTORY")
    private String antennaFactory;
    //极化方式
    @TableFieId("POLARIZATION_MODE")
    private String polarizationMode;
    //天线方位角
    @TableFieId("ANTENNA_AZIMUTH")
    private String antennaAzimuth;
    //馈线系统总损耗
    @TableFieId("FEEDER_LOSS")
    private String feederLoss;
    //海拔高度
    @TableFieId("ALTITUDE")
    private String altitude;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public Long getIsValid() {
        return isValid;
    }

    public void setIsValid(Long isValid) {
        this.isValid = isValid;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendStartFreq() {
        return sendStartFreq;
    }

    public void setSendStartFreq(String sendStartFreq) {
        this.sendStartFreq = sendStartFreq;
    }

    public String getSendEndFreq() {
        return sendEndFreq;
    }

    public void setSendEndFreq(String sendEndFreq) {
        this.sendEndFreq = sendEndFreq;
    }

    public String getAccStartFreq() {
        return accStartFreq;
    }

    public void setAccStartFreq(String accStartFreq) {
        this.accStartFreq = accStartFreq;
    }

    public String getAccEndFreq() {
        return accEndFreq;
    }

    public void setAccEndFreq(String accEndFreq) {
        this.accEndFreq = accEndFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getIsHandle() {
        return isHandle;
    }

    public void setIsHandle(String isHandle) {
        this.isHandle = isHandle;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAntennaGain() {
        return antennaGain;
    }

    public void setAntennaGain(String antennaGain) {
        this.antennaGain = antennaGain;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getAntennaModel() {
        return antennaModel;
    }

    public void setAntennaModel(String antennaModel) {
        this.antennaModel = antennaModel;
    }

    public String getAntennaFactory() {
        return antennaFactory;
    }

    public void setAntennaFactory(String antennaFactory) {
        this.antennaFactory = antennaFactory;
    }

    public String getPolarizationMode() {
        return polarizationMode;
    }

    public void setPolarizationMode(String polarizationMode) {
        this.polarizationMode = polarizationMode;
    }

    public String getAntennaAzimuth() {
        return antennaAzimuth;
    }

    public void setAntennaAzimuth(String antennaAzimuth) {
        this.antennaAzimuth = antennaAzimuth;
    }

    public String getFeederLoss() {
        return feederLoss;
    }

    public void setFeederLoss(String feederLoss) {
        this.feederLoss = feederLoss;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }
}