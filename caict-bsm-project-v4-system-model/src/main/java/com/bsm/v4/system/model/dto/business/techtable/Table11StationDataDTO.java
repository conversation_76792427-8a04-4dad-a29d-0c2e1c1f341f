package com.bsm.v4.system.model.dto.business.techtable;



import java.util.Date;

/**
 * Created by yancheng<PERSON>g on 2020/2/25.
 */

public class Table11StationDataDTO {
    private String guid;
    //申请表编号
    private String appCode;
    //台站名称
    private String statName;
    //技术资料表申请类型（0-新增，1-变更）
    private String techType;
    //台站地址
    private String statAddr;
    //基站编号
    private String stCCode;
    //扇区数量
    private Integer stCSum;
    //台站经度（西经为负数）
    private Double statLg;
    //台站纬度（南纬为负数）
    private Double statLa;
    //海拔高度
    private Double statAt;
    //服务半径
    private Double stServR;
    //启用日期
    private Date statDateStart;

    //申报表类型
    private String statAppType;

    //技术资料申报表编号
    private String statTdi;

    //备注
    private String memo;

    //app guid
    private String appGuid;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getStatName() {
        return statName;
    }

    public void setStatName(String statName) {
        this.statName = statName;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public String getStCCode() {
        return stCCode;
    }

    public void setStCCode(String stCCode) {
        this.stCCode = stCCode;
    }

    public Integer getStCSum() {
        return stCSum;
    }

    public void setStCSum(Integer stCSum) {
        this.stCSum = stCSum;
    }

    public Double getStatLg() {
        return statLg;
    }

    public void setStatLg(Double statLg) {
        this.statLg = statLg;
    }

    public Double getStatLa() {
        return statLa;
    }

    public void setStatLa(Double statLa) {
        this.statLa = statLa;
    }

    public Double getStatAt() {
        return statAt;
    }

    public void setStatAt(Double statAt) {
        this.statAt = statAt;
    }

    public Double getStServR() {
        return stServR;
    }

    public void setStServR(Double stServR) {
        this.stServR = stServR;
    }

    public Date getStatDateStart() {
        return statDateStart;
    }

    public void setStatDateStart(Date statDateStart) {
        this.statDateStart = statDateStart;
    }

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }
}
