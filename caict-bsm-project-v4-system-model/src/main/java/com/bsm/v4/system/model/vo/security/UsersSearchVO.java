package com.bsm.v4.system.model.vo.security;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * @Title: UsersSearchVO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.vo.security
 * @Date 2023/8/16 10:10
 * @description:
 */

@ApiModel(value = "usersSearchVO", description = "用户信息搜索对象")
public class UsersSearchVO {


    @ApiModelProperty(value = "角色主键")
    private String roleId;

    @ApiModelProperty(value = "查询条件")
    private String isWhere;

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getIsWhere() {
        return isWhere;
    }

    public void setIsWhere(String isWhere) {
        this.isWhere = isWhere;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
