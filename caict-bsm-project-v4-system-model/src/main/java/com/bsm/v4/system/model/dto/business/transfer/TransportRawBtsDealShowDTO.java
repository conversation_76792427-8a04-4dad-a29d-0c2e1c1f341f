package com.bsm.v4.system.model.dto.business.transfer;



/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * jobGuid: 任务
 * type: 类型
 * compare: 流程状态
 * transportRawBtsDealNew: 新增数量
 * transportRawBtsDealUpdate: 变更数量
 * transportRawBtsDealDelete: 注销数量
 */

public class TransportRawBtsDealShowDTO {

    private String jobGuid;
    private String type;
    private String compare;
    private int transportRawBtsDealNew;
    private int transportRawBtsDealUpdate;
    private int transportRawBtsDealDelete;

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCompare() {
        return compare;
    }

    public void setCompare(String compare) {
        this.compare = compare;
    }

    public int getTransportRawBtsDealNew() {
        return transportRawBtsDealNew;
    }

    public void setTransportRawBtsDealNew(int transportRawBtsDealNew) {
        this.transportRawBtsDealNew = transportRawBtsDealNew;
    }

    public int getTransportRawBtsDealUpdate() {
        return transportRawBtsDealUpdate;
    }

    public void setTransportRawBtsDealUpdate(int transportRawBtsDealUpdate) {
        this.transportRawBtsDealUpdate = transportRawBtsDealUpdate;
    }

    public int getTransportRawBtsDealDelete() {
        return transportRawBtsDealDelete;
    }

    public void setTransportRawBtsDealDelete(int transportRawBtsDealDelete) {
        this.transportRawBtsDealDelete = transportRawBtsDealDelete;
    }
}
