package com.bsm.v4.system.model.dto.business.station;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.List;

/**
 * Created by yanchengpeng on 2019/6/4.
 */

@ApiModel(value = "stationdownload 使用的DTO")
public class StationDownloadDTO {

    @ApiModelProperty(value = "jobId")
    private String jobId;
    @ApiModelProperty(value = "申请表编号（可选）")
    private String applytable;
    @ApiModelProperty(value = "类别（可选）(1：新增；2：变更；3：删除)")
    private String dataType;
    ;
    @ApiModelProperty(value = "扇区名称（可选）")
    private String cellName;
    @ApiModelProperty(value = "扇区识别码（可选）")
    private String cellId;
    @ApiModelProperty(value = "基站名称（可选）")
    private String btsName;
    @ApiModelProperty(value = "基站识别码（可选）")
    private String btsId;
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startDate;
    @ApiModelProperty(value = "结束时间", required = true)
    private Long endDate;
    @ApiModelProperty(value = "办件流水号")
    private String jobCode;
    @ApiModelProperty(value = "guid集合")
    private List<String> guidList;

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getApplytable() {
        return applytable;
    }

    public void setApplytable(String applytable) {
        this.applytable = applytable;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public List<String> getGuidList() {
        return guidList;
    }

    public void setGuidList(List<String> guidList) {
        this.guidList = guidList;
    }
}
