package com.bsm.v4.system.model.vo.business.rule;

import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dsy62 on 2018-05-11.
 * 文件校验规则
 * id:id
 * orgGuid:组织机构guid
 * netTs:技术制式
 * genNum:信号
 * freqEfb:发射频率下限
 * freqEfe:发射频率上限
 * freqRfb:接收频率上限
 * freqRfe:接收频率下限
 * fileNo:频率使用许可证号或批准文号
 * state:状态，1：启用；2：停用
 */

@ApiModel(value = "fsaCheckRuleVO", description = "文件校验对象")
public class FsaCheckRuleVO {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "组织机构guid")
    private String orgGuid;
    @ApiModelProperty(value = "技术制式")
    private String netTs;
    @ApiModelProperty(value = "信号")
    private String genNum;
    @ApiModelProperty(value = "发射频率下限")
    private Double freqEfb;
    @ApiModelProperty(value = "发射频率上限")
    private Double freqEfe;
    @ApiModelProperty(value = "接收频率上限")
    private Double freqRfb;
    @ApiModelProperty(value = "接收频率下限")
    private Double freqRfe;
    @ApiModelProperty(value = "频率使用许可证号或批准文号")
    private String fileNo;
    @ApiModelProperty(value = "态，1：启用；2：停用")
    private String state;
    @ApiModelProperty(value = "组织机构类型")
    private String orgType;
    @ApiModelProperty(value = "频率期望时间")
    private Date expireDate;
    @ApiModelProperty(value = "频率开始时间")
    private Date startDate;
    @ApiModelProperty(value = "频率结束时间")
    private Date endDate;

    private String[] ids;

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    private String orgName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgGuid() {
        return orgGuid;
    }

    public void setOrgGuid(String orgGuid) {
        this.orgGuid = orgGuid;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public Double getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(Double freqEfb) {
        this.freqEfb = freqEfb;
    }

    public Double getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(Double freqEfe) {
        this.freqEfe = freqEfe;
    }

    public Double getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(Double freqRfb) {
        this.freqRfb = freqRfb;
    }

    public Double getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(Double freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String[] getIds() {
        return ids;
    }

    public void setIds(String[] ids) {
        this.ids = ids;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
