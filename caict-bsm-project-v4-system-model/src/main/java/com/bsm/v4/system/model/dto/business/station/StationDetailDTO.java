package com.bsm.v4.system.model.dto.business.station;

import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import io.swagger.annotations.ApiModelProperty;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/26
 */

public class StationDetailDTO {

    @ApiModelProperty(value = "基站id")
    private String stationId;

    //组织机构Id
    private String orgId;
    //技术制式
    private String netType;
    //基站集合
    private List<RsbtStation> rsbtStations;

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public List<RsbtStation> getRsbtStations() {
        return rsbtStations;
    }

    public void setRsbtStations(List<RsbtStation> rsbtStations) {
        this.rsbtStations = rsbtStations;
    }
}
