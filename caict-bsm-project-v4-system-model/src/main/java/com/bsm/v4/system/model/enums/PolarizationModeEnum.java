package com.bsm.v4.system.model.enums;

/**
 * Created by dsy62 on 2019-11-21.
 *
 * 极化方式 枚举
 */
public enum PolarizationModeEnum {

    V("V","垂直线极化"),H("H","水平线极化"),CR("CR","右旋圆极化"),CL("CL","左旋圆极化"),ODL("ODL","双极化"),QT("QT","其它极化方式");

    //成员变量
    private String type;
    private String name;

    //构造方法
    PolarizationModeEnum(String type, String name){
        this.type = type;
        this.name = name;
    }

    //数据打印
    public void print(){
        System.out.println("type:"+this.type+",name:"+this.name);
    }

    //返回类型
    public String getType(){
        return this.type;
    }

    //返回名称
    public String getName(){
        return this.name;
    }
}
