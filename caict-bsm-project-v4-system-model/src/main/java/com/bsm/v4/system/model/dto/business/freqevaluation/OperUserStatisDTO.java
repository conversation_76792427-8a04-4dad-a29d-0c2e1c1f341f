package com.bsm.v4.system.model.dto.business.freqevaluation;

import com.bsm.v4.system.model.entity.business.freqevaluation.OperUserStatis;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @description 忙时激活用户数/用户流量统计表
 * @date 2023年8月24日 11点13分
 */
@ApiModel(value = "operUserStatisDTO", description = "忙时激活用户数/用户流量统计表对象")
public class OperUserStatisDTO extends OperUserStatis {

    @ApiModelProperty(value = "地区名")
    private String areaName;

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
