package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2019-10-24.
 * 角色对象
 */

@TableName("sys_role")
public class Role {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "角色编号")
    @TableFieId("code")
    private String code;

    @ApiModelProperty(value = "角色名称")
    @TableFieId("name")
    private String name;

    @ApiModelProperty(value = "类型")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "权重")
    @TableFieId("sort")
    private Integer sort;

    @ApiModelProperty(value = "等级")
    @TableFieId("role_level")
    private Integer roleLevel;

    @ApiModelProperty(value = "保存时间")
    @TableFieId("update_date_time")
    private Date updateDateTime;

    @ApiModelProperty(value = "状态;1：启用；2：停用")
    @TableFieId("status")
    private Integer status;

    @ApiModelProperty(value = "角色备注")
    @TableFieId("remark")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getRoleLevel() {
        return roleLevel;
    }

    public void setRoleLevel(Integer roleLevel) {
        this.roleLevel = roleLevel;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
