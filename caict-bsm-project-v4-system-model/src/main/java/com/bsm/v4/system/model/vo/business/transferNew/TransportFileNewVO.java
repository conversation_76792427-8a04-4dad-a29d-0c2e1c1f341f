package com.bsm.v4.system.model.vo.business.transferNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 传输文件表VO
 */
@ApiModel(value = "transportFileNewVO", description = "传输文件表对象")
public class TransportFileNewVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 关联的任务ID
     */
    @ApiModelProperty(value = "关联的任务ID")
    private Long jobId;

    /**
     * 原始文件名称
     */
    @ApiModelProperty(value = "原始文件名称")
    private String fileLocalName;

    /**
     * 重命名后的文件名称
     */
    @ApiModelProperty(value = "重命名后的文件名称")
    private String fileRenamed;

    /**
     * 文件存储路径
     */
    @ApiModelProperty(value = "文件存储路径")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @ApiModelProperty(value = "文件大小（字节）")
    private Long fileSize;

    /**
     * 文件状态（0-创建，1-上传成功，2-处理完成，3-处理异常）
     */
    @ApiModelProperty(value = "文件状态（0-创建，1-上传成功，2-处理完成，3-处理异常）")
    private Integer fileState;

    /**
     * 文件类型（0-附件，1-CSV数据文件，2-材料附件）
     */
    @ApiModelProperty(value = "文件类型（0-附件，1-CSV数据文件，2-材料附件）")
    private String fileType;

    /**
     * 数据类型（全量、增量、注销等）
     */
    @ApiModelProperty(value = "数据类型（全量、增量、注销等）")
    private String dataType;

    /**
     * 文件扩展名
     */
    @ApiModelProperty(value = "文件扩展名")
    private String fileExtension;

    /**
     * 文件MIME类型
     */
    @ApiModelProperty(value = "文件MIME类型")
    private String mimeType;

    /**
     * 文件MD5值
     */
    @ApiModelProperty(value = "文件MD5值")
    private String fileMd5;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @ApiModelProperty(value = "是否删除（0-未删除，1-已删除）")
    private Integer isDeleted;

    /**
     * 上传者
     */
    @ApiModelProperty(value = "上传者")
    private String uploadedBy;

    /**
     * 文件描述
     */
    @ApiModelProperty(value = "文件描述")
    private String fileDescription;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getFileLocalName() {
        return fileLocalName;
    }

    public void setFileLocalName(String fileLocalName) {
        this.fileLocalName = fileLocalName;
    }

    public String getFileRenamed() {
        return fileRenamed;
    }

    public void setFileRenamed(String fileRenamed) {
        this.fileRenamed = fileRenamed;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getFileState() {
        return fileState;
    }

    public void setFileState(Integer fileState) {
        this.fileState = fileState;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getFileMd5() {
        return fileMd5;
    }

    public void setFileMd5(String fileMd5) {
        this.fileMd5 = fileMd5;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(String uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public String getFileDescription() {
        return fileDescription;
    }

    public void setFileDescription(String fileDescription) {
        this.fileDescription = fileDescription;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
