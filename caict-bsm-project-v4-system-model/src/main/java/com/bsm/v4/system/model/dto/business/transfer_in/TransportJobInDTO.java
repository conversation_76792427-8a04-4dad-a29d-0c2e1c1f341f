package com.bsm.v4.system.model.dto.business.transfer_in;

import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * jobDateStart：查询开始时间
 * jobDateEnd：查询结束时间
 */

public class TransportJobInDTO extends TransportJob {

    private Date appDateStart;
    private Date appDateEnd;

    private String userType;

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    //所属csv文件集合
    private List<TransportFileDTO> transportFileDTOList;
    //所属附件集合
    private List<TransportFileDTO> transportFileAttachedDTOList;

    //附件信息
    private String fileGuid;
    private String fileName;
    private String fileState;

    public Date getAppDateStart() {
        return appDateStart;
    }

    public void setAppDateStart(Date appDateStart) {
        this.appDateStart = appDateStart;
    }

    public Date getAppDateEnd() {
        return appDateEnd;
    }

    public void setAppDateEnd(Date appDateEnd) {
        this.appDateEnd = appDateEnd;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public List<TransportFileDTO> getTransportFileDTOList() {
        return transportFileDTOList;
    }

    public void setTransportFileDTOList(List<TransportFileDTO> transportFileDTOList) {
        this.transportFileDTOList = transportFileDTOList;
    }

    public List<TransportFileDTO> getTransportFileAttachedDTOList() {
        return transportFileAttachedDTOList;
    }

    public void setTransportFileAttachedDTOList(List<TransportFileDTO> transportFileAttachedDTOList) {
        this.transportFileAttachedDTOList = transportFileAttachedDTOList;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }
}
