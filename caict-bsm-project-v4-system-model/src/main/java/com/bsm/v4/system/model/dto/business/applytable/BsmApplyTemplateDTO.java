package com.bsm.v4.system.model.dto.business.applytable;

import io.swagger.annotations.ApiModelProperty;


/**
 * Created by dsy62 on 2020-12-8.
 * 外网端申请表打印
 */
public class BsmApplyTemplateDTO {

    @ApiModelProperty(value = "编号")
    private String appCode;

    @ApiModelProperty(value = "台站设置使用人")
    private String orgName;

    @ApiModelProperty(value = "通信地址")
    private String orgAddr;

    @ApiModelProperty(value = "统一社会信用代码")
    private String orgCode;

    @ApiModelProperty(value = "邮政编码")
    private String orgPost;

    @ApiModelProperty(value = "联系人")
    private String orgLinkPerson;

    @ApiModelProperty(value = "联系电话")
    private String orgPhone;

    @ApiModelProperty(value = "电子信箱")
    private String orgMail;

    @ApiModelProperty(value = "申请类型")
    private String dataType;

    @ApiModelProperty(value = "申请类型-新增")
    private String dataTypeNew;

    @ApiModelProperty(value = "申请类型-变更")
    private String dataTypeUpdate;

    @ApiModelProperty(value = "申请类型-注销")
    private String dataTypeDelete;

    @ApiModelProperty(value = "申请类型-延续")
    private String dataTypeOn;

    @ApiModelProperty(value = "基站数量")
    private String stationCount;

    //关联查询技术资料信息
    @ApiModelProperty(value = "任务主键")
    private String jobGuid;

    @ApiModelProperty(value = "用户主键")
    private String userGuid;

    @ApiModelProperty(value = "区域编码")
    private String regionCode;

    @ApiModelProperty(value = "信号")
    private String genNum;

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAddr() {
        return orgAddr;
    }

    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgPost() {
        return orgPost;
    }

    public void setOrgPost(String orgPost) {
        this.orgPost = orgPost;
    }

    public String getOrgLinkPerson() {
        return orgLinkPerson;
    }

    public void setOrgLinkPerson(String orgLinkPerson) {
        this.orgLinkPerson = orgLinkPerson;
    }

    public String getOrgPhone() {
        return orgPhone;
    }

    public void setOrgPhone(String orgPhone) {
        this.orgPhone = orgPhone;
    }

    public String getOrgMail() {
        return orgMail;
    }

    public void setOrgMail(String orgMail) {
        this.orgMail = orgMail;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataTypeNew() {
        return dataTypeNew;
    }

    public void setDataTypeNew(String dataTypeNew) {
        this.dataTypeNew = dataTypeNew;
    }

    public String getDataTypeUpdate() {
        return dataTypeUpdate;
    }

    public void setDataTypeUpdate(String dataTypeUpdate) {
        this.dataTypeUpdate = dataTypeUpdate;
    }

    public String getDataTypeDelete() {
        return dataTypeDelete;
    }

    public void setDataTypeDelete(String dataTypeDelete) {
        this.dataTypeDelete = dataTypeDelete;
    }

    public String getDataTypeOn() {
        return dataTypeOn;
    }

    public void setDataTypeOn(String dataTypeOn) {
        this.dataTypeOn = dataTypeOn;
    }

    public String getStationCount() {
        return stationCount;
    }

    public void setStationCount(String stationCount) {
        this.stationCount = stationCount;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }
}
