package com.bsm.v4.system.model.vo.business.freqevaluation;

import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;


import java.util.Date;


@ApiModel(value = "freqEvaluationJobVO", description = "省级公众移动通信系统用户数及用户流量任务VO")
public class FreqEvaluationJobVO extends FreqEvaluationJob {

    private Date startTime;
    private Date endTime;
    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
