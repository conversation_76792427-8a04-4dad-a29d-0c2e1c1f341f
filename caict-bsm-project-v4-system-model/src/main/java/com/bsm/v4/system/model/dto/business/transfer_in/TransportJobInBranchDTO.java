package com.bsm.v4.system.model.dto.business.transfer_in;

import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.dto.business.station.StationDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */

public class TransportJobInBranchDTO extends TransportJobBranch {
    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    private Date appDateStart;
    private Date appDateEnd;

    private String isValid;
    private String dataStatus;

    private List<String> guids;

    //所属附件集合
    private List<TransportFileDTO> transportFileAttachedDTOList;

    //申请表信息
    private List<RsbtApplyDTO> applyDTOList;

    //关联台站信息
    private List<StationDTO> stationDTOList;

    //附件信息
    private String fileGuid;
    private String fileName;
    private String fileState;

    //运营商类型
    private String userType;

    //审批意见
    private String poinion;

    private String stationName;
    private String stationAddr;

    //总条数
    private int total;

    //对应的申请表guid
    private String appGuid;

    @ApiModelProperty(value = "区域名称")
    private String regionName;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public Date getAppDateStart() {
        return appDateStart;
    }

    public void setAppDateStart(Date appDateStart) {
        this.appDateStart = appDateStart;
    }

    public Date getAppDateEnd() {
        return appDateEnd;
    }

    public void setAppDateEnd(Date appDateEnd) {
        this.appDateEnd = appDateEnd;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(String dataStatus) {
        this.dataStatus = dataStatus;
    }

    public List<String> getGuids() {
        return guids;
    }

    public void setGuids(List<String> guids) {
        this.guids = guids;
    }

    public List<TransportFileDTO> getTransportFileAttachedDTOList() {
        return transportFileAttachedDTOList;
    }

    public void setTransportFileAttachedDTOList(List<TransportFileDTO> transportFileAttachedDTOList) {
        this.transportFileAttachedDTOList = transportFileAttachedDTOList;
    }

    public List<RsbtApplyDTO> getApplyDTOList() {
        return applyDTOList;
    }

    public void setApplyDTOList(List<RsbtApplyDTO> applyDTOList) {
        this.applyDTOList = applyDTOList;
    }

    public List<StationDTO> getStationDTOList() {
        return stationDTOList;
    }

    public void setStationDTOList(List<StationDTO> stationDTOList) {
        this.stationDTOList = stationDTOList;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getPoinion() {
        return poinion;
    }

    public void setPoinion(String poinion) {
        this.poinion = poinion;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationAddr() {
        return stationAddr;
    }

    public void setStationAddr(String stationAddr) {
        this.stationAddr = stationAddr;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }
}
