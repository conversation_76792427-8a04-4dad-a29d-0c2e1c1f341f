package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import com.caictframework.utils.annotation.TableParentId;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2019-10-24.
 * 菜单对象
 */

@TableName("sys_menu")
public class Menu {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "名称")
    @TableFieId("name")
    private String name;

    @ApiModelProperty(value = "类型")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "图标")
    @TableFieId("img")
    private String img;

    @ApiModelProperty(value = "菜单别名")
    @TableFieId("key_name")
    private String keyName;

    @ApiModelProperty(value = "URL地址")
    @TableFieId("url")
    private String url;

    @ApiModelProperty(value = "父级id")
    @TableFieId("parent_id")
    @TableParentId
    private String parentId;

    @ApiModelProperty(value = "权重")
    @TableFieId("sort")
    private Integer sort;

    @ApiModelProperty(value = "保存时间")
    @TableFieId("update_date_time")
    private Date updateDateTime;

    @ApiModelProperty(value = "状态;1：启用；2：停用")
    @TableFieId("status")
    private Integer status;

    @ApiModelProperty(value = "备注")
    @TableFieId("remark")
    private String remark;

    @ApiModelProperty(value = "查询参数")
    @TableFieId("query")
    private String query;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }
}
