package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTemp;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * status:状态
 */

public class CoordinateScheduleTempDTO extends CoordinateScheduleTemp {

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    //状态
    private String status;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
