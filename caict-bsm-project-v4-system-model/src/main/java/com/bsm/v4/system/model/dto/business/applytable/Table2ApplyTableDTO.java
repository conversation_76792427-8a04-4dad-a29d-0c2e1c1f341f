package com.bsm.v4.system.model.dto.business.applytable;



import java.util.Date;
import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/11.
 */
public class Table2ApplyTableDTO {
    private String appCode;

    private String memo;

    private String appPerson;

    private Date appDate;

    private ApplyOrgDTO orgDTO;

    private ApplyNetDTO netDTO;

    private List<ApplyFreqDTO> freqDTOs;

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getAppPerson() {
        return appPerson;
    }

    public void setAppPerson(String appPerson) {
        this.appPerson = appPerson;
    }

    public Date getAppDate() {
        return appDate;
    }

    public void setAppDate(Date appDate) {
        this.appDate = appDate;
    }

    public ApplyOrgDTO getOrgDTO() {
        return orgDTO;
    }

    public void setOrgDTO(ApplyOrgDTO orgDTO) {
        this.orgDTO = orgDTO;
    }

    public ApplyNetDTO getNetDTO() {
        return netDTO;
    }

    public void setNetDTO(ApplyNetDTO netDTO) {
        this.netDTO = netDTO;
    }

    public List<ApplyFreqDTO> getFreqDTOs() {
        return freqDTOs;
    }

    public void setFreqDTOs(List<ApplyFreqDTO> freqDTOs) {
        this.freqDTOs = freqDTOs;
    }
}
