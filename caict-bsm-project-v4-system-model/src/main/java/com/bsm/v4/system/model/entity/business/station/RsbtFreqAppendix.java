package com.bsm.v4.system.model.entity.business.station;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("RSBT_FREQ_APPENDIX")
public class RsbtFreqAppendix {

    /**
     * 系统ID
     */
    @TableId("GUID")
    private String guid;

    /**
     * 是否同步
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;

    @TableFieId("SECTION_NAME")
    private String sectionName;

    //1-5G干扰分析通过；2-5G干扰分析不通过
    @TableFieId("ANALYSIS_STATUS")
    private String analysisStatus;
    //频段是否合规(1-正常；3-不合规)
    @TableFieId("IS_VALID")
    private String isValid;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getSectionName() {
        return sectionName;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public String getAnalysisStatus() {
        return analysisStatus;
    }

    public void setAnalysisStatus(String analysisStatus) {
        this.analysisStatus = analysisStatus;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }
}
