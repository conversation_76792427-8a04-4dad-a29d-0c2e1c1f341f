package com.bsm.v4.system.model.dto.business.rule;

import com.bsm.v4.system.model.entity.business.rule.FsaCheckRule;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.List;

/**
 * Created by dsy62 on 2018-05-11.
 * <p>
 * orgName:组织机构名称
 * orgType:组织机构类型
 * <p>
 * ids:ids
 * fsaCheckRuleDTOList:子级集合
 */
@ApiModel(value = "fsaCheckRuleDTO", description = "文件校验对象")
public class FsaCheckRuleDTO extends FsaCheckRule {

    private String orgName;
    private String orgType;

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    //子级集合
    private List<FsaCheckRuleDTO> fsaCheckRuleDTOList;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public List<FsaCheckRuleDTO> getFsaCheckRuleDTOList() {
        return fsaCheckRuleDTOList;
    }

    public void setFsaCheckRuleDTOList(List<FsaCheckRuleDTO> fsaCheckRuleDTOList) {
        this.fsaCheckRuleDTOList = fsaCheckRuleDTOList;
    }
}
