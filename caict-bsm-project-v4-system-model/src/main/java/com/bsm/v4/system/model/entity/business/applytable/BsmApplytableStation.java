package com.bsm.v4.system.model.entity.business.applytable;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-03
 */

@TableName("BSM_APPLYTABLE_STATION")
public class BsmApplytableStation {

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;
    /**
     * 唯一标志
     */
    @TableId("GUID")
    private String guid;
    /**
     * 申请表ID
     */
    @TableFieId("APPLYTABLE_GUID")
    private String applytableGuid;
    /**
     * 台站ID
     */
    @TableFieId("STATION_GUID")
    private String stationGuid;

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getApplytableGuid() {
        return applytableGuid;
    }

    public void setApplytableGuid(String applytableGuid) {
        this.applytableGuid = applytableGuid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }
}
