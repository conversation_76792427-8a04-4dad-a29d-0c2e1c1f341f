package com.bsm.v4.system.model.dto.business.applytable;

import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;


import java.util.Map;

public class BsmApplytableImportDTO {

    private RsbtApply bsmApplytable;
    private Map<String, RsbtStation> stationMap;

    public RsbtApply getBsmApplytable() {
        return bsmApplytable;
    }

    public void setBsmApplytable(RsbtApply bsmApplytable) {
        this.bsmApplytable = bsmApplytable;
    }

    public Map<String, RsbtStation> getStationMap() {
        return stationMap;
    }

    public void setStationMap(Map<String, RsbtStation> stationMap) {
        this.stationMap = stationMap;
    }
}
