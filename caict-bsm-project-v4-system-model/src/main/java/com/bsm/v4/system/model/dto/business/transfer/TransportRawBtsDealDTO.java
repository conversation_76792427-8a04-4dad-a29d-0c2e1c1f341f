package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDeal;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


/**
 * Created by dengsy on 2020-05-06.
 * <p>
 * guids: id数组
 * <p>
 * logDetail:异常信息
 */

public class TransportRawBtsDealDTO extends TransportRawBtsDeal {

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    private String[] guids;

    //异常信息
    private String logDetail;

    private String jobId;

    private String type;
    //执照编号
    private String licenseCode;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String[] getGuids() {
        return guids;
    }

    public void setGuids(String[] guids) {
        this.guids = guids;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }
}
