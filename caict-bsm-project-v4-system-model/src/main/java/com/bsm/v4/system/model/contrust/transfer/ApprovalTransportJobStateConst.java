package com.bsm.v4.system.model.contrust.transfer;

/**
 * Created by dengsy on 2020-04-22.
 * <p>
 * approved
 * 0：未提交
 * 1：同意
 * 2：不同意
 * <p>
 * compare
 * 0：待对比
 * 1：对比中
 * 2：对比失败
 * 3：对比成功（待提交）
 * 4：提交失败
 * 5：提交成功

 * 9：待审核入库（干扰协调提交成功）
 * 10：审核入库中
 * 11：审核入库失败
 * 12：完结(专网)
 * 12:分任务待生成申请表(内网)
 * 13:分任务生成申请表中
 * 14:分任务生成申请表失败
 * 15:分任务生成申请表成功待入库
 * 16:分任务提交审核中（入库中）
 * 17:分任务审核失败
 * 18:分任务审核入库成功
 * 19:放弃(审核不通过)
 *
 * 6：干扰协调中
 * 7：干扰协调待提交
 * 8：干扰协调提交失败
 */
public class ApprovalTransportJobStateConst {

    public static final long APPROVED_UPLOAD = 0;

    public static final long APPROVED_PASS = 1;

    public static final long APPROVED_NOT_PASS = 2;

    public static final String TO_BE_COMPARED = "0";

    public static final String COMPARE_PROCESSING = "1";

    public static final String COMPARE_FAIL = "2";

    public static final String COMPARE_SUCCESS = "3";

    public static final String COMMIT_FAIL = "4";

    public static final String COMMIT_SUCCESS = "5";

    public static final String COORDINATE_PROCESSING = "6";

    public static final String COORDINATE_COMMIT_UPLOAD = "7";

    public static final String COORDINATE_COMMIT_FAILURE = "8";

    public static final String COORDINATE_COMMIT_SUCCESS = "9";

    public static final String CHECKIN_PROCESSING = "10";

    public static final String CHECKIN_FAIL = "11";

    public static final String COMPLETE = "12";

    public static final String BRANCHIN_APPLY_WAIT = "12";

    public static final String BRANCHIN_APPLYING = "13";

    public static final String BRANCHIN_APPLY_FAIL = "14";

    public static final String BRANCHIN_CHECK_WAIT = "15";

    public static final String BRANCHIN_CHECKING = "16";

    public static final String BRANCHIN_CHECK_FAIL = "17";

    public static final String BRANCHIN_CHECK_SUCCESS = "18";

    public static final String BRANCHIN_GIVEUP = "19";

}
