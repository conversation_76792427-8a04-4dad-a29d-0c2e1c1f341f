package com.bsm.v4.system.model.dto.business.station;


import io.swagger.annotations.ApiModelProperty;


/**
 * 台站名称（可为空）
 * 台站识别码（可为空）
 * Created by qhy on 2018/8/3.
 */

public class StationPageDTO {
    @ApiModelProperty(value = "当前页", required = true)
    private int page;
    @ApiModelProperty(value = "每页条数", required = true)
    private int rows;
    @ApiModelProperty(value = "申请表id")
    private String applytableGuid;
    @ApiModelProperty(value = "台站名称")
    private String stationName;
    @ApiModelProperty(value = "台站识别码")
    private String stationCode;
    @ApiModelProperty(value = "基站状态")
    private String staState;

    @ApiModelProperty(value = "基站经度")
    private double longitude;
    @ApiModelProperty(value = "基站纬度")
    private double latitude;
    @ApiModelProperty(value = "半径范围")
    private double radius;
    @ApiModelProperty(value = "发射频率下限")
    private String freqEfb;
    @ApiModelProperty(value = "发射频率上限")
    private String freqEfe;
    @ApiModelProperty(value = "接收频率下限")
    private String freqRfb;
    @ApiModelProperty(value = "接收频率上限")
    private String freqRfe;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getApplytableGuid() {
        return applytableGuid;
    }

    public void setApplytableGuid(String applytableGuid) {
        this.applytableGuid = applytableGuid;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getStaState() {
        return staState;
    }

    public void setStaState(String staState) {
        this.staState = staState;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getRadius() {
        return radius;
    }

    public void setRadius(double radius) {
        this.radius = radius;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }
}
