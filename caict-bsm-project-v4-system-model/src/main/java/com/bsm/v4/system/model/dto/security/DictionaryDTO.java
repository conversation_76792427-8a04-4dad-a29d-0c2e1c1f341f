package com.bsm.v4.system.model.dto.security;

import com.caictframework.utils.annotation.TableChildren;
import com.caictframework.utils.annotation.TableParentId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Title: DictionaryDTO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.dto.security
 * @Date 2023/8/16 16:37
 * @description:
 */

@ApiModel(value = "dictionaryDTO", description = "数据字典对象")
public class DictionaryDTO {

    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "权重")
    private Integer sort;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "数据级别")
    private Integer dataLevel;

    @ApiModelProperty(value = "保存时间")
    private Date updateDateTime;

    @ApiModelProperty(value = "状态;1：非必要；2：必要；3：停用")
    private Integer status;

    @ApiModelProperty(value = "数据类型")
    private String dataType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "子类集合")
    @TableChildren
    private List<DictionaryDTO> dictionaryDTOList = new ArrayList<DictionaryDTO>();

    @ApiModelProperty(value = "父级id")
    @TableParentId
    private String parentId;

    public DictionaryDTO() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getDataLevel() {
        return dataLevel;
    }

    public void setDataLevel(Integer dataLevel) {
        this.dataLevel = dataLevel;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<DictionaryDTO> getDictionaryDTOList() {
        return dictionaryDTOList;
    }

    public void setDictionaryDTOList(List<DictionaryDTO> dictionaryDTOList) {
        this.dictionaryDTOList = dictionaryDTOList;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
