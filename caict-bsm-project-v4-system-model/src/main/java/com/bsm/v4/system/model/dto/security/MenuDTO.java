package com.bsm.v4.system.model.dto.security;

import com.caictframework.utils.annotation.TableChildren;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableParentId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 * <p>
 * 菜单DTO
 */

@ApiModel(value = "menuDTO", description = "菜单对象")
public class MenuDTO {

    @ApiModelProperty(value = "主键")
    @TableId
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "图标")
    private String img;

    @ApiModelProperty(value = "菜单别名")
    private String keyName;

    @ApiModelProperty(value = "URL地址")
    private String url;

    @ApiModelProperty(value = "父级id")
    @TableParentId
    private String parentId;

    @ApiModelProperty(value = "权重")
    private Integer sort;

    @ApiModelProperty(value = "状态;1：启用；2：停用")
    private Integer status;

    @ApiModelProperty(value = "操作用户主键")
    private String usersId;

    @ApiModelProperty(value = "更新时间")
    private Date updateDateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "查询参数")
    private String query;

    @ApiModelProperty(value = "子节点集合",hidden = true)
    @TableChildren
    private List<MenuDTO> childrenList = new ArrayList<>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUsersId() {
        return usersId;
    }

    public void setUsersId(String usersId) {
        this.usersId = usersId;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public List<MenuDTO> getChildrenList() {
        return childrenList;
    }

    public void setChildrenList(List<MenuDTO> childrenList) {
        this.childrenList = childrenList;
    }
}
