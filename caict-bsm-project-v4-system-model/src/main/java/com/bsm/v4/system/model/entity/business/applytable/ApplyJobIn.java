package com.bsm.v4.system.model.entity.business.applytable;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * Created by yanchengpeng on 2021/5/30.
 */

@TableName("APPLY_JOB_IN")
public class ApplyJobIn {
    @TableId("GUID")
    private String guid;
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("APP_GUID")
    private String appGuid;
    @TableFieId("APP_CODE")
    private String appCode;
    @TableFieId("IS_COMPARE")
    private String isCompare;
    @TableFieId("REGION_CODE")
    private String regionCode;
    @TableFieId("USER_GUID")
    private String userGuid;
    @TableFieId("DATA_TYPE")
    private String dataType;
    @TableFieId("GEN_NUM")
    private String genNum;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }
}
