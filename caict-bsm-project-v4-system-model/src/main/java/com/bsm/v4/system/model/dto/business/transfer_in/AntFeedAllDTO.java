package com.bsm.v4.system.model.dto.business.transfer_in;


import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import com.caictframework.utils.annotation.TableFieId;


/**
 * Created by yanchengpeng on 2020/12/14.
 */

public class AntFeedAllDTO extends RsbtAntfeed {
    /**
     * 天线序号
     */
    @TableFieId("AT_ANT_NO")
    private String atAntNo;

    /**
     * 射线仰角范围起
     */
    @TableFieId("AT_SE_B")
    private Double atSeB;

    /**
     * 射线仰角范围止
     */
    @TableFieId("AT_SE_E")
    private Double atSeE;

    /**
     * 方位角范围起
     */
    @TableFieId("AT_ANG_B")
    private Double atAngB;

    /**
     * 方位角范围止
     */
    @TableFieId("AT_ANG_E")
    private Double atAngE;

    /**
     * 接收系统噪声温度
     */
    @TableFieId("AT_RNT")
    private Double atRnt;

    /**
     * 波束宽度
     */
    @TableFieId("AT_BWID")
    private Double atBwid;

    /**
     * 第一旁瓣电平
     */
    @TableFieId("AT_LEL")
    private Double atLel;

    /**
     * 扫描速度
     */
    @TableFieId("AT_SSPEED")
    private Double atSspeed;

    /**
     * 扇区号
     */
    @TableFieId("AT_CCODE")
    private String atCcode;

    /**
     * 3dB角宽（发）
     */
    @TableFieId("AT_3DBE")
    private Double at3dbe;

    /**
     * 3dB角宽（收）
     */
    @TableFieId("AT_3DBR")
    private Double at3dbr;

    /**
     * 收倾角
     */
    @TableFieId("AT_RANG")
    private Double atRang;

    /**
     * 发倾角
     */
    @TableFieId("AT_EANG")
    private Double atEang;

    /**
     * 扇区标识码
     */
    @TableFieId("AT_CSGN")
    private String atCsgn;

    /**
     * 直放站上行/下行
     */
    @TableFieId("AT_UPDN")
    private String atUpdn;

    /**
     * 天线数量
     */
    @TableFieId("AT_SUM")
    private int atSum;

    /**
     * 接收系统品质
     */
    @TableFieId("AT_QUA")
    private String atQua;

    /**
     * 天线增益单位
     */
    @TableFieId("AT_UNIT_TYPE")
    private String atUnitType;

    /**
     * 水平方向图
     */
    @TableFieId("AT_H_HPIC")
    private String atHHpic;

    /**
     * 垂直方向图
     */
    @TableFieId("AT_H_VPIC")
    private String atHVpic;

    /**
     * 天线仰角
     */
    @TableFieId("AT_ANT_UPANG")
    private Double atAntUpang;

    public String getAtAntNo() {
        return atAntNo;
    }

    public void setAtAntNo(String atAntNo) {
        this.atAntNo = atAntNo;
    }

    public Double getAtSeB() {
        return atSeB;
    }

    public void setAtSeB(Double atSeB) {
        this.atSeB = atSeB;
    }

    public Double getAtSeE() {
        return atSeE;
    }

    public void setAtSeE(Double atSeE) {
        this.atSeE = atSeE;
    }

    public Double getAtAngB() {
        return atAngB;
    }

    public void setAtAngB(Double atAngB) {
        this.atAngB = atAngB;
    }

    public Double getAtAngE() {
        return atAngE;
    }

    public void setAtAngE(Double atAngE) {
        this.atAngE = atAngE;
    }

    public Double getAtRnt() {
        return atRnt;
    }

    public void setAtRnt(Double atRnt) {
        this.atRnt = atRnt;
    }

    public Double getAtBwid() {
        return atBwid;
    }

    public void setAtBwid(Double atBwid) {
        this.atBwid = atBwid;
    }

    public Double getAtLel() {
        return atLel;
    }

    public void setAtLel(Double atLel) {
        this.atLel = atLel;
    }

    public Double getAtSspeed() {
        return atSspeed;
    }

    public void setAtSspeed(Double atSspeed) {
        this.atSspeed = atSspeed;
    }

    public String getAtCcode() {
        return atCcode;
    }

    public void setAtCcode(String atCcode) {
        this.atCcode = atCcode;
    }

    public Double getAt3dbe() {
        return at3dbe;
    }

    public void setAt3dbe(Double at3dbe) {
        this.at3dbe = at3dbe;
    }

    public Double getAt3dbr() {
        return at3dbr;
    }

    public void setAt3dbr(Double at3dbr) {
        this.at3dbr = at3dbr;
    }

    public Double getAtRang() {
        return atRang;
    }

    public void setAtRang(Double atRang) {
        this.atRang = atRang;
    }

    public Double getAtEang() {
        return atEang;
    }

    public void setAtEang(Double atEang) {
        this.atEang = atEang;
    }

    public String getAtCsgn() {
        return atCsgn;
    }

    public void setAtCsgn(String atCsgn) {
        this.atCsgn = atCsgn;
    }

    public String getAtUpdn() {
        return atUpdn;
    }

    public void setAtUpdn(String atUpdn) {
        this.atUpdn = atUpdn;
    }

    public int getAtSum() {
        return atSum;
    }

    public void setAtSum(int atSum) {
        this.atSum = atSum;
    }

    public String getAtQua() {
        return atQua;
    }

    public void setAtQua(String atQua) {
        this.atQua = atQua;
    }

    public String getAtUnitType() {
        return atUnitType;
    }

    public void setAtUnitType(String atUnitType) {
        this.atUnitType = atUnitType;
    }

    public String getAtHHpic() {
        return atHHpic;
    }

    public void setAtHHpic(String atHHpic) {
        this.atHHpic = atHHpic;
    }

    public String getAtHVpic() {
        return atHVpic;
    }

    public void setAtHVpic(String atHVpic) {
        this.atHVpic = atHVpic;
    }

    public Double getAtAntUpang() {
        return atAntUpang;
    }

    public void setAtAntUpang(Double atAntUpang) {
        this.atAntUpang = atAntUpang;
    }
}
