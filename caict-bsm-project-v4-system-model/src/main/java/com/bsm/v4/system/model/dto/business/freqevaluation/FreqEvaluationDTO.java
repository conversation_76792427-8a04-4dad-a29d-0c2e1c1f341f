package com.bsm.v4.system.model.dto.business.freqevaluation;

import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@ApiModel(value = "freqEvaluationDTO", description = "无线电频率使用率评价结果对象")
public class FreqEvaluationDTO extends FreqEvaluation {

    @ApiModelProperty(value = "地区名")
    private String areaName;

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
