package com.bsm.v4.system.model.entity.business.license;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-08
 */

@TableName("BSM_LICENSE_EXPRECORD")
public class BsmLicenseExprecord {

    @TableFieId("EXPORT_NAME")
    private String exportName;
    @TableFieId("EXPORT_STOP")
    private String exportStop;
    /**
     * 过期
     */
    @TableFieId("EXPORT_EXPIRED")
    private String exportExpired;
    /**
     * 注销
     */
    @TableFieId("EXPORT_INVALID")
    private String exportInvalid;
    /**
     * 有效
     */
    @TableFieId("EXPORT_VALID")
    private String exportValid;
    @TableFieId("EXPORT_COUNT")
    private String exportCount;
    /**
     * 导出时间
     */
    @TableFieId("EXPORT_TIME")
    private Date exportTime;

    @TableId("EXPORT_GUID")
    private String exportGuid;

    public String getExportName() {
        return exportName;
    }

    public void setExportName(String exportName) {
        this.exportName = exportName;
    }

    public String getExportStop() {
        return exportStop;
    }

    public void setExportStop(String exportStop) {
        this.exportStop = exportStop;
    }

    public String getExportExpired() {
        return exportExpired;
    }

    public void setExportExpired(String exportExpired) {
        this.exportExpired = exportExpired;
    }

    public String getExportInvalid() {
        return exportInvalid;
    }

    public void setExportInvalid(String exportInvalid) {
        this.exportInvalid = exportInvalid;
    }

    public String getExportValid() {
        return exportValid;
    }

    public void setExportValid(String exportValid) {
        this.exportValid = exportValid;
    }

    public String getExportCount() {
        return exportCount;
    }

    public void setExportCount(String exportCount) {
        this.exportCount = exportCount;
    }

    public Date getExportTime() {
        return exportTime;
    }

    public void setExportTime(Date exportTime) {
        this.exportTime = exportTime;
    }

    public String getExportGuid() {
        return exportGuid;
    }

    public void setExportGuid(String exportGuid) {
        this.exportGuid = exportGuid;
    }
}
