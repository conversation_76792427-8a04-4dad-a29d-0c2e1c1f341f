package com.bsm.v4.system.model.entity.business.stationbak;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;

/**
 * <p>
 * <AUTHOR>
 * @since 2018-08-03
 */

@TableName("BSM_STATION_LICENSE_BAK")
public class BsmStationLicenseBak {

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;
    @TableFieId("GMT_MODIFIED")
    private Date gmtModified;
    @TableFieId("IS_DELETED")
    private Long isDeleted;
    /**
     * 执照状态
     */
    @TableFieId("LICENSE_STATE")
    private Long licenseState;
    @TableId("GUID")
    private String guid;
    @TableFieId("STATION_GUID")
    private String stationGuid;
    /**
     * 执照编号
     */
    @TableFieId("LICENSE_CODE")
    private String licenseCode;

    @TableFieId("LICENSE_START_DATE")
    private Date licenseStartDate;
    @TableFieId("LICENSE_END_DATE")
    private Date licenseEndDate;
    /**
     * 1重庆主城2万州3涪陵4黔江
     */
    @TableFieId("LICENSE_COUNTY")
    private String licenseCounty;
    @TableFieId("USER_GUID")
    private String userGuid;

    /**
     * 是否同步
     */
    @TableFieId("IS_SYNC")
    private String isSync;

    /**
     * 数据类型（1-新增；2-变更；3-删除）
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    /**
     * 插入时间
     */
    @TableFieId("BAK_DATE")
    private Date bakDate;

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Long getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(Long licenseState) {
        this.licenseState = licenseState;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public Date getLicenseStartDate() {
        return licenseStartDate;
    }

    public void setLicenseStartDate(Date licenseStartDate) {
        this.licenseStartDate = licenseStartDate;
    }

    public Date getLicenseEndDate() {
        return licenseEndDate;
    }

    public void setLicenseEndDate(Date licenseEndDate) {
        this.licenseEndDate = licenseEndDate;
    }

    public String getLicenseCounty() {
        return licenseCounty;
    }

    public void setLicenseCounty(String licenseCounty) {
        this.licenseCounty = licenseCounty;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Date getBakDate() {
        return bakDate;
    }

    public void setBakDate(Date bakDate) {
        this.bakDate = bakDate;
    }
}
