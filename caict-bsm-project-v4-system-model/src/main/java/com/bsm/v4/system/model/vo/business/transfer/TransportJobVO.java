package com.bsm.v4.system.model.vo.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;

import java.util.Date;
import java.util.List;


public class TransportJobVO {

    private Date jobDateStart;
    private Date jobDateEnd;
    private String userType;
    private String appGuid;
    private String regionCode;
    private String jobStatus;
    private String jobId;
    private String guid;
    private String fileId;
    private String dataType;
    private String genNum;
    private String jobName;
    private Date gmtCreateStart;//起始时间
    private Date gmtCreateEnd;//截至时间
    private String userId;//用户id
    private String fileNum;//文件数量
    private String materialNum;//材料数量
    private String isCompare;//文件状态
    private String queryFileType;//查询方法
    private String deleteType;//不为空则删除未选中文件数据

    //附件信息
    private String token;
    private String fileGuid;
    private String fileName;
    private String fileState;

    //所属csv文件集合
    private List<TransportFileDTO> transportFileDTOList;
    //所属附件集合
    private List<TransportFileDTO> transportFileAttachedDTOList;

    //对比查询
    private String compResult;//比对结果
    private String jobCreationTime;//任务上报时间
    private String compCreationTime;//比对时间
    private Date jobCreateStart;//起始时间
    private Date jobCreateEnd;//截至时间
    private Date compCreateStart;//起始时间
    private Date compCreateEnd;//截至时间

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    public Date getJobDateStart() {
        return jobDateStart;
    }

    public void setJobDateStart(Date jobDateStart) {
        this.jobDateStart = jobDateStart;
    }

    public Date getJobDateEnd() {
        return jobDateEnd;
    }

    public void setJobDateEnd(Date jobDateEnd) {
        this.jobDateEnd = jobDateEnd;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public Date getGmtCreateStart() {
        return gmtCreateStart;
    }

    public void setGmtCreateStart(Date gmtCreateStart) {
        this.gmtCreateStart = gmtCreateStart;
    }

    public Date getGmtCreateEnd() {
        return gmtCreateEnd;
    }

    public void setGmtCreateEnd(Date gmtCreateEnd) {
        this.gmtCreateEnd = gmtCreateEnd;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFileNum() {
        return fileNum;
    }

    public void setFileNum(String fileNum) {
        this.fileNum = fileNum;
    }

    public String getMaterialNum() {
        return materialNum;
    }

    public void setMaterialNum(String materialNum) {
        this.materialNum = materialNum;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public String getQueryFileType() {
        return queryFileType;
    }

    public void setQueryFileType(String queryFileType) {
        this.queryFileType = queryFileType;
    }

    public String getDeleteType() {
        return deleteType;
    }

    public void setDeleteType(String deleteType) {
        this.deleteType = deleteType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileState() {
        return fileState;
    }

    public void setFileState(String fileState) {
        this.fileState = fileState;
    }

    public List<TransportFileDTO> getTransportFileDTOList() {
        return transportFileDTOList;
    }

    public void setTransportFileDTOList(List<TransportFileDTO> transportFileDTOList) {
        this.transportFileDTOList = transportFileDTOList;
    }

    public List<TransportFileDTO> getTransportFileAttachedDTOList() {
        return transportFileAttachedDTOList;
    }

    public void setTransportFileAttachedDTOList(List<TransportFileDTO> transportFileAttachedDTOList) {
        this.transportFileAttachedDTOList = transportFileAttachedDTOList;
    }

    public String getCompResult() {
        return compResult;
    }

    public void setCompResult(String compResult) {
        this.compResult = compResult;
    }

    public String getJobCreationTime() {
        return jobCreationTime;
    }

    public void setJobCreationTime(String jobCreationTime) {
        this.jobCreationTime = jobCreationTime;
    }

    public String getCompCreationTime() {
        return compCreationTime;
    }

    public void setCompCreationTime(String compCreationTime) {
        this.compCreationTime = compCreationTime;
    }

    public Date getJobCreateStart() {
        return jobCreateStart;
    }

    public void setJobCreateStart(Date jobCreateStart) {
        this.jobCreateStart = jobCreateStart;
    }

    public Date getJobCreateEnd() {
        return jobCreateEnd;
    }

    public void setJobCreateEnd(Date jobCreateEnd) {
        this.jobCreateEnd = jobCreateEnd;
    }

    public Date getCompCreateStart() {
        return compCreateStart;
    }

    public void setCompCreateStart(Date compCreateStart) {
        this.compCreateStart = compCreateStart;
    }

    public Date getCompCreateEnd() {
        return compCreateEnd;
    }

    public void setCompCreateEnd(Date compCreateEnd) {
        this.compCreateEnd = compCreateEnd;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
