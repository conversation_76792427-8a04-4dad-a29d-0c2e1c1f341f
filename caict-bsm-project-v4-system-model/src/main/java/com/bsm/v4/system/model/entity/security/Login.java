package com.bsm.v4.system.model.entity.security;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * Created by dengsy on 2019-10-24.
 * 登录对象
 */

@TableName("sys_login")
public class Login {

    @ApiModelProperty(value = "主键ID")
    @TableId("id")
    private String id;

    @ApiModelProperty(value = "登录名")
    @TableFieId("login_name")
    private String loginName;

    @ApiModelProperty(value = "登录密码")
    @TableFieId("password")
    private String password;

    @ApiModelProperty(value = "登录状态")
    @TableFieId("status")
    private Integer status;

    @ApiModelProperty(value = "登录类型")
    @TableFieId("type")
    private String type;

    @ApiModelProperty(value = "用户id")
    @TableFieId("user_id")
    private String userId;

    @ApiModelProperty(value = "最近登录时间")
    @TableFieId("last_date")
    private Date lastDate;

    @ApiModelProperty(value = "最近登录方式")
    @TableFieId("last_way")
    private String lastWay;

    @ApiModelProperty(value = "最近登录ip")
    @TableFieId("last_ip")
    private String lastIp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }

    public String getLastWay() {
        return lastWay;
    }

    public void setLastWay(String lastWay) {
        this.lastWay = lastWay;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }
}
