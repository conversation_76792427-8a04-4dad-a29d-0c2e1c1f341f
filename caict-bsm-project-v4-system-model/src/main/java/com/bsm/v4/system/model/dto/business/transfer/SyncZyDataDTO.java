package com.bsm.v4.system.model.dto.business.transfer;


import com.bsm.v4.system.model.entity.business.transfer.SyncZyData;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;


/**
 * SyncZyDataDTO.
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 7/8/21 2:19 PM
 **/

public class SyncZyDataDTO extends SyncZyData {

    //分页信息
    @TablePageNum
    private int page;
    @TablePageSize
    private int rows;

    private String[] guids;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String[] getGuids() {
        return guids;
    }

    public void setGuids(String[] guids) {
        this.guids = guids;
    }
}
