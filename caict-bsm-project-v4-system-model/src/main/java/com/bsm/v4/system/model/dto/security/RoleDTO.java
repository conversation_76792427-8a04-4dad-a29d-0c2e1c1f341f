package com.bsm.v4.system.model.dto.security;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 *
 * 角色DTO
 */

@ApiModel(value = "roleDTO", description = "角色对象")
public class RoleDTO  {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "权重")
    private Integer sort;

    @ApiModelProperty(value = "等级")
    private Integer roleLevel;

    @ApiModelProperty(value = "操作用户主键")
    private String usersId;

    @ApiModelProperty(value = "更新时间")
    private Date updateDateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "角色所有用户信息",hidden = true)
    private List<UsersDTO> usersDTOs;

    @ApiModelProperty(value = "角色菜单信息",hidden = true)
    private List<MenuDTO> menuDTOs;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getRoleLevel() {
        return roleLevel;
    }

    public void setRoleLevel(Integer roleLevel) {
        this.roleLevel = roleLevel;
    }

    public String getUsersId() {
        return usersId;
    }

    public void setUsersId(String usersId) {
        this.usersId = usersId;
    }

    public Date getUpdateDateTime() {
        return updateDateTime;
    }

    public void setUpdateDateTime(Date updateDateTime) {
        this.updateDateTime = updateDateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<UsersDTO> getUsersDTOs() {
        return usersDTOs;
    }

    public void setUsersDTOs(List<UsersDTO> usersDTOs) {
        this.usersDTOs = usersDTOs;
    }

    public List<MenuDTO> getMenuDTOs() {
        return menuDTOs;
    }

    public void setMenuDTOs(List<MenuDTO> menuDTOs) {
        this.menuDTOs = menuDTOs;
    }
}
