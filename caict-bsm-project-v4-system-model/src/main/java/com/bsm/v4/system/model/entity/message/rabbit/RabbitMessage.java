package com.bsm.v4.system.model.entity.message.rabbit;

/**
 * Created by dsy62 on 2018-05-11.
 * 消息实体类
 * id:id
 * title:消息标题
 * content:消息内容
 * sendTime:消息发送时间
 * from:消息来源
 */
public class RabbitMessage {

    private String id;
    private String title;
    private String content;
    private String sendTime;
    private String from;
    private String remark;
    private String type;            //消息类型  1：CSV文件处理；2：EXL文件处理

    public RabbitMessage(){}

    public RabbitMessage(String id, String title, String content, String sendTime, String from, String remark, String type){
        this.id = id;
        this.title = title;
        this.content = content;
        this.sendTime = sendTime;
        this.from = from;
        this.remark = remark;
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
