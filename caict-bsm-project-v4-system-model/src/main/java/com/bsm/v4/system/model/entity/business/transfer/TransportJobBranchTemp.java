package com.bsm.v4.system.model.entity.business.transfer;

import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/15
 *
 * 运营商申请表
 */

@TableName("TRANSPORT_JOB_BRANCH_TEMP")
public class TransportJobBranchTemp {

    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "任务主键")
    @TableFieId("JOB_GUID")
    private String jobGuid;

    @ApiModelProperty(value = "申请表状态")
    @TableFieId("IS_COMPARE")
    private String isCompare;

    @ApiModelProperty(value = "创建时间")
    @TableFieId("GMT_CREATE")
    private Date gmtCreate;

    @ApiModelProperty(value = "用户主键")
    @TableFieId("USER_GUID")
    private String userGuid;

    @ApiModelProperty(value = "基站数量")
    @TableFieId("STATION_COUNT")
    private String stationCount;

    @ApiModelProperty(value = "扇区数量")
    @TableFieId("CELL_COUNT")
    private String cellCount;

    @ApiModelProperty(value = "状态 1：未提交；2：已提交")
    @TableFieId("STATE")
    private String state;

    @ApiModelProperty(value = "申请表编号")
    @TableFieId("APP_CODE")
    private String appCode;

    @ApiModelProperty(value = "审批意见")
    @TableFieId("OP_DETAIL")
    private String opDetail;

    @ApiModelProperty(value = "编号")
    @TableFieId("REGION_CODE")
    private String regionCode;

    @ApiModelProperty(value = "判断增/全量提交的标志")
    @TableFieId("DATA_TYPE")
    private String dataType;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getIsCompare() {
        return isCompare;
    }

    public void setIsCompare(String isCompare) {
        this.isCompare = isCompare;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getStationCount() {
        return stationCount;
    }

    public void setStationCount(String stationCount) {
        this.stationCount = stationCount;
    }

    public String getCellCount() {
        return cellCount;
    }

    public void setCellCount(String cellCount) {
        this.cellCount = cellCount;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getOpDetail() {
        return opDetail;
    }

    public void setOpDetail(String opDetail) {
        this.opDetail = opDetail;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
}
