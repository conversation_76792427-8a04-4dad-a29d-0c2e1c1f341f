package com.bsm.v4.system.model.enums;

/**
 * Created by dsy62 on 2019-11-21.
 *
 * 天线类型 枚举
 */
public enum  AntennaModelEnum {

    AA("AA","T形天线"),AB("AB","Γ形天线"),AC("AC","伞形天线"),AD("AD","水平对称振子天线"),AE("AE","垂直对称振子天线"),
    AF("AF","角形天线"),AG("AG","单导线行波天线"),AH("AH","斜V形天线"),AI("AI","菱形天线"),AJ("AJ","同相水平天线"),
    AK("AK","鱼骨形天线"),AL("AL","对数周期天线"),AM("AM","垂直杆状天线"),AN("AN","有源环形天线"),AO("AO","环形天线阵"),
    BA("BA","J形天线"),BB("BB","布朗天线"),BC("BC","鞭状天线"),BD("BD","全向天线"),BE("BE","对称振子天线"),
    BF("BF","同轴偶极天线"),BG("BG","折合振子天线阵"),CA("CA","抛物面天线"),CB("CB","双反射抛物面天线"),CC("CC","喇叭抛物面天线"),
    CD("CD","潜望镜天线"),CE("CE","喇叭天线"),CF("CF","赋形天线"),DA("DA","垂直天线"),DB("DB","水平天线"),
    DC("DC","定向天线"),DD("DD","宽带天线"),DE("DE","隙缝天线"),DF("DF","蝙蝠翼天线"),DG("DG","四偶极板天线"),
    DI("DI","螺旋天线"),DJ("DJ","圆极化天线"),CX("CX","上述未包括的其它天线类型"),XY("XY","引向天线"),BZ("BZ","板状天线"),
    FZ("FZ","扇区天线"),PA("PA","平面阵列天线"),AP("AP","斜双极天线"),AV("AV","V形天线"),AQ("AQ","斜天线"),
    AR("AR","环形天线"),MB("MB","微带天线");

    //成员变量
    private String type;
    private String name;

    //构造方法
    AntennaModelEnum(String type, String name){
        this.type = type;
        this.name = name;
    }

    //数据打印
    public void print(){
        System.out.println("type:"+this.type+",name:"+this.name);
    }

    //返回类型
    public String getType(){
        return this.type;
    }

    //返回名称
    public String getName(){
        return this.name;
    }
}
