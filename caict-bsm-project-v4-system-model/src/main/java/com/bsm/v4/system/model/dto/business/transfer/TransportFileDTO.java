package com.bsm.v4.system.model.dto.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.TransportFile;



public class TransportFileDTO extends TransportFile {
    private String fileName;
    private String fileId;
    private String creationTime;
    private String fileStatus;
    private String jobGuid;
    private String fileLinesNum;//文件内容条数
    private String errReason;//异常原因

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(String creationTime) {
        this.creationTime = creationTime;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getFileLinesNum() {
        return fileLinesNum;
    }

    public void setFileLinesNum(String fileLinesNum) {
        this.fileLinesNum = fileLinesNum;
    }

    public String getErrReason() {
        return errReason;
    }

    public void setErrReason(String errReason) {
        this.errReason = errReason;
    }
}
