package com.bsm.v4.system.model.vo.security;

import com.bsm.v4.system.model.entity.security.RsbtOrg;
import com.caictframework.utils.annotation.TablePageNum;
import com.caictframework.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


import java.util.Date;

/**
 * @Title: OrgVO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.vo.security
 * @Date 2023/8/14 16:48
 * @description:
 */

@ApiModel(value = "orgVO", description = "组织机构对象")
public class OrgVO extends RsbtOrg {

    /**
     * 系统ID
     */
    @ApiModelProperty(value = "主键ID、与Org主键一致")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    @ApiModelProperty(value = "状态 1：启用；2：停用")
    private Integer status;

    @ApiModelProperty(value = "是否同步")
    private String isSync;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "父级id")
    private String parentId;

    @ApiModelProperty(value = "区域详情")
    private String regionDetails;

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    @Override
    public String getGuid() {
        return guid;
    }

    @Override
    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getRegionDetails() {
        return regionDetails;
    }

    public void setRegionDetails(String regionDetails) {
        this.regionDetails = regionDetails;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }
}
