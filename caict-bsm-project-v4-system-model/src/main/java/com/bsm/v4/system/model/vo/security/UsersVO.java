package com.bsm.v4.system.model.vo.security;

import com.bsm.v4.system.model.entity.security.Users;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * @Title: UsersVO
 * <AUTHOR>
 * @Package com.bsm.v4.system.model.vo.security
 * @Date 2023/8/15 14:50
 * @description:
 */

@ApiModel(value = "usersVO", description = "用户对象")
public class UsersVO extends Users {
    @ApiModelProperty(value = "登录名")
    private String loginName;

    @ApiModelProperty(value = "登录密码")
    private String password;

    @ApiModelProperty(value = "旧密码")
    private String passwordOld;

    @ApiModelProperty(value = "最近登录时间")
    private String lastDate;

    @ApiModelProperty(value = "最近登录方式")
    private String lastWay;

    @ApiModelProperty(value = "最近登录ip")
    private String lastIp;

    @ApiModelProperty(value = "登录id")
    private String loginId;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPasswordOld() {
        return passwordOld;
    }

    public void setPasswordOld(String passwordOld) {
        this.passwordOld = passwordOld;
    }

    public String getLastDate() {
        return lastDate;
    }

    public void setLastDate(String lastDate) {
        this.lastDate = lastDate;
    }

    public String getLastWay() {
        return lastWay;
    }

    public void setLastWay(String lastWay) {
        this.lastWay = lastWay;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
}
