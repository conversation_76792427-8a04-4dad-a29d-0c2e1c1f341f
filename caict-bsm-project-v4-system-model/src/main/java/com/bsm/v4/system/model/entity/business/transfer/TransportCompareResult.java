package com.bsm.v4.system.model.entity.business.transfer;


import com.caictframework.utils.annotation.TableFieId;
import com.caictframework.utils.annotation.TableId;
import com.caictframework.utils.annotation.TableName;


import java.util.Date;


@TableName("TRANSPORT_COMPARE_RESULT")
public class TransportCompareResult {

    @TableId("GUID")
    private String guid;

    /**
     * 对比任务
     */
    @TableFieId("APP_GUID")
    private String appGuid;

    /**
     * Job任务
     */
    @TableFieId("JOB_GUID")
    private String jobGuid;

    /**
     * 导入数据的guid
     */
    @TableFieId("IMPORT_DATAID")
    private String importDateid;

    /**
     * 已存在数据的guid
     */
    @TableFieId("EXIST_DATAID")
    private String existDataid;

    /**
     * 比对结果
     */
    @TableFieId("COMPARE_RESULT")
    private String compareResult;

    /**
     * 比对结果类型;1-新增异常，2-变更异常，3-注销异常,4-对比通过
     */
    @TableFieId("RESULT_TYPE")
    private String resultType;

    /**
     * 详情
     */
    @TableFieId("DETAIL")
    private String detail;

    @TableFieId("GMT_CREATE")
    private Date gmtCreate;

    @TableFieId("USER_GUID")
    private String userId;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public String getImportDateid() {
        return importDateid;
    }

    public void setImportDateid(String importDateid) {
        this.importDateid = importDateid;
    }

    public String getExistDataid() {
        return existDataid;
    }

    public void setExistDataid(String existDataid) {
        this.existDataid = existDataid;
    }

    public String getCompareResult() {
        return compareResult;
    }

    public void setCompareResult(String compareResult) {
        this.compareResult = compareResult;
    }

    public String getResultType() {
        return resultType;
    }

    public void setResultType(String resultType) {
        this.resultType = resultType;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
