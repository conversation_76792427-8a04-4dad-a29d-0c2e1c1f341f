package com.bsm.v4.system.model.dto.business.station;



import java.io.Serializable;
import java.util.List;

public class RegionSortDTO implements Serializable, Comparable<RegionSortDTO> {
    //地区名称
    private String regionName;
    //地区名称对应基站数量
    private Long regionStationNum;
    //地区对应的组织基站总数
    private List<OrgStationNumSortDTO> orgStationNumSortDTOList;

    @Override
    public int compareTo(RegionSortDTO o) {
        return 0;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public Long getRegionStationNum() {
        return regionStationNum;
    }

    public void setRegionStationNum(Long regionStationNum) {
        this.regionStationNum = regionStationNum;
    }

    public List<OrgStationNumSortDTO> getOrgStationNumSortDTOList() {
        return orgStationNumSortDTOList;
    }

    public void setOrgStationNumSortDTOList(List<OrgStationNumSortDTO> orgStationNumSortDTOList) {
        this.orgStationNumSortDTOList = orgStationNumSortDTOList;
    }
}
