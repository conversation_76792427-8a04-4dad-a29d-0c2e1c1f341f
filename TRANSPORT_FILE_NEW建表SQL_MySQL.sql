-- 创建TRANSPORT_FILE_NEW表（MySQL版本）
CREATE TABLE TRANSPORT_FILE_NEW (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_id BIGINT,
    file_local_name VARCHAR(255) NOT NULL,
    file_renamed VA<PERSON><PERSON>R(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_state TINYINT DEFAULT 0 NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    data_type VARCHAR(50),
    file_extension VARCHAR(20),
    mime_type VARCHAR(100),
    file_md5 VARCHAR(32),
    is_deleted TINYINT DEFAULT 0 NOT NULL,
    uploaded_by VA<PERSON>HA<PERSON>(100),
    file_description VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加注释
ALTER TABLE TRANSPORT_FILE_NEW COMMENT = '传输文件表（新版本）';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT '主键ID';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN job_id BIGINT COMMENT '关联的任务ID';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_local_name VARCHAR(255) NOT NULL COMMENT '原始文件名称';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_renamed VARCHAR(255) NOT NULL COMMENT '重命名后的文件名称';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_size BIGINT NOT NULL COMMENT '文件大小（字节）';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_state TINYINT DEFAULT 0 NOT NULL COMMENT '文件状态（0-创建，1-上传成功，2-处理完成，3-处理异常）';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_type VARCHAR(10) NOT NULL COMMENT '文件类型（0-附件，1-CSV数据文件，2-材料附件）';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN data_type VARCHAR(50) COMMENT '数据类型（全量、增量、注销等）';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_extension VARCHAR(20) COMMENT '文件扩展名';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN mime_type VARCHAR(100) COMMENT '文件MIME类型';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_md5 VARCHAR(32) COMMENT '文件MD5值';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN is_deleted TINYINT DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN uploaded_by VARCHAR(100) COMMENT '上传者';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN file_description VARCHAR(500) COMMENT '文件描述';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间';
ALTER TABLE TRANSPORT_FILE_NEW MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间';

-- 创建索引
CREATE INDEX IDX_TRANSPORT_FILE_NEW_JOB_ID ON TRANSPORT_FILE_NEW(job_id);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_FILE_TYPE ON TRANSPORT_FILE_NEW(file_type);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_FILE_STATE ON TRANSPORT_FILE_NEW(file_state);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_MD5 ON TRANSPORT_FILE_NEW(file_md5);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_CREATED_AT ON TRANSPORT_FILE_NEW(created_at);
CREATE INDEX IDX_TRANSPORT_FILE_NEW_IS_DELETED ON TRANSPORT_FILE_NEW(is_deleted);

-- 创建复合索引
CREATE INDEX IDX_TRANSPORT_FILE_NEW_JOB_TYPE ON TRANSPORT_FILE_NEW(job_id, file_type, is_deleted);

-- 添加外键约束（可选，如果需要强制引用完整性）
-- ALTER TABLE TRANSPORT_FILE_NEW ADD CONSTRAINT FK_TRANSPORT_FILE_NEW_JOB_ID 
-- FOREIGN KEY (job_id) REFERENCES TRANSPORT_JOB_NEW(id) ON DELETE SET NULL ON UPDATE CASCADE;
