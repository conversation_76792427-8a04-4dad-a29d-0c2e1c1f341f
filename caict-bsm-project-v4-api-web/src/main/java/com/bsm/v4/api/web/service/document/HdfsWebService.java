package com.bsm.v4.api.web.service.document;

import com.bsm.v4.domain.security.service.business.transfer.TransportFileService;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.bsm.v4.system.model.vo.business.transfer.TransportFileVO;
import com.caictframework.bigdata.hadoop.hdfs.HdfsClient;
import com.caictframework.data.service.BasicWebService;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.junit.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Date;
import java.util.Map;

/**
 * @author: ycp
 * @createTime: 2023/09/04 15:21
 * @company: 成渝（成都）信息通信研究院
 * @description: hadoop文件处理webservice
 */
@Service
public class HdfsWebService extends BasicWebService {

    @Value("${hadoop.url}")
    private String url;

    @Value("${hadoop.namespace}")
    private String dir;

    @Value("${hadoop.user}")
    private String user;

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportFileService transportFileService;

    private static final Logger LOG = LoggerFactory.getLogger(HdfsWebService.class);

    /**
     * 获取HDFS文件系统对象
     *
     * @return FileSystem
     * @throws Exception
     */
    @Before
    public FileSystem getFileSystem() throws Exception {
        HdfsClient hdfsClient = new HdfsClient(url, user);
        return hdfsClient.get();
    }

    /**
     * 上传文件到FastFDS
     */
    public Map<String, Object> hdfsUpload(MultipartFile file, String fileType) {
        FSDataOutputStream outputStream = null;
        FileSystem fileSystem = null;
        try {
            // 上传时默认当前目录，后面自动拼接文件的目录
            long date = System.currentTimeMillis();
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) return this.basicReturnFailure("文件名为空！");
            //获取文件的后缀名 .jpg
            String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            String newFileName = date + suffix;
            Path newPath = new Path(url + dir + newFileName);
            fileSystem = getFileSystem();
            outputStream = fileSystem.create(newPath);
            outputStream.write(file.getBytes());
            //写入数据库
            TransportFileVO transportFileVO = new TransportFileVO();
            transportFileVO.setFileType(fileType);
            transportFileVO.setFilePath(dir + newFileName);
            transportFileVO.setFileState(0L);
            transportFileVO.setGmtCreate(new Date());
            transportFileVO.setFileLocalName(file.getOriginalFilename());

            TransportFile transportFile = new TransportFile();
            BeanUtils.copyProperties(transportFileVO, transportFile);
            String fileGuid = transportFileService.save(transportFile);
            if (fileGuid == null) return this.basicReturnFailure("上传文件失败！");

            transportFileVO.setFileId(fileGuid);
            return this.basicReturnSuccess(transportFileVO);
        } catch (Exception e) {
            LOG.error(e.getMessage());
            return this.basicReturnFailure(e.getMessage());
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (fileSystem != null) {
                    fileSystem.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 下载HDFS文件
     *
     * @param fileId 文件id
     */
    public Map<String, Object> downloadFile(String fileId) {
        TransportFile transportFile = transportFileService.findById(fileId);
        FileSystem fileSystem = null;
        try {
            // hdfs目标路径
            Path clientPath = new Path(url + transportFile.getFilePath());
            // 客户端存放路径
            Path serverPath = new Path(myFilePath + transportFile.getFileLocalName());
            // 调用文件系统的文件复制方法，第一个参数是否删除原文件 true为删除，默认为false
            fileSystem = getFileSystem();
            fileSystem.copyToLocalFile(false, clientPath, serverPath);
        } catch (Exception e) {
            LOG.error("hdfs downloadFile {}", e);
            return basicReturnFailure("下载失败");
        } finally {
            try {
                if (fileSystem != null) {
                    fileSystem.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return basicReturnSuccess(transportFile.getFileLocalName());
    }

    /**
     * 删除HDFS文件
     *
     * @param fileId 文件id
     * @return map
     */
    public Map<String, Object> deleteFile(String fileId) {
        //file表里查询文件信息
        TransportFile transportFile = transportFileService.findById(fileId);
        FileSystem fileSystem = null;
        try {
            fileSystem = getFileSystem();
            //判断hdfs服务器是否有文件
            if (!existFile(url + transportFile.getFilePath(), fileSystem)) {
                return basicReturnFailure("未获取到需要删除的文件信息！");
            }
            Path srcPath = new Path(url + transportFile.getFilePath());
            //删除服务器上文件

            boolean result = fileSystem.deleteOnExit(srcPath);
            if (result) {
                //删除文件记录表
                transportFileService.delete(fileId);
                return basicReturnSuccess("删除成功");
            }
        } catch (Exception e) {
            LOG.error("hdfs deleteFile {}", e);
        } finally {
            try {
                //删除时必须要将fileSystem关闭才能删除hdfs上文件，类似于提交，具体原因不详
                if (fileSystem != null) {
                    fileSystem.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return basicReturnFailure("删除失败");
    }

    /**
     * 判断HDFS文件是否存在
     *
     * @param path hdfs文件存储路径
     * @return bool
     */
    public boolean existFile(String path, FileSystem fileSystem) {
        boolean isExists = false;
        if (StringUtils.isEmpty(path)) {
            return false;
        }
        try {
            Path srcPath = new Path(path);
            isExists = fileSystem.exists(srcPath);
        } catch (Exception e) {
            LOG.error("existFile {}", e);
        }
        return isExists;
    }

    /**
     * 从hdfs获取文件流 inputstream
     */
    public InputStream getFileInputStream(String fileId) {
        //file表里查询文件信息
        TransportFile transportFile = transportFileService.findById(fileId);
        if (transportFile == null) return null;

        FileSystem fileSystem;
        InputStream inputStream;
        try {
            fileSystem = getFileSystem();
            inputStream = fileSystem.open(new Path(url + transportFile.getFilePath()));
            return inputStream;
        } catch (Exception e) {
            LOG.error(e.getMessage());
            return null;
        }
    }
}
