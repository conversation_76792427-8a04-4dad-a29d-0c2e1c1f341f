package com.bsm.v4.api.web.service.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.security.RegionService;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Region;
import com.bsm.v4.system.model.vo.security.RegionVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.service.RedisService;
import com.caictframework.utils.util.TreeBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Title: RegionWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/16 17:24
 * @description:
 */
@Service
public class RegionWebService extends BasicWebService {
    private static final Logger LOG = LoggerFactory.getLogger(RegionWebService.class);

    @Autowired
    private RegionService regionService;

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private RedisService redisService;

    /**
     * 添加、修改省市区数据
     */
    public Map<String, Object> save(RegionVO regionVO) {
        Region region = new Region();
        BeanUtils.copyProperties(regionVO, region);

        var id = regionService.save(region);
        return this.basicReturnResultJson(id);
    }

    /**
     * 查询所有省市区信息
     */
    public Map<String, Object> findAll() {
        return this.basicReturnResultJson(regionService.findAll());
    }

    /**
     * 查询所有省市区，以树形结构显示
     */
    public Map<String, Object> findAllTree() {
        List<RegionDTO> regionDTOList = regionService.findAllDto();
        if (regionDTOList != null) {
            return this.basicReturnResultJson(new TreeBuilder<RegionDTO>(regionDTOList).buildTree("0"));
        }
        return null;
    }

    /**
     * 查询省市区信息（根据id）
     */
    public Map<String, Object> findOne(String id) {
        RegionDTO regionDTO = new RegionDTO();
        Region region = regionService.findById(id);
        if (region != null) {
            BeanUtils.copyProperties(region, regionDTO);
        }
        return this.basicReturnResultJson(regionDTO);
    }

    /**
     * 根据code查询单个省市区信息
     */
    public Map<String, Object> findOneCode(String code) {
        RegionDTO regionDTO = regionService.findOneByCode(code);
        if (regionDTO != null) return this.basicReturnResultJson(regionDTO);
        return null;
    }

    /**
     * 查询所有的省市区信息（根据父级菜单Id)
     */
    public Map<String, Object> findAllByParentId(String parentId) {
        List<RegionDTO> regionDTOList = regionService.findAllByParentId(parentId);
        if (regionDTOList != null) return this.basicReturnResultJson(regionDTOList);
        return null;
    }

    /**
     * 查询所有的省市区信息（根据父级菜单id）（树形结构）
     */
    public Map<String, Object> findAllOfTreeByParentId(String parentId) {
        List<RegionDTO> regionDTOList = regionService.findAllByParentId(parentId);
        if (regionDTOList != null)
            return this.basicReturnResultJson(new TreeBuilder<RegionDTO>(regionDTOList).buildTree(parentId));
        return null;
    }

    /**
     * 根据id查询自身和下级的所有数据
     */
    public Map<String, Object> findAllTreeByIdOrParentId(String parentId) {
        List<RegionDTO> regionDTOList = regionService.findAllByIdOrParentId(parentId);
        if (regionDTOList != null)
            return this.basicReturnResultJson(new TreeBuilder<RegionDTO>(regionDTOList).buildTree(parentId));
        return null;
    }

    /**
     * 根据id查询自身和下级的所有数据
     */
    public Map<String, Object> findAllByIdOrParentId(String parentId) {
        return this.basicReturnResultJson(regionService.findAllByIdOrParentId(parentId));
    }

    /**
     * 删除省市区（根据id）
     */
    public Map<String, Object> delete(String id) {
        if (regionService.delete(id) > 0) return this.basicReturnResultJson(id);
        return null;
    }

    /**
     * 按照市名查询区
     */
    public Map<String, Object> getAreaByProvince(String name) {
        List<RegionDTO> regionList = regionService.findAreaByProvince(name);
        if (regionList != null && regionList.size() != 0) {
            List<Map<String, String>> mapList = new ArrayList<>();
            Map<String, List<Map<String, String>>> map = new HashMap<>();
            for (RegionDTO regionDTO : regionList) {
                Map<String, String> areaMap = new HashMap<>();
                areaMap.put("name", regionDTO.getName());
                mapList.add(areaMap);
            }
            map.put("area", mapList);
            return this.basicReturnResultJson(map);
        }
        return this.basicReturnResultJson("未找到相关区域");

    }

    /**
     * 查询登录账号市级数据
     */
    public Map<String, Object> findAllCityByUsers(String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        return this.basicReturnResultJson(regionService.findAllByParentLevel(usersDTO.getRegionId(), 3));
    }

    /**
     * 获取系统所属省的所有行政区放在内存中
     */
    public void findAllByOrg(String orgCode) {
        //存入redis
        List<RegionDTO> regionDTOList = regionService.findAllByOrg(orgCode);
        if (regionDTOList != null)
            redisService.set("Caict:regionCounty:orgRegion", JSONObject.toJSONString(regionDTOList));
    }
}
