package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.domain.security.service.business.transfer.CoordinateScheduleTempLogService;
import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleTempLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTempLog;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class CoordinateScheduleTempLogWebService extends BasicWebService {

    @Autowired
    private CoordinateScheduleTempLogService coordinateScheduleTempLogService;

    public void insertBatch(List<CoordinateScheduleTempLog> list) {
        coordinateScheduleTempLogService.insertBatch(list);
    }

    /**
     * 根据jobId、类型分页查询
     */
    public Map<String, Object> findAllPageByJobIdDataType(CoordinateScheduleTempLogDTO dto) {
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(coordinateScheduleTempLogService.findAllPageByJobIdDataType(dto)));
    }

    /**
     * 根据jobId、类型查询总数
     */
    public int selectCountByJobIdDataType(String appGuid, String dataType) {
        return coordinateScheduleTempLogService.selectCountByJobIdDataType(appGuid, dataType);
    }
}
