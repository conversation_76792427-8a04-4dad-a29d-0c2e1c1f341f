package com.bsm.v4.api.web.controller.document;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.document.FastFDSWebService;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * Created by dengsy on 2023-02-10.
 */
@RestController
@RequestMapping(value = "/apiWeb/document/fastFds")
@Api(value = "web端-文件系统-文件处理接口", tags = "web端-文件系统-文件处理接口")
public class FastFDSController extends BasicController {

    @ApiOperation(value = "上传fastFDS文件", notes = "上传文件接口")
    @PostMapping(value = "/")
    public JSONObject upload(@RequestParam("file") MultipartFile file, @RequestParam String fileType) {
        return this.basicReturnJson(file, FastFDSWebService.class, (vo, service) -> service.upload(vo, fileType));
    }

    @ApiOperation(value = "删除fastFDS文件", notes = "删除文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fastFdsVO", value = "FastFDS文件处理对象", required = true, paramType = "body", dataType = "fastFdsVO")
    })
    @DeleteMapping(value = "/delete/{fileId}")
    public JSONObject delete(@PathVariable String fileId) {
        return this.basicReturnJson(fileId, FastFDSWebService.class, (vo, service) -> service.delete(vo));
    }

    @ApiOperation(value = "下载fastFDS文件", notes = "下载文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fastFdsVO", value = "FastFDS文件处理对象", required = true, paramType = "body", dataType = "fastFdsVO")
    })
    @GetMapping(value = "/down/{fileId}")
    public void down(@PathVariable String fileId, HttpServletResponse response) {
        this.basicReturnJson(fileId, FastFDSWebService.class, (vo, service) -> service.downLoad(vo, response));
    }
}
