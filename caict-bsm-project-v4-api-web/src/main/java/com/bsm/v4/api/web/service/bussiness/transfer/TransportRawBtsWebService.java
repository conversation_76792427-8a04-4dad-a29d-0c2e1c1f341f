package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.business.transfer.TransportRawBtsService;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranchTemp;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportRawBtsWebService {

    @Autowired
    private TransportRawBtsService transportRawBtsService;

    //根据fileId删除所有数据
    public JSONObject deleteAllByFileId(String fileId) {
        if (transportRawBtsService.deleteAllByFileId(fileId) > 0) {
            return JSONResult.getSuccessJson("删除成功");
        }
        return JSONResult.getFailureJson("删除失败，无数据");
    }

    /**
     * 根据jobId查询总数
     */
    public int selectCountByJobId(String jobId) {
        return transportRawBtsService.selectCountByJobId(jobId);
    }

    /**
     * 根据jobId分页查询
     */
    public PageInfo<TransportRawBtsDTO> findAllPageByJobId(int page, int rows, String jobId) {
        TransportRawBtsDTO transportRawBtsDTO = new TransportRawBtsDTO();
        transportRawBtsDTO.setPageNum(page);
        transportRawBtsDTO.setPageSize(rows);
        return new PageHandle(transportRawBtsDTO).buildPage(transportRawBtsService.findAllPageByJobId(jobId));
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     *
     * @param jobGuid jobGuid
     * @return list
     */
    public List<TransportRawBtsDTO> findAllBtsId1(String jobGuid) {
        return transportRawBtsService.findAllBtsId1(jobGuid);
    }

    /**
     * 根据cellid查询数据
     *
     * @param orgType orgType
     * @return list
     */
    public List<AsyncRawBts> findLayuanByOrgType(String orgType) {
        return transportRawBtsService.findLayuanByOrgType(orgType);
    }

    /**
     * 更新数据
     *
     * @param transportRawBts transportRawBts
     */
    public void update(TransportRawBts transportRawBts) {
        transportRawBtsService.update(transportRawBts);
    }

    /**
     * 根据btsids查询所有
     *
     * @param jobId  jobId
     * @param btsIds btsId
     * @return list
     */
    public List<TransportRawBts> findAllByBtsIds(String[] btsIds, String jobId) {
        return transportRawBtsService.findAllByBtsIds(btsIds, jobId);
    }

    /**
     * 将表数据按地区分类
     *
     * @param jobId jobId
     * @return list
     */
    public List<TransportJobBranchTemp> selectDistinctRegionByJobid(String jobId) {
        return transportRawBtsService.selectDistinctRegionByJobid(jobId);
    }

    /**
     * 根据fileId查询总数
     */
    public int selectCountByFileId(String fileId) {
        return transportRawBtsService.selectCountByFileId(fileId);
    }

    /**
     * 批量添加
     */
    public int insertBatch(List<TransportRawBts> transportRawBtsList) {
        return transportRawBtsService.insertBatch(transportRawBtsList);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<TransportRawBts> transportRawBtsList) {
        return transportRawBtsService.updateBatch(transportRawBtsList);
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     */
    public List<String> findAllBtsId(String jobGuid, String fileGuid) {
        return transportRawBtsService.findAllBtsId(jobGuid, fileGuid);
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     */
    public List<TransportRawBtsDTO> findAllBtsIdExpend(String jobGuid, String fileGuid) {
        return transportRawBtsService.findAllBtsIdExpend(jobGuid, fileGuid);
    }

    /**
     * 根据基站批量修改状态
     */
    public int updateIsValid(String jobGuid, String fileGuid, long isValid) {
        return transportRawBtsService.updateIsValid(jobGuid, fileGuid, isValid);
    }

    /**
     * 批量处理直放站，修改bts_id为cell_id
     */
    public int updateBtsFromCell(String jobGuid, String fileGuid) {
        return transportRawBtsService.updateBtsFromCell(jobGuid, fileGuid);
    }

    /**
     * 识别新增（方法待优化）
     */
    public int updateBtsDataTypeAdd(String jobGuid, String regionCode) {
        return transportRawBtsService.updateBtsDataTypeAdd(jobGuid, regionCode);
    }

    /**
     * 识别变更和不变（方法待优化）
     */
    public int updateBtsDataTypeUpdate(String jobGuid, String regionCode) {
        return transportRawBtsService.updateBtsDataTypeUpdate(jobGuid, regionCode);
    }

    /**
     * 识别延续和不变（方法待优化）
     */
    public int updateBtsDataTypeContinue(String jobGuid, String regionCode) {
        return transportRawBtsService.updateBtsDataTypeContinue(jobGuid, regionCode);
    }

    /**
     * 运营商增量提交新增数据时进行数据类型更改
     */
    public int updateBtsDataTypeSingleAdd(String jobGuid, String regionCode) {
        return transportRawBtsService.updateBtsDataTypeSingleAdd(jobGuid, regionCode);
    }

    /**
     * 运营商增量提交变更数据时进行数据类型更改
     */
    public int updateBtsDataTypeSingleUpdate(String jobGuid, String regionCode) {
        return transportRawBtsService.updateBtsDataTypeSingleUpdate(jobGuid, regionCode);
    }

    /**
     * 运营商增量提交注销数据时进行数据类型更改
     */
    public int updateBtsDataTypeSingleDel(String jobGuid, String regionCode) {
        return transportRawBtsService.updateBtsDataTypeSingleDel(jobGuid, regionCode);
    }

    /**
     * 根据Job删除
     */
    public int deleteAllByJob(String jobGuid) {
        return transportRawBtsService.deleteAllByJob(jobGuid);
    }
}