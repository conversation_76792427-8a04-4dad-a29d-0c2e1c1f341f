package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtAntfeedService;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtAntfeedWebService extends BasicWebService {

    @Autowired
    private RsbtAntfeedService rsbtAntfeedService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtAntfeed> rsbtAntfeedListInsert) {
        return rsbtAntfeedService.insertBatch(rsbtAntfeedListInsert);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtAntfeed> rsbtAntfeedListUpdate) {
        return rsbtAntfeedService.updateBatch(rsbtAntfeedListUpdate);
    }

    /**
     * 批量修改
     */
    public void deleteBatch(List<RsbtAntfeed> rsbtAntfeedListDel) {
        rsbtAntfeedService.deleteBatch(Collections.singletonList(rsbtAntfeedListDel));
    }

    /**
     * 根据基站id和扇区id查询
     */
    public RsbtAntfeed findOneByStationCell(String stationGuid, String cellId) {
        return rsbtAntfeedService.findOneByStationCell(stationGuid, cellId);
    }
}
