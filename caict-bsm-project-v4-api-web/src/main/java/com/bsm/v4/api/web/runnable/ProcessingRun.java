package com.bsm.v4.api.web.runnable;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportJobBranchWebService;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.caictframework.utils.component.ApplicationContextProvider;
import com.caictframework.utils.service.RedisService;
import com.caictframework.utils.util.RedisLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;

/**
 * Created by dengsy on 2020-06-18.
 * 自动提交处理任务多线程
 */
public class ProcessingRun implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(ProcessingRun.class);

    private String jobGuid;
    private String userGuid;

    public ProcessingRun(String jobGuid,String userGuid){
        this.jobGuid = jobGuid;
        this.userGuid = userGuid;
    }

    private RedisService redisService;
    private TransportJobBranchWebService transportJobBranchWebService;

    @Override
    public void run() {
        try {
            LOG.warn("自动提交处理任务已开启！");
            transportJobBranchWebService = ApplicationContextProvider.getBean(TransportJobBranchWebService.class);
            redisService = ApplicationContextProvider.getBean(RedisService.class);

            List<TransportJobBranchDTO> transportJobBranchDTOList = transportJobBranchWebService.findAllByJobState(jobGuid,"1");
            if (transportJobBranchDTOList!= null && transportJobBranchDTOList.size() > 0){
                //创建同步锁用于入库处理结束判断
                int pageLock = transportJobBranchDTOList.size();
                RedisLock redisLock = new RedisLock();
                redisLock.setLock("1");
                redisLock.setContent(pageLock);
                //存入处理数量锁
                String jobGuid = transportJobBranchDTOList.get(0).getJobGuid();
                redisService.set(jobGuid+".approve." + "number", JSONObject.toJSONString(redisLock));

                //创建同步锁用于入库操作结束判断
                redisLock.setContent(0);
                redisService.set(jobGuid+".approve.data." + "number",JSONObject.toJSONString(redisLock));

                //处理提交任务
                for (TransportJobBranchDTO transportJobBranchDTO : transportJobBranchDTOList){
                    //transportJobBranchWebService.commit(userGuid,transportJobBranchDTO.getGuid());
                }
            }
        } catch (Exception e) {
            LOG.error("自动提交处理任务已开启错误：" + e.getMessage());
        }
    }
}
