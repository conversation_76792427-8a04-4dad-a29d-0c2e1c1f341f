package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.api.web.utils.CSVHelperUtil;
import com.bsm.v4.domain.security.service.business.transfer.LogTransportJobService;
import com.bsm.v4.system.model.contrust.DBBoolConst;
import com.bsm.v4.system.model.contrust.transfer.LogTransportJobTypeConst;
import com.bsm.v4.system.model.dto.business.transfer.LogTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.ConfirmDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.LogTransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;
import com.bsm.v4.system.model.vo.business.transfer.LogTransportJobVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.data.util.SnowflakeManager;
import com.caictframework.utils.util.JSONResult;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.*;

@Service
public class LogTransportJobWebService extends BasicWebService {

    @Autowired
    private LogTransportJobService logTransportJobService;

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private SnowflakeManager snowflakeManager;

    @Value("${caict.myFilePath}")
    private String filePath;

    @Value("${caict.myFileExportPath}")
    private String serverExportPath;

    /**
     * 根据日志类型和文件id查询
     */
    public Map<String, Object> findAllByLogTypeFileId(LogTransportJobDTO logTransportJobDTO) {
        return this.basicReturnResultJson(new PageHandle(logTransportJobDTO).buildPage(
                logTransportJobService.findAllByLogTypeFileId(LogTransportJobTypeConst.ERROR, logTransportJobDTO.getFileGuid())));
    }

    public void deleteByFileId(String fileId) {
        logTransportJobService.deleteByFileId(fileId);
    }

    /**
     * 下载
     */
    public JSONObject exportLog(LogTransportJobDTO logTransportJobDTO) {
        String fileName = null;
        try {
            List<LogTransportJobDTO> logList = logTransportJobService.findAllByLogTypeFileId(LogTransportJobTypeConst.ERROR, logTransportJobDTO.getFileGuid());
            if (logList != null && logList.size() != 0) {
                Workbook workbook = exportData(logList);
                if (workbook != null) {
                    fileName = "文件异常信息" + new Date().getTime() + ".xlsx";
                    OutputStream out = new FileOutputStream(filePath + fileName);
                    workbook.write(out);
                    out.flush();
                }
                return JSONResult.getSuccessJson(fileName, "导出成功");
            } else {
                return JSONResult.getFailureJson("查询下载数据为空！");
            }
        } catch (Exception e) {
            return JSONResult.getFailureJson("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 下载校验后错误的日志
     *
     * @param logTransportJobDTO
     * @param response
     * @return
     */
    public JSONObject exportErrorLog(LogTransportJobDTO logTransportJobDTO, HttpServletResponse response) {

        // 当前年月日
        String timeStr = new Timestamp(System.currentTimeMillis()).toString().replaceAll("\\.", "_")
                .replace(" ", "_").replaceAll("-", "").replaceAll(":", "")
                .replaceAll("\\\\", "");
        // 打包后的文件路径
        String filePath = serverExportPath + "errorLogExport" + File.separator + timeStr + File.separator;
        String fileName = timeStr;
        try {
            List<LogTransportJob> logList = logTransportJobService.findErrorDataByFileId(logTransportJobDTO.getFileGuid());
            if (logList != null && logList.size() != 0) {

                List<List<Object>> list = new ArrayList<>();

                list.add(Arrays.asList("错误详情", "基站ID", "扇区ID"));
                for (LogTransportJob logTransportJob : logList) {
                    List<Object> listTemp = new ArrayList<>();
                    listTemp.add(logTransportJob.getLogDetail());
                    listTemp.add(logTransportJob.getBtsId());
                    listTemp.add(logTransportJob.getCellId());
                    list.add(listTemp);
                }

                CSVHelperUtil.createCSVFile(list, filePath, fileName, response);
                return JSONResult.getSuccessJson(fileName, "导出成功");
            } else {
                return JSONResult.getFailureJson("下载数据为空！");
            }
        } catch (Exception e) {
            return JSONResult.getFailureJson("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 生成Excel并写入数据信息
     *
     * @param dataList 数据列表
     * @return 写入数据后的工作簿对象
     */
    public Workbook exportData(List<LogTransportJobDTO> dataList) {
        // 生成xlsx的Excel
        Workbook workbook = new SXSSFWorkbook();

        // 如需生成xls的Excel，请使用下面的工作簿对象，注意后续输出时文件后缀名也需更改为xls
        //Workbook workbook = new HSSFWorkbook();

        // 生成Sheet表，写入第一行的列头
        Sheet sheet = buildDataSheet(workbook);
        //构建每行的数据内容
        int rowNum = 1;
        for (Iterator<LogTransportJobDTO> it = dataList.iterator(); it.hasNext(); ) {
            LogTransportJobDTO data = it.next();
            if (data == null) {
                continue;
            }
            //输出行数据
            Row row = sheet.createRow(rowNum++);
            convertDataToRow(data, row);
        }
        return workbook;
    }

    /**
     * 生成sheet表，并写入第一行数据（列头）
     *
     * @param workbook 工作簿对象
     * @return 已经写入列头的Sheet
     */
    protected Sheet buildDataSheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet();
        // 写入第一行各列的数据
        createFirstRow(sheet);
        return sheet;
    }

    protected void createFirstRow(Sheet sheet) {
        Row firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue("错误详情");
        firstRow.createCell(1).setCellValue("错误类型");
        firstRow.createCell(2).setCellValue("基站id");
        firstRow.createCell(3).setCellValue("扇区id");
        firstRow.createCell(4).setCellValue("文件名称");
    }

    /**
     * 将数据转换成行
     *
     * @param data 源数据
     * @param row  行对象
     * @return
     */
    private static void convertDataToRow(LogTransportJobDTO data, Row row) {
        int cellNum = 0;
        Cell cell;
        // 错误详情
        cell = row.createCell(cellNum++);
        cell.setCellValue(null == data.getLogDetail() ? "" : data.getLogDetail());
        // 错误类型
        cell = row.createCell(cellNum++);
        cell.setCellValue(null == data.getLogShort() ? "" : data.getLogShort());
        // 基站id
        cell = row.createCell(cellNum++);
        cell.setCellValue(null == data.getBtsId() ? "" : data.getBtsId());
        // 扇区id
        cell = row.createCell(cellNum++);
        cell.setCellValue(null == data.getCellId() ? "" : data.getCellId());
        //文件名称
        cell = row.createCell(cellNum++);
        cell.setCellValue(null == data.getFileName() ? "" : data.getFileName());

    }

    /**
     * 设置Log数据
     */
    public LogTransportJob setLogTransportJob(String jobId, Long type, String brief, String detail, String fileGuid) {
        return logTransportJobService.setLogTransportJob(jobId, type, brief, detail, fileGuid);
    }

    /**
     * 保存
     */
    public String save(LogTransportJob logTransportJob) {
        return logTransportJobService.save(logTransportJob).toString();
    }

    /**
     * 添加
     */
    public int insert(LogTransportJob logTransportJob) {

        return logTransportJobService.insert(logTransportJob);


    }

    /**
     * 赋值log数据
     */
    public LogTransportJob addLogValue(String jobId, Long type, String brief, String detail, String fileGuid, String btsId, String cellId) {
        LogTransportJob logTransportJob = new LogTransportJob();
        logTransportJob.setGuid(String.valueOf(snowflakeManager.nextValue()));
        logTransportJob.setIsDeleted(DBBoolConst.FALSE);
        logTransportJob.setGmtCreate(new Date());
        logTransportJob.setGmtModified(new Date());
        logTransportJob.setJobGuid(jobId);
        logTransportJob.setLogType(type);
        logTransportJob.setLogShort(brief);
        logTransportJob.setLogDetail(detail);
        logTransportJob.setFileGuid(fileGuid);
        if (btsId != null && cellId != null) {
            logTransportJob.setBtsId(btsId);
            logTransportJob.setCellId(cellId);
        }

        return logTransportJob;
    }

    /**
     * 赋值log数据
     */
    public LogTransportJob addLogValueAsNew(String jobId, Long type, String brief, String detail, String fileGuid, TransportRawBts transportRawBts) {
        LogTransportJob logTransportJob = new LogTransportJob();
        logTransportJob.setGuid(String.valueOf(snowflakeManager.nextValue()));
        logTransportJob.setIsDeleted(DBBoolConst.FALSE);
        logTransportJob.setGmtCreate(new Date());
        logTransportJob.setGmtModified(new Date());
        logTransportJob.setJobGuid(jobId);
        logTransportJob.setLogType(type);
        logTransportJob.setLogShort(brief);
        logTransportJob.setLogDetail(detail);
        logTransportJob.setFileGuid(fileGuid);
        if (transportRawBts.getBtsId() != null && transportRawBts.getCellId() != null) {
            logTransportJob.setBtsId(transportRawBts.getBtsId() );
            logTransportJob.setCellId(transportRawBts.getCellId());
            logTransportJob.setCellName(transportRawBts.getCellName());
            logTransportJob.setBtsName(transportRawBts.getBtsName());
            logTransportJob.setTechType(transportRawBts.getTechType());
            logTransportJob.setLocation(transportRawBts.getLocation());
            logTransportJob.setCounty(transportRawBts.getCounty());
            logTransportJob.setLongItude(transportRawBts.getLongitude());
            logTransportJob.setLatitude(transportRawBts.getLatitude());
            logTransportJob.setSendStartFreq(transportRawBts.getSendStartFreq());
            logTransportJob.setSendEndFreq(transportRawBts.getSendEndFreq());
            logTransportJob.setAccStartFreq(transportRawBts.getAccStartFreq());
            logTransportJob.setAccEndFreq(transportRawBts.getAccEndFreq());
            logTransportJob.setMaxEmissivePower(transportRawBts.getMaxEmissivePower());
            logTransportJob.setHeight(transportRawBts.getHeight());
            logTransportJob.setDeviceFactory(transportRawBts.getVendorName());
            logTransportJob.setDeviceModel(transportRawBts.getDeviceModel());
            logTransportJob.setModelCode(transportRawBts.getModelCode());
            logTransportJob.setAntennaModel(transportRawBts.getAntennaModel());
            logTransportJob.setAntennaFactory(transportRawBts.getAntennaFactory());
            logTransportJob.setPolarizationMode(transportRawBts.getPolarizationMode());
            logTransportJob.setAntennaAzimuth(transportRawBts.getAntennaAzimuth());
            logTransportJob.setFeederLoss(transportRawBts.getFeederLoss());
            logTransportJob.setAntennaGain(transportRawBts.getAntennaGain());
            logTransportJob.setAltitude(transportRawBts.getAltitude());
            logTransportJob.setExpandStation(transportRawBts.getExpandStation());
            logTransportJob.setStScene(transportRawBts.getStServR());
            logTransportJob.setTrfDate(transportRawBts.getTrfDate());
            logTransportJob.setTrfUser(transportRawBts.getTrfUser());
            logTransportJob.setTrfData(transportRawBts.getTrfData());
        }

        return logTransportJob;
    }

    /**
     * 添加log数据
     */
    public LogTransportJob addLog(String jobId, Long type, String brief, String detail, String fileGuid) {
        LogTransportJob logTransportJob = addLogValue(jobId, type, brief, detail, fileGuid, null, null);
        logTransportJobService.insert(logTransportJob);
        return logTransportJob;
    }

    /**
     * 根据日志类型和FileId查询总条数
     */
    public int selectCountByTypeFileId(Long logType, String fileId) {
        return logTransportJobService.selectCountByTypeFileId(logType, fileId);
    }

    /**
     * 批量添加
     */
    public int insertBatch(List<LogTransportJob> logTransportJobList) {
        return logTransportJobService.insertBatch(logTransportJobList);
    }

    /**
     * 初始加载登录用户待办任务-运营商
     */
    public Map<String, Object> loadInitTaskDetail(String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        if ("admin".equals(usersDTO.getRoleDTO().getType()) || "wuweiProvincial".equals(usersDTO.getRoleDTO().getType())){
            usersDTO.setId(null);
        }
        List<ConfirmDTO> taskDataForOperator = logTransportJobService.getTaskDataForOperator(usersDTO.getId());
        if (taskDataForOperator!=null && taskDataForOperator.size()!=0){
            taskDataForOperator.removeIf(confirmDTO -> confirmDTO.getTransportJobBranchList().size() == 0);
        }
        return this.basicReturnSuccess(taskDataForOperator);
    }

    /**
     * 待办数据详情导出
     */
    public Map<String, Object> exportTaskDetail(LogTransportJobVO logTransportJobVO, HttpServletResponse response) {
        String timeStr = new Timestamp(System.currentTimeMillis()).toString().replaceAll("\\.", "_")
                .replace(" ", "_").replaceAll("-", "").replaceAll(":", "")
                .replaceAll("\\\\", "");
        // 打包后的文件路径
        String filePath = serverExportPath + "exportTaskDetail" + File.separator + timeStr + File.separator;
        String fileName = timeStr;
        String resultPath = "export/exportTaskDetail" + File.separator + timeStr + File.separator + fileName + ".csv";

        logTransportJobVO.setUserType(authWebService.findLoginUsersDTO(logTransportJobVO.getToken()).getRoleDTO().getType());
        logTransportJobVO.setUserId(authWebService.findLoginUsersDTO(logTransportJobVO.getToken()).getUserId());
        try {
            List<LogTransportJobDTO> logList = new ArrayList<>();
            if(logTransportJobVO.getDownType().equals("1")){
                logList = logTransportJobService.findExportPassTaskDetail(logTransportJobVO);
            }else if(logTransportJobVO.getDownType().equals("0")){
                logList = logTransportJobService.findExportFailedTaskDetail(logTransportJobVO);
            }else{
                return this.basicReturnSuccess("下载请求异常！");
            }

            if (logList != null && logList.size() != 0) {
                List<List<Object>> list = new ArrayList<>();
                list.add(Arrays.asList("任务编号","错误描述","错误类别","小区名称", "小区识别码", "基站名称","基站识别码","技术体制","台址","地区","经度"
                        ,"纬度","发射起始频率","发射终止频率","接收起始频率","接收终止频率","最大发射功率","天线距地高度","设备工厂"
                        ,"设备型号","设备核准代码","天线类型","天线生产厂家","极化方式","天线方位角","馈线系统总损耗","天线增益"
                        ,"海拔高度","站点","基站覆盖场景","用户数或流量统计年月","平均忙时激活用户数","平均忙时激活用户流量"
                ));
                for (LogTransportJobDTO logTransportJob : logList) {
                    List<Object> listTemp = new ArrayList<>();
                    listTemp.add(logTransportJob.getJobId());
                    listTemp.add(logTransportJob.getLogDetail());
                    listTemp.add(logTransportJob.getLogShort());
                    listTemp.add(logTransportJob.getCellName());
                    listTemp.add(logTransportJob.getCellId());
                    listTemp.add(logTransportJob.getBtsName());
                    listTemp.add(logTransportJob.getBtsId());
                    listTemp.add(logTransportJob.getTechType());
                    listTemp.add(logTransportJob.getLocation());
                    listTemp.add(logTransportJob.getLatitude());
                    listTemp.add(logTransportJob.getSendStartFreq());
                    listTemp.add(logTransportJob.getSendEndFreq());
                    listTemp.add(logTransportJob.getAccStartFreq());
                    listTemp.add(logTransportJob.getAccEndFreq());
                    listTemp.add(logTransportJob.getMaxEmissivePower());
                    listTemp.add(logTransportJob.getHeight());
                    listTemp.add(logTransportJob.getDeviceFactory());
                    listTemp.add(logTransportJob.getDeviceModel());
                    listTemp.add(logTransportJob.getModelCode());
                    listTemp.add(logTransportJob.getAntennaModel());
                    listTemp.add(logTransportJob.getAntennaFactory());
                    listTemp.add(logTransportJob.getPolarizationMode());
                    listTemp.add(logTransportJob.getAntennaAzimuth());
                    listTemp.add(logTransportJob.getFeederLoss());
                    listTemp.add(logTransportJob.getAntennaGain());
                    listTemp.add(logTransportJob.getAltitude());
                    listTemp.add(logTransportJob.getExpandStation());
                    listTemp.add(logTransportJob.getStScene());
                    listTemp.add(logTransportJob.getTrfDate());
                    listTemp.add(logTransportJob.getTrfUser());
                    listTemp.add(logTransportJob.getTrfData());
                    list.add(listTemp);
                }
                if(CSVHelperUtil.createCSVFileUrl(list, filePath, fileName, serverExportPath + "exportTaskDetail")){
                    return this.basicReturnSuccess(resultPath);
                }else{
                    return this.basicReturnFailure("下载数据失败！");
                }
            } else {
                return this.basicReturnFailure("下载数据为空！");
            }
        } catch (Exception e) {
            return this.basicReturnFailure("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 批量添加
     */
    public int insetrtBatch(List<LogTransportJob> logTransportJobList) {
        return logTransportJobService.insertBatch(logTransportJobList);
    }

    public List<TransportRawBtsDealDTO> findByJobId(String jobId){
        return logTransportJobService.findByJobId(jobId);
    }
}
