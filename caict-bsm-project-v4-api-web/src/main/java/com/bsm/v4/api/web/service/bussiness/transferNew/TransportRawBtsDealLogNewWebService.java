package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealLogNewService;
import com.bsm.v4.system.model.vo.business.transferNew.FileUploadResultVO;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 传输原始数据处理错误日志表WebService
 */
@Service
public class TransportRawBtsDealLogNewWebService extends BasicWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportRawBtsDealLogNewService transportRawBtsDealLogNewService;



}
