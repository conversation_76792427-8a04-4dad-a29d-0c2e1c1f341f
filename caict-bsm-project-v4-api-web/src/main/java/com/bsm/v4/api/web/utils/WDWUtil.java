package com.bsm.v4.api.web.utils;

import javax.imageio.ImageIO;
import javax.imageio.stream.ImageInputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;

/**
 * Created by dsy62 on 2017-12-07.
 * 判断word和excel是03或是07+
 */
public class WDWUtil {

    /**
     * 判断是否为03excel
     * 返回true为是
     */
    static boolean isExcel03(String filePath) {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    /**
     * 判断是否为07excel
     * 返回true为是
     */
    static boolean isExcel07(String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }

    /**
     * 判断是否为03word
     * 返回true为是
     */
    public static boolean isWord03(String filePath) {
        return filePath.matches("^.+\\.(?i)(doc)$");
    }

    /**
     * 判断是否为07word
     * 返回true为是
     */
    public static boolean isWord07(String filePath) {
        return filePath.matches("^.+\\.(?i)(docx)$");
    }

    /**
     * 判断是否为图片
     * 返回true为是
     */
    public static boolean isImage(String filePath) throws IOException {
        ImageInputStream iis = ImageIO.createImageInputStream(new File(filePath));
        Iterator i = ImageIO.getImageReaders(iis);
        BufferedImage bi = ImageIO.read(new File(filePath));
        if (!i.hasNext() || bi == null) {
            return false;
        } else {
            return true;
        }
    }
}
