package com.bsm.v4.api.web.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;

/**
 * HttpClintUtil
 *
 * <AUTHOR>
 * @Date 2023年4月12日14:24:15
 */
public class HttpClintUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClintUtil.class);

    /**
     * Java通过get请求发送给PHP数据，并接收返回消息。
     *
     * @param url:请求url。
     * @return errorStr:错误信息;status:状态码，response：返回数据。
     */
    public static Map<String, Object> sendGetOthers(String url, String token, String userId) {
        Map<String, Object> result = new HashMap<>();
        String errorStr = "";
        StringBuilder response = new StringBuilder();
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接.
            URLConnection conn = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            // 设置请求属性.
            httpUrlConnection.setRequestProperty("Accept", "application/json, text/plain, */*");
            httpUrlConnection.setRequestProperty("Connection", "Keep-Alive");
            httpUrlConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            httpUrlConnection.setRequestProperty("Cookie", "tssotoken=" + token + ", loginName=" + userId);
            // 忽略缓存.
            httpUrlConnection.setUseCaches(false);

            // 设置URL请求方法.
            httpUrlConnection.setRequestMethod("GET");
            httpUrlConnection.connect();
            // 定义BufferedReader输入流来读取URL的响应.
            InputStream is;
            // 判断响应的状态码.200,201,202.
            if (httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_OK
                    || httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_CREATED
                    || httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_ACCEPTED) {
                is = httpUrlConnection.getInputStream();
            } else {
                is = httpUrlConnection.getErrorStream();
            }
            in = new BufferedReader(new InputStreamReader(is));
            result.put("status", httpUrlConnection.getResponseCode());
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
        } catch (Exception e) {
            logger.error("post exception [].info." + e);
            errorStr = "post exception [].info: " + e.getMessage();
        }
        // 使用finally块来关闭输出流、输入流.
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        result.put("errorStr", errorStr);
        result.put("response", response.toString());
        return result;

    }

    /**
     * Java通过post请求发送数据，并接收返回消息。
     *
     * @param url:请求url。
     * @param content:   请求体(参数)。
     * @param cookie:    cookie。
     * @param cTimeOut:  链接超时。
     * @param rTimeOut:  读超时。
     * @return errorStr:错误信息;status:状态码，response：返回数据。
     */
    public static Map<String, Object> sendPostAndRead(String url, String content, String cookie, Integer cTimeOut, Integer rTimeOut) {
        Map<String, Object> result = new HashMap<>();
        if (!StringUtils.isNotEmpty(url)) {
            result.put("errorStr", "url为空");
            result.put("response", "");
            result.put("status", "");
            return result;
        }
        String errorStr = "";
        StringBuilder response = new StringBuilder();
        PrintWriter out = null;
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接.
            URLConnection conn = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            // 设置请求属性.
            httpUrlConnection.setRequestProperty("Content-Type", "application/json");
            // 发送POST请求必须设置如下两行.设置连接属性.使用 URL 连接进行输出.使用 URL 连接进行输入.
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setDoInput(true);
            httpUrlConnection.setRequestProperty("cookie", cookie);
            // 忽略缓存.
            httpUrlConnection.setUseCaches(false);
            // 设置URL请求方法.
            httpUrlConnection.setRequestMethod("POST");
            if (cTimeOut != null && cTimeOut != 0) {
                httpUrlConnection.setConnectTimeout(cTimeOut);
            }
            if (rTimeOut != null && rTimeOut != 0) {
                httpUrlConnection.setReadTimeout(rTimeOut);
            }
            // 获取URLConnection对象对应的输出流.
            out = new PrintWriter(httpUrlConnection.getOutputStream());
            // 发送请求参数.
            out.write(content);
            // flush输出流的缓冲.
            out.flush();
            httpUrlConnection.connect();
            result.put("status", httpUrlConnection.getResponseCode());
            // 定义BufferedReader输入流来读取URL的响应.
            in = new BufferedReader(new InputStreamReader(httpUrlConnection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
        } catch (Exception e) {
            logger.error("发送 POST 请求出现异常！[].info" + e);
            errorStr = e.getMessage();
        }
        // 使用finally块来关闭输出流、输入流.
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        result.put("errorStr", errorStr);
        result.put("response", response.toString());
        return result;

    }


    /**
     * Java通过post请求发送数据，并接收返回消息。
     *
     * @param url:请求url。
     * @param content:   请求体(参数)。
     * @return fileName
     */
    public static Map<String, Object> sendPostAndRead(String url, String content, String token) {
        Map<String, Object> result = new HashMap<>();
        String errorStr = "";
        PrintWriter out = null;
        StringBuilder response = new StringBuilder();
        BufferedReader in;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接.
            URLConnection conn = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            // 设置请求属性.
            httpUrlConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            httpUrlConnection.setRequestProperty("Accept", "application/json, text/plain, */*");
            httpUrlConnection.setRequestProperty("Connection", "Keep-Alive");
            httpUrlConnection.setRequestProperty("token", token);
            // 发送POST请求必须设置如下两行.设置连接属性.使用 URL 连接进行输出.使用 URL 连接进行输入.
            httpUrlConnection.setDoOutput(true);
            httpUrlConnection.setDoInput(true);
            // 忽略缓存.
            httpUrlConnection.setUseCaches(false);
            // 设置URL请求方法.
            httpUrlConnection.setRequestMethod("POST");
            // 获取URLConnection对象对应的输出流.
            httpUrlConnection.connect();
            out = new PrintWriter(httpUrlConnection.getOutputStream());
            // 发送请求参数.
            out.write(content);
            // flush输出流的缓冲.
            out.flush();

            // 定义BufferedReader输入流来读取URL的响应.
            InputStream is;
            // 判断响应的状态码.200,201,202.
            if (httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_OK
                    || httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_CREATED
                    || httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_ACCEPTED) {
                is = httpUrlConnection.getInputStream();
            } else {
                is = httpUrlConnection.getErrorStream();
            }
            in = new BufferedReader(new InputStreamReader(is));
            result.put("status", httpUrlConnection.getResponseCode());
            String line;
            while ((line = in.readLine()) != null) {
                response.append(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("post exception [].info." + e);
            errorStr = "post exception [].info: " + e.getMessage();
        }
        // 使用finally块来关闭输出流、输入流.
        finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        result.put("errorStr", errorStr);
        result.put("response", response.toString());
        return result;
    }

    /**
     * Java通过get请求发送给PHP数据，并接收返回消息。
     *
     * @param url:请求url。
     * @return errorStr:错误信息;status:状态码，response：返回数据。
     */
    public static Map<String, Object> sendGetLogin(String url) {
        Map<String, Object> result = new HashMap<>();
        String errorStr = "";
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接.
            URLConnection conn = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            // 忽略缓存.
            httpUrlConnection.setUseCaches(false);
            // 设置URL请求方法.
            httpUrlConnection.setRequestMethod("GET");
            httpUrlConnection.connect();
            String key;
            for (int i = 1; (key = httpUrlConnection.getHeaderFieldKey(i)) != null; i++) {
                if (key.equals("Set-Cookie")) {
                    String cookie = httpUrlConnection.getHeaderField(i);
                    result.put("cookie", cookie.substring(11, cookie.split(";")[0].length()));
                    break;
                }
            }
            // 定义BufferedReader输入流来读取URL的响应.
            InputStream is;
            // 判断响应的状态码.200,201,202.
            if (httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_OK
                    || httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_CREATED
                    || httpUrlConnection.getResponseCode() == HttpURLConnection.HTTP_ACCEPTED) {
                is = httpUrlConnection.getInputStream();
            } else {
                is = httpUrlConnection.getErrorStream();
            }
            in = new BufferedReader(new InputStreamReader(is));
            result.put("status", httpUrlConnection.getResponseCode());
        } catch (Exception e) {
            logger.error("post exception [].info." + e);
            errorStr = "post exception [].info: " + e.getMessage();
        }
        // 使用finally块来关闭输出流、输入流.
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        result.put("errorStr", errorStr);
        return result;

    }
}
