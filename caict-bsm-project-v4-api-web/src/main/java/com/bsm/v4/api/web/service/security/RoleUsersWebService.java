package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.RoleUsersService;
import com.caictframework.data.service.BasicWebService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * @Title: RoleUsersWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/16 11:28
 * @description:
 */
@Service
public class RoleUsersWebService extends BasicWebService {
    private static final Logger LOG = LoggerFactory.getLogger(RoleUsersWebService.class);

    @Autowired
    private RoleUsersService roleUsersService;

    /**
     * 绑定用户
     */
    @Transactional
    public Map<String, Object> setUsersRole(String roleId, String[] usersIds) {
        return this.basicReturnResultJson(roleUsersService.setUsersRole(roleId, usersIds));
    }

    /**
     * 根据role_id删除role_users
     */
    public int deleteByRoleId(String roleId) {
        return roleUsersService.deleteByRoleId(roleId);
    }

    /**
     * 根据users_id删除role_users
     */
    public int deleteByUsersId(String usersId) {
        return roleUsersService.deleteByUsersId(usersId);
    }
}
