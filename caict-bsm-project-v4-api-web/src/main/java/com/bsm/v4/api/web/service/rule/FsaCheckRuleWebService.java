package com.bsm.v4.api.web.service.rule;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.utils.ExcelReader;
import com.bsm.v4.api.web.utils.ExcelWriter;
import com.bsm.v4.domain.security.service.business.rule.FsaCheckRuleService;
import com.bsm.v4.domain.security.service.security.OrgService;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.entity.business.rule.FsaCheckRule;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.security.RsbtOrg;
import com.bsm.v4.system.model.vo.business.rule.FsaCheckRuleVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.service.RedisService;
import com.github.pagehelper.PageInfo;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

@Service
public class FsaCheckRuleWebService extends BasicWebService {

    @Autowired
    private FsaCheckRuleService fsaCheckRuleService;
    @Autowired
    private OrgService rsbtOrgService;
    @Autowired
    private RedisService redisService;

    @Value("${caict.myFilePath}")
    private String filePath;

    /**
     * 保存
     */
    public Map<String, Object> saveFsaCheckRule(FsaCheckRuleVO fsaCheckRuleVO) {
        String id = fsaCheckRuleVO.getId();
        FsaCheckRule fsaCheckRule = new FsaCheckRule();
        BeanUtils.copyProperties(fsaCheckRuleVO, fsaCheckRule);
        OrgDTO orgDetail = rsbtOrgService.findOneDetails(fsaCheckRuleVO.getOrgGuid());
        if (orgDetail != null) {
            fsaCheckRule.setOrgType(orgDetail.getType());
        }
        //判断数据是否重复
        if (!"".equals(id)) {
            if (fsaCheckRuleService.selectCountByOrgAndNetTsAndGenNum(fsaCheckRuleVO) < 2) {
                String save = fsaCheckRuleService.save(fsaCheckRule);
                if (save != null) {
                    //更新redis规则
                    findAllCheckRuleByIsDeleted();
                    findAllCheckRuleNetTsByIsDeleted();
                    return this.basicReturnResultJson(save);
                }
            }
            return this.basicReturnResultJson("发射频段或接收频率与其他重复");
        } else {
            if (fsaCheckRuleService.selectCountByOrgAndNetTsAndGenNum(fsaCheckRuleVO) < 1) {
                Long save = fsaCheckRuleService.save(fsaCheckRule);
                if (save != null) {
                    //更新redis规则
                    findAllCheckRuleByIsDeleted();
                    findAllCheckRuleNetTsByIsDeleted();
                    return this.basicReturnResultJson(save.toString());
                }
            }
            return this.basicReturnResultJson("该运营商的同一信号或制式下的发射频段或接收频率重复");
        }
    }

    /**
     * 查询详情
     */
    public Map<String, Object> findOneDto(String id) {
        return this.basicReturnResultJson(fsaCheckRuleService.findOneDto(id));
    }

    /**
     * 删除一个
     */
    public Map<String, Object> delete(String id) {
        if (fsaCheckRuleService.delete(id) > 0) {
            //更新redis规则
            findAllCheckRuleByIsDeleted();
            findAllCheckRuleNetTsByIsDeleted();
            return this.basicReturnResultJson("删除成功");
        } else {
            return this.basicReturnResultJson("删除失败");
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     */
    public Map<String, Object> deletes(String[] ids) {
        //将ids由String[]转换为List<Object>

        List<Object> list = new ArrayList<>();
        Collections.addAll(list, ids);


        if (fsaCheckRuleService.deleteBatch(list) > 0) {
            //更新redis规则
            findAllCheckRuleByIsDeleted();
            findAllCheckRuleNetTsByIsDeleted();
            return this.basicReturnResultJson("删除成功");
        } else {
            return this.basicReturnResultJson("删除失败");
        }
    }

    public List<FsaCheckRule> findAll() {
        return fsaCheckRuleService.findAll();
    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllPageByWhere(FsaCheckRuleVO fsaCheckRuleVO) {
        PageInfo<FsaCheckRuleDTO> fsaCheckRuleDTOList = new PageHandle(fsaCheckRuleVO).buildPage(fsaCheckRuleService.findAllPageByWhere(fsaCheckRuleVO));
        if (fsaCheckRuleDTOList.getList() != null && fsaCheckRuleDTOList.getList().size() != 0) {
            return this.basicReturnResultJson(fsaCheckRuleDTOList);
        } else
            return this.basicReturnResultJson("查询失败");
    }


    /**
     * 修改全部状态
     */
    public Map<String, Object> updateAllState(String state) {
        if (fsaCheckRuleService.updateAllState(state) > 0) {
            //更新redis规则
            findAllCheckRuleByIsDeleted();
            findAllCheckRuleNetTsByIsDeleted();
            return this.basicReturnResultJson("修改成功");
        }
        return this.basicReturnResultJson("修改失败");
    }

    /**
     * 查询全部启用规则，并存储到redis
     */
    public void findAllCheckRuleByIsDeleted() {
        List<FsaCheckRuleDTO> fsaCheckRuleDTOList = rsbtOrgService.findAllCheckRuleByIsDeleted();
        if (fsaCheckRuleDTOList != null && fsaCheckRuleDTOList.size() > 0) {
            //详细校验规则
            Map<String, Object> map = new HashMap<>();
            //信号校验规则
            Map<String, Object> genNumMap = new HashMap<>();

            for (FsaCheckRuleDTO fsaCheckRuleVO : fsaCheckRuleDTOList) {

                //获取制式集合
                List<FsaCheckRuleDTO> netTsList = fsaCheckRuleVO.getFsaCheckRuleDTOList();
                Map<String, Object> netTsMap = new HashMap<>();
                Map<String, Object> genNumNetTsMap = new HashMap<>();

                if (netTsList != null && netTsList.size() > 0) {
                    for (FsaCheckRuleDTO fsaCheckRule : netTsList) {
                        //获取详情集合
                        List<FsaCheckRuleDTO> fsaCheckRuleList = fsaCheckRule.getFsaCheckRuleDTOList();
                        if (fsaCheckRuleList != null && fsaCheckRuleList.size() > 0) {
                            netTsMap.put(fsaCheckRule.getNetTs(), fsaCheckRuleList);
                        }
                        genNumNetTsMap.put(fsaCheckRule.getNetTs(), fsaCheckRule.getGenNum());
                    }
                }
                map.put(fsaCheckRuleVO.getOrgType(), netTsMap);
                genNumMap.put(fsaCheckRuleVO.getOrgType(), genNumNetTsMap);
            }

            //存入redis
            redisService.set("Caict:checkRule:netTs", JSONObject.toJSONString(map));
            redisService.set("Caict:checkRule:genNum", JSONObject.toJSONString(genNumMap));
        }
    }

    /**
     * 查询全部启用规则的制式，并存储到redis
     */
    public void findAllCheckRuleNetTsByIsDeleted() {
        List<FsaCheckRuleDTO> fsaCheckRuleDTOList = rsbtOrgService.findDistinctCheckRuleByIsDeleted();
        if (fsaCheckRuleDTOList != null && fsaCheckRuleDTOList.size() > 0) {
            //所有制式
            Map<String, Object> orgNetTsMap = new HashMap<>();
            for (FsaCheckRuleDTO fsaCheckRuleVO : fsaCheckRuleDTOList) {

                //获取制式集合
                List<FsaCheckRuleDTO> netTsList = fsaCheckRuleVO.getFsaCheckRuleDTOList();
                List<String> orgNetTsList = new ArrayList<>();

                if (netTsList != null && netTsList.size() > 0) {
                    for (FsaCheckRuleDTO fsaCheckRule : netTsList) {
                        orgNetTsList.add(fsaCheckRule.getNetTs());
                    }
                }
                orgNetTsMap.put(fsaCheckRuleVO.getOrgType(), orgNetTsList);
            }
            //存入redis
            if (orgNetTsMap != null) redisService.set("Caict:checkRule:orgNetTs", JSONObject.toJSONString(orgNetTsMap));
        }
    }

    /**
     * 获取所有运营商的制式
     */
    public Map<String, Object> getCheckRuleNetTs() {
        String json = redisService.get("Caict:checkRule:orgNetTs");
        if (!"".equals(json) && json != null) {
            Map<String, Object> orgNetTsMap = JSONObject.parseObject(json, Map.class);
            return this.basicReturnResultJson(orgNetTsMap);
        }
        return null;
    }

    public int saveList(List<FsaCheckRule> list) {
        return fsaCheckRuleService.insertBatch(list);
    }

    public Map<String, Object> importExcel(MultipartFile file) {
        List<FsaCheckRule> list = ExcelReader.readExcel(file);
        for (FsaCheckRule fsaCheckRule : list) {
            String name = fsaCheckRule.getOrgGuid();
            OrgDTO org = rsbtOrgService.findOneByOrgName(name);
            if (org != null) {
                fsaCheckRule.setOrgGuid(org.getGuid());
            }
        }
        int i = saveList(list);
        if (i < 0) return this.basicReturnResultJson("导入失败");
        return this.basicReturnResultJson("导入成功！");
    }

    /**
     * 批量导出数据到excel
     *
     * @return
     */
    public Map<String, Object> exportExcel() throws IOException {
        Workbook workbook = null;
        OutputStream out = null;
        String fileName = null;
        List<FsaCheckRule> list = findAll();
        for (FsaCheckRule fsaCheckRule : list) {
            String orgGuid = fsaCheckRule.getOrgGuid();
            RsbtOrg org = rsbtOrgService.findById(orgGuid);
            if (org != null) fsaCheckRule.setOrgGuid(org.getOrgName());
        }
        try {
            // 生成Excel工作簿对象并写入数据
            workbook = ExcelWriter.exportData(list);
            if (workbook != null) {
                String excelName = "导出Excel";
                fileName = excelName + UUID.randomUUID() + ".xlsx";
                out = new FileOutputStream(filePath + fileName);
                workbook.write(out);
                out.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != workbook) {
                workbook.close();
            }
            if (null != out) {
                out.close();
            }
        }

        return this.basicReturnResultJson(fileName);
    }

    public List<FsaCheckRuleDTO> findByOrgGuidAndTechType(String type, String netType) {
        return fsaCheckRuleService.findByOrgGuidAndTechType(type, netType);
    }
}
