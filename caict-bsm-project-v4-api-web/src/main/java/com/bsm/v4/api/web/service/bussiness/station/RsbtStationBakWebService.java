package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtStationBakService;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationBakWebService extends BasicWebService {

    @Autowired
    private RsbtStationBakService rsbtStationBakService;

    /**
     * 批量添加
     */
    public void insertBatch(List<RsbtStationBak> rsbtStationBakList) {
        if (rsbtStationBakList.size() > 30) {
            int pageNum = rsbtStationBakList.size() % 30 == 0 ? rsbtStationBakList.size() / 30 : rsbtStationBakList.size() / 30 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtStationBak> stations;
                if (i != pageNum) {
                    stations = rsbtStationBakList.subList((i - 1) * 30, i * 30);
                } else {
                    stations = rsbtStationBakList.subList((i - 1) * 30, rsbtStationBakList.size());
                }
                rsbtStationBakService.insertBatch(stations);
            }
        } else {
            rsbtStationBakService.insertBatch(rsbtStationBakList);
        }
    }
}
