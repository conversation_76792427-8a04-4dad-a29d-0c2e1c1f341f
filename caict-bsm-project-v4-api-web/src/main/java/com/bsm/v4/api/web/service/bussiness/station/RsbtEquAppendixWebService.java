package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtEquAppendixService;
import com.bsm.v4.domain.security.service.business.station.RsbtEquService;
import com.bsm.v4.domain.security.service.business.station.RsbtEquTService;
import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import com.bsm.v4.system.model.entity.business.station.RsbtEquAppendix;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtEquAppendixWebService extends BasicWebService {

    @Autowired
    private RsbtEquAppendixService rsbtEquAppendixService;

    @Autowired
    private RsbtEquTService rsbtEquTService;

    @Autowired
    private RsbtEquService rsbtEquService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtEquAppendix> rsbtEquAppendixListInsert) {
        return rsbtEquAppendixService.insertBatch(rsbtEquAppendixListInsert);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtEquAppendix> rsbtEquAppendixList) {
        return rsbtEquAppendixService.updateBatch(rsbtEquAppendixList);
    }

    /**
     * 批量删除（修改状态）
     */
    public void deleteBatchByEquGuid(List<RsbtEqu> rsbtEquListDelete) {
        List<Object> ids = rsbtEquListDelete.stream().map(RsbtEqu::getGuid).collect(Collectors.toList());
        rsbtEquAppendixService.deleteBatch(ids);
        rsbtEquTService.deleteBatch(ids);
        rsbtEquService.deleteBatch(ids);
    }
}
