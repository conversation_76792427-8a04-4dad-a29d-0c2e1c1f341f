package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.domain.security.service.business.transfer.TransportJobBranchTempService;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranchTemp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description TransportJobBranchTempWebService
 * @date 2023/4/27 17:12
 */
@Service
public class TransportJobBranchTempWebService {

    @Autowired
    private TransportJobBranchTempService transportJobBranchTempService;

    public int insertBatch(List<TransportJobBranchTemp> transportJobBranchTemps) {
        return transportJobBranchTempService.insertBatch(transportJobBranchTemps);
    }

    public List<TransportJobBranchTemp> findAllBranch(String jobGuid) {
        return transportJobBranchTempService.findAllBranch(jobGuid);
    }

    public List<TransportJobBranchTemp> findGroupByRegion() {
        return transportJobBranchTempService.findGroupByRegion();
    }
}
