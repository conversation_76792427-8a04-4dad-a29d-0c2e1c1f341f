package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtAntfeedTService;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeedT;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class RsbtAntfeedTWebService extends BasicWebService {

    @Autowired
    private RsbtAntfeedTService rsbtAntfeedTService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtAntfeedT> rsbtAntfeedTList) {
        return rsbtAntfeedTService.insertBatch(rsbtAntfeedTList);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtAntfeedT> rsbtAntfeedTList) {
        return rsbtAntfeedTService.updateBatch(rsbtAntfeedTList);
    }

    /**
     * 批量刪除
     */
    public void deleteBatch(List<RsbtAntfeedT> rsbtAntfeedTList) {
        rsbtAntfeedTService.deleteBatch(Collections.singletonList(rsbtAntfeedTList));
    }
}
