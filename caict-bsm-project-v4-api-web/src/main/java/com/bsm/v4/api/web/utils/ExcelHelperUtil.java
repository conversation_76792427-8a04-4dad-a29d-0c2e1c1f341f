package com.bsm.v4.api.web.utils;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.StringTokenizer;

/**
 * ExcelHelperUtil.
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 5/13/21 9:22 AM
 **/
public class ExcelHelperUtil {

    // 加载slf4j日志记录器。
    private static Logger logger = LoggerFactory.getLogger(ExcelHelperUtil.class);

    /**
     * 保存数据到Excel。
     *
     * @param data 数组。
     */
    @SuppressWarnings("resource")
    public void exportDataToExcel(String[][] data, HttpServletResponse response) {
        int rowLength = data.length;
        int columnLength = data[1].length;
        // 创建Excel的工作书册 Workbook,对应到一个excel文档。
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建Excel的工作sheet,对应到一个excel文档的tab。
        HSSFSheet sheet = wb.createSheet("sheet1");
        // 设置相关基本属性。
        // 1、设置excel每列宽度。
        for (int i = 0; i < columnLength; i++) {
            sheet.setColumnWidth(i, 4000);
        }
        // 2、创建字体样式。
        HSSFFont font = wb.createFont();
        // 字体。
        font.setFontName("Verdana");
        // 颜色。
        font.setColor(Font.COLOR_NORMAL);
        // 3、创建单元格样式。
        HSSFCellStyle style = wb.createCellStyle();
        // 设置居中。
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中。
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置颜色。
        style.setFillForegroundColor(IndexedColors.GREEN.index);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        // 设置字体。
        style.setFont(font);
        // 设置单元格内容格式。
        HSSFCellStyle style1 = wb.createCellStyle();
        // 自动换行。
        style1.setWrapText(true);
        for (int i = 0; i < rowLength; i++) {
            // 创建Excel的sheet的一行。
            HSSFRow row = sheet.createRow(i);
            // 设定行的高度。
            row.setHeight((short) 500);
            for (int j = 0; j < columnLength; j++) {
                // 创建一个Excel的单元格。
                HSSFCell cell = row.createCell(j);
                if (i == 0) {
                    // 标题
                    CellRangeAddress region = new CellRangeAddress((short) 0, (short) 0, (short) 0, (short) columnLength - 1);
                    cell.setCellStyle(style1);
                    cell.setCellStyle(style);
                    sheet.removeMergedRegion(0);
                    sheet.addMergedRegion(region);
                    cell.setCellValue(data[i][j]);
                } else {
                    // 设置单元格的样式格式。
                    cell.setCellStyle(style1);
                    cell.setCellStyle(style);
                    cell.setCellValue(data[i][j]);
                }
            }
        }
        // 格式化时间。
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmssSSS");
        // 获取当前时间。
        Date date = new Date();
        try {
            response.reset();
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + new String((sdf.format(date) + ".xls").getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8) + "\"");
            OutputStream out = response.getOutputStream();
            wb.write(out);
            out.flush();
            out.close();
            response.flushBuffer();
        } catch (IOException e) {
            logger.error("异常：{}", e);
        }
    }

    /**
     * 保存数据到Excel。
     *
     * @param data 数组。
     */
    @SuppressWarnings("resource")
    public String importDataToExcel(String[][] data, String excelDir, String fileName) {
        // 读取文件。
        File exclDir = new File(excelDir);
        // 判断是否有这个文件夹，没有就创建一个。
        if (!exclDir.exists())
            exclDir.mkdirs();
        String msg;
        int rowLenth = data.length;
        int columnLenth = data[0].length;
        // 创建Excel的工作书册 Workbook,对应到一个excel文档。
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建Excel的工作sheet,对应到一个excel文档的tab。
        HSSFSheet sheet = wb.createSheet("sheet1");
        // 设置相关基本属性。
        // 1、设置excel每列宽度。
        for (int i = 0; i < columnLenth; i++) {
            sheet.setColumnWidth(i, 4000);
        }
        // 2、创建字体样式。
        HSSFFont font = wb.createFont();
        // 字体。
        font.setFontName("Verdana");
        // 颜色。
        font.setColor(Font.COLOR_NORMAL);
        // 3、创建单元格样式。
        HSSFCellStyle style = wb.createCellStyle();
        // 设置居中。
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中。
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置颜色。
        style.setFillForegroundColor(IndexedColors.GREEN.index);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        // 设置字体。
        style.setFont(font);
        // 设置单元格内容格式。
        HSSFCellStyle style1 = wb.createCellStyle();
        // 自动换行。
        style1.setWrapText(true);
        for (int i = 0; i < rowLenth; i++) {
            // 创建Excel的sheet的一行。
            HSSFRow row = sheet.createRow(i);
            // 设定行的高度。
            row.setHeight((short) 500);
            for (int j = 0; j < columnLenth; j++) {
                // 创建一个Excel的单元格。
                HSSFCell cell = row.createCell(j);
                // 设置单元格的样式格式。
                cell.setCellStyle(style1);
                cell.setCellStyle(style);
                cell.setCellValue(data[i][j]);
            }
        }
        try {
            // 格式化时间。
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmssSSS");
            // 获取当前时间。
            Date date = new Date();
            // 将时间作为文件名输出。
            FileOutputStream os = new FileOutputStream(excelDir + (fileName == null ? sdf.format(date) : sdf.format(date) + fileName) + ".xls");
            try {
                wb.write(os);
                os.close();
                msg = "文件生成成功！" + excelDir + (fileName == null ? sdf.format(date) : sdf.format(date) + fileName) + ".xls";
                return msg;
            } catch (IOException e) {
                msg = "文件生成失败！";
                logger.error("异常：{}", e);
                return msg;
            }
        } catch (FileNotFoundException e) {
            msg = "文件生成失败！";
            logger.error("异常：{}", e);
            return msg;
        }
    }

    /**
     * 保存数据到Excel。
     *
     * @param data 数组。
     */
    @SuppressWarnings("resource")
    public String importDataToExcel(String[][] data, String excelDir) {
        // 读取文件。
        File exclDir = new File(excelDir);
        // 判断是否有这个文件夹，没有就创建一个。
        if (!exclDir.exists())
            exclDir.mkdirs();
        String msg;
        int rowLenth = data.length;
        int columnLenth = data[0].length;
        // 创建Excel的工作书册 Workbook,对应到一个excel文档。
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建Excel的工作sheet,对应到一个excel文档的tab。
        HSSFSheet sheet = wb.createSheet("sheet1");
        // 设置相关基本属性。
        // 1、设置excel每列宽度。
        for (int i = 0; i < columnLenth; i++) {
            sheet.setColumnWidth(i, 4000);
        }
        // 2、创建字体样式。
        HSSFFont font = wb.createFont();
        // 字体。
        font.setFontName("Verdana");
        // 颜色。
        font.setColor(Font.COLOR_NORMAL);
        // 3、创建单元格样式。
        HSSFCellStyle style = wb.createCellStyle();
        // 设置居中。
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中。
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置颜色。
        style.setFillForegroundColor(IndexedColors.GREEN.index);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        // 设置字体。
        style.setFont(font);
        // 设置单元格内容格式。
        HSSFCellStyle style1 = wb.createCellStyle();
        // 自动换行。
        style1.setWrapText(true);
        for (int i = 0; i < rowLenth; i++) {
            // 创建Excel的sheet的一行。
            HSSFRow row = sheet.createRow(i);
            // 设定行的高度。
            row.setHeight((short) 500);
            for (int j = 0; j < columnLenth; j++) {
                // 创建一个Excel的单元格。
                HSSFCell cell = row.createCell(j);
                // 设置单元格的样式格式。
                cell.setCellStyle(style1);
                cell.setCellStyle(style);
                cell.setCellValue(data[i][j]);
            }
        }
        try {
            // 格式化时间。
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmssSSS");
            // 获取当前时间。
            Date date = new Date();
            // 将时间作为文件名输出。
            FileOutputStream os = new FileOutputStream(excelDir + sdf.format(date) + ".xls");
            try {
                wb.write(os);
                os.close();
                msg = "文件生成成功！" + excelDir + sdf.format(date) + ".xls";
                return msg;
            } catch (IOException e) {
                msg = "文件生成失败！";
                logger.error("异常：{}", e);
                return msg;
            }
        } catch (FileNotFoundException e) {
            msg = "文件生成失败！";
            logger.error("异常：{}", e);
            return msg;
        }
    }

    /**
     * 保存数据到Excel。
     *
     * @param data 数组。
     */
    @SuppressWarnings("resource")
    public void exportDataToExcel2(String[][] data, HttpServletResponse response) {
        int rowLength = data.length;
        int columnLength = data[1].length;
        // 创建Excel的工作书册 Workbook,对应到一个excel文档。
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建Excel的工作sheet,对应到一个excel文档的tab。
        HSSFSheet sheet = wb.createSheet("sheet1");
        // 设置相关基本属性。
        // 1、设置excel每列宽度。
        for (int i = 0; i < columnLength; i++) {
            if (i == 0) {
                sheet.setColumnWidth(i, 3000);
            } else if (i == columnLength - 1) {
                sheet.setColumnWidth(i, 6500);
            } else {
                sheet.setColumnWidth(i, 5000);
            }
        }
        // 2、创建字体样式。
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) 14);
        // 字体。
        font.setFontName("Verdana");
        // 颜色。
        font.setColor(Font.COLOR_NORMAL);
        // 3、创建单元格样式。
        HSSFCellStyle style = wb.createCellStyle();
        // 设置居中。
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中。
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置颜色。
        style.setFillForegroundColor(IndexedColors.GREEN.index);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        // 设置字体。
        style.setFont(font);
        // 设置单元格内容格式。
        HSSFCellStyle style1 = wb.createCellStyle();
        // 设置居中。
        style1.setAlignment(HorizontalAlignment.LEFT);
        // 设置垂直居中。
        style1.setVerticalAlignment(VerticalAlignment.CENTER);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style1.setBorderBottom(BorderStyle.THIN);
        style1.setBorderLeft(BorderStyle.THIN);
        style1.setBorderRight(BorderStyle.THIN);
        style1.setBorderTop(BorderStyle.THIN);
        for (int i = 0; i < rowLength; i++) {
            // 创建Excel的sheet的一行。
            HSSFRow row = sheet.createRow(i);
            // 设定行的高度。
            row.setHeight((short) 1000);
            for (int j = 0; j < columnLength; j++) {
                // 创建一个Excel的单元格。
                HSSFCell cell = row.createCell(j);
                if (i == 0) {
                    // 标题
                    CellRangeAddress region = new CellRangeAddress((short) 0, (short) 0, (short) 0, (short) columnLength - 1);
//                    cell.setCellStyle(style1);
                    cell.setCellStyle(style);
                    sheet.removeMergedRegion(0);
                    sheet.addMergedRegion(region);
                    cell.setCellValue(data[i][j]);
                } else if (i == 1) {
                    // 标题
                    if (j == 0) {
                        CellRangeAddress region = new CellRangeAddress((short) 1, (short) 1, (short) 0, (short) columnLength - 2);
                        sheet.addMergedRegion(region);
                        cell.setCellStyle(style1);
                        cell.setCellStyle(style);
                        cell.setCellValue(data[i][j]);
                    } else if (j == columnLength - 1) {
                        // 设置单元格的样式格式。
                        cell.setCellStyle(style1);
                        cell.setCellStyle(style);
                        cell.setCellValue(data[i][j]);
                    }
                } else {
                    // 设置单元格的样式格式。
                    if (j > 0 && i > 2) {
                        cell.setCellStyle(style1);
                        cell.setCellStyle(style);
                        cell.setCellValue(data[i][j]);
                    } else {
                        cell.setCellStyle(style);
                        cell.setCellValue(data[i][j]);
                    }
                }
            }
        }
        // 格式化时间。
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmssSSS");
        // 获取当前时间。
        Date date = new Date();
        try {
            response.reset();
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + new String((sdf.format(date) + ".xls").getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8) + "\"");
            OutputStream out = response.getOutputStream();
            wb.write(out);
            out.flush();
            out.close();
            response.flushBuffer();
        } catch (IOException e) {
            logger.error("异常：{}", e);
        }
    }

    /**
     * 保存数据到Excel。
     *
     * @param data 数组。
     */
    @SuppressWarnings("resource")
    public String exportDataToExcel3(String[][] data, String excelDir, String fileName) {
        // 读取文件。
        File exclDir = new File(excelDir);
        // 判断是否有这个文件夹，没有就创建一个。
        if (!exclDir.exists())
            exclDir.mkdirs();
        String msg;
        int rowLength = data.length;
        int columnLength = data[1].length;
        // 创建Excel的工作书册 Workbook,对应到一个excel文档。
        HSSFWorkbook wb = new HSSFWorkbook();
        // 创建Excel的工作sheet,对应到一个excel文档的tab。
        HSSFSheet sheet = wb.createSheet("sheet1");
        // 设置相关基本属性。
        // 1、设置excel每列宽度。
        for (int i = 0; i < columnLength; i++) {
            sheet.setColumnWidth(i, 3000);
        }
        // 2、创建字体样式。
        HSSFFont font = wb.createFont();
        font.setFontHeightInPoints((short) 12);
        // 字体。
        font.setFontName("Verdana");
        // 颜色。
        font.setColor(Font.COLOR_NORMAL);
        // 3、创建单元格样式。
        HSSFCellStyle style = wb.createCellStyle();
        // 设置居中。
        style.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中。
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置颜色。
        style.setFillForegroundColor(IndexedColors.GREEN.index);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        // 设置字体。
        style.setFont(font);
        // 设置单元格内容格式。
        HSSFCellStyle style1 = wb.createCellStyle();
        // 自动换行
        style1.setWrapText(true);
        // 设置居中。
        style1.setAlignment(HorizontalAlignment.LEFT);
        // 设置垂直居中。
        style1.setVerticalAlignment(VerticalAlignment.CENTER);
        // 4、设置边框。
        // 一个单元格的上下左右的边框。
        style1.setBorderBottom(BorderStyle.THIN);
        style1.setBorderLeft(BorderStyle.THIN);
        style1.setBorderRight(BorderStyle.THIN);
        style1.setBorderTop(BorderStyle.THIN);
        for (int i = 0; i < rowLength; i++) {
            // 创建Excel的sheet的一行。
            HSSFRow row = sheet.createRow(i);
            // 设定行的高度。
            row.setHeight((short) 1000);
            for (int j = 0; j < columnLength; j++) {
                // 创建一个Excel的单元格。
                HSSFCell cell = row.createCell(j);
                if (i == 0) {
                    // 标题
                    cell.setCellStyle(style1);
                    cell.setCellValue(data[i][j]);
                } else {
                    cell.setCellStyle(style);
                    cell.setCellValue(data[i][j]);
                }
            }
        }

        try {
            // 格式化时间。
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmssSSS");
            // 获取当前时间。
            Date date = new Date();
            // 将时间作为文件名输出。
            FileOutputStream os = new FileOutputStream(excelDir + (fileName == null ? sdf.format(date) : fileName + sdf.format(date)) + ".xls");
            try {
                wb.write(os);
                os.close();
                msg = excelDir + (fileName == null ? sdf.format(date) : fileName + sdf.format(date)) + ".xls";
                return msg;
            } catch (IOException e) {
                msg = "文件生成失败！";
                logger.error("异常：{}", e);
                return msg;
            }
        } catch (FileNotFoundException e) {
            msg = "文件生成失败！";
            logger.error("异常：{}", e);
            return msg;
        }
    }


    /**
     * 从Excel获取数据。
     *
     * @param filePath 文件地址。
     * @return String 二维数组。
     */
    public String[][] getDataFromExcel(String filePath) {

        ArrayList<Object> result = new ArrayList<>();
        // 创建Workbook工作薄对象，表示整个excel。
        Workbook wb = null;
        try {
            // 读取文件。
            File file = new File(filePath);
            BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
            // 检查文件。
            checkExcel(file);
            // 获取文件名。
            File tempFile = new File(filePath.trim());
            String fileName = tempFile.getName();
            try {
                // 判断是xls还是xlsx。
                if (fileName.endsWith("xls")) {
                    wb = new HSSFWorkbook(bis);
                } else if (fileName.endsWith("xlsx")) {
                    wb = new XSSFWorkbook(bis);
                }
                if (wb != null) {
                    // 读取工作表的个数。
                    for (int sheetIndex = 0; sheetIndex < wb.getNumberOfSheets(); sheetIndex++) {
                        Sheet st = wb.getSheetAt(sheetIndex);
                        // 读取表的行数。
                        for (int rowIndex = 0; rowIndex <= st.getLastRowNum(); rowIndex++) {
                            // 创建行。
                            Row row = st.getRow(rowIndex);
                            if (row == null) {
                                continue;
                            }
                            // 一维数组的长度。
                            int tempRowSize = row.getLastCellNum();
                            // 创建一个有tempRowSize的一维数组。
                            String[] values = new String[tempRowSize];
                            // 用空值填充数组。
                            Arrays.fill(values, "");
                            boolean hasValue = false;
                            // columnIndex：列数。
                            for (int columnIndex = 0; columnIndex < tempRowSize; columnIndex++) {
                                // 初始化单元格的值。
                                String value = "";
                                // 获取单元格的数据。
                                // 读取单元格的类。
                                Cell cell = row.getCell(columnIndex);
                                if (cell != null) {
                                    // 判断单元格中数据的类型。
                                    switch (cell.getCellTypeEnum()) {
                                        // 字符串。
                                        case STRING:
                                            value = cell.getStringCellValue();
                                            break;
                                        // 数字。
                                        // case HSSFCell.CELL_TYPE_NUMERIC:
                                        case NUMERIC:
                                            if (HSSFDateUtil.isCellDateFormatted(cell)) {
                                                // 时间类型。
                                                Date date = cell.getDateCellValue();
                                                if (date != null) {
                                                    value = new SimpleDateFormat("yyyy-MM-dd").format(date);
                                                } else {
                                                    value = "";
                                                }
                                            } else {
                                                // 其他数字类型。
                                                value = new DecimalFormat("0").format(cell.getNumericCellValue());
                                            }
                                            break;
                                        // 公式。
                                        case FORMULA:
                                            // 导入时如果为公式生成的数据则无值。
                                            if (!cell.getStringCellValue().equals("")) {
                                                value = cell.getStringCellValue();
                                            } else {
                                                value = cell.getNumericCellValue() + "";
                                            }
                                            break;
                                        // 空值。
                                        case BLANK:
                                            value = "";
                                            break;
                                        // 故障。
                                        case ERROR:
                                            value = "非法字符";
                                            break;
                                        // boolean。
                                        case BOOLEAN:
                                            value = (cell.getBooleanCellValue() ? "Y" : "N");
                                            break;
                                        default:
                                            value = "";
                                    }
                                }
                                // 当列数为0、或者value去除首尾的空格之后的值为空时，退出循环。
                                if (columnIndex == 0 && value.trim().equals("")) {
                                    break;
                                }
                                // 将数据放入数组中。
                                values[columnIndex] = value;
                                hasValue = true;
                            }
                            if (hasValue) {
                                result.add(values);
                            }
                        }
                    }
                }
                // 关流。
                bis.close();
                // 创建一个二维数组。
                String[][] returnArray = new String[result.size()][];
                // 循环将数据放入二维数组中，得到二维数组。
                for (int i = 0; i < returnArray.length; i++) {
                    returnArray[i] = (String[]) result.get(i);
                }
                return returnArray;
            } catch (IOException e) {
                logger.error("异常信息：{}", e);
            }
        } catch (FileNotFoundException e) {
            logger.error("异常信息：{}", e);
        }
        return null;
    }

    /**
     * 检查文件是不是Excel。
     *
     * @param file file
     */
    private void checkExcel(File file) {
        if (!(file.isFile() && (file.getName().endsWith("xls") || file.getName().endsWith("xlsx")))) {
            try {
                throw new Exception("不是Excel文件！");
            } catch (Exception e) {
                logger.error("异常信息：{}", e);
            }
        }
    }

    /**
     * 将json字符串转成二维数组。
     *
     * @param stringList json字符串。
     * @param trLength   数组的行数。
     * @param tdLength   数组的列数。
     * @return data 二维数组。
     */
    public String[][] stringToArray(String stringList, int trLength, int tdLength) {
        // 二维数组的长度。
        String[][] data = new String[trLength][tdLength / trLength];
        // 截取字符串。
        StringTokenizer kenizer = new StringTokenizer(stringList, "],[");
        // 获取长度。
        int tokenNumber = kenizer.countTokens();
        // 判断长度是否相等。
        if (tokenNumber == tdLength) {
            // column.
            int col = 0;
            int row = 0;
            // 判断是否存在值。
            while (kenizer.hasMoreElements()) {
                // 去掉字符串中的换行和引号.
                String value = kenizer.nextToken().replace("\"", "").replace("\\n", "");
                // 去掉第一列的空列。
                if (col == 0 && "".equals(value)) {
                    continue;
                } else {
                    // 将数据放入数组中。
                    data[row][col] = value;
                }
                if (col == tdLength / trLength - 1) {
                    row++;
                    col = 0;
                    continue;
                }
                col++;
            }
            return data;
        }
        return null;
    }
}
