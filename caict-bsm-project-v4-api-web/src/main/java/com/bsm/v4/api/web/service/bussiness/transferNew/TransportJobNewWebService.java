package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportJobNewService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealNewService;
import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealLogNewService;
import com.bsm.v4.system.model.entity.business.transferNew.TransportJobNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealNew;
import com.bsm.v4.system.model.entity.business.transferNew.TransportRawBtsDealLogNew;
import com.bsm.v4.system.model.vo.business.transferNew.FileUploadResultVO;
import com.bsm.v4.system.model.vo.business.transferNew.JobMetaVO;
import com.bsm.v4.system.model.vo.business.transferNew.CsvValidationResultVO;
import com.caictframework.data.service.BasicWebService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.fastjson.JSON;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 传输任务表WebService
 */
@Service
public class TransportJobNewWebService extends BasicWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportJobNewService transportJobNewService;

    @Autowired
    private TransportRawBtsDealNewService transportRawBtsDealNewService;

    @Autowired
    private TransportRawBtsDealLogNewService transportRawBtsDealLogNewService;

    @Autowired
    private AuthWebService authWebService;

    // CSV字段映射配置：CSV表头 -> 实体类字段名
    private static final Map<String, String> CSV_FIELD_MAPPING = new LinkedHashMap<>();

    // 必填字段配置
    private static final Set<String> REQUIRED_FIELDS = new HashSet<>();

    // 数字字段配置
    private static final Set<String> NUMERIC_FIELDS = new HashSet<>();

    // 字符串字段长度限制配置
    private static final Map<String, Integer> STRING_LENGTH_LIMITS = new HashMap<>();

    static {
        // 初始化字段映射（根据TransportRawBtsDealNew实体类）
        CSV_FIELD_MAPPING.put("CELL_NAME", "cellName");
        CSV_FIELD_MAPPING.put("CELL_ID", "etEquCCode");
        CSV_FIELD_MAPPING.put("BTS_NAME", "statName");
        CSV_FIELD_MAPPING.put("BTS_ID", "stCCode");
        CSV_FIELD_MAPPING.put("TECH_TYPE", "netTs");
        CSV_FIELD_MAPPING.put("LOCATION", "statAddr");
        CSV_FIELD_MAPPING.put("COUNTY", "county");
        CSV_FIELD_MAPPING.put("LONGITUDE", "statLg");
        CSV_FIELD_MAPPING.put("LATITUDE", "statLa");
        CSV_FIELD_MAPPING.put("SEND_START_FREQ", "freqEfe");
        CSV_FIELD_MAPPING.put("SEND_END_FREQ", "freqEfb");
        CSV_FIELD_MAPPING.put("ACC_START_FREQ", "freqRfb");
        CSV_FIELD_MAPPING.put("ACC_END_FREQ", "freqRfe");
        CSV_FIELD_MAPPING.put("MAX_EMISSIVE_POWER", "maxEmissivePower");
        CSV_FIELD_MAPPING.put("HEIGHT", "antHight");
        CSV_FIELD_MAPPING.put("DEVICE_FACTORY", "equMenu");
        CSV_FIELD_MAPPING.put("DEVICE_MODEL", "equModel");
        CSV_FIELD_MAPPING.put("MODEL_CODE", "equAuth");
        CSV_FIELD_MAPPING.put("ANTENNA_MODEL", "antType");
        CSV_FIELD_MAPPING.put("ANTENNA_FACTORY", "antMenu");
        CSV_FIELD_MAPPING.put("POLARIZATION_MODE", "antPole");
        CSV_FIELD_MAPPING.put("ANTENNA_AZIMUTH", "seComm");
        CSV_FIELD_MAPPING.put("AT_RANG", "atRang");
        CSV_FIELD_MAPPING.put("AT_EANG", "atEang");
        CSV_FIELD_MAPPING.put("FEEDER_LOSS", "feedLose");
        CSV_FIELD_MAPPING.put("ANTENNA_GAIN", "antGain");
        CSV_FIELD_MAPPING.put("ALTITUDE", "statAt");
        CSV_FIELD_MAPPING.put("SET_YEAR", "setYear");
        CSV_FIELD_MAPPING.put("SET_MONTH", "setMonth");
        CSV_FIELD_MAPPING.put("EXPAND_STATION", "expandStation");
        CSV_FIELD_MAPPING.put("ATTRIBUTE_STATION", "attributeStation");
        CSV_FIELD_MAPPING.put("ST_SERV_R", "stServR");

        // 初始化必填字段
        REQUIRED_FIELDS.add("CELL_NAME");
        REQUIRED_FIELDS.add("CELL_ID");
        REQUIRED_FIELDS.add("BTS_NAME");
        REQUIRED_FIELDS.add("BTS_ID");
        REQUIRED_FIELDS.add("TECH_TYPE");
        REQUIRED_FIELDS.add("LOCATION");
        REQUIRED_FIELDS.add("LONGITUDE");
        REQUIRED_FIELDS.add("LATITUDE");
        REQUIRED_FIELDS.add("MAX_EMISSIVE_POWER");

        // 初始化数字字段
        NUMERIC_FIELDS.add("LONGITUDE");
        NUMERIC_FIELDS.add("LATITUDE");
        NUMERIC_FIELDS.add("SEND_START_FREQ");
        NUMERIC_FIELDS.add("SEND_END_FREQ");
        NUMERIC_FIELDS.add("ACC_START_FREQ");
        NUMERIC_FIELDS.add("ACC_END_FREQ");
        NUMERIC_FIELDS.add("MAX_EMISSIVE_POWER");
        NUMERIC_FIELDS.add("HEIGHT");
        NUMERIC_FIELDS.add("ANTENNA_AZIMUTH");
        NUMERIC_FIELDS.add("AT_RANG");
        NUMERIC_FIELDS.add("AT_EANG");
        NUMERIC_FIELDS.add("FEEDER_LOSS");
        NUMERIC_FIELDS.add("ANTENNA_GAIN");
        NUMERIC_FIELDS.add("ALTITUDE");
        NUMERIC_FIELDS.add("SET_YEAR");
        NUMERIC_FIELDS.add("SET_MONTH");
        NUMERIC_FIELDS.add("ST_SERV_R");

        // 初始化字符串长度限制（根据数据库字段长度）
        STRING_LENGTH_LIMITS.put("CELL_NAME", 100);
        STRING_LENGTH_LIMITS.put("CELL_ID", 50);
        STRING_LENGTH_LIMITS.put("BTS_NAME", 100);
        STRING_LENGTH_LIMITS.put("BTS_ID", 50);
        STRING_LENGTH_LIMITS.put("TECH_TYPE", 20);
        STRING_LENGTH_LIMITS.put("LOCATION", 200);
        STRING_LENGTH_LIMITS.put("COUNTY", 50);
        STRING_LENGTH_LIMITS.put("DEVICE_FACTORY", 50);
        STRING_LENGTH_LIMITS.put("DEVICE_MODEL", 50);
        STRING_LENGTH_LIMITS.put("MODEL_CODE", 50);
        STRING_LENGTH_LIMITS.put("ANTENNA_MODEL", 50);
        STRING_LENGTH_LIMITS.put("ANTENNA_FACTORY", 50);
        STRING_LENGTH_LIMITS.put("POLARIZATION_MODE", 20);
        STRING_LENGTH_LIMITS.put("EXPAND_STATION", 20);
        STRING_LENGTH_LIMITS.put("ATTRIBUTE_STATION", 20);
    }

    /**
     * 上传CSV文件
     * @param file 上传的文件
     * @return 上传结果
     */
    public Map<String, Object> uploadCsvFile(MultipartFile file) {
        try {
            // 校验文件后缀名
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".csv")) {
                return this.basicReturnFailure("文件后缀名必须是.csv");
            }

            // 确保目录存在
            File uploadDir = new File(myFilePath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 生成新文件名避免覆盖
            String newFileName = generateUniqueFileName(originalFilename);
            String filePath = myFilePath + File.separator + newFileName;

            // 保存文件
            File destFile = new File(filePath);
            file.transferTo(destFile);

            // 返回结果
            FileUploadResultVO result = new FileUploadResultVO(originalFilename, filePath);
            return this.basicReturnSuccess(result);

        } catch (IOException e) {
            return this.basicReturnFailure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            return this.basicReturnFailure("系统异常：" + e.getMessage());
        }
    }

    /**
     * 解析并校验CSV文件
     * @param filePath CSV文件路径
     * @param jobMeta 作业元数据
     * @return 校验结果
     */
    public Map<String, Object> parseAndValidateCsv(String filePath, JobMetaVO jobMeta) {
        return parseAndValidateCsv(filePath, jobMeta, null);
    }

    /**
     * 解析并校验CSV文件（指定编码）
     * @param filePath CSV文件路径
     * @param jobMeta 作业元数据
     * @param encoding 指定的编码，为null则自动检测
     * @return 校验结果
     */
    public Map<String, Object> parseAndValidateCsv(String filePath, JobMetaVO jobMeta, String encoding) {
        try {
            // 检测或指定文件编码
            Charset charset;
            if (StringUtils.isNotBlank(encoding)) {
                try {
                    charset = Charset.forName(encoding);
                    System.out.println("使用指定的文件编码：" + charset.name());
                } catch (Exception e) {
                    System.out.println("指定的编码无效，使用自动检测：" + encoding);
                    charset = detectFileEncoding(filePath);
                }
            } else {
                charset = detectFileEncoding(filePath);
                System.out.println("检测到的文件编码：" + charset.name());
            }

            // 读取CSV文件
            List<Map<String, String>> csvData = readCsvFileWithCharset(filePath, charset);
            if (csvData.isEmpty()) {
                return this.basicReturnFailure("CSV文件为空或格式错误");
            }

            // 校验表头
            Map<String, String> headerValidationResult = validateHeaders(csvData.get(0));
            if (headerValidationResult != null) {
                return this.basicReturnFailure("表头校验失败：" + headerValidationResult.get("message"));
            }

            // 校验数据行
            CsvValidationResultVO validationResult = validateDataRows(csvData, jobMeta);

            // 在返回结果中添加编码信息
            Map<String, Object> result = this.basicReturnSuccess(validationResult);
            result.put("detectedEncoding", charset.name());

            return result;

        } catch (Exception e) {
            return this.basicReturnFailure("CSV解析失败：" + e.getMessage());
        }
    }

    /**
     * 读取CSV文件为List<Map<String, String>>
     * @param filePath 文件路径
     * @return CSV数据
     */
    private List<Map<String, String>> readCsvFile(String filePath) throws IOException {
        // 自动检测文件编码
        Charset detectedCharset = detectFileEncoding(filePath);
        return readCsvFileWithCharset(filePath, detectedCharset);
    }

    /**
     * 使用指定编码读取CSV文件为List<Map<String, String>>
     * @param filePath 文件路径
     * @param charset 字符编码
     * @return CSV数据
     */
    private List<Map<String, String>> readCsvFileWithCharset(String filePath, Charset charset) throws IOException {
        List<Map<String, String>> result = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), charset))) {

            String line;
            String[] headers = null;
            boolean isFirstLine = true;

            while ((line = reader.readLine()) != null) {
                // 处理BOM
                if (isFirstLine && line.startsWith("\uFEFF")) {
                    line = line.substring(1);
                }

                String[] values = parseCsvLine(line);

                if (isFirstLine) {
                    headers = values;
                    isFirstLine = false;
                } else {
                    Map<String, String> row = new LinkedHashMap<>();
                    for (int i = 0; i < headers.length && i < values.length; i++) {
                        row.put(headers[i], values[i]);
                    }
                    result.add(row);
                }
            }
        }

        return result;
    }

    /**
     * 检测文件编码
     * @param filePath 文件路径
     * @return 检测到的字符编码
     */
    private Charset detectFileEncoding(String filePath) throws IOException {
        // 常见的编码列表，按优先级排序
        Charset[] charsets = {
            Charset.forName("GBK"),        // 优先尝试GBK，因为旧系统使用GBK
            StandardCharsets.UTF_8,        // UTF-8
            Charset.forName("GB2312"),     // GB2312
            StandardCharsets.ISO_8859_1    // ISO-8859-1
        };

        // 读取文件前几行进行编码检测
        byte[] buffer = new byte[4096];
        try (FileInputStream fis = new FileInputStream(filePath)) {
            int bytesRead = fis.read(buffer);
            if (bytesRead == -1) {
                return StandardCharsets.UTF_8; // 空文件默认UTF-8
            }

            // 尝试每种编码
            for (Charset charset : charsets) {
                try {
                    String content = new String(buffer, 0, bytesRead, charset);

                    // 检查是否包含乱码字符
                    if (isValidEncoding(content, charset)) {
                        return charset;
                    }
                } catch (Exception e) {
                    // 编码转换失败，尝试下一个
                    continue;
                }
            }
        }

        // 如果都失败了，默认使用GBK（因为旧系统使用GBK）
        return Charset.forName("GBK");
    }

    /**
     * 验证编码是否有效
     * @param content 解码后的内容
     * @param charset 使用的字符集
     * @return 是否有效
     */
    private boolean isValidEncoding(String content, Charset charset) {
        // 检查是否包含替换字符（通常表示编码错误）
        if (content.contains("\uFFFD")) {
            return false;
        }

        // 检查是否包含明显的乱码模式（连续的高位字符）
        if (content.matches(".*[\u00C0-\u00FF]{3,}.*")) {
            return false;
        }

        // 检查常见的乱码模式
        if (content.contains("ï¿½") || content.contains("Äú") || content.contains("¸ñ")) {
            return false;
        }

        // 对于GBK编码，检查中文字符是否正常
        if ("GBK".equals(charset.name()) || "GB2312".equals(charset.name())) {
            boolean hasChineseChar = false;
            boolean hasGarbledChar = false;

            for (char c : content.toCharArray()) {
                // 检查中文字符范围
                if (c >= 0x4E00 && c <= 0x9FFF) {
                    hasChineseChar = true;
                }
                // 检查常见乱码字符
                if ((c >= 0x00C0 && c <= 0x00FF) || c == 0xFFFD) {
                    hasGarbledChar = true;
                    break;
                }
            }

            // 如果有中文字符且没有乱码，认为编码正确
            if (hasChineseChar && !hasGarbledChar) {
                return true;
            }

            // 如果有乱码字符，认为编码错误
            if (hasGarbledChar) {
                return false;
            }
        }

        // 对于UTF-8编码，检查是否包含正常的中文字符
        if (StandardCharsets.UTF_8.equals(charset)) {
            for (char c : content.toCharArray()) {
                if (c >= 0x4E00 && c <= 0x9FFF) {
                    // 包含中文字符，认为编码正确
                    return true;
                }
            }
        }

        // 如果没有特殊字符问题，且内容不为空，认为编码有效
        return !content.trim().isEmpty();
    }

    /**
     * 解析CSV行（处理逗号和引号）
     * @param line CSV行
     * @return 字段数组
     */
    private String[] parseCsvLine(String line) {
        List<String> result = new ArrayList<>();
        boolean inQuotes = false;
        StringBuilder currentField = new StringBuilder();

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 转义的双引号
                    currentField.append('"');
                    i++; // 跳过下一个引号
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                // 字段分隔符
                result.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }

        // 添加最后一个字段
        result.add(currentField.toString().trim());

        return result.toArray(new String[0]);
    }

    /**
     * 校验表头字段
     * @param firstRow 第一行数据（表头）
     * @return 校验结果，null表示通过
     */
    private Map<String, String> validateHeaders(Map<String, String> firstRow) {
        Set<String> actualHeaders = firstRow.keySet();
        Set<String> expectedHeaders = CSV_FIELD_MAPPING.keySet();

        // 检查字段数量
        if (actualHeaders.size() != expectedHeaders.size()) {
            Map<String, String> error = new HashMap<>();
            error.put("message", String.format("表头字段数量不匹配，期望%d个，实际%d个",
                    expectedHeaders.size(), actualHeaders.size()));
            return error;
        }

        // 检查字段顺序和名称
        List<String> actualHeaderList = new ArrayList<>(actualHeaders);
        List<String> expectedHeaderList = new ArrayList<>(expectedHeaders);

        for (int i = 0; i < expectedHeaderList.size(); i++) {
            if (!expectedHeaderList.get(i).equals(actualHeaderList.get(i))) {
                Map<String, String> error = new HashMap<>();
                error.put("message", String.format("第%d列表头不匹配，期望'%s'，实际'%s'",
                        i + 1, expectedHeaderList.get(i), actualHeaderList.get(i)));
                return error;
            }
        }

        return null; // 校验通过
    }

    /**
     * 校验数据行
     * @param csvData CSV数据
     * @param jobMeta 作业元数据
     * @return 校验结果
     */
    private CsvValidationResultVO validateDataRows(List<Map<String, String>> csvData, JobMetaVO jobMeta) {
        List<Map<String, Object>> validRows = new ArrayList<>();
        List<Map<String, Object>> invalidRows = new ArrayList<>();

        // 跳过表头，从第二行开始处理
        for (int i = 1; i < csvData.size(); i++) {
            Map<String, String> row = csvData.get(i);
            Map<String, Object> processedRow = new LinkedHashMap<>();
            List<String> errors = new ArrayList<>();

            // 添加元数据
//            processedRow.put("jobId", jobMeta.getJobId());
//            processedRow.put("jobBranchId", jobMeta.getJobBranchId());
            processedRow.put("rowNumber", i + 1);

            // 校验每个字段
            for (Map.Entry<String, String> entry : CSV_FIELD_MAPPING.entrySet()) {
                String csvField = entry.getKey();
                String entityField = entry.getValue();
                String value = row.get(csvField);

                // 校验必填字段
                if (REQUIRED_FIELDS.contains(csvField) && StringUtils.isBlank(value)) {
                    errors.add(String.format("字段'%s'不能为空", csvField));
                    continue;
                }

                // 校验字符串长度
                if (StringUtils.isNotBlank(value) && STRING_LENGTH_LIMITS.containsKey(csvField)) {
                    int maxLength = STRING_LENGTH_LIMITS.get(csvField);
                    if (value.length() > maxLength) {
                        errors.add(String.format("字段'%s'长度超过限制(%d)，当前长度：%d",
                                csvField, maxLength, value.length()));
                    }
                }

                // 校验数字字段
                if (NUMERIC_FIELDS.contains(csvField) && StringUtils.isNotBlank(value)) {
                    Object convertedValue = validateAndConvertNumericField(csvField, value, errors);
                    processedRow.put(entityField, convertedValue);
                } else {
                    processedRow.put(entityField, value);
                }
            }

            // 添加错误信息
            if (!errors.isEmpty()) {
                processedRow.put("errors", errors);
                invalidRows.add(processedRow);
            } else {
                validRows.add(processedRow);
            }
        }

        return new CsvValidationResultVO(validRows, invalidRows);
    }

    /**
     * 校验并转换数字字段
     * @param fieldName 字段名
     * @param value 字段值
     * @param errors 错误列表
     * @return 转换后的值
     */
    private Object validateAndConvertNumericField(String fieldName, String value, List<String> errors) {
        try {
            // 检查是否包含字母
            Pattern letterPattern = Pattern.compile("[a-zA-Z]");
            if (letterPattern.matcher(value).find()) {
                errors.add(String.format("字段'%s'不能包含字母，当前值：%s", fieldName, value));
                return value;
            }

            // 根据字段类型转换
            if ("SET_YEAR".equals(fieldName) || "SET_MONTH".equals(fieldName)) {
                int intValue = Integer.parseInt(value);

                // 校验年份范围
                if ("SET_YEAR".equals(fieldName) && (intValue < 1900 || intValue > 2100)) {
                    errors.add(String.format("字段'%s'年份范围应在1900-2100之间，当前值：%d", fieldName, intValue));
                }

                // 校验月份范围
                if ("SET_MONTH".equals(fieldName) && (intValue < 1 || intValue > 12)) {
                    errors.add(String.format("字段'%s'月份范围应在1-12之间，当前值：%d", fieldName, intValue));
                }

                return intValue;
            } else {
                BigDecimal decimalValue = new BigDecimal(value);

                // 校验功率字段范围
                if ("MAX_EMISSIVE_POWER".equals(fieldName)) {
                    if (decimalValue.compareTo(BigDecimal.ZERO) < 0 ||
                        decimalValue.compareTo(new BigDecimal("500")) > 0) {
                        errors.add(String.format("字段'%s'功率范围应在0-500之间，当前值：%s", fieldName, value));
                    }
                }

                return decimalValue;
            }
        } catch (NumberFormatException e) {
            errors.add(String.format("字段'%s'数字格式错误，当前值：%s", fieldName, value));
            return value;
        }
    }

    /**
     * 生成唯一文件名，避免文件覆盖
     * @param originalFilename 原始文件名
     * @return 新的唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String baseName = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
        String extension = originalFilename.substring(originalFilename.lastIndexOf('.'));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = sdf.format(new Date());

        String newFileName = baseName + "_" + timestamp + extension;

        // 检查文件是否存在，如果存在则添加序号
        File file = new File(myFilePath + File.separator + newFileName);
        int counter = 1;
        while (file.exists()) {
            newFileName = baseName + "_" + timestamp + "_" + counter + extension;
            file = new File(myFilePath + File.separator + newFileName);
            counter++;
        }

        return newFileName;
    }

    /**
     * 处理CSV解析后的数据并插入数据库
     * @param validRows 有效数据行
     * @param invalidRows 无效数据行
     * @param csvFilePath CSV文件路径
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> processCsvDataAndSave(List<Map<String, Object>> validRows,
                                                     List<Map<String, Object>> invalidRows,
                                                     String csvFilePath) {
        try {
            // 1. 创建新的传输任务
            TransportJobNew transportJob = createTransportJob(csvFilePath);
            Long jobId = transportJob.getId();

            // 2. 插入有效数据到 TRANSPORT_RAW_BTS_DEAL_NEW
            int validCount = insertValidRows(validRows, jobId);

            // 3. 插入无效数据到 TRANSPORT_RAW_BTS_DEAL_LOG_NEW
            int invalidCount = insertInvalidRows(invalidRows, jobId);

            // 4. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("jobId", jobId);
            result.put("validRowsCount", validCount);
            result.put("invalidRowsCount", invalidCount);
            result.put("totalRowsCount", validCount + invalidCount);
            result.put("csvFilePath", csvFilePath);
            result.put("processTime", new Date());

            return this.basicReturnSuccess(result);

        } catch (Exception e) {
            System.err.println("处理CSV数据时发生错误：" + e.getMessage());
            e.printStackTrace();
            return this.basicReturnFailure("数据处理失败：" + e.getMessage());
        }
    }

    /**
     * 创建传输任务记录
     * @param csvFilePath CSV文件路径
     * @return 创建的任务对象
     */
    private TransportJobNew createTransportJob(String csvFilePath) {
        TransportJobNew transportJob = new TransportJobNew();
        transportJob.setOperatorCode("CMCC");
        transportJob.setApplyType("1");
        transportJob.setStatus("1");
        transportJob.setCsvFilePath(csvFilePath);
        transportJob.setCreatedBy("admin");
        transportJob.setCreatedAt(new Date());
        transportJob.setUpdatedAt(new Date());

        // 保存到数据库
        transportJobNewService.save(transportJob);

        return transportJob;
    }

    /**
     * 插入有效数据行到 TRANSPORT_RAW_BTS_DEAL_NEW
     * @param validRows 有效数据行
     * @param jobId 任务ID
     * @return 插入的记录数
     */
    private int insertValidRows(List<Map<String, Object>> validRows, Long jobId) {
        if (validRows == null || validRows.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (Map<String, Object> row : validRows) {
            try {
                TransportRawBtsDealNew entity = convertToTransportRawBtsDealNew(row, jobId);
                transportRawBtsDealNewService.save(entity);
                count++;
            } catch (Exception e) {
                System.err.println("插入有效数据行失败：" + e.getMessage() + ", 数据：" + JSON.toJSONString(row));
                // 继续处理其他行，不中断整个过程
            }
        }

        return count;
    }

    /**
     * 插入无效数据行到 TRANSPORT_RAW_BTS_DEAL_LOG_NEW
     * @param invalidRows 无效数据行
     * @param jobId 任务ID
     * @return 插入的记录数
     */
    private int insertInvalidRows(List<Map<String, Object>> invalidRows, Long jobId) {
        if (invalidRows == null || invalidRows.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (Map<String, Object> row : invalidRows) {
            try {
                // 一行数据可能有多个错误，为每个错误创建一条日志记录
                @SuppressWarnings("unchecked")
                List<String> errors = (List<String>) row.get("errors");
                if (errors != null && !errors.isEmpty()) {
                    for (String error : errors) {
                        TransportRawBtsDealLogNew logEntity = convertToTransportRawBtsDealLogNew(row, jobId, error);
                        transportRawBtsDealLogNewService.save(logEntity);
                        count++;
                    }
                } else {
                    // 如果没有具体错误信息，创建一条通用错误记录
                    TransportRawBtsDealLogNew logEntity = convertToTransportRawBtsDealLogNew(row, jobId, "数据校验失败");
                    transportRawBtsDealLogNewService.save(logEntity);
                    count++;
                }
            } catch (Exception e) {
                System.err.println("插入无效数据行失败：" + e.getMessage() + ", 数据：" + JSON.toJSONString(row));
                // 继续处理其他行，不中断整个过程
            }
        }

        return count;
    }

    /**
     * 将Map数据转换为TransportRawBtsDealNew实体
     * @param row 数据行
     * @param jobId 任务ID
     * @return 实体对象
     */
    private TransportRawBtsDealNew convertToTransportRawBtsDealNew(Map<String, Object> row, Long jobId) {
        TransportRawBtsDealNew entity = new TransportRawBtsDealNew();

        // 设置任务相关字段
        entity.setJobId(jobId);
        entity.setJobBranchId(getLongValue(row, "jobBranchId"));

        // 设置业务字段（根据CSV字段映射）
        entity.setCellName(getStringValue(row, "cellName"));
        entity.setEtEquCCode(getStringValue(row, "etEquCCode"));
        entity.setStatName(getStringValue(row, "statName"));
        entity.setStCCode(getStringValue(row, "stCCode"));
        entity.setNetTs(getStringValue(row, "netTs"));
        entity.setStatAddr(getStringValue(row, "statAddr"));
        entity.setCounty(getStringValue(row, "county"));
        entity.setStatLg(getBigDecimalValue(row, "statLg"));
        entity.setStatLa(getBigDecimalValue(row, "statLa"));
        entity.setFreqEfe(getBigDecimalValue(row, "freqEfe"));
        entity.setFreqEfb(getBigDecimalValue(row, "freqEfb"));
        entity.setFreqRfb(getBigDecimalValue(row, "freqRfb"));
        entity.setFreqRfe(getBigDecimalValue(row, "freqRfe"));
        entity.setMaxEmissivePower(getBigDecimalValue(row, "maxEmissivePower"));
        entity.setAntHight(getBigDecimalValue(row, "antHight"));
        entity.setEquMenu(getStringValue(row, "equMenu"));
        entity.setEquModel(getStringValue(row, "equModel"));
        entity.setEquAuth(getStringValue(row, "equAuth"));
        entity.setAntType(getStringValue(row, "antType"));
        entity.setAntMenu(getStringValue(row, "antMenu"));
        entity.setAntPole(getStringValue(row, "antPole"));
        entity.setSeComm(getBigDecimalValue(row, "seComm"));
        entity.setAtRang(getBigDecimalValue(row, "atRang"));
        entity.setAtEang(getBigDecimalValue(row, "atEang"));
        entity.setFeedLose(getBigDecimalValue(row, "feedLose"));
        entity.setAntGain(getBigDecimalValue(row, "antGain"));
        entity.setStatAt(getBigDecimalValue(row, "statAt"));
        entity.setSetYear(getIntegerValue(row, "setYear"));
        entity.setSetMonth(getIntegerValue(row, "setMonth"));
        entity.setExpandStation(getStringValue(row, "expandStation"));
        entity.setAttributeStation(getStringValue(row, "attributeStation"));
        entity.setStServR(getBigDecimalValue(row, "stServR"));

        // 设置时间字段
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        return entity;
    }

    /**
     * 将Map数据转换为TransportRawBtsDealLogNew实体
     * @param row 数据行
     * @param jobId 任务ID
     * @param errorMessage 错误信息
     * @return 日志实体对象
     */
    private TransportRawBtsDealLogNew convertToTransportRawBtsDealLogNew(Map<String, Object> row, Long jobId, String errorMessage) {
        TransportRawBtsDealLogNew logEntity = new TransportRawBtsDealLogNew();

        // 设置任务相关字段
        logEntity.setJobId(jobId);
        logEntity.setJobBranchId(getLongValue(row, "jobBranchId"));

        // 解析错误信息，提取字段名
        String fieldName = extractFieldNameFromError(errorMessage);
        logEntity.setFieldName(fieldName);

        // 设置错误相关字段
        logEntity.setErrorType("VALIDATION_ERROR");
        logEntity.setErrorMessage(errorMessage);
        logEntity.setOriginalValue(getOriginalValueFromError(row, fieldName));

        // 将整行数据序列化为JSON存储
        logEntity.setDealData(JSON.toJSONString(row));
        logEntity.setCreatedAt(new Date());

        return logEntity;
    }

    /**
     * 从错误信息中提取字段名
     * @param errorMessage 错误信息
     * @return 字段名
     */
    private String extractFieldNameFromError(String errorMessage) {
        if (StringUtils.isBlank(errorMessage)) {
            return "UNKNOWN";
        }

        // 尝试从错误信息中提取字段名（格式：字段'FIELD_NAME'xxx）
        if (errorMessage.contains("字段'") && errorMessage.contains("'")) {
            int start = errorMessage.indexOf("字段'") + 3;
            int end = errorMessage.indexOf("'", start);
            if (end > start) {
                return errorMessage.substring(start, end);
            }
        }

        return "UNKNOWN";
    }

    /**
     * 获取出错字段的原始值
     * @param row 数据行
     * @param fieldName 字段名
     * @return 原始值
     */
    private String getOriginalValueFromError(Map<String, Object> row, String fieldName) {
        if ("UNKNOWN".equals(fieldName) || row == null) {
            return "";
        }

        // 根据字段名获取对应的值
        String entityFieldName = CSV_FIELD_MAPPING.get(fieldName);
        if (entityFieldName != null && row.containsKey(entityFieldName)) {
            Object value = row.get(entityFieldName);
            return value != null ? value.toString() : "";
        }

        return "";
    }

    /**
     * 安全获取字符串值
     * @param row 数据行
     * @param key 键名
     * @return 字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? value.toString().trim() : null;
    }

    /**
     * 安全获取Long值
     * @param row 数据行
     * @param key 键名
     * @return Long值
     */
    private Long getLongValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Long) {
            return (Long) value;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        try {
            return Long.parseLong(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取Integer值
     * @param row 数据行
     * @param key 键名
     * @return Integer值
     */
    private Integer getIntegerValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof Integer) {
            return (Integer) value;
        }

        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        try {
            return Integer.parseInt(value.toString().trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取BigDecimal值
     * @param row 数据行
     * @param key 键名
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }

        try {
            String strValue = value.toString().trim();
            if (StringUtils.isBlank(strValue)) {
                return null;
            }
            return new BigDecimal(strValue);
        } catch (NumberFormatException e) {
            return null;
        }
    }

}
