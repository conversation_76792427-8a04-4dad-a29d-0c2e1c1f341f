package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.UsersLogWebService;
import com.bsm.v4.system.model.vo.security.UsersLogSearchVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Title: UsersLogController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.security
 * @Date 2023/8/17 11:46
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb /security/usersLog")
@Api(value = "web端-系统-用户日志接口", tags = "web端-系统-用户日志接口")
public class UsersLogController extends BasicController {

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersLogSearchVO", value = "用户日志搜索对象", required = true, paramType = "body", dataType = "usersLogSearchVO")
    })
    @PostMapping(value = "/findAllPageWhere")
    public JSONObject findAllPageWhere(@RequestBody UsersLogSearchVO usersLogSearchVO) {
        return this.basicReturnJson(usersLogSearchVO, UsersLogWebService.class, (vo, service) -> service.findAllPageByWhere(vo));

    }


}
