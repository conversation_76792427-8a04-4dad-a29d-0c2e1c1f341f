package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportScheduleWebService;
import com.bsm.v4.system.model.dto.business.transfer.TransportScheduleDTO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: ycp
 * @createTime: 2023/08/17 16:12
 * @company: 成渝（成都）信息通信研究院
 * @description: 代办任务controller
 */

@RestController
@RequestMapping(value = "/apiWeb/transfer/transportSchedule")
@Api(value = "web端代办任务管理接口", tags = "web端代办任务管理接口")
public class TransportScheduleController extends BasicController {

    @ApiOperation(value = "查询各运营商各数据类型的待办数量", notes = "查询各运营商各数据类型的待办数量接口")
    @RequestMapping(value = "/findAllTransportSchedule", method = RequestMethod.GET)
    public JSONObject findAllTransportSchedule(){
        return this.basicReturnJson(TransportScheduleWebService.class, TransportScheduleWebService::findAllTransportSchedule);
    }

    @ApiOperation(value = "根据jobId、类型分页查询待办数据", notes = "根据jobId、类型分页查询待办数据接口")
    @RequestMapping(value = "/findAllPageByJobIdDataType", method = RequestMethod.POST)
    public JSONObject findAllPageByJobIdDataType(@RequestBody TransportScheduleDTO transportScheduleDTO){
        return this.basicReturnJson(transportScheduleDTO, TransportScheduleWebService.class, (model, service) -> service.findAllPageByJobIdDataType(transportScheduleDTO));
    }
}
