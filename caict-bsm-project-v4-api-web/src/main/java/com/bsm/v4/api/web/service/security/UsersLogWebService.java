package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.UsersLogService;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.dto.security.UsersLogDTO;
import com.bsm.v4.system.model.entity.security.UsersLog;
import com.bsm.v4.system.model.vo.security.UsersLogSearchVO;
import com.bsm.v4.system.model.vo.security.UsersSearchVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Title: UsersLogWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/17 11:48
 * @description:
 */
@Service
public class UsersLogWebService extends BasicWebService {
    @Autowired
    private UsersLogService usersLogService;

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllPageByWhere(UsersLogSearchVO usersLogSearchVO) {
        return this.basicReturnResultJson(new PageHandle(usersLogSearchVO).buildPage(findAllByWhere(usersLogSearchVO)));
    }

    /**
     * 条件查询
     */
    private List<UsersLogDTO> findAllByWhere(UsersLogSearchVO usersLogSearchVO) {
        return usersLogService.findAllByWhere(usersLogSearchVO);
    }

    /**
     * 添加
     */
    public String insert(String usersId, String content, String type) {
        var usersLog = new UsersLog();
        usersLog.setUsersId(usersId);
        usersLog.setContent(content);
        usersLog.setType(type);
        return save(usersLog);
    }

    /**
     * 添加/编辑
     */
    public String save(UsersLog usersLog) {
        if (usersLog != null) {
            usersLog.setUpdateDateTime(new Date());
            return usersLogService.save(usersLog).toString();
        }
        return null;
    }
}
