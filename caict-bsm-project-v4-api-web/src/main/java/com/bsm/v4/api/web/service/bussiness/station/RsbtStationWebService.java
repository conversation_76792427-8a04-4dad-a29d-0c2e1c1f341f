package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtStationService;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationWebService extends BasicWebService {

    @Autowired
    private RsbtStationService rsbtStationService;


    public int updateAppCode(String oldAppCode, String newAppCode) {
        return rsbtStationService.updateAppCode(oldAppCode, newAppCode);
    }

    /**
     * 批量添加
     */
    public void insertBatch(List<RsbtStation> rsbtStationListInsert) {
        if (rsbtStationListInsert.size() > 30) {
            int pageNum = rsbtStationListInsert.size() % 30 == 0 ? rsbtStationListInsert.size() / 30 : rsbtStationListInsert.size() / 30 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtStation> stations;
                if (i != pageNum) {
                    stations = rsbtStationListInsert.subList((i - 1) * 30, i * 30);
                } else {
                    stations = rsbtStationListInsert.subList((i - 1) * 30, rsbtStationListInsert.size());
                }
                rsbtStationService.insertBatch(stations);
            }
        } else {
            rsbtStationService.insertBatch(rsbtStationListInsert);
        }
    }

    /**
     * 批量添加
     */
    public void delete(List<RsbtStation> station) {
        List<String> ids = station.stream().map(RsbtStation::getGuid).distinct().collect(Collectors.toList());
        rsbtStationService.deleteByStationIds(ids);
    }

    /**
     * 批量修改
     */
    public void updateBatch(List<RsbtStation> rsbtStationListUpdate) {
        if (rsbtStationListUpdate.size() > 30) {
            int pageNum = rsbtStationListUpdate.size() % 30 == 0 ? rsbtStationListUpdate.size() / 30 : rsbtStationListUpdate.size() / 30 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtStation> stations;
                if (i != pageNum) {
                    stations = rsbtStationListUpdate.subList((i - 1) * 30, i * 30);
                } else {
                    stations = rsbtStationListUpdate.subList((i - 1) * 30, rsbtStationListUpdate.size());
                }
                rsbtStationService.updateBatch(stations);
            }
        } else {
            rsbtStationService.updateBatch(rsbtStationListUpdate);
        }
    }

    public List<RsbtStation> findListByAppCodePage(String appCode) {
        return rsbtStationService.findListByAppCodePage(appCode);
    }

    public RsbtStation selectByStationGuid(String guid) {
        return rsbtStationService.selectByStationGuid(guid);
    }

    /**
     * 根据基站识别号下载
     */
    public RsbtStation querySattionByBts(String bts, String techType, String orgCode) {
        return rsbtStationService.querySattionByBts(bts, techType, orgCode);
    }

}
