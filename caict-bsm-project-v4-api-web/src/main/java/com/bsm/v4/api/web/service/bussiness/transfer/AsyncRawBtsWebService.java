package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.domain.security.service.business.transfer.AsyncRawBtsService;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class AsyncRawBtsWebService extends BasicWebService {

    @Autowired
    private AsyncRawBtsService asyncRawBtsService;


    /**
     * 根据btsIds 刪除
     */
    public void deleteByBtsIds(List<String> btsIds, String guid) {
        if (btsIds.size() > 50) {
            int pageNum = btsIds.size() % 50 == 0 ? btsIds.size() / 50 : btsIds.size() / 50 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<String> btsids;
                if (i != pageNum) {
                    btsids = btsIds.subList((i - 1) * 50, i * 50);
                } else {
                    btsids = btsIds.subList((i - 1) * 50, btsIds.size());
                }
                asyncRawBtsService.deleteByBtsIds(btsids, guid);
            }
        } else {
            asyncRawBtsService.deleteByBtsIds(btsIds, guid);
        }
    }

    /**
     * 批量添加
     */
    public int insertBatch(List<AsyncRawBts> asyncRawBtsInsert) {
        return asyncRawBtsService.insertBatch(asyncRawBtsInsert);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<AsyncRawBts> asyncRawBtsUpdate) {
        return asyncRawBtsService.updateBatch(asyncRawBtsUpdate);
    }

    /**
     * 批量删除
     */
    public int deleteBatch(List<AsyncRawBts> asyncRawBtsDelete) {
        List<Object> idList = new ArrayList<>();
        for (AsyncRawBts asyncRawBts : asyncRawBtsDelete) {
            idList.add(asyncRawBts.getGuid());
        }
        return asyncRawBtsService.deleteBatch(idList);
    }

    /**
     * 根据运营商、数据状态删除
     */
    public int deleteAllByUserDataType(String userId, String dataType) {
        return asyncRawBtsService.deleteAllByUserDataType(userId, dataType);
    }

    /**
     * 根据扇区id查询一条记录
     */
    public AsyncRawBts getOneByCellId(String btsId, String cellId, String techType) {
        return asyncRawBtsService.getOneByCellId(btsId, cellId, techType);
    }

    /**
     * 根据基站编号和扇区号拆线呢
     */
    public AsyncRawBts selectOneByBtsIdCellId(String btsId, String cellId, String techType) {
        return asyncRawBtsService.selectOneByBtsIdCellId(btsId, cellId, techType);
    }

    /**
     * 查询上传数据中没有的数据
     */
    public List<AsyncRawBts> findAllNotTransport(String jobGuid, String userType) {
        return asyncRawBtsService.findAllNotTransport(jobGuid, userType);
    }

    /**
     * 根据btsIds 查询总表数据
     *
     * @param btsIds btsIds
     * @return list
     */
    public List<AsyncRawBts> findByBtsIds(List<String> btsIds) {
        return asyncRawBtsService.findByBtsIds(btsIds);
    }

    /**
     * 根据btsIds 查询总表数据
     *
     * @param btsId btsId
     * @return list
     */
    public List<AsyncRawBts> findByBtsId(String btsId) {
        return asyncRawBtsService.findByBtsId(btsId);
    }
}
