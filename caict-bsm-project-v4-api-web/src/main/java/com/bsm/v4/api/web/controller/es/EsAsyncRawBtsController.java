package com.bsm.v4.api.web.controller.es;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.es.EsAsyncRawBtsWebService;
import com.bsm.v4.system.model.entity.es.EsAsyncRawBts;
import com.bsm.v4.system.model.vo.es.EsAsyncRawBtsVO;
import com.caictframework.data.controller.BasicController;
import com.caictframework.utils.util.MyDate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: ycp
 * @createTime: 2023/08/10 10:14
 * @company: 成渝（成都）信息通信研究院
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb/es/asyncRawBts")
@Api(value = "web端-es-总表数据接口", tags = "web端-es-总表数据接口")
public class EsAsyncRawBtsController extends BasicController {

    @Autowired
    private EsAsyncRawBtsWebService esService;

    @ApiOperation(value = "查询使用率评价查询", notes = "查询使用率评价查询接口")
    @PostMapping(value = "/searchUsageEvaluation")
    public JSONObject searchUsageEvaluation(@RequestBody EsAsyncRawBtsVO vo) {
        return this.basicReturnJson(vo, EsAsyncRawBtsWebService.class, (param, service) -> esService.exportUsageEvaluation(vo));
    }

    @GetMapping(value = "/findById")
    public JSONObject findById() {
        System.out.println(new Timestamp(System.currentTimeMillis()).toString());
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            list.add((int) (Math.random() * 100000) + 1 + "");
        }
        int size = list.size();

        int count = 0;
        if (size > 50000) {
            for (var i = 0; i < size; i += 50000) {
                List<String> collect = list.stream().skip(i).limit(50000).collect(Collectors.toList());
                List<EsAsyncRawBts> list1 = esService.findAllById(collect);
                count += list1.size();
                System.out.println(new Timestamp(System.currentTimeMillis()).toString());
            }
        } else {
            List<EsAsyncRawBts> list1 = esService.findAllById(list);
            count += list1.size();
        }

        System.out.println(count);
        System.out.println(new Timestamp(System.currentTimeMillis()).toString());
        return null;
    }

    @GetMapping(value = "/saveList")
    public JSONObject saveList() {
        System.out.println(new Timestamp(System.currentTimeMillis()).toString());
        List<EsAsyncRawBts> esTestEntitys = new ArrayList<>();
        for (int i = 0; i < 300000; i++) {
            var esTestEntity = new EsAsyncRawBts();
            esTestEntity.setGuid("guid" + i);
            esTestEntity.setBtsId("btsId" + i);
            esTestEntity.setBtsName("btsName" + i);
            esTestEntity.setTechType("GSM");
            esTestEntity.setCounty("渝北区");
            esTestEntity.setOrgType("yidong");
            esTestEntity.setGenNum("5G");
            esTestEntity.setStScene("农村");
            esTestEntity.setTrfDate(MyDate.dateToLocalDateTime(MyDate.stringToDate("2023-08-01")));
            if (i < 50000) {
                esTestEntity.setTechType("TD-LTE");
                esTestEntity.setCounty("巴南区");
            } else if (i < 100000) {
                esTestEntity.setOrgType("dianxin");
                esTestEntity.setGenNum("4G");
            } else if (i < 150000) {
                esTestEntity.setOrgType("liantong");
                esTestEntity.setGenNum("2G");
                esTestEntity.setStScene("郊区");
            } else if (i < 200000) {
                esTestEntity.setStScene("城市");
            } else if (i < 250000) {
                esTestEntity.setTrfDate(MyDate.dateToLocalDateTime(MyDate.stringToDate("2023-07-01")));
            } else {
                esTestEntity.setCounty("南岸区");
                esTestEntity.setOrgType("broadnet");
            }
            esTestEntitys.add(esTestEntity);
        }
        System.out.println(new Timestamp(System.currentTimeMillis()).toString());
        int size = esTestEntitys.size();

        if (size > 50000) {
            for (var i = 0; i < size; i += 50000) {
                List<EsAsyncRawBts> collect = esTestEntitys.stream().skip(i).limit(50000).collect(Collectors.toList());
                esService.saveList(collect);
            }
        } else {
            esService.saveList(esTestEntitys);
        }
        System.out.println(new Timestamp(System.currentTimeMillis()).toString());
        return null;
    }

    @ApiOperation(value = "test", notes = "test")
    @PostMapping(value = "/test")
    public JSONObject test() {
        return this.basicReturnJson( EsAsyncRawBtsWebService.class, (service) -> esService.test());
    }
}
