package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.OrgService;
import com.bsm.v4.domain.security.service.security.RsbtOrgAppendixService;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.security.RsbtOrg;
import com.bsm.v4.system.model.entity.security.RsbtOrgAppendix;
import com.bsm.v4.system.model.vo.security.OrgVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Title: OrgWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/14 16:46
 * @description:
 */
@Service
public class OrgWebService extends BasicWebService {
    private static final Logger LOG = LoggerFactory.getLogger(OrgWebService.class);

    /**
     * 保存
     */
    @Autowired
    private OrgService orgService;
    @Autowired
    private RsbtOrgAppendixService appendixService;
    @Autowired
    private OrgUsersWebService orgUsersWebService;

    /**
     * 添加、修改组织机构
     */
    public Map<String, Object> save(OrgVO orgVO) {
        var org = new RsbtOrg();
        var orgAppendix = new RsbtOrgAppendix();
        BeanUtils.copyProperties(orgVO, org);
        BeanUtils.copyProperties(orgVO, orgAppendix);

        var id = orgService.save(org);
        appendixService.save(orgAppendix);
        return this.basicReturnResultJson(id);
    }

    /**
     * 删除组织机构（根据guid）
     */
    public Map<String, Object> delete(String orgGuid) {
        //删除用户关联
        orgUsersWebService.deleteAllByOrg(orgGuid);
        return this.basicReturnResultJson(orgGuid);
    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllPageByWhere(OrgVO orgVO) {
        return this.basicReturnResultJson(new PageHandle(orgVO).buildPage(findAllByWhere(orgVO)));
    }

    /**
     * 条件查询
     * @return
     */
    private List<OrgDTO> findAllByWhere(RsbtOrg orgVO) {
        return orgService.findAllByWhere1((OrgVO) orgVO);
    }

    /**
     * 根据id查询详情
     */
    public OrgDTO findOne(String orgId) {
        RsbtOrg org = orgService.findById(orgId);
        RsbtOrgAppendix appendix = appendixService.findById(orgId);
        if (org != null) {
            OrgDTO OrgDTO = new OrgDTO();
            BeanUtils.copyProperties(org, OrgDTO);
            OrgDTO.setSupCode(appendix.getSucCode());
            OrgDTO.setType(appendix.getType());
            OrgDTO.setGuid(orgId);
            return OrgDTO;
        }
        return null;
    }

    /**
     * 查询组织机构详情
     */
    public Map<String, Object> findOneDetails(String id) {
        return this.basicReturnResultJson(orgService.findOneDetails(id));
    }

    /**
     * 根据父级查询全部
     */
    public Map<String, Object> findAllBySupCode(String orgSupCode) {
        return this.basicReturnResultJson(orgService.findAllBySupCode(orgSupCode));
    }

//    /**
//     * 根据父ID查询
//     */
//    public Map<String, Object> findAllByParentId(String parentId) {
//        return this.basicReturnResultJson(orgService.findAllByParentId(parentId));
//    }

    /**
     * 绑定用户
     */
    public Map<String, Object> setUsersOrg(String orgGuid, String[] usersIds) {
        return this.basicReturnResultJson(orgUsersWebService.setUsersOrg(orgGuid, usersIds));
    }

    /**
     * 根据地区code查询
     */
    public String findUserIDByRegionCodeAndOrgType(String regionCode, String orgType) {
        return orgService.findUserIDByRegionCodeAndOrgType(regionCode, orgType);
    }
}
