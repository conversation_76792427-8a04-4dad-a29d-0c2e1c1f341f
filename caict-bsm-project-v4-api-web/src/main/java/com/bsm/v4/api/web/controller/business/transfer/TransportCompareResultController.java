package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportCompareResultWebService;
import com.bsm.v4.system.model.dto.business.transfer.TransportCompareResultDTO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * Created by dengsy on 2020-04-22.
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/transportCompareResult")
@Api(value = "web端审核差异数据接口", tags = "web端审核差异数据接口")
public class TransportCompareResultController extends BasicController {

    @ApiOperation(value = "返回各比对结果数据的条数", notes = "返回各比对结果数据的条数接口")
    @RequestMapping(value = "/getCount/{appGuid}", method = RequestMethod.GET)
    public JSONObject getCount(@PathVariable("appGuid") String appGuid) {
        return this.basicReturnJson(appGuid, TransportCompareResultWebService.class, (param, service) -> service.getCount(appGuid));
    }

    @ApiOperation(value = "查看审核后比对详情", notes = "查看比对详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "guid", value = "对比详情guid", paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/findOneDetail/{guid}", method = RequestMethod.GET)
    public JSONObject findOneDetail(@PathVariable("guid") String guid) {
        return this.basicReturnJson(guid, TransportCompareResultWebService.class, (param, service) -> service.findOneDetail(guid));
    }

    @ApiOperation(value = "查看审核前比对详情", notes = "查看比对详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "guid", value = "对比详情guid", paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/findDetail/{guid}", method = RequestMethod.GET)
    public JSONObject findDetail(@PathVariable("guid") String guid) {
        return this.basicReturnJson(guid, TransportCompareResultWebService.class, (param, service) -> service.findDetail(guid));
    }

    @ApiOperation(value = "分页查看比对结果列表(记录)", notes = "分页查看比对结果列表（记录）接口")
    @RequestMapping(value = "/getCompareVOListByPage", method = RequestMethod.POST)
    public JSONObject getCompareVOListByPage(@RequestBody TransportCompareResultDTO dto) {
        dto.setType("scheduleLog");
        return this.basicReturnJson(dto, TransportCompareResultWebService.class, (param, service) -> service.getCompareVOListByPage(dto));
    }

    @ApiOperation(value = "分页查看比对结果列表（审核）", notes = "分页查看比对结果列表（审核）接口")
    @RequestMapping(value = "/getCompareScheduleListByPage", method = RequestMethod.POST)
    public JSONObject getCompareScheduleListByPage(@RequestBody TransportCompareResultDTO dto) {
        dto.setType("schedule");
        return this.basicReturnJson(dto, TransportCompareResultWebService.class, (param, service) -> service.getCompareVOListByPage(dto));
    }
}
