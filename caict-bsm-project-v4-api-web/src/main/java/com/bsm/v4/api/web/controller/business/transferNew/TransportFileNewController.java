package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportFileNewWebService;
import com.bsm.v4.system.model.dto.business.transferNew.TransportFileNewDTO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 传输文件表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/transportFile")
@Api(value = "传输文件管理接口", tags = "传输文件管理接口")
public class TransportFileNewController extends BasicController {

    @Autowired
    private TransportFileNewWebService transportFileNewWebService;

    @ApiOperation(value = "上传文件", notes = "支持上传CSV文件和材料附件，自动校验文件类型和大小")
    @PostMapping(value = "/upload")
    public JSONObject uploadFile(
            @ApiParam(value = "上传的文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "关联的任务ID", required = true) @RequestParam("jobId") Long jobId,
            @ApiParam(value = "文件类型（0-附件，1-CSV数据文件，2-材料附件）", required = true) @RequestParam("fileType") String fileType,
            @ApiParam(value = "数据类型（全量、增量、注销等）", required = false) @RequestParam(value = "dataType", required = false) String dataType,
            @ApiParam(value = "上传者", required = false) @RequestParam(value = "uploadedBy", required = false, defaultValue = "admin") String uploadedBy,
            @ApiParam(value = "文件描述", required = false) @RequestParam(value = "fileDescription", required = false) String fileDescription) {
        
        return this.basicReturnJson(file, TransportFileNewWebService.class,
                (fileParam, service) -> service.uploadFile(fileParam, jobId, fileType, dataType, uploadedBy, fileDescription));
    }

    @ApiOperation(value = "根据任务ID查询文件列表", notes = "查询指定任务下的所有文件")
    @GetMapping(value = "/findByJobId")
    public JSONObject findByJobId(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        
        return this.basicReturnJson(jobId, TransportFileNewWebService.class,
                (id, service) -> service.findByJobId(id));
    }

    @ApiOperation(value = "根据任务ID和文件类型查询文件列表", notes = "查询指定任务下指定类型的文件")
    @GetMapping(value = "/findByJobIdAndFileType")
    public JSONObject findByJobIdAndFileType(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId,
            @ApiParam(value = "文件类型", required = true) @RequestParam("fileType") String fileType) {
        
        return this.basicReturnJson(jobId, TransportFileNewWebService.class,
                (id, service) -> service.findByJobIdAndFileType(id, fileType));
    }

    @ApiOperation(value = "分页查询文件列表", notes = "支持多条件分页查询文件列表")
    @PostMapping(value = "/findPageWithConditions")
    public JSONObject findPageWithConditions(
            @ApiParam(value = "查询条件", required = true) @RequestBody TransportFileNewDTO dto) {
        
        return this.basicReturnJson(dto, TransportFileNewWebService.class,
                (condition, service) -> service.findPageWithConditions(condition));
    }

    @ApiOperation(value = "更新文件状态", notes = "更新文件的处理状态")
    @PutMapping(value = "/updateFileState")
    public JSONObject updateFileState(
            @ApiParam(value = "文件ID", required = true) @RequestParam("id") Long id,
            @ApiParam(value = "新状态（0-创建，1-上传成功，2-处理完成，3-处理异常）", required = true) @RequestParam("fileState") Integer fileState) {
        
        return this.basicReturnJson(id, TransportFileNewWebService.class,
                (fileId, service) -> service.updateFileState(fileId, fileState));
    }

    @ApiOperation(value = "删除文件", notes = "软删除文件记录")
    @DeleteMapping(value = "/delete")
    public JSONObject deleteFile(
            @ApiParam(value = "文件ID", required = true) @RequestParam("id") Long id) {
        
        return this.basicReturnJson(id, TransportFileNewWebService.class,
                (fileId, service) -> service.deleteFile(fileId));
    }

    @ApiOperation(value = "统计任务文件", notes = "统计指定任务下各类型文件的数量")
    @GetMapping(value = "/countByJobIdGroupByFileType")
    public JSONObject countByJobIdGroupByFileType(
            @ApiParam(value = "任务ID", required = true) @RequestParam("jobId") Long jobId) {
        
        return this.basicReturnJson(jobId, TransportFileNewWebService.class,
                (id, service) -> service.countByJobIdGroupByFileType(id));
    }
}
