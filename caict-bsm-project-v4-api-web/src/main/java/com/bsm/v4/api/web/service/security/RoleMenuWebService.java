package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.RoleMenuService;
import com.caictframework.data.service.BasicWebService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Title: RoleMenuWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/16 11:45
 * @description:
 */
@Service
public class RoleMenuWebService extends BasicWebService {
    private static final Logger LOG = LoggerFactory.getLogger(RoleMenuWebService.class);
    @Autowired
    private RoleMenuService roleMenuService;

    /**
     * 根据角色删除
     */
    public int deleteAllByRole(String roleId) {
        return roleMenuService.deleteAllByRole(roleId);
    }

    /**
     * 根据菜单删除
     */
    public int deleteAllByMenu(String menuId) {
        return roleMenuService.deleteAllByMenu(menuId);
    }
}
