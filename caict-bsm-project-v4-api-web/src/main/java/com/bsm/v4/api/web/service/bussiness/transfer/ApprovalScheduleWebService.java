package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.api.web.utils.ListUtil;
import com.bsm.v4.domain.security.service.business.transfer.ApprovalScheduleService;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalSchedule;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ApprovalScheduleWebService extends BasicWebService {

    @Autowired
    private ApprovalScheduleService approvalScheduleService;

    /**
     * 根据appGuid删除全部
     */
    public int deleteAllByAppGuid(String appGuid) {
        return approvalScheduleService.deleteAllByAppGuid(appGuid);
    }

    /**
     * 根据appGuid查询全部
     */
    public List<ApprovalSchedule> findAllByAppGuid(String appGuid, String btsId, String expandStation, String dataType) {
        ApprovalSchedule approvalSchedule = new ApprovalSchedule();
        approvalSchedule.setAppGuid(appGuid);
        approvalSchedule.setBtsId(btsId);
        approvalSchedule.setExpandStation(expandStation);
        approvalSchedule.setBtsDataType(dataType);
        return approvalScheduleService.findAllByWhere(approvalSchedule);
    }

    @Transactional
    public void deleteBatch(List<ApprovalSchedule> approvalScheduleList) {
        //这里有点蠢，循环批量删除，等有时间再优化
        List<Object> idList = approvalScheduleList.stream().map(ApprovalSchedule::getGuid).collect(Collectors.toList());
        try {
            List<List<Object>> lists = ListUtil.subList(idList, 100);
            for (List<Object> subList : lists) {
                approvalScheduleService.deleteBatch(subList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据job删除
     */
    public int deleteAllByJob(String jobGuid) {
        return approvalScheduleService.deleteAllByJob(jobGuid);
    }
}
