package com.bsm.v4.api.web.controller.business.freqevaluation;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.freqevaluation.FreqEvaluationWebService;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationVO;
import com.bsm.v4.system.model.vo.business.freqevaluation.OperUserStatisVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@RestController
@RequestMapping(value = "/apiWeb/freq/evaluation")
@Api(value = "web端频率使用率评价结果接口", tags = "web端频率使用率评价结果接口")
public class FreqEvaluationController extends BasicController {

    @ApiOperation(value = "查询无线电频率使用率评价结果", notes = "查询无线电频率使用率评价结果接口")
    @RequestMapping(value = "/searchFREQEvaluation", method = RequestMethod.POST)
    public JSONObject searchFREQEvaluation(@RequestBody FreqEvaluationVO vo) {
        return this.basicReturnJson(vo, FreqEvaluationWebService.class, (param, service) -> service.searchFREQEvaluation(vo));
    }

    @ApiOperation(value = "忙时激活用户数/用户流量统计表", notes = "忙时激活用户数/用户流量统计表接口")
    @RequestMapping(value = "/searchOperUserStatis", method = RequestMethod.POST)
    public JSONObject searchOperUserStatis(@RequestBody OperUserStatisVO vo) {
        return this.basicReturnJson(vo, FreqEvaluationWebService.class, (param, service) -> service.searchOperUserStatis(vo));
    }
}
