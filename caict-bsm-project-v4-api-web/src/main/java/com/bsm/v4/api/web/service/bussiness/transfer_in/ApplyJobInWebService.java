package com.bsm.v4.api.web.service.bussiness.transfer_in;

import com.bsm.v4.domain.security.service.business.applytable.ApplyJobInService;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ApplyJobInWebService extends BasicWebService {
    @Autowired
    private ApplyJobInService applyJobInService;

    public int updateByAppCode(String appGuid, String appCode, String status) {
        return applyJobInService.updateByAppCode(appGuid, appCode, status);
    }

    public Map<String, Object> findDetailByPage(ApprovalTransportJobDTO approvalTransportJobDTO) {
        return this.basicReturnSuccess(new PageHandle(approvalTransportJobDTO).buildPage(applyJobInService.findDetailByPage(approvalTransportJobDTO)));
    }

    public ApplyJobInDTO selectByAppCode(String appCode) {
        return applyJobInService.selectByAppCode(appCode);
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return applyJobInService.updateAppCode(oldAppCode, newAppCode);
    }
}
