package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.utils.ExcelHelperUtil;
import com.bsm.v4.api.web.utils.ZipCompressor;
import com.bsm.v4.domain.security.service.business.transfer.TransportRawBtsDealLogService;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDealLog;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class TransportRawBtsDealLogWebService {

    @Autowired
    private TransportRawBtsDealLogService transportRawBtsDealLogService;

    @Value("${caict.myFileExportPath}")
    private String serverExportPath;

    /**
     * 查询任务不同状态的数量
     */
    public List<TransportRawBtsDealLogDTO> selectCountByUserGroupValid(String jobGuid) {
        return transportRawBtsDealLogService.selectCountByUserGroupValid(jobGuid);
    }

    /**
     * 批量添加
     */
    public int insertBatch(List<TransportRawBtsDealLog> list) {
        return transportRawBtsDealLogService.insertBatch(list);
    }


    /**
     * 分页查询任务详细日志
     */
    public PageInfo<TransportRawBtsDealLogDTO> findAllDetailPageByJob(String jobGuid, TransportRawBtsDealLogDTO transportRawBtsDealLogDTO) {
        return new PageHandle(transportRawBtsDealLogDTO).buildPage(transportRawBtsDealLogService.findAllDetailByJob(jobGuid));
    }

    /**
     * 导出任务详细日志
     *
     * @param jobGuid jobGuid
     */
    public JSONObject exportDetailPageByJob(String jobGuid) {

        // 当前年月日
        String timeStr = new Timestamp(System.currentTimeMillis()).toString().replaceAll("\\.", "_")
                .replace(" ", "_").replaceAll("-", "").replaceAll(":", "")
                .replaceAll("\\\\", "");
        // 打包后的文件路径
        String filePath = serverExportPath + File.separator + timeStr + File.separator;
        // 打包文件名
        String outZipPath = filePath + timeStr + ".zip";

        // 跳出循环
        boolean flag = true;
        // 页数
        int pageNum = 1;
        TransportRawBtsDealLogDTO dto = new TransportRawBtsDealLogDTO();
        while (flag) {
            dto.setPage(pageNum);
            // 每次查询条数
            dto.setRows(50000);
            PageInfo pageInfo = new PageHandle(dto).buildPage(transportRawBtsDealLogService.findAllDetailByJob(jobGuid));
            List<TransportRawBtsDealLogDTO> listTemp = (List<TransportRawBtsDealLogDTO>) pageInfo.getList();

            // 每次查询生成一个excel文件，最后合并
            List<String[]> dataList = new ArrayList<>();
            dataList.add(getHead());
            dataList.addAll(getBody(listTemp));
            String[][] datas = new String[dataList.size()][dataList.get(0).length];
            for (int i = 0; i < dataList.size(); i++) {
                System.arraycopy(dataList.get(i), 0, datas[i], 0, dataList.get(0).length);
            }
            ExcelHelperUtil helper = new ExcelHelperUtil();
            String msg = helper.importDataToExcel(datas, filePath);
            System.out.println(msg);

            // 是否最后一页
            if (listTemp.size() < 50000) {
                flag = false;
            }
            pageNum++;
        }
        File fileServer = new File(filePath);
        // 打包文件夹
        packageFiles(outZipPath, fileServer);
        // 下载zip
//        downloadZip(outZipPath, fileServer, timeStr, response);

        return JSONResult.getSuccessJson(outZipPath.split("/file/")[1], "下载成功");
    }

    /**
     * 打包文件夹
     *
     * @param outZipPath zip文件地址
     * @param fileServer Excel文件路劲
     */
    private void packageFiles(String outZipPath, File fileServer) {
        File[] fileArray = fileServer.listFiles();
        if (fileArray != null) {
            List<File> files = new ArrayList<>(Arrays.asList(fileArray));
            File outFile = new File(outZipPath);
            System.out.println(new Timestamp(System.currentTimeMillis()) + "\t导出.outFile.out.-->:" + outZipPath);
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(outFile);
                ZipCompressor.toZip(files, fos);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 下载zip
     *
     * @param outZipPath zip文件地址
     * @param fileServer Excel文件路径
     * @param timeStr    当前年月日
     * @param response   response
     */
    private void downloadZip(String outZipPath, File fileServer, String timeStr, HttpServletResponse response) {
        // 下载zip
        InputStream in = null;
        OutputStream out = null;
        //将文件读入文件流.
        try {
            in = new FileInputStream(outZipPath);
            out = response.getOutputStream();
            response.reset();
            response.setContentType("application/x-download");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + timeStr + ".zip\"");
            byte[] b = new byte[8192];
            int len;
            while ((len = in.read(b)) > 0) {
                out.write(b, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            // 删除文件夹下文件
//            File[] files = fileServer.listFiles();
//            if (files != null) {
//                for (File file : files) {
//                    //删除
//                    boolean res = file.delete();
//                    if (res) System.out.println("文件删除成功！");
//                    else System.out.println("文件删除失败！");
//                }
//            }
        }
    }

    /**
     * 获取excel数据
     *
     * @param listData data list
     * @return List'<String[]>
     */
    private List<String[]> getBody(List<TransportRawBtsDealLogDTO> listData) {
        List<String[]> list = new ArrayList<>();

        for (TransportRawBtsDealLogDTO dto : listData) {
            String[] data = new String[26];
            data[0] = dto.getLogDetail();
            data[1] = dto.getDataType();
            if (dto.getDataType().equals("1")) {
                data[1] = "新增";
            } else if (dto.getDataType().equals("2")) {
                data[1] = "变更";
            } else {
                data[1] = "删除";
            }
            data[2] = dto.getIsValid() == 1 ? "有效" : "无效";
            data[3] = dto.getBtsName();
            data[4] = dto.getBtsId();
            data[5] = dto.getCellId();
            data[6] = dto.getCellName();
            data[7] = dto.getTechType();
            data[8] = dto.getLocation();
            data[9] = dto.getLongitude();
            data[10] = dto.getLatitude();
            data[11] = dto.getCounty();
            data[12] = dto.getDeviceModel();
            data[13] = dto.getVendorName();
            data[14] = dto.getModelCode();
            data[15] = dto.getSendStartFreq() + "~" + dto.getSendEndFreq();
            data[16] = dto.getAccStartFreq() + "~" + dto.getAccEndFreq();
            data[17] = dto.getHeight();
            data[18] = dto.getAntennaFactory();
            data[19] = dto.getAntennaModel();
            data[20] = dto.getAntennaAzimuth();
            data[21] = dto.getAntennaGain();
            data[22] = dto.getMaxEmissivePower();
            data[23] = dto.getPolarizationMode();
            data[24] = dto.getFeederLoss();
            data[25] = dto.getAltitude();

            list.add(data);
        }
        return list;
    }

    /**
     * 获取excel表头
     *
     * @return String[]
     */
    private String[] getHead() {
        String[] head = new String[26];
        head[0] = "异常原因";
        head[1] = "数据变更类型";
        head[2] = "状态";
        head[3] = "BTS_NAME/基站名称";
        head[4] = "BTS_ID/基站识别码";
        head[5] = "CELL_NAME/扇区名称";
        head[6] = "CELL_ID/扇区识别码";
        head[7] = "TECH_TYPE/技术体制";
        head[8] = "LOCATION/台址";
        head[9] = "LONGITUDE/经度";
        head[10] = "LATITUDE纬度";
        head[11] = "COUNTY/所属地区";
        head[12] = "DEVICE_MODEL/设备型号";
        head[13] = "DEVICE_FACTORY/设备生产厂家";
        head[14] = "MODEL_CODE/型号核准代码";
        head[15] = "SEND_FREQ/发射频率/频段(MHz)";
        head[16] = "ACC_FREQ/接收频率/频段(MHz)";
        head[17] = "HEIGHT/天线距地高度（M）";
        head[18] = "DEVICE_FACTORY/天线生产厂家";
        head[19] = "ANTENNA_MODEL/天线类型";
        head[20] = "ANTENNA_AZIMUTH/天线方位角(°)";
        head[21] = "ANTENNA_GAIN/天线增益(dBi)";
        head[22] = "MAX_EMISSIVE_POWER/最大发射功率(W)";
        head[23] = "POLARIZATION_MODE/极化方式";
        head[24] = "FEEDER_LOSS/馈线系统总损耗(dB)";
        head[25] = "ALTITUDE/海拔高度(m)";

        return head;
    }

    public List<TransportRawBtsDealDTO> findAllPageNew(String userId, TransportRawBtsDealDTO transportRawBtsDealDTO) {
        return transportRawBtsDealLogService.findAllPageNew(userId, transportRawBtsDealDTO);
    }

    /**
     * 生成下载数据
     */
    public int insertByTransportRawBts(String jobGuid) {
        return transportRawBtsDealLogService.insertByTransportRawBts(jobGuid);
    }

    /**
     * 查询数据
     *
     * @param jobId jobId
     * @param btsId btsId
     * @return list
     */
    public List<TransportRawBtsDealLog> findByBtsIdJobGuid(String btsId, String jobId) {
        return transportRawBtsDealLogService.findByBtsIdJobGuid(btsId, jobId);
    }

    /**
     * 根据btsId更新数据
     *
     * @param jobId  jobId
     * @param btsIds list
     */
    public void updateByBtsIds(String dataType, String jobId, List<String> btsIds, String regionCode) {
        transportRawBtsDealLogService.updateByBtsIds(dataType, jobId, btsIds, regionCode);
    }

    public List<TransportRawBtsDealDTO> findDealDataListR(TransportJobVO vo){
        return transportRawBtsDealLogService.findDealDataListR(vo);
    }
}
