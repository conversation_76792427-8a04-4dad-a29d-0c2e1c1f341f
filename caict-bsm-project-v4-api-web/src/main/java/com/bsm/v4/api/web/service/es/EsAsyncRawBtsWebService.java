package com.bsm.v4.api.web.service.es;

import com.bsm.v4.api.web.utils.DateUtils;
import com.bsm.v4.api.web.utils.ExcelHelperUtil;
import com.bsm.v4.domain.security.service.business.transfer.AsyncRawBtsService;
import com.bsm.v4.domain.security.service.es.EsAsyncRawBtsService;
import com.bsm.v4.system.model.entity.es.EsAsyncRawBts;
import com.bsm.v4.system.model.vo.es.EsAsyncRawBtsVO;
import com.caictframework.data.service.BasicWebService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description es总表数据 web service
 * @date 2023年8月11日 14点28分
 */
@Service
public class EsAsyncRawBtsWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(EsAsyncRawBtsWebService.class);

    @Value("${caict.myFileExportPath}")
    private String myFileExportPath;

    @Autowired
    private EsAsyncRawBtsService esAsyncRawBtsService;

    // todo
    @Autowired
    private AsyncRawBtsService asyncRawBtsService;

    /**
     * 保存数据
     *
     * @param entities entities
     */
    public void saveList(List<EsAsyncRawBts> entities) {
        esAsyncRawBtsService.saveList(entities);
    }

    /**
     * 根据guid查询数据
     *
     * @param ids ids
     */
    public List<EsAsyncRawBts> findAllById(List<String> ids) {
        return esAsyncRawBtsService.findAllById(ids);
    }

    /**
     * 生成使用率评价excel
     *
     * @param vo vo
     */
    public Map<String, Object> exportUsageEvaluation(EsAsyncRawBtsVO vo) {
        if (StringUtils.isNoneBlank(vo.getTrfDateStr())) {
            vo.setTrfDate(DateUtils.getDateByString(vo.getTrfDateStr()));
        }

        // todo
        List<EsAsyncRawBts> search = asyncRawBtsService.searchUsageEvaluation(vo);
//        List<EsAsyncRawBts> search = esAsyncRawBtsService.searchUsageEvaluation(vo);
        // 生成Excel文件
        List<String[]> dataList = new ArrayList<>(getBody(search));
        String[][] datas = new String[dataList.size()][dataList.get(0).length];
        for (int i = 0; i < dataList.size(); i++) {
            System.arraycopy(dataList.get(i), 0, datas[i], 0, dataList.get(i).length);
        }
        ExcelHelperUtil helper = new ExcelHelperUtil();
        String msg = helper.exportDataToExcel3(datas, myFileExportPath, "重庆市公众移动通信系统无线电频率使用率评价所需基站信息统计表_"
                + new Timestamp(System.currentTimeMillis()).toString().substring(0,10));
//        String msg = "/home/<USER>/bsm/4.0/file/export/重庆市公众移动通信系统无线电频率使用率评价所需基站信息统计表_" + new Timestamp(System.currentTimeMillis()).toString().substring(0, 10) + ".xls";
        LOG.info("频率使用率评价生成的文件地址：" + msg);
        return this.basicReturnResultJson(msg);
    }

    /**
     * 获取excel表头
     *
     * @return List'<String[]>
     */
    private List<String[]> getHead() {
        List<String[]> list = new ArrayList<>();
        String[] head = new String[13];
        head[0] = "基站编号";
        head[1] = "台站名称";
        head[2] = "台站设置使用人";
        head[3] = "发射频率下限(MHz)";
        head[4] = "发射频率上限(MHz)";
        head[5] = "技术体制(GSM/WCDMA/TD-LTE等)";
        head[6] = "扇区标识码(全球小区识别码)";
        head[7] = "台站地址";
        head[8] = "台站经度";
        head[9] = "台站纬度";
        head[10] = "地区编码";
        head[11] = "覆盖场景(城区/郊区/农村)";
        head[12] = "平均忙时激活用户数/用户流量";
        list.add(head);
        return list;
    }

    /**
     * 获取excel数据
     *
     * @param options data list
     * @return List'<String[]>
     */
    private List<String[]> getBody(List<EsAsyncRawBts> options) {
        List<String[]> list = new ArrayList<>();
        list.add(getHead().get(0));
        for (EsAsyncRawBts dto : options) {
            String[] data = new String[13];
            data[0] = dto.getBtsId();
            data[1] = dto.getBtsName();
            data[2] = dto.getOrgName();
            data[3] = dto.getSendStartFreq();
            data[4] = dto.getSendEndFreq();
            data[5] = dto.getTechType();
            data[6] = dto.getCellId();
            data[7] = dto.getLocation();
            data[8] = dto.getLongitude();
            data[9] = dto.getLatitude();
            data[10] = dto.getCounty();
            data[11] = dto.getStScene();
            data[12] = dto.getTrfUser() != null ? dto.getTrfUser().toString() : "";
            list.add(data);
        }
        return list;
    }

    public Map<String,Object> test(){
        esAsyncRawBtsService.test();
        return null;
    }
}
