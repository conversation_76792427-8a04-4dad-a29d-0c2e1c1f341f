package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.domain.security.service.business.transferNew.TransportFileNewService;
import com.bsm.v4.system.model.dto.business.transferNew.TransportFileNewDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportFileNew;
import com.bsm.v4.system.model.vo.business.transferNew.TransportFileNewVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 传输文件表WebService
 */
@Service
public class TransportFileNewWebService extends BasicWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportFileNewService transportFileNewService;

    // 支持的文件类型
    private static final Set<String> ALLOWED_EXTENSIONS = new HashSet<>(Arrays.asList(
            "txt", "doc", "docx", "pdf", "xls", "xlsx", "jpg", "jpeg", "png", "csv"
    ));

    // 文件类型映射
    private static final Map<String, String> MIME_TYPE_MAP = new HashMap<>();
    
    static {
        MIME_TYPE_MAP.put("txt", "text/plain");
        MIME_TYPE_MAP.put("doc", "application/msword");
        MIME_TYPE_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPE_MAP.put("pdf", "application/pdf");
        MIME_TYPE_MAP.put("xls", "application/vnd.ms-excel");
        MIME_TYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_TYPE_MAP.put("jpg", "image/jpeg");
        MIME_TYPE_MAP.put("jpeg", "image/jpeg");
        MIME_TYPE_MAP.put("png", "image/png");
        MIME_TYPE_MAP.put("csv", "text/csv");
    }

    /**
     * 上传文件
     * @param file 上传的文件
     * @param jobId 关联的任务ID
     * @param fileType 文件类型（0-附件，1-CSV数据文件，2-材料附件）
     * @param dataType 数据类型
     * @param uploadedBy 上传者
     * @param fileDescription 文件描述
     * @return 上传结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> uploadFile(MultipartFile file, Long jobId, String fileType, 
                                         String dataType, String uploadedBy, String fileDescription) {
        try {
            // 1. 校验文件
            Map<String, Object> validationResult = validateFile(file);
            if (!(Boolean) validationResult.get("valid")) {
                return this.basicReturnFailure((String) validationResult.get("message"));
            }

            // 2. 计算文件MD5
            String fileMd5 = calculateMD5(file.getBytes());
            
            // 3. 检查文件是否已存在
            TransportFileNew existingFile = transportFileNewService.findByMd5(fileMd5);
            if (existingFile != null) {
                return this.basicReturnFailure("文件已存在，MD5: " + fileMd5);
            }

            // 4. 生成新文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String newFileName = generateUniqueFileName(originalFilename);

            // 5. 确保目录存在
            File uploadDir = new File(myFilePath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 6. 保存文件
            String filePath = myFilePath + File.separator + newFileName;
            File destFile = new File(filePath);
            file.transferTo(destFile);

            // 7. 创建文件记录
            TransportFileNew transportFile = new TransportFileNew();
            transportFile.setJobId(jobId);
            transportFile.setFileLocalName(originalFilename);
            transportFile.setFileRenamed(newFileName);
            transportFile.setFilePath(filePath);
            transportFile.setFileSize(file.getSize());
            transportFile.setFileState(1); // 上传成功
            transportFile.setFileType(fileType);
            transportFile.setDataType(dataType);
            transportFile.setFileExtension(fileExtension);
            transportFile.setMimeType(MIME_TYPE_MAP.get(fileExtension.toLowerCase()));
            transportFile.setFileMd5(fileMd5);
            transportFile.setIsDeleted(0);
            transportFile.setUploadedBy(uploadedBy);
            transportFile.setFileDescription(fileDescription);
            
            Date now = new Date();
            transportFile.setCreatedAt(now);
            transportFile.setUpdatedAt(now);

            // 8. 保存到数据库
            transportFileNewService.save(transportFile);

            // 9. 构建返回结果
            TransportFileNewVO result = new TransportFileNewVO();
            BeanUtils.copyProperties(transportFile, result);

            return this.basicReturnSuccess(result);

        } catch (IOException e) {
            return this.basicReturnFailure("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            return this.basicReturnFailure("系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据任务ID查询文件列表
     * @param jobId 任务ID
     * @return 文件列表
     */
    public Map<String, Object> findByJobId(Long jobId) {
        try {
            List<TransportFileNewDTO> fileList = transportFileNewService.findByJobId(jobId);
            return this.basicReturnSuccess(fileList);
        } catch (Exception e) {
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务ID和文件类型查询文件列表
     * @param jobId 任务ID
     * @param fileType 文件类型
     * @return 文件列表
     */
    public Map<String, Object> findByJobIdAndFileType(Long jobId, String fileType) {
        try {
            List<TransportFileNewDTO> fileList = transportFileNewService.findByJobIdAndFileType(jobId, fileType);
            return this.basicReturnSuccess(fileList);
        } catch (Exception e) {
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询文件列表
     * @param dto 查询条件
     * @return 分页结果
     */
    public Map<String, Object> findPageWithConditions(TransportFileNewDTO dto) {
        try {
            return this.basicReturnResultJson(new PageHandle(dto).buildPage(
                    transportFileNewService.findPageWithConditions(dto)));
        } catch (Exception e) {
            return this.basicReturnFailure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新文件状态
     * @param id 文件ID
     * @param fileState 新状态
     * @return 更新结果
     */
    public Map<String, Object> updateFileState(Long id, Integer fileState) {
        try {
            int result = transportFileNewService.updateFileState(id, fileState);
            if (result > 0) {
                return this.basicReturnSuccess("状态更新成功");
            } else {
                return this.basicReturnFailure("状态更新失败");
            }
        } catch (Exception e) {
            return this.basicReturnFailure("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件（软删除）
     * @param id 文件ID
     * @return 删除结果
     */
    public Map<String, Object> deleteFile(Long id) {
        try {
            int result = transportFileNewService.softDelete(id);
            if (result > 0) {
                return this.basicReturnSuccess("文件删除成功");
            } else {
                return this.basicReturnFailure("文件删除失败");
            }
        } catch (Exception e) {
            return this.basicReturnFailure("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务ID统计各类型文件数量
     * @param jobId 任务ID
     * @return 统计结果
     */
    public Map<String, Object> countByJobIdGroupByFileType(Long jobId) {
        try {
            List<TransportFileNewDTO> statistics = transportFileNewService.countByJobIdGroupByFileType(jobId);
            return this.basicReturnSuccess(statistics);
        } catch (Exception e) {
            return this.basicReturnFailure("统计失败：" + e.getMessage());
        }
    }

    /**
     * 校验文件
     * @param file 上传的文件
     * @return 校验结果
     */
    private Map<String, Object> validateFile(MultipartFile file) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            result.put("valid", false);
            result.put("message", "文件不能为空");
            return result;
        }

        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            result.put("valid", false);
            result.put("message", "文件名不能为空");
            return result;
        }

        // 检查文件扩展名
        String fileExtension = getFileExtension(originalFilename);
        if (!ALLOWED_EXTENSIONS.contains(fileExtension.toLowerCase())) {
            result.put("valid", false);
            result.put("message", "不支持的文件类型，支持的格式：" + String.join(", ", ALLOWED_EXTENSIONS));
            return result;
        }

        // 检查文件大小（限制为100MB）
        long maxSize = 100 * 1024 * 1024; // 100MB
        if (file.getSize() > maxSize) {
            result.put("valid", false);
            result.put("message", "文件大小不能超过100MB");
            return result;
        }

        result.put("valid", true);
        result.put("message", "文件校验通过");
        return result;
    }

    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 扩展名
     */
    private String getFileExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 生成唯一文件名
     * @param originalFilename 原始文件名
     * @return 新的唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String baseName = originalFilename.substring(0, originalFilename.lastIndexOf('.'));
        String extension = originalFilename.substring(originalFilename.lastIndexOf('.'));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = sdf.format(new Date());

        String newFileName = baseName + "_" + timestamp + extension;

        // 检查文件是否存在，如果存在则添加序号
        File file = new File(myFilePath + File.separator + newFileName);
        int counter = 1;
        while (file.exists()) {
            newFileName = baseName + "_" + timestamp + "_" + counter + extension;
            file = new File(myFilePath + File.separator + newFileName);
            counter++;
        }

        return newFileName;
    }

    /**
     * 计算文件MD5值
     * @param fileBytes 文件字节数组
     * @return MD5值
     */
    private String calculateMD5(byte[] fileBytes) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(fileBytes);
            BigInteger bigInt = new BigInteger(1, digest);
            return bigInt.toString(16).toUpperCase();
        } catch (Exception e) {
            return "";
        }
    }
}
