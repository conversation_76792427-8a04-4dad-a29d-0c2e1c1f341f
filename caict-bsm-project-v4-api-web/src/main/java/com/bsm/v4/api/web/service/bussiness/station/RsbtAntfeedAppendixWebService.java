package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtAntfeedAppendixService;
import com.bsm.v4.domain.security.service.business.station.RsbtAntfeedService;
import com.bsm.v4.domain.security.service.business.station.RsbtAntfeedTService;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeedAppendix;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtAntfeedAppendixWebService extends BasicWebService {

    @Autowired
    private RsbtAntfeedAppendixService rsbtAntfeedAppendixService;

    @Autowired
    private RsbtAntfeedService rsbtAntfeedService;

    @Autowired
    private RsbtAntfeedTService rsbtAntfeedTService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtAntfeedAppendix> rsbtAntfeedAppendixListInsert) {
        return rsbtAntfeedAppendixService.insertBatch(rsbtAntfeedAppendixListInsert);
    }

    /**
     * 批量删除（修改状态）
     */
    public void deleteBatchByAntGuid(List<RsbtAntfeed> rsbtAntfeedListDelete) {
        List<Object> ids = rsbtAntfeedListDelete.stream().map(RsbtAntfeed::getGuid).collect(Collectors.toList());
        rsbtAntfeedAppendixService.deleteBatch(ids);
        rsbtAntfeedTService.deleteBatch(ids);
        rsbtAntfeedService.deleteBatch(ids);
    }
}
