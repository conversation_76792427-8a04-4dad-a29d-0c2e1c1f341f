package com.bsm.v4.api.web.controller.business.transferNew;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transferNew.TransportJobNewWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.system.model.vo.business.transferNew.JobMetaVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 传输任务表Controller
 */
@RestController
@RequestMapping(value = "/apiWeb/transferNew/zhuanTransportJob")
@Api(value = "传输任务管理接口", tags = "传输任务管理接口")
public class TransportJobNewController extends BasicController {

    @Autowired
    private TransportJobNewWebService transportJobNewWebService;

    @Autowired
    private AuthWebService authWebService;

    @ApiOperation(value = "上传CSV文件", notes = "支持上传CSV文件，校验后缀名并自动重命名避免覆盖")
    @PostMapping(value = "/uploadCsv")
    public JSONObject uploadCsv(
            @ApiParam(value = "CSV文件", required = true) @RequestParam("file") MultipartFile file) {
        return this.basicReturnJson(file, TransportJobNewWebService.class,
                (fileParam, service) -> service.uploadCsvFile(fileParam));
    }

    @ApiOperation(value = "解析并校验CSV文件", notes = "解析CSV文件内容并进行数据校验，返回有效和无效数据")
    @PostMapping(value = "/parseAndValidateCsv")
    public JSONObject parseAndValidateCsv(
            @ApiParam(value = "CSV文件路径", required = true) @RequestParam("filePath") String filePath,
            @ApiParam(value = "作业元数据", required = true) @RequestBody JobMetaVO jobMeta) {
        return this.basicReturnJson(filePath, TransportJobNewWebService.class,
                (path, service) -> service.parseAndValidateCsv(path, jobMeta));
    }

}
