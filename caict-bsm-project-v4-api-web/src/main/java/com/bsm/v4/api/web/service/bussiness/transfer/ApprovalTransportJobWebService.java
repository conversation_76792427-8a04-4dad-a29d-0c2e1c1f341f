package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.station.BsmStationLicenseWebService;
import com.bsm.v4.api.web.service.document.FastFDSWebService;
import com.bsm.v4.api.web.service.document.HdfsWebService;
import com.bsm.v4.api.web.service.rule.BtsDataCheckRuleWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.api.web.service.security.OrgWebService;
import com.bsm.v4.api.web.utils.CompareData;
import com.bsm.v4.api.web.utils.ParseXlsxExcel;
import com.bsm.v4.api.web.utils.ThreadPoolUtil;
import com.bsm.v4.domain.security.service.WebSocketService;
import com.bsm.v4.domain.security.service.business.transfer.*;
import com.bsm.v4.domain.security.service.security.OrgUsersService;
import com.bsm.v4.domain.security.service.security.RegionService;
import com.bsm.v4.domain.security.service.security.UsersService;
import com.bsm.v4.system.model.contrust.DBBoolConst;
import com.bsm.v4.system.model.contrust.UserRoleConst;
import com.bsm.v4.system.model.contrust.transfer.*;
import com.bsm.v4.system.model.dto.business.station.StationScheduleDataDTO;
import com.bsm.v4.system.model.dto.business.transfer.*;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.station.*;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.bsm.v4.system.model.entity.business.transfer.*;
import com.bsm.v4.system.model.entity.security.Region;
import com.bsm.v4.system.model.entity.security.Users;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.service.RedisService;
import com.caictframework.utils.util.JSONResult;
import com.caictframework.utils.util.RedisLock;
import com.caictframework.utils.util.VerificationCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ApprovalTransportJobWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(ApprovalTransportJobWebService.class);
    @Autowired
    public ApprovalTransportJobService approvalTransportJobService;
    @Autowired
    private BsmStationLicenseWebService bsmStationLicenseWebService;
    @Autowired
    public ApprovalDataService approvalDataService;
    @Autowired
    public RegionService regionService;
    @Autowired
    public DataApproveService dataApproveService;
    @Autowired
    public UsersService usersService;
    @Autowired
    public HdfsWebService hdfsWebService;
    @Autowired
    public OrgWebService orgWebService;
    @Autowired
    public OrgUsersService orgUsersService;
    @Autowired
    public FastFDSWebService fastFDSWebService;
    @Autowired
    public TransportApprovalRawBtsService transportApprovalRawBtsService;
    @Autowired
    public ApprovalScheduleLogService approvalScheduleLogService;
    @Autowired
    public BtsDataCheckRuleWebService btsDataCheckRuleWebService;
    @Autowired
    public TransportRawBtsDealService transportRawBtsDealService;
    @Autowired
    public ApprovalScheduleService approvalScheduleService;
    @Autowired
    public TransportJobWebService transportJobWebService;
    @Autowired
    public TransportFileWebService transportFileWebService;
    @Autowired
    public TransportCompareResultService transportCompareResultService;
    @Autowired
    public LogTransportJobWebService logTransportJobWebService;
    @Autowired
    public TransportScheduleWebService transportScheduleWebService;
    @Autowired
    public CoordinateScheduleTempWebService coordinateScheduleTempWebService;
    @Autowired
    public ApprovalScheduleWebService approvalScheduleWebService;
    @Autowired
    public ApprovalRawBtsService approvalRawBtsService;
    @Autowired
    public CoordinateScheduleTempLogWebService coordinateScheduleTempLogWebService;
    @Autowired
    public AuthWebService authWebService;
    @Autowired
    public RedisService redisService;
    @Autowired
    public TransportJobBranchWebService transportJobBranchWebService;
    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;

    @Value("${caict.myFilePath}")
    public String myFilePath;

    private boolean flag = true;

    /**
     * 给文件绑定任务id
     */
    public void setFileJobId(ApprovalTransportJobDTO dtoTemp) {
        TransportFile file = transportFileWebService.findById(dtoTemp.getFileId());
        file.setDataType(dtoTemp.getDataType());
        file.setGenNum(dtoTemp.getGenNum());
        file.setJobGuid(dtoTemp.getGuid());
        transportFileWebService.saveTransportFile(file);
    }

    /**
     * 新建中转任务
     */
    @Transactional
    public Map<String, Object> createApproveJob(ApprovalTransportJobDTO dtoTemp) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dtoTemp.getToken());
        if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
        //判断是否有未提交的数据
        ApprovalTransportJobDTO dto = approvalTransportJobService.findOneByJobGuidUpload(dtoTemp.getJobGuid(), dtoTemp.getDataType(), dtoTemp.getGenNum());
        if (dto != null) {
            TransportFile file = new TransportFile();
            file.setJobGuid(dto.getGuid());
            file.setDataType(dto.getDataType());
            file.setGenNum(dto.getGenNum());
//            if ("approve".equals(dto.getType()))
            file.setFileType(TransportFileStateConst.EXAMINE);
//            else if ("coordinate".equals(dto.getType()))
//                file.setFileType(TransportFileStateConst.COORDINATE);
            //查询所属exl
            List<TransportFileDTO> transportFileCSVDTOList = transportFileWebService.findAllByWhere(file);
            dto.setTransportFileDTOList(transportFileCSVDTOList);
        } else {
            TransportJobDTO transportJobDTO = transportJobWebService.findOneById(dtoTemp.getJobGuid());
            TransportJobBranchDTO transportJobBranchDTO = transportJobBranchWebService.findOneByJobUserTypeGen(dtoTemp.getJobGuid(), transportJobDTO.getUserGuid(),
                    dtoTemp.getDataType(), dtoTemp.getGenNum(), dtoTemp.getRegionCode(), dtoTemp.getTechType());
            //添加
            ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
            approvalTransportJob.setOpUserGuid(usersDTO.getUserId());
            approvalTransportJob.setJobGuid(dtoTemp.getJobGuid());
            approvalTransportJob.setDataType(dtoTemp.getDataType());
            approvalTransportJob.setGenNum(dtoTemp.getGenNum());
            // todo
//            var region = regionService.findById(usersDTO.getRegionId());
//            approvalTransportJob.setRegionCode(region.getCode());
            approvalTransportJob.setTechType(dtoTemp.getTechType());
            approvalTransportJob.setIsApproved(ApprovalTransportJobStateConst.APPROVED_UPLOAD);
//            approvalTransportJob.setUserGuid(orgWebService.findUserIDByRegionCodeAndOrgType(dtoTemp.getRegionCode(), usersDTO.getType()));
            approvalTransportJob.setUserGuid(transportJobBranchDTO.getUserGuid());
            approvalTransportJob.setIsCompare(ApprovalTransportJobStateConst.TO_BE_COMPARED);
            approvalTransportJob.setGmtCreate(new Date());
            approvalTransportJob.setDiffCount(0L);
            approvalTransportJob.setIdenCount(0L);
//            approvalTransportJob.setRegionCode(dtoTemp.getRegionCode());
            approvalTransportJob.setAppCode(transportJobBranchDTO.getAppCode());
            String save = approvalTransportJobService.save(approvalTransportJob);
            if (save == null) return this.basicReturnFailure("创建任务失败");

            approvalTransportJob.setGuid(save);
            approvalTransportJob.setJobCode(save);

            dto = new ApprovalTransportJobDTO();
            BeanUtils.copyProperties(approvalTransportJob, dto);
        }

        return this.basicReturnSuccess(dto);
    }

    /**
     * 校验处理上传文件
     */
    @Transactional
    public Map<String, Object> processingApproveJob(ApprovalTransportJobDTO dtoTemp) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dtoTemp.getToken());
        if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
        //将exl文件的状态改成校验中
        String fileSave = transportFileWebService.updateFileStateByFileId(dtoTemp.getFileId(), TransportFileStateConst.CHECK_PROCESSING);
        if (fileSave != null) {
            LOG.info(new Timestamp(System.currentTimeMillis()).toString() + ":处理无委" + usersDTO.getLoginName() + "上传的exl文件");
            // 要执行的任务
            // 创建线程对象并启动
//            ThreadPoolUtil.getThread().execute(() -> approvalTransportJobWebService.processing(dtoTemp, usersDTO.getOrgDTO().getOrgAreaCode()));
            ThreadPoolUtil.getThread().execute(() -> approvalTransportJobWebService.processing(dtoTemp, null));
            return this.basicReturnSuccess("已提交处理，请稍后");
        }
        return this.basicReturnFailure("处理失败");
    }

    /**
     * 重点台站校验
     */
    public Map<String, Object> commitCoordinate(ApprovalTransportJobDTO dtoTemp) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dtoTemp.getToken());
        if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
        //判断job下所有文件都已经处理
        Map<String, Object> fileStateConst = transportFileWebService.fileStateConst(dtoTemp.getAppCode(), TransportFileStateConst.EXAMINE);
        if (fileStateConst != null) return fileStateConst;

        //提交重点台站操作
        ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(dtoTemp.getGuid());
        approvalTransportJob.setGmtModified(new Date());
        approvalTransportJob.setIsCompare(ApprovalTransportJobStateConst.COORDINATE_PROCESSING);
        Long saveJob = approvalTransportJobService.save(approvalTransportJob);
        if (saveJob != null) {
            //如果是全量上传修改任务状态为干扰协调中
            if (transportScheduleWebService.selectCountAllByJob(approvalTransportJob.getJobGuid(), TransportScheduleConst.ISHANDLE_UPLOAD)
                    <= approvalRawBtsService.selectCountAllByAppGuid(dtoTemp.getGuid()))
                transportJobWebService.updateIsCompareById(approvalTransportJob.getJobGuid(), TransportJobStateConst.INTERFERE_ING);
            //发送rabbit todo
//            sendRabbit(taskData, "提交干扰协调", saveJob, "处理提交干扰协调", RabbitMessageConst.JOB_COMMIT_COORDINATE, usersDTO.getUserId());
            return this.basicReturnSuccess(approvalTransportJob);
        }
        return this.basicReturnFailure("提交任务失败");
    }

    /**
     * 处理重点台站上传文件
     */
    public Map<String, Object> processingCoordinate(ApprovalTransportJobDTO dtoTemp) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dtoTemp.getToken());
        if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
        //将exl文件的状态改成校验中
        String fileSave = transportFileWebService.updateFileStateByFileId(dtoTemp.getFileId(), TransportFileStateConst.CHECK_PROCESSING);
        if (fileSave != null) {
            //发送rabbit todo
//            sendRabbit(taskData, "文件处理", fileId, "处理无委" + usersDTO.getUserId() + "上传的干扰协调exl文件", RabbitMessageConst.JOB_COORDINATE_EXL, usersDTO.getUserId());
            return JSONResult.getSuccessJson("已提交处理，请稍后");
        }
        return this.basicReturnFailure("处理失败");
    }

    /**
     * 重点台站不同意提交
     */
    public Map<String, Object> commitApproveJobCoordinate(String appGuid) {
        //判断job下所有文件都已经处理
        Map<String, Object> fileStateConst = transportFileWebService.fileStateConst(appGuid, TransportFileStateConst.COORDINATE);
        if (fileStateConst != null) return fileStateConst;

        //提交操作
        ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(appGuid);
        approvalTransportJob.setGmtModified(new Date());
        approvalTransportJob.setIsCompare(ApprovalTransportJobStateConst.COMPLETE);

        //修改审核任务完结
        approvalTransportJobService.save(approvalTransportJob);
        transportJobBranchWebService.updateByConfirm(approvalTransportJob.getJobGuid(), approvalTransportJob.getDataType(),
                approvalTransportJob.getGenNum(), approvalTransportJob.getTechType(), ApprovalTransportJobStateConst.COMPLETE);
        //删除重点台站同步数据
        coordinateScheduleTempWebService.deleteAllByAppGuid(appGuid);

        //删除审核待办表
        approvalScheduleWebService.deleteAllByAppGuid(appGuid);

        //修改任务完结
        transportJobWebService.updateIsCompareById(approvalTransportJob.getJobGuid(), TransportJobStateConst.COMPLETE);
        //删除消息通知 todo
//        myRestRepository.getForStringNpList(messageUrl + "deleteByFromGuid/" + approvalTransportJob.getJobGuid());

        return this.basicReturnSuccess("操作成功，任务已经终结");
    }

    /**
     * 提交任务
     */
    public Map<String, Object> commitApproveJob(ApprovalTransportJobDTO dtoTemp) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dtoTemp.getToken());
        if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
        //判断job下所有文件都已经处理
        Map<String, Object> fileStateConst = transportFileWebService.fileStateConst(dtoTemp.getGuid(), TransportFileStateConst.EXAMINE);
        if (fileStateConst != null) return fileStateConst;

        //提交操作
        if (!StringUtils.isNotEmpty(dtoTemp.getGuid()) || "null".equals(dtoTemp.getGuid()))
            return this.basicReturnFailure("任务已提交，无法重复提交任务");
        ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(dtoTemp.getGuid());
        //判断提交状态
        if (approvalTransportJob == null) return this.basicReturnFailure("任务已提交，无法重复提交任务");
        if ((ApprovalTransportJobStateConst.COMMIT_SUCCESS.equals(approvalTransportJob.getIsCompare())
                || ApprovalTransportJobStateConst.COORDINATE_PROCESSING.equals(approvalTransportJob.getIsCompare()))) {
            if ("coordinate".equals(dtoTemp.getType())) return this.basicReturnFailure("无法提交任务，请确认该审批任务的干扰协调数据已全部上传");
            return this.basicReturnFailure("任务已提交，无法重复提交任务");
        }

        //判断提交数据不能大于待办数据
        if ("coordinate".equals(dtoTemp.getType())) {
            if (transportScheduleWebService.selectCountAllByJob(approvalTransportJob.getJobGuid(), TransportScheduleConst.ISHANDLE_SUCCUSS)
                    < coordinateScheduleTempLogWebService.selectCountByJobIdDataType(dtoTemp.getGuid(), null))
                return this.basicReturnFailure("无法提交任务，提交数据大于待办数据");
        } else {
            if (transportScheduleWebService.selectCountAllByJob(approvalTransportJob.getJobGuid(), null)
                    < approvalRawBtsService.selectCountAllByAppGuid(dtoTemp.getGuid())) {
                // 提交失败
                approvalTransportJob.setIsCompare(ApprovalTransportJobStateConst.COMMIT_FAIL);
                approvalTransportJobService.save(approvalTransportJob);
                return this.basicReturnFailure("无法提交任务，提交数据大于待办数据");
            }
        }

        approvalTransportJob.setGmtModified(new Date());
        // 提交成功
        approvalTransportJob.setIsCompare(ApprovalTransportJobStateConst.COMMIT_SUCCESS);
//        approvalTransportJob.setDataType(dataType);
//        approvalTransportJob.setGenNum(genNum);
        String saveJob = approvalTransportJobService.save(approvalTransportJob);
        if (saveJob != null) {
            approvalTransportJob.setGuid(saveJob);
            //发送rabbit
            LOG.info(new Timestamp(System.currentTimeMillis()).toString() + ":处理上传的exl文件");
            // 要执行的任务
            // 创建线程对象并启动
            ThreadPoolUtil.getThread().execute(() -> approvalTransportJobWebService.commit(approvalTransportJob.getGuid(), usersDTO.getUserId()));
            return this.basicReturnSuccess(approvalTransportJob);
        }
        return this.basicReturnFailure("提交任务失败");
    }

    /**
     * 审核
     */
    public Map<String, Object> approveApproveJob(ApprovalTransportJobDTO dto) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dto.getToken());
        if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
        ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(dto.getGuid());
        approvalTransportJob.setGmtModified(new Date());
        approvalTransportJob.setIsApproved(dto.getIsApproved());
        approvalTransportJob.setOpDetail(dto.getOpDetail() == null ? "" : dto.getOpDetail());
        approvalTransportJob.setIsCompare(ApprovalTransportJobStateConst.CHECKIN_PROCESSING);
        //修改状态为审核中，并提交处理模块处理
        String saveJob = approvalTransportJobService.save(approvalTransportJob);
        if (saveJob != null) {
            approvalTransportJob.setGuid(saveJob);
            //发送rabbit
            LOG.info(new Timestamp(System.currentTimeMillis()).toString() + ":审核任务文件");
            // 要执行的任务
            // 创建线程对象并启动
            ThreadPoolUtil.getThread().execute(() -> approvalTransportJobWebService.approve(approvalTransportJob.getGuid(), usersDTO.getUserId()));
            return this.basicReturnSuccess(approvalTransportJob);
        }
        return this.basicReturnFailure("审核失败");
    }

    /**
     * 分页查看对比任务列表
     */
    public Map<String, Object> findPageCompareJobVOListByPage(ApprovalTransportJobDTO dto) {
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(approvalTransportJobService.findPageCompareJobVOListByPage(dto)));
    }

    /**
     * 分页查看异常任务列表
     */
    public Map<String, Object> findApprovalTransportJobVOLogPage(ApprovalTransportJobDTO dto) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(dto.getToken());
        if (usersDTO == null) return JSONResult.getFailureJson("登录超时，请重新登录");
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(approvalTransportJobService.findApprovalTransportJobVOLogPage(dto.getUserType(),
                usersDTO.getUserId(), TransportFileStateConst.CHECK_FAIL)));
    }

    /**
     * 分页查询待办任务
     */
    public Map<String, Object> findAllPageByWhere(ApprovalTransportJobDTO dto) {
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(approvalTransportJobService.findAllPageByWhere(dto)));
    }

    /**
     * 权限查询分页查看审核列表历史记录
     */
    public Map<String, Object> findAllPageAppTransportJobByJur(ApprovalTransportJobDTO dto) {
        if (StringUtils.isEmpty(dto.getUserType()) || dto.getUserType().equals(UserRoleConst.USER_TYPE_ADMIN) || dto.getUserType().equals(UserRoleConst.USER_TYPE_WUWEI)) {
            //无委和管理员操作
        } else {
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(dto.getToken());
            if (usersDTO == null) return this.basicReturnFailure("登录超时，请重新登录");
            dto.setUserGuid(usersDTO.getUserId());
        }
        return findAllPageAppTransportJob(dto);
    }

    /**
     * 分页查看审核列表历史记录
     */
    public Map<String, Object> findAllPageAppTransportJob(ApprovalTransportJobDTO dto) {
        return this.basicReturnResultJson(new PageHandle(dto).buildPage(approvalTransportJobService.findAllPageAppTransportJob(dto)));
    }

    /**
     * 查询详情
     */
    public Map<String, Object> approveDetail(String appGuid) {
        ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(appGuid);
        if (approvalTransportJob != null) {
            ApprovalTransportJobDTO dto = new ApprovalTransportJobDTO();
            BeanUtils.copyProperties(approvalTransportJob, dto);
            return this.basicReturnSuccess(dto);
        }
        return this.basicReturnFailure("查询失败，无数据");
    }

    //发送rabbit
//    protected void sendRabbit(String key, String title, String content, String remark, String type, String from) {
//        RabbitMessage rabbitMessage = new RabbitMessage(VerificationCode.idGet("rabbit", 4), title, content, DateUtils.getDateTime(), from, remark, type);
//        rabbitService.sendSimple(key, JSONObject.toJSONString(rabbitMessage));
//    }

    /**
     * 根据jobGuid查询未提交的数据
     */
    public ApprovalTransportJobDTO findOneByJobGuidUpload(String jobGuid, String dataType, String genNum) {
        return approvalTransportJobService.findOneByJobGuidUpload(jobGuid, dataType, genNum);
    }

    public void deleteByGuid(String guid) {
        approvalTransportJobService.delete(guid);
    }

    /**
     * 处理上传文件
     */
    public void processing(ApprovalTransportJobDTO dtoTemp, String regionCode) {

        Map<String, Integer> map = processingExcel(dtoTemp.getFileId(), dtoTemp.getType(), dtoTemp.getDataType(), dtoTemp.getGenNum(), regionCode);

        //csv文件总条数
        int csvSize = map.get("excelSize");
        //校验成功的条数
        int dealSize = approvalRawBtsService.selectCountByFileId(dtoTemp.getFileId());
        //校验失败的条数
        int errorSize = logTransportJobWebService.selectCountByTypeFileId(LogTransportJobTypeConst.ERROR, dtoTemp.getFileId());

        //处理消息推送到前台
        this.processing(map, csvSize, dealSize, errorSize);
    }

    /**
     * 处理消息推送到前台
     * <p>
     * Size:文件总条数
     * dealSize:校验成功的条数
     * errorSize:校验失败的条数
     */
    private void processing(Map<String, Integer> map, int fileSize, int dealSize, int errorSize) {
        String msg;
        if (map.get("type") == 0) {
            fileSize = 0;
            dealSize = 0;
            errorSize = 0;
        }
        if (map.get("result") != 0) {
            msg = "校验成功！成功条数：" + dealSize + "，自动过滤了" + (fileSize - dealSize) + "条重复数据！";
        } else {
            msg = "校验失败！失败条数：" + errorSize + "，成功条数：" + (fileSize - errorSize) + "！";
        }
        //将实时数据推送给前端
        try {
            WebSocketService.sendInfo(msg, "csv");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * excel文件在线处理
     *
     * @param fileId fileId
     * @param type   type
     * @return map
     */
    private Map<String, Integer> processingExcel(String fileId, String type, String dataType, String genNum, String regionCode) {
        Map<String, Integer> map = new HashMap<>();
        map.put("result", 0);    //返回结果：0为失败，1为成功
        map.put("excelSize", 0);   //csv文件大小
        map.put("type", 0);      //类型：0为未校验每条数据，1为已校验每条数据

        TransportFile file = transportFileWebService.findOneByGuid(fileId);
        if (file != null) {
            ApprovalTransportJob atJob = approvalTransportJobService.findById(file.getJobGuid());
            // 对比中
            atJob.setIsCompare(ApprovalTransportJobStateConst.COMPARE_PROCESSING);
            approvalTransportJobService.update(atJob);
            Map<String, Integer> isFileStateMap = this.isFileState(map, file.getFileState(), atJob.getGuid(), fileId);
            if (isFileStateMap != null) return isFileStateMap;

            //获取运营商信息
            Users users = usersService.findById(atJob.getUserGuid());

            int excelSize = 0;
            if ("approval".equals(type))
                excelSize = insertData(atJob.getGuid(), users.getId(), fileId, atJob.getJobGuid(), users.getType(), dataType, genNum, regionCode);

            else if ("coordinate".equals(type))
                excelSize = insertCoordinateData(atJob.getGuid(), fileId, atJob.getJobGuid());

            if (excelSize > 0) {
                map.put("result", 1);
                map.put("type", 1);
                map.put("excelSize", excelSize);
                //更新文件为已处理
                file.setFileState(TransportFileStateConst.CHECK_SUCCESS);
                atJob.setIsCompare(ApprovalTransportJobStateConst.COMPARE_SUCCESS);
            } else {
                atJob.setIsCompare(ApprovalTransportJobStateConst.COMPARE_FAIL);
                logTransportJobWebService.addLog(atJob.getGuid(), LogTransportJobTypeConst.ERROR,
                        "上传文件失败", "任务（" + atJob.getGuid() + "）上传文件失败，更新数据库状态失败", fileId);
                map.put("result", 0);
                map.put("type", 0);
                //更新文件为异常
                file.setFileState(TransportFileStateConst.CHECK_FAIL);
            }
            approvalTransportJobService.update(atJob);
            //更新文件状态
            transportFileWebService.update(file);
            return map;
        }
        return map;
    }


    /**
     * 处理excel文件入库
     *
     * @param appGuid appGuid
     * @param userId  userId
     * @param fileId  fileId
     * @param jobGuid jobGuid
     * @param orgType orgType
     * @return int
     */
    private int insertData(String appGuid, String userId, String fileId, String jobGuid, String orgType, String dataType, String genNum, String regionCode) {
//        //获取校验规则
//        JSONObject checkRuleRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:netTs"));
//        JSONObject netTsJson = checkRuleRedisJson.getJSONObject(orgType);

        String orgRegionJson = redisService.get("Caict:regionCounty:orgRegion");
        if (orgRegionJson == null) {
            LOG.error("未获取到系统所属省的所有行政区信息");
            return 0;
        }

        flag = true;
        try {
            // 获取文件流
            InputStream fileStream = hdfsWebService.getFileInputStream(fileId);
//            fastFDSWebService.getFileInputStream(fileId);
            // 创建临时文件，保存文件流数据
            File tempFile = File.createTempFile(myFilePath + "WUGUANJU_UPLOAD_APPROVAL_" + UUID.randomUUID(), ".xlsx");
            tempFile.deleteOnExit();
            FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }

            List<TransportApprovalRawBts> allData = new ArrayList<>();
            final List<List<String>> table = new ArrayList<>();
            new ParseXlsxExcel(tempFile).setHandler(new ParseXlsxExcel.SimpleSheetContentsHandler() {

                List<String> fields;

                @Override
                public void endRow(int rowNum) {
                    if (rowNum == 0) {
                        // 第一行字段名
                        fields = row;
                    } else {
                        // 数据
                        table.add(row);
                    }
                }
            }).parse();

            for (List<String> dealList : table) {
                //2021-04-01简化网审数据后修改版本
                TransportApprovalRawBts transportApprovalRawBts = new TransportApprovalRawBts();
                transportApprovalRawBts.setGuid(VerificationCode.myUUID());
                transportApprovalRawBts.setJobGuid(dealList.get(1).trim());
                if (!dealList.get(1).trim().equals(jobGuid)) {
                    logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "文件内容错误",
                            "审核文件jobGuid与代办任务jobGuid不符", null);
                    flag = false;
                    break;
                }
                transportApprovalRawBts.setAppGuid(appGuid);
                transportApprovalRawBts.setBtsId(dealList.get(2).trim());
                transportApprovalRawBts.setFileGuid(fileId);
                transportApprovalRawBts.setUserGuid(userId);
                transportApprovalRawBts.setUploadDate(new Date());
                allData.add(transportApprovalRawBts);
            }

            if (!flag) {
                return 0;
            }
            if (allData.size() > 0) {
                //检验上传数据是否大于代办数据
                int scheduleCount = transportScheduleWebService.selectCountByJobGuid(jobGuid);
                if (allData.size() > scheduleCount) {
                    logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "文件数量错误", "审核文件数据数量大于代办数量", null);
                    return 0;
                }
                transportApprovalRawBtsService.insertBatch(allData);
                //2021-04-01 生成excel详细数据表
                approvalRawBtsService.insertBySchedule(jobGuid, userId, dataType, genNum, appGuid, regionCode);
                return allData.size();
            }
            return 0;
        } catch (Exception e) {
            LOG.error(e.getMessage());
            logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "文件解析异常", "文件：" + fileId + "文件解析异常", fileId);
            return 0;
        }
    }

    /**
     * 干扰协调excel文件入库
     *
     * @param appGuid appGuid
     * @param fileId  fileId
     * @param jobGuid jobGuid
     * @return int
     */
    private int insertCoordinateData(String appGuid, String fileId, String jobGuid) {
        flag = true;

        try {
            // 获取文件流
            InputStream fileStream = hdfsWebService.getFileInputStream(fileId);
//            InputStream fileStream = fastFDSWebService.getFileInputStream(fileId);
            File tempFile = File.createTempFile(myFilePath + "WUGUANJU_UPLOAD_COORDINATE_" + UUID.randomUUID(), ".xlsx");
            tempFile.deleteOnExit();
            FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }

            //日志数据集合
            List<CoordinateScheduleTempLog> coordinateScheduleTempLogList = new ArrayList<>();
            //同步数据id
            List<String> coordinateScheduleTempGuidList = new ArrayList<>();
            //exl解析集合
            //List<List<List<String>>> lists = excelImportService.excelImport(path);
            final List<List<String>> table = new ArrayList<>();
            new ParseXlsxExcel(tempFile).setHandler(new ParseXlsxExcel.SimpleSheetContentsHandler() {

                List<String> fields;

                @Override
                public void endRow(int rowNum) {
                    if (rowNum == 0) {
                        // 第一行字段名
                        fields = row;
                    } else {
                        // 数据
                        table.add(row);
                    }
                }
            }).parse();

            for (List<String> dealList : table) {

                // 数据
                CoordinateScheduleTempLog coordinateScheduleTempLog = new CoordinateScheduleTempLog();

                coordinateScheduleTempLog.setGuid(dealList.get(0).trim());
                //添加同步数据guid集合
                coordinateScheduleTempGuidList.add(dealList.get(0).trim());

                //校验是否为本次数据
                if (!appGuid.equals(dealList.get(1).trim()) || !jobGuid.equals(dealList.get(2).trim())) {
                    flag = false;
                    break;
                }
                coordinateScheduleTempLog.setAppGuid(dealList.get(1).trim());
                coordinateScheduleTempLog.setJobGuid(dealList.get(2).trim());
                coordinateScheduleTempLog.setIsValid((dealList.get(3) == null) ? DBBoolConst.TURE : Long.valueOf(dealList.get(3).trim()));
                coordinateScheduleTempLog.setCellName(dealList.get(4).trim());
                coordinateScheduleTempLog.setCellId(dealList.get(5).trim());
                coordinateScheduleTempLog.setBtsName(dealList.get(6).trim());
                coordinateScheduleTempLog.setBtsId(dealList.get(7).trim());
                coordinateScheduleTempLog.setTechType(dealList.get(8).trim());
                coordinateScheduleTempLog.setLocation(dealList.get(9).trim());
                coordinateScheduleTempLog.setLongitude(dealList.get(10).trim());
                coordinateScheduleTempLog.setLatitude(dealList.get(11).trim());
                coordinateScheduleTempLog.setSendStartFreq(dealList.get(12).trim());
                coordinateScheduleTempLog.setSendEndFreq(dealList.get(13).trim());
                coordinateScheduleTempLog.setAccStartFreq(dealList.get(14).trim());
                coordinateScheduleTempLog.setAccEndFreq(dealList.get(15).trim());
                coordinateScheduleTempLog.setMaxEmissivePower(dealList.get(16).trim());
                coordinateScheduleTempLog.setHeight(dealList.get(17).trim());
                coordinateScheduleTempLog.setDataType(dealList.get(18).trim());
                coordinateScheduleTempLog.setCounty(dealList.get(19).trim());
                coordinateScheduleTempLog.setIsHandle(dealList.get(20).trim());
                coordinateScheduleTempLog.setUserId(dealList.get(21).trim());
                coordinateScheduleTempLog.setVendorName(dealList.get(22).trim());
                coordinateScheduleTempLog.setDeviceModel(dealList.get(23).trim());
                coordinateScheduleTempLog.setModelCode(dealList.get(24).trim());
                coordinateScheduleTempLog.setAntennaGain(dealList.get(25).trim());
                coordinateScheduleTempLog.setGenNum(dealList.get(26).trim());
                coordinateScheduleTempLog.setAntennaModel(dealList.get(27).trim());
                coordinateScheduleTempLog.setAntennaFactory(dealList.get(28).trim());
                coordinateScheduleTempLog.setPolarizationMode(dealList.get(29).trim());
                coordinateScheduleTempLog.setAntennaAzimuth(dealList.get(30).trim());
                coordinateScheduleTempLog.setFeederLoss(dealList.get(31).trim());
                coordinateScheduleTempLog.setAltitude(dealList.get(32).trim());
                coordinateScheduleTempLog.setStatus(dealList.get(33).trim());
                //添加集合
                coordinateScheduleTempLogList.add(coordinateScheduleTempLog);
            }

            if (!flag) {
                logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "干扰协调文件校验错误", "并非本次干扰协调数据", null);
                return 0;
            }

            if (coordinateScheduleTempLogList.size() > 0) {
                //检验上传数据是否大于代办数据
                int scheduleCount = coordinateScheduleTempWebService.selectCountByAppGuid(appGuid);
                if (coordinateScheduleTempLogList.size() > scheduleCount) {
                    //修改任务状态为审核中
                    transportJobWebService.updateIsCompareByGuid(TransportJobStateConst.CHECK_PROCESSING, jobGuid);
                    logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "干扰协调文件数量错误", "干扰协调文件数据数量大于代办数量", null);
                    return 0;
                }
                coordinateScheduleTempLogWebService.insertBatch(coordinateScheduleTempLogList);
                //批量删除同步数据
                coordinateScheduleTempWebService.deleteBatch(coordinateScheduleTempGuidList);

                return coordinateScheduleTempLogList.size();
            }
            return 0;
        } catch (Exception e) {
            LOG.error(e.getMessage());
            logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "文件解析异常", "文件：" + fileId + "文件解析异常", fileId);
            return 0;
        }
    }

    /**
     * 插入log
     *
     * @param map       map
     * @param fileState fileState
     * @param guid      guid
     * @param fileId    fileId
     * @return map
     */
    private Map<String, Integer> isFileState(Map<String, Integer> map, long fileState, String guid, String fileId) {
        if (TransportFileStateConst.CHECK_SUCCESS == fileState) {
            logTransportJobWebService.addLog(guid, LogTransportJobTypeConst.ERROR, "csv文件异常", "csv文件已经处理", fileId);
            map.put("result", 0);
            map.put("type", 0);
            return map;
        }
        return null;
    }

    /**
     * 任务提交处理
     *
     * @param appGuid appGuid
     * @param userId  userId
     */
    public void commit(String appGuid, String userId) {
        //对比结果集合
        List<TransportCompareResult> transportCompareResultList = Collections.synchronizedList(new ArrayList<>());
        //审核待办集合
        List<ApprovalSchedule> approvalScheduleList = Collections.synchronizedList(new ArrayList<>());
        //待办结果集合
        List<TransportSchedule> transportScheduleList = Collections.synchronizedList(new ArrayList<>());

        ApprovalTransportJob atJob = approvalTransportJobService.findById(appGuid);

        if (atJob != null) {
            //获取运营商信息
            Users users = usersService.findById(atJob.getUserGuid());

            List<ApprovalRawBts> approvalRawBtsList = approvalRawBtsService.findAllByAppGuid(appGuid);

            approvalRawBtsList.parallelStream().forEach(approvalRawBts -> {
                //获取待办
                TransportSchedule transportSchedule = transportScheduleWebService.findOneByCellBtsUser(approvalRawBts.getCellId(), approvalRawBts.getBtsId(),
                        approvalRawBts.getTechType(), approvalRawBts.getDataType());
                if (transportSchedule != null) {
                    TransportRawBts transportRawBts = new TransportRawBts();
                    BeanUtils.copyProperties(approvalRawBts, transportRawBts);
                    //判断数据是否为待办数据
                    JSONObject result = CompareData.comparison(transportRawBts, transportSchedule);
                    //对比数据计入TRANSPORT_COMPARE_RESULT表中
                    TransportCompareResult transportCompareResult = new TransportCompareResult();
                    transportCompareResult.setGuid(VerificationCode.myUUID());
                    transportCompareResult.setImportDateid(transportSchedule.getGuid());
                    transportCompareResult.setExistDataid(String.valueOf(transportRawBts.getGuid()));
                    transportCompareResult.setAppGuid(atJob.getGuid());
                    transportCompareResult.setJobGuid(atJob.getJobGuid());
                    String mes = result.getString("message");
                    transportCompareResult.setCompareResult(mes != null ? mes : "");
                    transportCompareResult.setGmtCreate(new Date());
                    transportCompareResult.setUserId(transportSchedule.getUserGuid());
                    //对比数据计入审核代办表
                    ApprovalSchedule approvalSchedule = new ApprovalSchedule();
                    BeanUtils.copyProperties(approvalRawBts, approvalSchedule);
                    //信号计算
                    approvalSchedule.setGenNum(btsDataCheckRuleWebService.genParameter(users.getType(), approvalSchedule.getTechType()));
                    approvalSchedule.setAppGuid(atJob.getGuid());
                    approvalSchedule.setJobGuid(atJob.getJobGuid());
                    approvalSchedule.setOrgType(users.getType());
                    approvalSchedule.setIsValid(1L);
                    if (!result.getBoolean("success")) {
                        transportCompareResult.setResultType(transportSchedule.getDataType());
                    } else {
                        //比对无差异的将resultType设为4
                        transportCompareResult.setResultType("4");
                    }
                    transportCompareResultList.add(transportCompareResult);
                    approvalScheduleList.add(approvalSchedule);
                    transportScheduleList.add(transportSchedule);
                }
            });

            if (approvalRawBtsList.size() != 0) {
                //添加对比数据
                if (transportCompareResultList.size() != 0) {
                    transportCompareResultService.insertBatch(transportCompareResultList);
                }
                //添加审核待办数据
                if (approvalScheduleList.size() != 0) {
                    approvalScheduleService.insertBatch(approvalScheduleList);
                }

                //删除待办数据
                transportScheduleWebService.deleteBatch(transportScheduleList.stream().map(TransportSchedule::getGuid).collect(Collectors.toList()));

                //更新审核待办中差异数量
                int identCount = transportCompareResultService.selectCountByResultType(appGuid, "4");
                int diffCount = transportCompareResultService.selectCountByNotResultType(appGuid, "4");

                approvalTransportJobService.updateCountByGuid(appGuid, identCount, diffCount);

                //修改审核任务状态
                approvalTransportJobService.updateIsCompareAndStatusByGuid(ApprovalTransportJobStateConst.COORDINATE_COMMIT_SUCCESS, appGuid);

            } else {
                logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "文件提交", "文件提交失败，未检测到提交数据", userId);//修改审核任务状态
                approvalTransportJobService.updateIsCompareByGuid(ApprovalTransportJobStateConst.COMMIT_FAIL, appGuid);
            }

            //判断待办任务是否全部处理
            if (transportScheduleWebService.selectCountByJobGuid(atJob.getJobGuid()) == 0) {
                //任务状态为确认中
                TransportJob transportJob = new TransportJob();
                transportJob.setGuid(atJob.getJobGuid());
                transportJob.setIsCompare(TransportJobStateConst.COMPLETE);
                transportJobWebService.update(transportJob);

                //删除deal表
                transportRawBtsDealService.deleteByJobId(atJob.getJobGuid());
            }
        }
    }

    /**
     * 无委审核操作
     *
     * @param appGuid appGuid
     * @param userId  userId
     */
    public void approve(String appGuid, String userId) {
        ApprovalTransportJob atJob = approvalTransportJobService.findById(appGuid);

        if (atJob != null) {
            Long isApproved = atJob.getIsApproved();
            if (isApproved == ApprovalTransportJobStateConst.APPROVED_PASS) {
                //同意
                //新增
                approveRedis(atJob, "1");

                //变更
                approveRedis(atJob, "2");

                //注销
                approveRedis(atJob, "3");

                //插入审核记录表
                var approvalScheduleLogList = approvalScheduleService.findAllApprovalScheduleLogByAppGuid(atJob.getGuid(), ApprovalTransportJobStateConst.APPROVED_PASS);
                if (approvalScheduleLogList != null && approvalScheduleLogList.size() != 0) {
                    approvalScheduleLogService.insertBatch(approvalScheduleLogList);
                }
                //删除审核待办
                //approvalScheduleService.deleteAllByAppGuid(atJob.getGuid());
            } else if (isApproved == ApprovalTransportJobStateConst.APPROVED_NOT_PASS) {
                //不同意
                //插入审核记录表
                var approvalScheduleLogList = approvalScheduleService.findAllApprovalScheduleLogByAppGuid(atJob.getGuid(), ApprovalTransportJobStateConst.APPROVED_NOT_PASS);
                if (approvalScheduleLogList != null) {
                    approvalScheduleLogService.insertBatch(approvalScheduleLogList);

                    //删除审核待办数据
                    approvalScheduleService.deleteBatchByApprovalScheduleLog(approvalScheduleLogList);
                }

                //判断是否还有待办任务
                if (transportScheduleWebService.selectCountByJobGuid(atJob.getJobGuid()) == 0) {
                    //修改审核任务表状态为完结
                    approvalTransportJobService.updateIsCompareByGuid(ApprovalTransportJobStateConst.BRANCHIN_GIVEUP, atJob.getGuid());

                    transportJobBranchWebService.updateByJobId(atJob.getJobGuid(), atJob.getDataType(), atJob.getGenNum(), ApprovalTransportJobStateConst.BRANCHIN_GIVEUP);

                    //修改任务状态为完结
                    transportJobWebService.updateIsCompareByGuid(TransportJobStateConst.CHECK_FAIL, atJob.getJobGuid());
                }
            } else {
                //异常
                logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "数据审核过程发生错误", "未检测到审核处理状态", null);
            }
        } else {
            logTransportJobWebService.addLog(appGuid, LogTransportJobTypeConst.ERROR, "数据审核过程发生错误", "未检测到审核数据", null);
        }
    }

    /**
     * 审核数据处理
     *
     * @param atJob    atJob
     * @param dataType dataType
     */
    private void approveRedis(ApprovalTransportJob atJob, String dataType) {
        var redisList = approvalScheduleService.findStationScheduleDataDTOPageByJobIdDataType(atJob.getGuid(), dataType);
        if (redisList != null && redisList.size() != 0) {
            // 处理数据 分页

            int page = redisList.size() % 10000 == 0 ? redisList.size() / 10000 : redisList.size() / 10000 + 1;
            LOG.info("总条数：" + redisList.size() + "    总页数：" + page + "    dataType：" + dataType);
            for (int i = 1; i <= page; i++) {
                List<StationScheduleDataDTO> transportScheduleList;
                if (i == page) {
                    transportScheduleList = redisList.subList((i - 1) * 10000, redisList.size());
                } else {
                    transportScheduleList = redisList.subList((i - 1) * 10000, i * 10000);
                }
                LOG.info("当前页：" + i + "    schedule条数：" + transportScheduleList.size());
                //校验通过后将数据写入redis,key的格式：guidId+jobId+类型（1：新增；2：变更；3：注销）
                var key = atJob.getGuid() + "." + atJob.getJobGuid() + ".approve." + dataType;
                redisService.lpush(key, JSONObject.toJSON(transportScheduleList));
                //发送rabbit进行审核处理
                switch (dataType) {
                    case "1":
                        LOG.info(new Timestamp(System.currentTimeMillis()).toString() + ":审核任务新增文件");
                        // 要执行的任务
                        Runnable runnable1 = () -> this.approve(key, atJob.getGuid(), "1");
                        // 创建线程对象并启动
                        new Thread(runnable1).start();
                        break;
                    case "2":
                        LOG.info(new Timestamp(System.currentTimeMillis()).toString() + ":审核任务变更文件");
                        // 要执行的任务
                        Runnable runnable2 = () -> this.approve(key, atJob.getGuid(), "2");
                        // 创建线程对象并启动
                        new Thread(runnable2).start();
                        break;
                    case "3":
                        LOG.info(new Timestamp(System.currentTimeMillis()).toString() + ":审核任务注销文件");
                        // 要执行的任务
                        Runnable runnable3 = () -> this.approve(key, atJob.getGuid(), "3");
                        // 创建线程对象并启动
                        new Thread(runnable3).start();
                        break;
                }
            }
        }
    }

    /**
     * 新增处理
     *
     * @param redisKey redisKey
     * @param appGuid  appGuid
     * @param dataType dataType
     */
    private void approve(String redisKey, String appGuid, String dataType) {
        //获取redis数据
        List<StationScheduleDataDTO> stationScheduleDataDTOList = JSONObject.parseArray(redisService.rpop(redisKey), StationScheduleDataDTO.class);
        if (stationScheduleDataDTOList != null) {
            ApprovalTransportJob atJob = approvalTransportJobService.findById(appGuid);
            TransportJobBranchDTO transportJobBranchDTO = transportJobBranchWebService.findOneByJobUserTypeGen(atJob.getJobGuid(), atJob.getUserGuid(),
                    atJob.getDataType(), atJob.getGenNum(), atJob.getRegionCode(), atJob.getTechType());
            String appCode = transportJobBranchDTO.getAppCode();
            //批量添加用户信息
            List<RsbtTraffic> rsbtTrafficInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站
            List<RsbtStation> rsbtStationListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站状态
            List<RsbtStationAppendix> rsbtStationAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站冗余表
            List<RsbtStationT> rsbtStationTListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站历史备份表
            List<RsbtStationBak> rsbtStationBakListInsert = Collections.synchronizedList(new ArrayList<>());

            //批量添加天线
            List<RsbtAntfeed> rsbtAntfeedListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加天线状态
            List<RsbtAntfeedAppendix> rsbtAntfeedAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            List<RsbtAntfeedT> rsbtAntfeedTListInsert = Collections.synchronizedList(new ArrayList<>());

            //批量添加设备
            List<RsbtEqu> rsbtEquListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加设备状态
            List<RsbtEquAppendix> rsbtEquAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            List<RsbtEquT> rsbtEquTLis = Collections.synchronizedList(new ArrayList<>());

            //批量添加参数
            List<RsbtFreq> rsbtFreqListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加参数状态
            List<RsbtFreqAppendix> rsbtFreqAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            List<RsbtFreqT> rsbtFreqTList = Collections.synchronizedList(new ArrayList<>());

            List<RsbtEaf> rsbtEafListInsert = Collections.synchronizedList(new ArrayList<>());
            //Net 表
            List<RsbtNet> rsbtNetListInsert = Collections.synchronizedList(new ArrayList<>());

            //批量添加总表
            List<AsyncRawBts> asyncRawBtsListInsert = Collections.synchronizedList(new ArrayList<>());

            //查询营运商对应的公司
            OrgDTO rsbtOrg = orgUsersService.findRsbtOrgByUserId(atJob.getUserGuid());
            if (rsbtOrg != null) {
                stationScheduleDataDTOList.parallelStream().forEach(stationScheduleDataDTO -> approvalDataService.HandleAdded(atJob.getGuid(), rsbtOrg, stationScheduleDataDTO,
                        rsbtNetListInsert, rsbtTrafficInsert, rsbtStationListInsert, rsbtStationAppendixListInsert, rsbtStationTListInsert, rsbtStationBakListInsert,
                        rsbtAntfeedListInsert, rsbtAntfeedAppendixListInsert, rsbtAntfeedTListInsert, rsbtEquListInsert, rsbtEquAppendixListInsert, rsbtEquTLis,
                        rsbtFreqListInsert, rsbtFreqAppendixListInsert, rsbtFreqTList, rsbtEafListInsert, asyncRawBtsListInsert, appCode));
            } else {
                LogTransportJob logTransportJob = logTransportJobWebService.setLogTransportJob(appGuid, LogTransportJobTypeConst.ERROR, "审核失败",
                        appGuid + "未检测运营商所属的组织机构", "");
                logTransportJobWebService.save(logTransportJob);
            }

            //将持久化数据存入redis，又持久化程序处理
            ProcessingDataDTO processingDataDTO = new ProcessingDataDTO(atJob.getUserGuid(), atJob.getJobGuid(), appGuid, dataType, rsbtNetListInsert,
                    rsbtStationListInsert, rsbtStationAppendixListInsert, rsbtStationTListInsert, rsbtStationBakListInsert, rsbtAntfeedListInsert,
                    rsbtAntfeedAppendixListInsert, rsbtAntfeedTListInsert, rsbtEquListInsert, rsbtEquAppendixListInsert, rsbtEquTLis, rsbtTrafficInsert,
                    rsbtFreqListInsert, rsbtFreqAppendixListInsert, rsbtFreqTList, rsbtEafListInsert, asyncRawBtsListInsert);

            //数据处理
            approveData(appGuid, atJob.getGuid(), processingDataDTO);
        } else {
            LogTransportJob logTransportJob = logTransportJobWebService.setLogTransportJob(appGuid, LogTransportJobTypeConst.ERROR, "审核失败",
                    appGuid + "未检测到审核数据", "");
            logTransportJobWebService.save(logTransportJob);
        }
    }

    /**
     * 数据处理
     *
     * @param appGuid           appGuid
     * @param jobGuid           jobGuid
     * @param processingDataDTO processingDataDTO
     */
    private void approveData(String appGuid, String jobGuid, ProcessingDataDTO processingDataDTO) {
        if (processingDataDTO == null) {
            //审核失败
            LogTransportJob logTransportJob = logTransportJobWebService.setLogTransportJob(appGuid, LogTransportJobTypeConst.ERROR, "审核失败",
                    appGuid + "审核记录处理失败", "");
            logTransportJobWebService.save(logTransportJob);
            //判断是否还有待办任务
            if (transportScheduleWebService.selectCountByJobIdDataType(jobGuid, null, null) == 0) {
                //修改任务状态为完结
                transportJobWebService.updateIsCompareByGuid(TransportJobStateConst.COMPLETE, jobGuid);
                //删除待办
                transportScheduleWebService.deleteByJobId(jobGuid);
            }
            //审核任务改为异常
            approvalTransportJobService.updateIsCompareByGuid(ApprovalTransportJobStateConst.CHECKIN_FAIL, appGuid);
            ApprovalTransportJob atJob = approvalTransportJobService.findById(appGuid);
            if (atJob != null) {
                transportJobBranchWebService.updateByConfirm(atJob.getJobGuid(), atJob.getDataType(), atJob.getGenNum(), atJob.getTechType(),
                        ApprovalTransportJobStateConst.CHECKIN_FAIL);
            }
        } else {
            //审核任务改为完结
            approvalTransportJobService.updateIsCompareByGuid(ApprovalTransportJobStateConst.BRANCHIN_CHECK_SUCCESS, appGuid);
            ApprovalTransportJob atJob = approvalTransportJobService.findById(appGuid);
            if (atJob != null) {
                transportJobBranchWebService.updateByConfirm(atJob.getJobGuid(), atJob.getDataType(), atJob.getGenNum(), atJob.getTechType(),
                        ApprovalTransportJobStateConst.COMPLETE);
            }
            //入库处理
            dataApproveService.processingData(processingDataDTO);

            LOG.info(new Timestamp(System.currentTimeMillis()) + "生成执照操作");
            ThreadPoolUtil.getThread().execute(() -> bsmStationLicenseWebService.bsmStationLicense());

            //获取同步锁用于入库处理结束判断
            RedisLock redisLock = redisService.getRedisLock(processingDataDTO.getJobId() + ".approve." + "number");
            //获取同步锁用于入库操作结束判断
            RedisLock redisLockData = redisService.getRedisLock(processingDataDTO.getJobId() + ".approve.data." + "number");
            if (redisLock != null && redisLockData != null) {
                int pageLock = (int) redisLock.getContent();
                int pageLockData = (int) redisLockData.getContent();

                pageLockData -= 1;
                redisLockData.setContent(pageLockData);
                redisLockData.setLock("1");
                //处理数量减1，并解锁
                redisLock.setLock("1");
                redisService.set(processingDataDTO.getJobId() + ".approve." + "number", JSONObject.toJSONString(redisLock));
                redisService.set(processingDataDTO.getJobId() + ".approve.data." + "number", JSONObject.toJSONString(redisLockData));

                //处理完成
                if (pageLock == 0 && pageLockData == 0) {
                    //入库结束处理
                    boolean b = dataApproveService.checkData(processingDataDTO.getUserId(), processingDataDTO.getJobId());
                    if (b) {
                        //删除入库处理锁
                        redisService.delete(processingDataDTO.getJobId() + ".approve." + "number");
                        //删除当前批处理数量
                        redisService.delete(processingDataDTO.getJobId() + ".approve.data." + "number");
                    }
                }
            }
        }
    }


    public int findNotComplete(String jobId, String isCompare) {
        return approvalTransportJobService.findNotComplete(jobId, isCompare);
    }


    /**
     * 根据jobId查询是否有不通过的任务
     */
    public List<ApprovalTransportJob> findNotPassList(String jobId, String opStatus) {
        return approvalTransportJobService.findNotPassList(jobId, opStatus);
    }

    /**
     * 查询当前任务中有跟上次审核不通过的任务相同的增量、代数、地区的任务
     */
    public ApprovalTransportJob findByDetail(String jobId, String dataType, String genNum, String regionCode, String techType) {
        return approvalTransportJobService.findByDetail(jobId, dataType, genNum, regionCode, techType);
    }

    public void save(ApprovalTransportJob approvalTransportJob) {
        approvalTransportJobService.save(approvalTransportJob);
    }

    /**
     * 根据appGuid插入log数据 解决审核变更对比扇区信息不全
     *
     * @param newAppGuid 新生成的appGuid
     * @param oldAppGuid 以前存在的appGuid
     */
    public void insertApprovalScheduleLog(String newAppGuid, String oldAppGuid) {
        approvalTransportJobService.insertApprovalScheduleLog(newAppGuid, oldAppGuid);
    }

    /**
     * 根据Id查询
     */
    public ApprovalTransportJob findById(String id) {
        return approvalTransportJobService.findById(id);
    }


    /**
     * 根据job_guid和is_compare 查询数据
     *
     * @param jobGuid jobGuid
     * @return Integer
     */
    public Integer findByJobGuidAndIsCompareCount(String jobGuid, String isCompare) {
        return approvalTransportJobService.findByJobGuidAndIsCompare(jobGuid, isCompare).size();
    }

    /**
     * 根据job_guid和is_compare 查询数据
     *
     * @param jobGuid jobGuid
     * @return Integer
     */
    public List<ApprovalTransportJob> findByJobGuidAndIsCompare(String jobGuid, String isCompare) {
        return approvalTransportJobService.findByJobGuidAndIsCompare(jobGuid, isCompare);
    }

    /**
     * 更新数据
     *
     * @param atJob atJob
     */
    public void update(ApprovalTransportJob atJob) {
        approvalTransportJobService.update(atJob);
    }


}
