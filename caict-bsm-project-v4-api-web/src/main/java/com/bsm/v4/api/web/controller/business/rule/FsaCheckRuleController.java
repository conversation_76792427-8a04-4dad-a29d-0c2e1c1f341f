package com.bsm.v4.api.web.controller.business.rule;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.rule.FsaCheckRuleWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.vo.business.rule.FsaCheckRuleVO;
import com.caictframework.data.controller.BasicController;
import com.caictframework.utils.util.JSONResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @Title: FsaCheckRuleController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.business.rule
 * @Date 2023/9/11 14:21
 * @description:
 */
@RestController
@RequestMapping(value = "/apiWeb/rule/fsaCheckRule")
@Api(value = "web端文件校验规则接口", tags = "web端文件校验规则接口")

public class FsaCheckRuleController extends BasicController {

    @Autowired
    private AuthWebService authWebService;

    @ApiOperation(value = "规则保存", notes = "规则保存接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fsaCheckRuleVO", value = "文件校验对象", required = true, paramType = "body", dataType = "fsaCheckRuleVO")
    })
    @PostMapping(value = "/save")
    public JSONObject save(@RequestBody FsaCheckRuleVO fsaCheckRuleVO) {
        return this.basicReturnJson(fsaCheckRuleVO, FsaCheckRuleWebService.class, (dto, service) -> service.saveFsaCheckRule(dto));

    }

    @ApiOperation(value = "查询详情", notes = "查询详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", paramType = "path", required = true, dataType = "String")
    })
    @GetMapping(value = "/findOneDto/{id}")
    public JSONObject findOne(@PathVariable("id") String id) {
        return this.basicReturnJson(id, FsaCheckRuleWebService.class, (vo, service) -> service.findOneDto(vo));
    }

    @ApiOperation(value = "删除一条规则", notes = "删除一条规则接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "规则id", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/delete/{id}")
    public JSONObject delete(@PathVariable("id") String id) {
        return this.basicReturnJson(id, FsaCheckRuleWebService.class, (vo, service) -> service.delete(id));
    }

    @ApiOperation(value = "批量删除", notes = "批量删除接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fsaCheckRuleVO", value = "文件校验对象", required = true, paramType = "body", dataType = "fsaCheckRuleVO")
    })
    @PostMapping(value = "/deletes")
    public JSONObject deletes(@RequestBody FsaCheckRuleVO fsaCheckRuleVO) {
        return this.basicReturnJson(fsaCheckRuleVO, FsaCheckRuleWebService.class, (dto, service) -> service.deletes(dto.getIds()));
    }

    @ApiOperation(value = "分页查看文件校验规则", notes = "分页查看文件校验规则接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fsaCheckRuleVO", value = "文件校验对象", required = true, paramType = "body", dataType = "fsaCheckRuleVO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPageByWhere(@RequestBody FsaCheckRuleVO fsaCheckRuleVO, HttpServletRequest request) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(request.getHeader("token"));
        if (usersDTO == null) return JSONResult.getFailureJson("登录超时，请重新登录");
        if ("wuwei".equals(usersDTO.getType())) {
            return this.basicReturnJson(fsaCheckRuleVO, FsaCheckRuleWebService.class, (dto, service) -> service.findAllPageByWhere(dto));
        } else {
            fsaCheckRuleVO.setOrgType(usersDTO.getType());
            return this.basicReturnJson(fsaCheckRuleVO, FsaCheckRuleWebService.class, (dto, service) -> service.findAllPageByWhere(dto));
        }
    }

    @ApiOperation(value = "修改全部状态", notes = "修改全部状态接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "state", value = "状态", paramType = "path", dataType = "String")
    })
    @GetMapping(value = "/updateAllState/{state}")
    public JSONObject updateAllState(@PathVariable("state") String state) {
        return this.basicReturnJson(state, FsaCheckRuleWebService.class, (vo, service) -> service.updateAllState(vo));
    }

    @ApiOperation(value = "获取所有运营商的制式", notes = "获取所有运营商的制式")
    @GetMapping(value = "/getCheckRuleNetTs")
    public JSONObject getCheckRuleNetTs() {
        return this.basicReturnJson(FsaCheckRuleWebService.class, (service) -> service.getCheckRuleNetTs());
    }

    //导入
    // TODO: 2023/9/13 还没测，暂时先不用管
    @ApiOperation(value = "excel文件批量导入数据", notes = "excel文件批量导入数据")
    @PostMapping(value = "/import")
    public JSONObject importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) return JSONResult.getFailureJson("文件上传失败");
        return this.basicReturnJson(file, FsaCheckRuleWebService.class, (vo, service) -> service.importExcel(vo));
    }

    //导出
    // TODO: 2023/9/13 还没测，暂时先不用管
    @ApiOperation(value = "批量导出数据到excel", notes = "批量导出数据到excel")
    @GetMapping(value = "/export")
    public JSONObject exportExcel() throws IOException {
        return this.basicReturnJson(FsaCheckRuleWebService.class, (service) -> {
            try {
                return service.exportExcel();
            } catch (IOException e) {
                e.printStackTrace();
            }
            return null;
        });
    }


}
