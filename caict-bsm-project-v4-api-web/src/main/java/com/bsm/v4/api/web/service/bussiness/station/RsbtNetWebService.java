package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtNetService;
import com.bsm.v4.system.model.entity.business.station.RsbtNet;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtNetWebService extends BasicWebService {

    @Autowired
    private RsbtNetService rsbtNetService;

    /**
     * 批量添加
     */
    public void insertBatch(List<RsbtNet> rsbtNetListInsert) {
        if (rsbtNetListInsert.size() > 30) {
            int pageNum = rsbtNetListInsert.size() % 30 == 0 ? rsbtNetListInsert.size() / 30 : rsbtNetListInsert.size() / 30 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtNet> stations;
                if (i != pageNum) {
                    stations = rsbtNetListInsert.subList((i - 1) * 30, i * 30);
                } else {
                    stations = rsbtNetListInsert.subList((i - 1) * 30, rsbtNetListInsert.size());
                }
                rsbtNetService.insertBatch(stations);
            }
        } else {
            rsbtNetService.insertBatch(rsbtNetListInsert);
        }
    }


    public RsbtNet getRsbtNetByStationGuid(String stationGuid) {
        return rsbtNetService.getRsbtNetByStationGuid(stationGuid);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtNet> rsbtNetListInsert) {
        return rsbtNetService.updateBatch(rsbtNetListInsert);
    }
}
