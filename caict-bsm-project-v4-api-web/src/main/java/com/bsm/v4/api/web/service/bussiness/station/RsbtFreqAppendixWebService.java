package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtFreqAppendixService;
import com.bsm.v4.domain.security.service.business.station.RsbtFreqService;
import com.bsm.v4.domain.security.service.business.station.RsbtFreqTService;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import com.bsm.v4.system.model.entity.business.station.RsbtFreqAppendix;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtFreqAppendixWebService extends BasicWebService {

    @Autowired
    private RsbtFreqAppendixService rsbtFreqAppendixService;

    @Autowired
    private RsbtFreqService rsbtFreqService;

    @Autowired
    private RsbtFreqTService rsbtFreqTService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtFreqAppendix> rsbtFreqAppendixListInsert) {
        return rsbtFreqAppendixService.insertBatch(rsbtFreqAppendixListInsert);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtFreqAppendix> rsbtFreqAppendixListInsert) {
        return rsbtFreqAppendixService.updateBatch(rsbtFreqAppendixListInsert);
    }

    /**
     * 批量删除（修改状态）
     */
    public void deleteBatchByFreqGuid(List<RsbtFreq> rsbtFreqListDelete) {
        List<Object> ids = rsbtFreqListDelete.stream().map(RsbtFreq::getGuid).collect(Collectors.toList());
        rsbtFreqAppendixService.deleteBatch(ids);
        rsbtFreqService.deleteBatch(ids);
        rsbtFreqTService.deleteBatch(ids);
    }
}
