package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.domain.security.service.business.transfer.TransportScheduleService;
import com.bsm.v4.system.model.contrust.transfer.TransportJobStateConst;
import com.bsm.v4.system.model.contrust.transfer.TransportScheduleConst;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportScheduleDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportSchedule;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class TransportScheduleWebService extends BasicWebService {

    @Autowired
    private TransportScheduleService transportScheduleService;

    /**
     * 根据jobId删除
     */
    public int deleteByJobId(String jobId) {
        return transportScheduleService.deleteByJobId(jobId);
    }


    /**
     * 根据jobId删除
     */
    public int deleteByJobIdDataTypeGenNum(String jobId, String dataType, String genNum) {
        return transportScheduleService.deleteByJobIdDataTypeGenNum(jobId, dataType, genNum);
    }

    /**
     * 查询代办数量
     * 5：对比成功（无委待确认）
     * 6：无委确认中
     * 8：无委确认通过（待审核）
     * 9：审核中
     */
    public Map<String, Object> findAllTransportSchedule() {
        List<String> compares = new ArrayList<>(Arrays.asList(TransportJobStateConst.COMPARE_SUCCESS, TransportJobStateConst.CONFIRM_PROCESSING,
                TransportJobStateConst.CONFIRM_PASS, TransportJobStateConst.CHECK_PROCESSING));
        return JSONResult.getSuccessJson(transportScheduleService.findAllTransportSchedule(compares, TransportScheduleConst.ISHANDLE_SUCCUSS), "查询成功");
    }

    /**
     * 根据jobId、类型分页查询
     */
    public Map<String, Object> findAllPageByJobIdDataType(TransportScheduleDTO transportScheduleDTO) {
        return this.basicReturnResultJson(new PageHandle(transportScheduleDTO).buildPage(transportScheduleService.findAllPageByJobIdDataType
                (transportScheduleDTO.getJobGuid(), transportScheduleDTO.getDataType(), TransportScheduleConst.ISHANDLE_UPLOAD)));
    }

    /**
     * 根据jobGuid查询总数
     */
    public int selectCountAllByJob(String jobGuid, String isHandle) {
        return transportScheduleService.selectCountAllByJob(jobGuid, isHandle);
    }

    public void updateConfirm(String jobId, String dataType, String genNum) {
        transportScheduleService.updateConfirm(jobId, dataType, genNum);
    }

    /**
     * 根据jobGuid查询数量
     */
    public int selectCountByJobGuid(String jobGuid) {
        return transportScheduleService.selectCountByJobGuid(jobGuid);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<TransportSchedule> transportScheduleList) {
        return transportScheduleService.updateBatch(transportScheduleList);
    }

    /**
     * 根据扇区id，基站识别id和userId查询
     */
    public TransportSchedule findOneByCellBtsUser(String cellId, String btsId, String techType, String dataType) {
        return transportScheduleService.findOneByCellBtsUser(cellId, btsId, techType, dataType);
    }

    /**
     * 根据制式和类型查询
     */
    public List<TransportSchedule> findByTechTypeAndDataType(String techType, String dataType) {
        return transportScheduleService.findByTechTypeAndDataType(techType, dataType);
    }

    /**
     * 批量删除
     */
    public int deleteBatch(List<Object> idList) {
        return transportScheduleService.deleteBatch(idList);
    }

    /**
     * 批量添加
     */
    public int insertBatch(List<TransportSchedule> transportScheduleList) {
        return transportScheduleService.insertBatch(transportScheduleList);
    }

    public List<TransportJobBranchDTO> selectDistinctDataTypeAndGenNum(String jobId) {
        return transportScheduleService.selectDistinctDataTypeAndGenNum(jobId);
    }

    /**
     * 生成待办
     */
    public int insertByTransportRawBts(String jobId) {
        return transportScheduleService.insertByTransportRawBts(jobId);
    }

    /**
     * 根据app删除全部
     */
    public int deleteAllByAppGuid(String appGuid) {
        return transportScheduleService.deleteAllByAppGuid(appGuid);
    }


    public int selectCountByJobGuidAndDataType(String jobGuid, String dataType) {
        return transportScheduleService.selectCountByJobGuidAndDataType(jobGuid, dataType);
    }

    /**
     * 查询同一个btsId下dataType不一致的基站ID和dataType
     *
     * @return list
     */
    public List<TransportSchedule> selectDisAccordBtsIds() {
        return transportScheduleService.selectDisAccordBtsIds();
    }

    /**
     * 查询同一个btsId下dataType一致的基站ID
     *
     * @return list
     */
    public List<TransportSchedule> selectAccordBtsIdsAndDataType() {
        return transportScheduleService.selectAccordBtsIdsAndDataType();
    }

    /**
     * 查询同一个btsId下dataType一致的基站ID
     *
     * @return list
     */
    public List<TransportSchedule> selectAccordBtsIdCellIdAndDataType() {
        return transportScheduleService.selectAccordBtsIdCellIdAndDataType();
    }

    /**
     * 根据btsId更新数据
     *
     * @param jobId  jobId
     * @param btsIds list
     */
    public void updateByBtsIds(String dataType, String jobId, List<String> btsIds, String regionCode) {
        transportScheduleService.updateByBtsIds(dataType, jobId, btsIds, regionCode);
    }

    /**
     * 根据jobId、类型查询总数
     */
    public int selectCountByJobIdDataType(String jobId, String dataType, String isHandle) {
        return transportScheduleService.selectCountByJobIdDataType(jobId, dataType, isHandle);
    }

}
