package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.domain.security.service.business.transfer.ApprovalScheduleLogService;
import com.bsm.v4.system.model.dto.business.station.SectionDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public class ApprovalScheduleLogWebService extends BasicWebService {

    @Autowired
    private ApprovalScheduleLogService approvalScheduleLogService;

    /**
     * 根据appGuid、数据类型分页查询
     */
    public Map<String, Object> findAllPageByAppGuidDateType(ApprovalScheduleLogDTO approvalScheduleLogDTO) {
        return this.basicReturnSuccess(new PageHandle(approvalScheduleLogDTO).buildPage(approvalScheduleLogService.findAllPageByAppGuidDateType
                (approvalScheduleLogDTO.getJobGuid(), approvalScheduleLogDTO.getDataType(), approvalScheduleLogDTO.getGenNum())));
    }

    public List<ApprovalScheduleLog> findByAppGuid(String appGuid) {
        return approvalScheduleLogService.findByAppGuid(appGuid);
    }

    /**
     * 根据jobId删除数据
     */
    public int deleteByJobGuid(String jobGuid) {
        return approvalScheduleLogService.deleteByJobGuid(jobGuid);
    }

    public List<SectionDTO> findSectionList(String appGuid, String stCCode) {
        return approvalScheduleLogService.findSectionList(appGuid, stCCode);
    }

}
