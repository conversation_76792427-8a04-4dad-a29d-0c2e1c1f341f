package com.bsm.v4.api.web.service.bussiness.freqevaluation;

import com.bsm.v4.domain.security.service.business.freqevaluation.FreqEvaluationService;
import com.bsm.v4.domain.security.service.business.freqevaluation.OperUserStatisService;
import com.bsm.v4.system.model.dto.business.freqevaluation.FreqEvaluationDTO;
import com.bsm.v4.system.model.dto.business.freqevaluation.OperUserStatisDTO;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationProvVO;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationVO;
import com.bsm.v4.system.model.vo.business.freqevaluation.OperUserStatisVO;
import com.caictframework.data.service.BasicWebService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationWebService extends BasicWebService {

    @Autowired
    private FreqEvaluationService freqEvaluationService;

    @Autowired
    private OperUserStatisService operUserStatisService;

//    private List<String> NET_TS_23 = Arrays.asList("CDMA", "GSM", "CDMA2000", "TD-SCDMA", "WCDMA", "GSM-R");
//
//    private List<String> NET_TS_45 = Arrays.asList("TD-LTE", "LTE FDD", "5G");

    private Map<String, String> ORG_TYPE_MAP = new HashMap<>() {{
        put("mobile", "中国移动重庆分公司");
        put("telecom", "中国电信重庆分公司");
        put("unicom", "中国联通重庆分公司");
        put("broadnet", "中广电重庆分公司");
    }};

    /**
     * 查询无线电频率使用率评价结果
     *
     * @param vo vo
     * @return map
     */
    public Map<String, Object> searchFREQEvaluation(FreqEvaluationVO vo) {
        if (StringUtils.isEmpty(vo.getStartDate()) || StringUtils.isEmpty(vo.getEndDate())) {
            return this.basicReturnFailure("时间不能为空");
        }
        vo.setOrgLevel("2");
        var dtoList = freqEvaluationService.searchFREQEvaluation(vo);
        int num = freqEvaluationService.searchFREQEvaluationNum(vo);
        String[][] data = null;
        if (num != 0) {
            // 先写入市级 2 后写入省级 1
            var orgLevelMap = dtoList.stream().collect(Collectors.groupingBy(FreqEvaluationDTO::getOrgLevel));
            var dtoList2 = orgLevelMap.get("2");
            // 运营商类型map
            var orgTypeMap = dtoList2.stream().collect(Collectors.groupingBy(FreqEvaluationDTO::getOrgType));
            // 每个运营商有几个频段 <运营商, 频段>
            var orgTypeSize = new HashMap<String, List<String>>();
            var size = 0;
            for (Map.Entry<String, List<FreqEvaluationDTO>> orgTypeEntry : orgTypeMap.entrySet()) {
                var freqMap = orgTypeEntry.getValue().stream().collect(Collectors.groupingBy((item -> item.getEmitStartFreq() + "-" + item.getEmitStopFreq() + "!" + item.getOrgType())));
                List<String> list = new ArrayList<>(freqMap.keySet());
                size += list.size();
                orgTypeSize.put(orgTypeEntry.getKey(), list);
            }
            // 初始化data
            data = new String[num * 3 + 7][size + 1];
            // 地市名称map
            var areaNameMap = dtoList2.stream().collect(Collectors.groupingBy(FreqEvaluationDTO::getAreaName));
            var i = 0;

            for (Map.Entry<String, List<FreqEvaluationDTO>> areaNameEntry : areaNameMap.entrySet()) {
                data[i + 2][0] = areaNameEntry.getKey();

                for (int j = 0; j < areaNameEntry.getValue().size(); j++) {
                    FreqEvaluationDTO dto = areaNameEntry.getValue().get(j);
                    var temp = 0;
                    for (Map.Entry<String, List<String>> entry : orgTypeSize.entrySet()) {
                        for (int k = 0; k < entry.getValue().size(); k++) {
                            data[0][1 + temp] = ORG_TYPE_MAP.get(entry.getKey());
                            data[1][1 + temp] = entry.getValue().get(k).split("!")[0];
                            if (entry.getValue().get(k).equals(dto.getEmitStartFreq() + "-" + dto.getEmitStopFreq() + "!" + dto.getOrgType())) {
                                data[i + 2][1 + temp] = dto.getFrequencyOccupancy().toString();
                                data[i + 2 + 1][1 + temp] = dto.getRegionalCoverage().toString();
                                data[i + 2 + 2][1 + temp] = dto.getUserLoadCapacity().toString();
                            }
                            temp++;
                        }
                    }
                }
                i += 3;
            }
            var dtoList1 = orgLevelMap.get("1");
            // 运营商类型map
            orgTypeMap = dtoList1.stream().collect(Collectors.groupingBy(FreqEvaluationDTO::getOrgType));
            // 每个运营商有几个频段 <运营商, 频段>
            orgTypeSize = new HashMap<>();
            size = 0;
            for (Map.Entry<String, List<FreqEvaluationDTO>> orgTypeEntry : orgTypeMap.entrySet()) {
                var freqMap = orgTypeEntry.getValue().stream().collect(Collectors.groupingBy((item -> item.getEmitStartFreq() + "-" + item.getEmitStopFreq() + "!" + item.getOrgType())));
                List<String> list = new ArrayList<>(freqMap.keySet());
                size += list.size();
                orgTypeSize.put(orgTypeEntry.getKey(), list);
            }

            var prVO = new FreqEvaluationProvVO();
            prVO.setEndDate(vo.getEndDate());
            prVO.setStartDate(vo.getStartDate());
            var provList = freqEvaluationService.searchFREQEvaluationProv(prVO);

            if (dtoList1.size() > 0) {
                data[num * 3 + 2][0] = "全省(区、市)评价结果";
                for (FreqEvaluationDTO dto : dtoList1) {
                    var temp = 0;
                    for (Map.Entry<String, List<String>> entry : orgTypeSize.entrySet()) {
                        for (int k = 0; k < entry.getValue().size(); k++) {
                            if (entry.getValue().get(k).equals(dto.getEmitStartFreq() + "-" + dto.getEmitStopFreq() + "!" + dto.getOrgType())) {
                                data[num * 3 + 2][1 + temp] = dto.getFrequencyOccupancy().toString();
                                data[num * 3 + 2 + 1][1 + temp] = dto.getRegionalCoverage().toString();
                                data[num * 3 + 2 + 2][1 + temp] = dto.getUserLoadCapacity().toString();
                                data[num * 3 + 2 + 3][1 + temp] = dto.getBaseStationOwnership().toString();
                            }
                            temp++;
                        }
                    }
                }
            }
            var str2 = "";
            var str3 = "";
            var str4 = "";
            var str5 = "";
            for (int j = 0; j < provList.size(); j++) {
                var provDTO = provList.get(j);
                if ("2G".equals(provDTO.getNetTsLevel())) {
                    str2 = "2G：" + provDTO.getTrfValue();
                    break;
                } else if (j == provList.size() - 1) {
                    str2 = "2G：0";
                }
            }
            for (int j = 0; j < provList.size(); j++) {
                var provDTO = provList.get(j);
                if ("3G".equals(provDTO.getNetTsLevel())) {
                    str3 = "、3G：" + provDTO.getTrfValue();
                    break;
                } else if (j == provList.size() - 1) {
                    str3 = "、3G：0";
                }
            }
            for (int j = 0; j < provList.size(); j++) {
                var provDTO = provList.get(j);
                if ("4G".equals(provDTO.getNetTsLevel())) {
                    str4 = "、4G：" + provDTO.getTrfValue();
                    break;
                } else if (j == provList.size() - 1) {
                    str4 = "、4G：0";
                }
            }
            for (int j = 0; j < provList.size(); j++) {
                var provDTO = provList.get(j);
                if ("5G".equals(provDTO.getNetTsLevel())) {
                    str5 = "、5G：" + provDTO.getTrfValue();
                    break;
                } else if (j == provList.size() - 1) {
                    str5 = "、5G：0";
                }
            }
            data[num * 3 + 2 + 4][1] = str2 + str3 + str4 + str5;
        }
        return this.basicReturnSuccess(data);
    }

    /**
     * 忙时激活用户数/用户流量统计表
     *
     * @param vo vo
     * @return map
     */
    public Map<String, Object> searchOperUserStatis(OperUserStatisVO vo) {
        if (StringUtils.isEmpty(vo.getStartDate()) || StringUtils.isEmpty(vo.getEndDate())) {
            return this.basicReturnFailure("时间不能为空");
        }
        var dtoList = operUserStatisService.searchOperUserStatis(vo);
        Map<String, String[][]> map = new HashMap<>();
        if (dtoList != null && dtoList.size() != 0) {
            // 先写入市级 2 后写入省级 1
            var orgLevelMap = dtoList.stream().collect(Collectors.groupingBy(OperUserStatisDTO::getOrgLevel));
            var dtoList2 = orgLevelMap.get("2");
            var dtoList1 = orgLevelMap.get("1");
            // 运营商类型map
            var orgTypeMap = dtoList2.stream().collect(Collectors.groupingBy(OperUserStatisDTO::getOrgType));
            for (Map.Entry<String, List<OperUserStatisDTO>> orgTypeEntry : orgTypeMap.entrySet()) {
                // 初始化data
                var orgTypeList = orgTypeEntry.getValue();
                var data = new String[orgTypeList.size() + 3][5];
                data[0][1] = ORG_TYPE_MAP.get(orgTypeEntry.getKey());
                var areaNameMap = orgTypeList.stream().collect(Collectors.groupingBy(OperUserStatisDTO::getAreaName));
                var i = 0;
                for (Map.Entry<String, List<OperUserStatisDTO>> areaNameEntry : areaNameMap.entrySet()) {
                    data[i + 1][0] = areaNameEntry.getKey();
                    for (OperUserStatisDTO dto : areaNameEntry.getValue()) {
                        data[i + 1][1] = dto.getUser2g().toString();
                        data[i + 1][2] = dto.getUser3g().toString();
                        data[i + 1][3] = dto.getUserTraffic4g().toString();
                        data[i + 1][4] = dto.getUserTraffic5g().toString();
                        i++;
                    }
                    if (i == (orgTypeList.size())) {
                        for (OperUserStatisDTO dto : dtoList1) {
                            data[i + 1][0] = "全省";
                            if (dto.getOrgType().equals(orgTypeEntry.getKey())) {
                                data[i + 1][1] = dto.getUser2g().toString();
                                data[i + 1][2] = dto.getUser3g().toString();
                                data[i + 1][3] = dto.getUserTraffic4g().toString();
                                data[i + 1][4] = dto.getUserTraffic5g().toString();
                                i++;
                                break;
                            }
                        }
                    }
                }
                // 系统忙时
                data[data.length - 1][1] = "";
                // 起止日期
                var calendar = Calendar.getInstance();
                calendar.add(Calendar.MONTH, -1);
                var lastMonth = calendar.getTime();
                var lastDayOfMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                String lastMonthStr = new Timestamp(lastMonth.getTime()).toString().substring(0, 8);
                data[data.length - 1][3] = lastMonthStr + "01至" + lastMonthStr + lastDayOfMonth;
                map.put(orgTypeEntry.getKey(), data);
            }
        }
        return this.basicReturnSuccess(map);
    }

}
