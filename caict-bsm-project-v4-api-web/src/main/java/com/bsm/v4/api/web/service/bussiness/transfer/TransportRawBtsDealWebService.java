package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.api.web.utils.ZipUtil;
import com.bsm.v4.domain.security.service.business.transfer.TransportRawBtsDealService;
import com.bsm.v4.system.model.contrust.transfer.TransportFileStateConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobStateConst;
import com.bsm.v4.system.model.dto.business.transfer.*;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDeal;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TransportRawBtsDealWebService extends BasicWebService {

    @Autowired
    private TransportRawBtsDealService transportRawBtsDealService;
    @Autowired
    private AuthWebService authWebService;
    @Autowired
    private TransportFileWebService transportFileWebService;
    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;
    @Autowired
    private TransportRawBtsDealLogWebService transportRawBtsDealLogWebService;

    @Value("${caict.myFilePath}")
    private String myFilePath;

    /**
     * 批量添加
     */
    public int insertBatch(List<TransportRawBtsDeal> errListAndLog) {
        return transportRawBtsDealService.insertBatch(errListAndLog);
    }

    /**
     * 根据job删除
     */
    public int deleteByJobId(String jobId) {
        return transportRawBtsDealService.deleteByJobId(jobId);
    }

    /**
     * 根据jobid、dataType、genNum删除
     */
    public int deleteByJobIdDataTypeGenNum(String jobId, String dataType, String genNum) {
        return transportRawBtsDealService.deleteByJobIdDataTypeGenNum(jobId, dataType, genNum);
    }

    public void updateConfirm(String jobId, String dataType, String genNum) {
        transportRawBtsDealService.updateConfirm(jobId, dataType, genNum);
    }

    /**
     * 根据job流程状态用户分页查询
     * */
    /*public JSONObject findAllPage(String token, TransportRawBtsDealDTO transportRawBtsDealDTO){
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
//        PageInfo<TransportRawBtsDealDTO> transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPage(usersDTO.getUserId(),transportRawBtsDealDTO));
        PageInfo<TransportRawBtsDealDTO> transportRawBtsDealDTOList;
        if (transportRawBtsDealDTO.getDataType().equals("3")) {
            // 3
            transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPage(usersDTO.getUserId(), transportRawBtsDealDTO, "3", "3", "3"));
        } else if (!StringUtils.isNotEmpty(transportRawBtsDealDTO.getDataType())) {
            // 1,2,3
            transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPage(usersDTO.getUserId(), transportRawBtsDealDTO, null, null, null));
            if (transportRawBtsDealDTOList.getList().size() > 1) {
                List<TransportRawBtsDealDTO> list = transportRawBtsDealDTOList.getList().stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportRawBtsDealDTO::getBtsId))), ArrayList::new));
                transportRawBtsDealDTOList.setList(list);
            }
        } else {
            // 1,2
            transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPage(usersDTO.getUserId(), transportRawBtsDealDTO, "1", "2", "3"));
            if (transportRawBtsDealDTOList.getList().size() > 1) {
                List<TransportRawBtsDealDTO> list = transportRawBtsDealDTOList.getList().stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportRawBtsDealDTO::getBtsId))), ArrayList::new));
                transportRawBtsDealDTOList.setList(list);
            }
        }
        if(transportRawBtsDealDTOList.getList() != null){
            //int total = transportRawBtsDealService.selectAllCount(usersDTO.getUserId(),transportRawBtsDealDTO);
            return JSONResult.getSuccessJson(transportRawBtsDealDTOList);
        }
        return JSONResult.getPageFailureJson(transportRawBtsDealDTO.getPage(),transportRawBtsDealDTO.getPage() + 1,transportRawBtsDealDTO.getRows(),0,"操作失败");
    }*/

    /**
     * 根据job流程状态用户分页查询
     */
    public JSONObject findAllPage(String token, TransportRawBtsDealDTO transportRawBtsDealDTO) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
//        PageInfo<TransportRawBtsDealDTO> transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPage(usersDTO.getUserId(),transportRawBtsDealDTO));
        PageInfo<TransportRawBtsDealDTO> transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealLogWebService.findAllPageNew(usersDTO.getUserId(), transportRawBtsDealDTO));

        if (transportRawBtsDealDTOList.getList() != null) {
            //int total = transportRawBtsDealService.selectAllCount(usersDTO.getUserId(),transportRawBtsDealDTO);
            return JSONResult.getSuccessJson(transportRawBtsDealDTOList);
        }
        return JSONResult.getPageFailureJson(transportRawBtsDealDTO.getPage(), transportRawBtsDealDTO.getPage() + 1, transportRawBtsDealDTO.getRows(), 0, "操作失败");
    }

    public Map<String,Object> findDealDataList(TransportDealDTO transportDealDTO){
        return this.basicReturnResultJson(new PageHandle(transportDealDTO).buildPage(transportRawBtsDealService.findDealDataList(transportDealDTO)));
    }



    /**
     * 下载交互数据
     */
    public JSONObject exportExcel(String token, TransportRawBtsDealDTO transportRawBtsDealDTO) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        String fileName = usersDTO.getLoginName() + "-" + new Date().getTime() + ".xlsx";

        String export = "export/";
        String exlPath = "exl/";

        List<TransportRawBtsDealDTO> transportRawBtsDealDTOS = transportRawBtsDealLogWebService.findAllPageNew(usersDTO.getUserId(), transportRawBtsDealDTO);
        if (transportRawBtsDealDTOS.size() != 0) {
            createExcel(transportRawBtsDealDTOS, fileName, export, exlPath);
        }

        return JSONResult.getSuccessJson(export + exlPath + fileName, "下载成功");
    }

    /**
     * 下载交互数据(重庆)
     */
    public Map<String,Object> exportExcelCq(String token, TransportRawBtsDealDTO transportRawBtsDealDTO) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
//        if (usersDTO == null) return JSONResult.getFailureJson("登录超时，请重新登录");
        String fileName = usersDTO.getLoginName();
        List<String> filePathList = new ArrayList<>();
        String export = "export/";
        String exlPath = "exl/";
        int isDelete = 1;
        if ("".equals(transportRawBtsDealDTO.getDataType()) || transportRawBtsDealDTO.getDataType() == null) {
            //未选择数据类型时
            for (int j = 1; j <= 3; j++) {
                String dataType = "";
                if ("1".equals(String.valueOf(j))) {
                    dataType = "新增" + ".xlsx";
                } else if ("2".equals(String.valueOf(j))) {
                    dataType = "变更" + ".xlsx";
                } else {
                    dataType = "注销" + ".xlsx";
                }
                List<TransportRawBtsDealDTO> transportRawBtsDealDTOList = new ArrayList<>();
                String name = "";
                if (transportRawBtsDealDTO.getGenNum() != null && !"".equals(transportRawBtsDealDTO.getGenNum())) {
                    //选择代数查询时，直接按选择的代数生成excel
                    name = fileName + transportRawBtsDealDTO.getGenNum() + "G" + dataType;
                    if (j == 3) {
                        isDelete = 3;
                        transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, "3", transportRawBtsDealDTO.getGenNum(), "3", "3", "3");
                    } else if (j == 2) {
                        isDelete = 2;
                        transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, "2", transportRawBtsDealDTO.getGenNum(), "1", "2", "3");
                    } else {
                        transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, "1", transportRawBtsDealDTO.getGenNum(), "1", "2", "3");
                    }
                } else {
                    //未选择代数查询时，直接按代数分类生成excel
                    //以代数分数据，每次生成一次查询的量
                    for (int i = 2; i <= 5; i++) {
                        name = fileName + i + "G" + dataType;
                        if (j == 3) {
                            isDelete = 3;
                            transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, "3", String.valueOf(i), "3", "3", "3");
                        } else if (j == 2) {
                            isDelete = 2;
                            transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, "2", String.valueOf(i), "1", "2", "3");
                        } else {
                            transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, "1", String.valueOf(i), "1", "2", "3");
                        }
                    }
                }
                transportRawBtsDealDTOList = transportRawBtsDealDTOList.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportRawBtsDealDTO::getBtsId))), ArrayList::new));
                String excel = createExcelCq(transportRawBtsDealDTOList, name, export, exlPath, isDelete);
                if (excel != null) {
                    filePathList.add(excel);
                }
            }
        } else {
            //选择数据类型时
            String dataType;
            if ("1".equals(transportRawBtsDealDTO.getDataType())) {
                dataType = "新增" + ".xlsx";
            } else if ("2".equals(transportRawBtsDealDTO.getDataType())) {
                dataType = "变更" + ".xlsx";
            } else {
                dataType = "注销" + ".xlsx";
            }
            if (transportRawBtsDealDTO.getGenNum() != null && !"".equals(transportRawBtsDealDTO.getGenNum())) {
                //选择代数查询时，直接按选择的代数生成excel
                String name = fileName + transportRawBtsDealDTO.getGenNum() + "G" + dataType;
                List<TransportRawBtsDealDTO> transportRawBtsDealDTOList;
                if (transportRawBtsDealDTO.getDataType().equals("3")) {
                    isDelete = 3;
                    transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "3", "3", "3");
                } else if (transportRawBtsDealDTO.getDataType().equals("2")) {
                    isDelete = 2;
                    transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "1", "2", "3");
                } else {
                    transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "1", "2", "3");
                }
                transportRawBtsDealDTOList = transportRawBtsDealDTOList.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportRawBtsDealDTO::getBtsId))), ArrayList::new));
                String excel = createExcelCq(transportRawBtsDealDTOList, name, export, exlPath, isDelete);
                if (excel != null) {
                    filePathList.add(excel);
                }
            } else {
                //未选择代数查询时，直接按代数分类生成excel
                //以代数分数据，每次生成一次查询的量
                for (int i = 2; i <= 5; i++) {
                    String name = fileName + i + "G" + dataType;
                    List<TransportRawBtsDealDTO> transportRawBtsDealDTOList;
                    if (transportRawBtsDealDTO.getDataType().equals("3")) {
                        isDelete = 3;
                        transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, transportRawBtsDealDTO.getDataType(), String.valueOf(i), "3", "3", "3");
                    } else if (transportRawBtsDealDTO.getDataType().equals("2")) {
                        isDelete = 2;
                        transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, transportRawBtsDealDTO.getDataType(), String.valueOf(i), "1", "2", "3");
                    } else {
                        transportRawBtsDealDTOList = transportRawBtsDealService.selectPage(usersDTO.getUserId(), transportRawBtsDealDTO, transportRawBtsDealDTO.getDataType(), String.valueOf(i), "1", "2", "3");
                    }
                    String excel = createExcelCq(transportRawBtsDealDTOList, name, export, exlPath, isDelete);
                    if (excel != null) {
                        filePathList.add(excel);
                    }
                }
            }

        }

        ZipUtil zipUtil = new ZipUtil();
        //打包成压缩文件
        String zipName = zipUtil.zipFiles(myFilePath + export, filePathList);
        //删除临时文件下的excel
        zipUtil.deleteExcelPath(new File(myFilePath + export + exlPath));
        return this.basicReturnSuccess(export+zipName);
    }

    public String createExcel(List<TransportRawBtsDealDTO> transportRawBtsDealDTOList, String fileName, String export, String exlPath) {
        try {
            if (transportRawBtsDealDTOList != null && transportRawBtsDealDTOList.size() != 0) {

                //判断路径
                File uploadDir = new File(myFilePath + export + exlPath);
                if (!uploadDir.exists()) {
                    uploadDir.mkdirs();
                }

                SXSSFWorkbook wb = null;
                SXSSFSheet sheet = null;
                SXSSFRow row = null;
                wb = new SXSSFWorkbook();
                wb.setCompressTempFiles(false);
                //创建sheet
                sheet = wb.createSheet(fileName);
                //生成第一行标题
                createFirstRow(sheet);
                int j = 0;

                for (TransportRawBtsDealDTO transportRawBtsDTO : transportRawBtsDealDTOList) {
                    row = sheet.createRow(j + 1);
                    row.createCell(0).setCellValue(transportRawBtsDTO.getCellName() == null ? "无" : transportRawBtsDTO.getCellName());
                    row.createCell(1).setCellValue(transportRawBtsDTO.getCellId() == null ? "无" : transportRawBtsDTO.getCellId());
                    row.createCell(2).setCellValue(transportRawBtsDTO.getBtsName() == null ? "无" : transportRawBtsDTO.getBtsName());
                    row.createCell(3).setCellValue(transportRawBtsDTO.getBtsId() == null ? "无" : transportRawBtsDTO.getBtsId());
                    row.createCell(4).setCellValue(transportRawBtsDTO.getTechType() == null ? "无" : transportRawBtsDTO.getTechType());
                    row.createCell(5).setCellValue(transportRawBtsDTO.getLocation() == null ? "无" : transportRawBtsDTO.getLocation());
                    row.createCell(6).setCellValue(transportRawBtsDTO.getLongitude() == null ? "无" : transportRawBtsDTO.getLongitude());
                    row.createCell(7).setCellValue(transportRawBtsDTO.getLatitude() == null ? "无" : transportRawBtsDTO.getLatitude());
                    row.createCell(8).setCellValue(transportRawBtsDTO.getSendStartFreq() == null ? "无" : transportRawBtsDTO.getSendStartFreq());
                    row.createCell(9).setCellValue(transportRawBtsDTO.getSendEndFreq() == null ? "无" : transportRawBtsDTO.getSendEndFreq());
                    row.createCell(10).setCellValue(transportRawBtsDTO.getAccStartFreq() == null ? "无" : transportRawBtsDTO.getAccStartFreq());
                    row.createCell(11).setCellValue(transportRawBtsDTO.getAccEndFreq() == null ? "无" : transportRawBtsDTO.getAccEndFreq());
                    row.createCell(12).setCellValue(transportRawBtsDTO.getMaxEmissivePower() == null ? "无" : transportRawBtsDTO.getMaxEmissivePower());
                    row.createCell(13).setCellValue(transportRawBtsDTO.getHeight() == null ? "无" : transportRawBtsDTO.getHeight());
                    row.createCell(14).setCellValue(transportRawBtsDTO.getCounty() == null ? "无" : transportRawBtsDTO.getCounty());
                    row.createCell(15).setCellValue(transportRawBtsDTO.getDataType() == null ? "无" : transportRawBtsDTO.getDataType());
                    row.createCell(16).setCellValue(transportRawBtsDTO.getVendorName() == null ? "无" : transportRawBtsDTO.getVendorName());
                    row.createCell(17).setCellValue(transportRawBtsDTO.getDeviceModel() == null ? "无" : transportRawBtsDTO.getDeviceModel());
                    row.createCell(18).setCellValue(transportRawBtsDTO.getModelCode() == null ? "无" : transportRawBtsDTO.getModelCode());
                    row.createCell(19).setCellValue(transportRawBtsDTO.getAntennaGain() == null ? "无" : transportRawBtsDTO.getAntennaGain());
                    row.createCell(20).setCellValue(transportRawBtsDTO.getAntennaModel() == null ? "无" : transportRawBtsDTO.getAntennaModel());
                    row.createCell(21).setCellValue(transportRawBtsDTO.getAntennaFactory() == null ? "无" : transportRawBtsDTO.getAntennaFactory());
                    row.createCell(22).setCellValue(transportRawBtsDTO.getPolarizationMode() == null ? "无" : transportRawBtsDTO.getPolarizationMode());
                    row.createCell(23).setCellValue(transportRawBtsDTO.getAntennaAzimuth() == null ? "无" : transportRawBtsDTO.getAntennaAzimuth());
                    row.createCell(24).setCellValue(transportRawBtsDTO.getFeederLoss() == null ? "无" : transportRawBtsDTO.getFeederLoss());
                    row.createCell(25).setCellValue(transportRawBtsDTO.getAltitude() == null ? "无" : transportRawBtsDTO.getAltitude());
                    j++;
                }
                FileOutputStream fileOutputStream = new FileOutputStream(myFilePath + export + exlPath + File.separator + fileName);
                wb.write(fileOutputStream);
                fileOutputStream.close();
                wb.dispose();
                return myFilePath + export + exlPath + fileName;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;

    }

    protected void createFirstRow(SXSSFSheet sheet) {
        SXSSFRow firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue("CELL_NAME");
        firstRow.createCell(1).setCellValue("CELL_ID");
        firstRow.createCell(2).setCellValue("BTS_NAME");
        firstRow.createCell(3).setCellValue("BTS_ID");
        firstRow.createCell(4).setCellValue("TECH_TYPE");
        firstRow.createCell(5).setCellValue("LOCATION");
        firstRow.createCell(6).setCellValue("LONGITUDE");
        firstRow.createCell(7).setCellValue("LATITUDE");
        firstRow.createCell(8).setCellValue("SEND_START_FREQ");
        firstRow.createCell(9).setCellValue("SEND_END_FREQ");
        firstRow.createCell(10).setCellValue("ACC_START_FREQ");
        firstRow.createCell(11).setCellValue("ACC_END_FREQ");
        firstRow.createCell(12).setCellValue("MAX_EMISSIVE_POWER");
        firstRow.createCell(13).setCellValue("HEIGHT");
        firstRow.createCell(14).setCellValue("COUNTY");
        firstRow.createCell(15).setCellValue("DATA_TYPE");
        firstRow.createCell(16).setCellValue("VENDOR_NAME");
        firstRow.createCell(17).setCellValue("DEVICE_MODEL");
        firstRow.createCell(18).setCellValue("MODEL_CODE");
        firstRow.createCell(19).setCellValue("ANTENNA_GAIN");
        firstRow.createCell(20).setCellValue("ANTENNA_MODEL");
        firstRow.createCell(21).setCellValue("ANTENNA_FACTORY");
        firstRow.createCell(22).setCellValue("POLARIZATION_MODE");
        firstRow.createCell(23).setCellValue("ANTENNA_AZIMUTH");
        firstRow.createCell(24).setCellValue("FEEDER_LOSS");
        firstRow.createCell(25).setCellValue("ALTITUDE");
    }

    /**
     * 查询待确认数量
     */
    public JSONObject findAllTransportRawBtsDealShowDTO() {
        List<String> compares = new ArrayList<>(Arrays.asList(TransportJobStateConst.COMPARE_SUCCESS, TransportJobStateConst.CONFIRM_PROCESSING));
        return JSONResult.getSuccessJson(transportRawBtsDealService.findAllTransportRawBtsDealShowDTO(compares), "查询成功");
    }

    /**
     * 根据jobGuid、类型分页查询
     */
    public JSONObject findAllPageByJobDataType(TransportRawBtsDealDTO transportRawBtsDealDTO) {
        if ("confirm".equals(transportRawBtsDealDTO.getType())) {
            PageInfo<TransportRawBtsDealDTO> transportRawBtsDealDTOList = new PageInfo<>();
            if (transportRawBtsDealDTO.getDataType().equals("1") || transportRawBtsDealDTO.getDataType().equals("2")) {
                transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPageByJobDataType(transportRawBtsDealDTO.getJobGuid(), transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "1", "2", "3"));
            } else if (transportRawBtsDealDTO.getDataType().equals("3")) {
                transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findAllPageByJobDataType(transportRawBtsDealDTO.getJobGuid(), transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "3", "3", "3"));
            }
            if (transportRawBtsDealDTOList.getList() != null) {
                TransportRawFileDTO transportRawFileDTO = new TransportRawFileDTO();
                transportRawFileDTO.setTransportRawBtsDealDTOS(transportRawBtsDealDTOList.getList());
                //int total = transportRawBtsDealService.selectCountByJobDataType(transportRawBtsDealDTO.getJobGuid(),transportRawBtsDealDTO.getDataType(),transportRawBtsDealDTO.getGenNum());
                return JSONResult.getSuccessJson(transportRawFileDTO);
            }
        } else if ("check".equals(transportRawBtsDealDTO.getType())) {
            PageInfo<TransportRawBtsDealDTO> transportRawBtsDealDTOList = new PageInfo<>();
            if (transportRawBtsDealDTO.getDataType().equals("1") || transportRawBtsDealDTO.getDataType().equals("2")) {
                transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findCheckPageByJobDataType(transportRawBtsDealDTO.getJobGuid(), transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "1", "2", "3"));
            } else if (transportRawBtsDealDTO.getDataType().equals("3")) {
                transportRawBtsDealDTOList = new PageHandle(transportRawBtsDealDTO).buildPage(transportRawBtsDealService.findCheckPageByJobDataType(transportRawBtsDealDTO.getJobGuid(), transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), "3", "3", "3"));
            }
            if (transportRawBtsDealDTOList.getList() != null) {
                TransportRawFileDTO transportRawFileDTO = new TransportRawFileDTO();
                //返回appGuid
                ApprovalTransportJobDTO approvalTransportJobDTO = approvalTransportJobWebService.findOneByJobGuidUpload(transportRawBtsDealDTO.getJobGuid(), transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum());
                if (approvalTransportJobDTO != null) {
                    transportRawFileDTO.setAppGuid(approvalTransportJobDTO.getGuid());
                    transportRawFileDTO.setJobStatus(approvalTransportJobDTO.getIsCompare());
                }
                //int total = transportRawBtsDealService.selectCountByJobDataType(transportRawBtsDealDTO.getJobGuid(),transportRawBtsDealDTO.getDataType(),transportRawBtsDealDTO.getGenNum());
                List<TransportFileDTO> transportFileCSVDTOList = transportFileWebService.findByJobGuid(transportRawBtsDealDTO.getJobGuid(), transportRawBtsDealDTO.getDataType(), transportRawBtsDealDTO.getGenNum(), TransportFileStateConst.EXAMINE);
                if (transportFileCSVDTOList != null && transportFileCSVDTOList.size() != 0) {
                    transportRawFileDTO.setTransportFileDTOS(transportFileCSVDTOList);
                }
                transportRawFileDTO.setTransportRawBtsDealDTOS(transportRawBtsDealDTOList.getList());
                return JSONResult.getSuccessJson(transportRawFileDTO);
            }
        }

        return JSONResult.getPageFailureJson(transportRawBtsDealDTO.getPage(), transportRawBtsDealDTO.getPage() + 1, transportRawBtsDealDTO.getRows(), 0, "操作失败");
    }

    /**
     * 根据jobId、btsId查询数据
     *
     * @param dealDTO transportRawBtsDealDTO
     * @return json
     */
    public JSONObject findAllCellByJobBtsId(TransportRawBtsDealDTO dealDTO) {
        List<TransportRawBtsDealDTO> dealDTOList = transportRawBtsDealService.findAllCellByJobBtsId(dealDTO.getJobGuid(), dealDTO.getBtsId());
        return JSONResult.getSuccessJson(dealDTOList, "查询成功");
    }

    /**
     * 生成下载数据
     */
    public int insertByTransportRawBts(String jobGuid, String regionCode) {
        return transportRawBtsDealService.insertByTransportRawBts(jobGuid, regionCode);
    }

    /**
     * 根据btsId更新数据
     *
     * @param jobId  jobId
     * @param btsIds list
     * @return list
     */
    public void updateByBtsIds(String dataType, String jobId, List<String> btsIds, String regionCode) {
        transportRawBtsDealService.updateByBtsIds(dataType, jobId, btsIds, regionCode);
    }

    public List<TransportRawBtsDeal> findLayuanByJobId(String jobGuid){
        return transportRawBtsDealService.findLayuanByJobId(jobGuid);
    }

    public String createExcelCq(List<TransportRawBtsDealDTO> transportRawBtsDealDTOList, String fileName, String export, String exlPath, int isDelete) {
        try {
            if (transportRawBtsDealDTOList != null && transportRawBtsDealDTOList.size() != 0) {

                //判断路径
                File uploadDir = new File(myFilePath + export + exlPath);
                if (!uploadDir.exists()) {
                    uploadDir.mkdirs();
                }

                SXSSFWorkbook wb = null;
                SXSSFSheet sheet = null;
                SXSSFRow row = null;
                wb = new SXSSFWorkbook();
                //创建sheet
                sheet = wb.createSheet(fileName);
                //生成第一行标题
                createFirstRow(sheet, isDelete);
                int j = 0;

                for (TransportRawBtsDealDTO transportRawBtsDealDTOExl : transportRawBtsDealDTOList) {
                    row = sheet.createRow(j + 1);
                    row.createCell(0).setCellValue(j + 1);
                    row.createCell(1).setCellValue(transportRawBtsDealDTOExl.getJobGuid() == null ? "无" : transportRawBtsDealDTOExl.getJobGuid());
                    row.createCell(2).setCellValue(transportRawBtsDealDTOExl.getBtsId() == null ? "无" : transportRawBtsDealDTOExl.getBtsId());
                    row.createCell(3).setCellValue(transportRawBtsDealDTOExl.getBtsName() == null ? "无" : transportRawBtsDealDTOExl.getBtsName());
                    if (isDelete != 1)
                        row.createCell(4).setCellValue(transportRawBtsDealDTOExl.getLicenseCode() == null ? "无" : transportRawBtsDealDTOExl.getLicenseCode());
                    j++;
                }
                FileOutputStream fileOutputStream = new FileOutputStream(myFilePath + export + exlPath + File.separator + fileName);
                wb.write(fileOutputStream);
                fileOutputStream.close();
                wb.dispose();
                return myFilePath + export + exlPath + fileName;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;

    }

    protected void createFirstRow(SXSSFSheet sheet, int isDelete) {
        SXSSFRow firstRow = sheet.createRow(0);
        firstRow.createCell(0).setCellValue("NUMBER");
        firstRow.createCell(1).setCellValue("JOB_ID");
        firstRow.createCell(2).setCellValue("BTS_ID");
        firstRow.createCell(3).setCellValue("BTS_NAME");
        if (isDelete != 1) firstRow.createCell(4).setCellValue("LICENSE_CODE");
    }

    public int updateBatch(List<TransportRawBtsDeal> transportRawBtsDeals){
        return transportRawBtsDealService.updateBatch(transportRawBtsDeals);
    }
}