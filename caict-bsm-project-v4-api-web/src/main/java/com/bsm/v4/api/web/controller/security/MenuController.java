package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.caictframework.data.controller.BasicController;
import com.bsm.v4.api.web.service.security.MenuWebService;
import com.bsm.v4.system.model.vo.security.MenuVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 */
@RestController
@RequestMapping(value = "/apiWeb/security/menu")
@Api(value = "web端-系统-菜单接口", tags = "web端-系统-菜单接口")
public class MenuController extends BasicController {

    @ApiOperation(value = "保存菜单信息", notes = "保存菜单信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "menuVO", value = "菜单对象", required = true, paramType = "body", dataType = "menuVO")
    })
    @PostMapping(value = "/")
    public JSONObject save(@RequestBody MenuVO menuVO) {
        return this.basicReturnJson(menuVO, MenuWebService.class, (vo, service) -> service.save(vo));
    }

    @ApiOperation(value = "批量添加菜单信息", notes = "批量添加菜单信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "menuVOList", value = "菜单对象集合", allowMultiple = true, required = true, paramType = "body", dataType = "menuVO")
    })
    @PostMapping(value = "/insertBatch")
    public JSONObject insertBatch(@RequestBody List<MenuVO> menuVOList) {
        return this.basicReturnJson(menuVOList, MenuWebService.class, (vo, service) -> service.insertBatch(vo));
    }

    @ApiOperation(value = "查询全部菜单信息", notes = "查询全部菜单信息接口")
    @GetMapping(value = "/")
    public JSONObject findAll() {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findAll());
    }


    @ApiOperation(value = "查询全部菜单（树形结构）", notes = "查询全部菜单（树形结构）接口")
    @GetMapping(value = "/findAllTree")
    public JSONObject findAllTree() {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findAllTree());
    }

    @ApiOperation(value = "查询一个菜单信息", notes = "查询一个菜单接口")
    @ApiImplicitParam(name = "id", value = "菜单id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findOneById/{id}")
    public JSONObject findOneById(@PathVariable String id) {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findOne(id));
    }

    @ApiOperation(value = "查询所有菜单（根据菜单类型）", notes = "查询所有菜单（根据菜单类型）接口")
    @ApiImplicitParam(name = "type", value = "菜单类型", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "findAllByType/{type}")
    public JSONObject findAllByType(@PathVariable String type) {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findAllByType(type));
    }

    @ApiOperation(value = "查询所有菜单（根据父级菜单Id）", notes = "查询所有菜单（根据父级菜单Id）")
    @ApiImplicitParam(name = "parentId", value = "父级菜单id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "findAllByParentId/{parentId}")
    public JSONObject findAllByParentId(@PathVariable String parentId) {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findAllByParentId(parentId));
    }

    @ApiOperation(value = "查询所有菜单（根据父级菜单Id）（树形结构）", notes = "查询所有菜单（根据父级菜单Id）（树形结构）接口")
    @ApiImplicitParam(name = "parentId", value = "父级菜单id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "findAllByParentIdTree/{parentId}")
    public JSONObject findAllByParentIdTree(@PathVariable String parentId) {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findAllByParentIdTree(parentId));
    }

    @ApiOperation(value = "删除菜单", notes = "删除菜单接口")
    @ApiImplicitParam(name = "id", value = "菜单id", required = true, paramType = "path", dataType = "String")
    @DeleteMapping(value = "delete/{id}")
    public JSONObject deleteMenu(@PathVariable String id) {
        return this.basicReturnJson(id, MenuWebService.class, (vo, service) -> service.delete(id));
    }

    @ApiOperation(value = "登录用户获取菜单", notes = "登录用户获取菜单接口")
    @GetMapping(value = "findAllUserTree")
    public JSONObject findAllUserTree(@RequestHeader("token") String token) {
        return this.basicReturnJson(MenuWebService.class, (service) -> service.findAllByUserTree(token));

    }
}
