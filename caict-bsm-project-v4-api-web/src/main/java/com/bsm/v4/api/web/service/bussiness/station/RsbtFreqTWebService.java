package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtFreqTService;
import com.bsm.v4.system.model.entity.business.station.RsbtFreqT;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class RsbtFreqTWebService extends BasicWebService {

    @Autowired
    private RsbtFreqTService rsbtFreqTService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtFreqT> rsbtFreqTList) {
        return rsbtFreqTService.insertBatch(rsbtFreqTList);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtFreqT> rsbtFreqTList) {
        return rsbtFreqTService.updateBatch(rsbtFreqTList);
    }

    /**
     * 批量修改
     */
    public int deleteBatch(List<RsbtFreqT> rsbtFreqTList) {
        return rsbtFreqTService.deleteBatch(Collections.singletonList(rsbtFreqTList));
    }
}
