package com.bsm.v4.api.web.service.bussiness.transfer_in;

import com.bsm.v4.domain.security.service.business.applytable.ApplyLinkService;
import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class ApplyLinkWebService extends BasicWebService {

    @Autowired
    private ApplyLinkService applyLinkService;

    public int insertBatch(List<ApplyLink> applyLinks) {
        return applyLinkService.insertBatch(applyLinks);
    }

    public Long insert(ApplyLink applyLink) {
        return applyLinkService.insert(applyLink);
    }

    public void updateByJobGuid(String jobGuid, String syncStatus) {
        applyLinkService.updateByJobGuid(jobGuid, syncStatus);
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return applyLinkService.updateAppCode(oldAppCode, newAppCode);
    }

    public int updateByAppCode(String appCode, String status) {
        return applyLinkService.updateByAppCode(appCode, status);
    }
}
