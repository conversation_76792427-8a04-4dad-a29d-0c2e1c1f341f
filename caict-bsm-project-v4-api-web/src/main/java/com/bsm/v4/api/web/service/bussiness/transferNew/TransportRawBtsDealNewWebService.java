package com.bsm.v4.api.web.service.bussiness.transferNew;

import com.bsm.v4.domain.security.service.business.transferNew.TransportRawBtsDealNewService;
import com.bsm.v4.system.model.vo.business.transferNew.FileUploadResultVO;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * CSV 原始数据存储表WebService
 */
@Service
public class TransportRawBtsDealNewWebService extends BasicWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private TransportRawBtsDealNewService transportRawBtsDealNewService;

    /**
     * 上传CSV文件
     * @param file 上传的文件
     * @return 上传结果
     */


}
