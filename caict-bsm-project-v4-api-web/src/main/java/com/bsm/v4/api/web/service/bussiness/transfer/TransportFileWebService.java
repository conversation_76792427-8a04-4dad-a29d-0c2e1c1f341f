package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.business.transfer.TransportFileService;
import com.bsm.v4.system.model.contrust.transfer.TransportFileStateConst;
import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.util.JSONResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportFileWebService extends BasicWebService {

    @Autowired
    private TransportFileService transportFileService;

    /**
     * 根据Id查询
     */
    public TransportFile findById(String fileId) {
        return transportFileService.findById(fileId);
    }

    /**
     * 修改
     */

    public void update(TransportFile transportFileCsv) {
        transportFileService.update(transportFileCsv);
    }


    /**
     * 保存
     */
    public JSONObject saveTransportFile(TransportFile transportFileCsv) {
        String save = transportFileService.save(transportFileCsv);
        if (save != null) {
            transportFileCsv.setGuid(save);
            return JSONResult.getSuccessJson(transportFileCsv, "操作成功");
        }
        return JSONResult.getFailureJson("操作失败");
    }

    /**
     * 添加一条csv记录
     */
    public JSONObject createFileRecord(String jobId, String fileType, String dataType, String genNum) {
        TransportFile transportFile = new TransportFile();
        transportFile.setGmtCreate(new Date());
        transportFile.setGmtModified(new Date());
        transportFile.setJobGuid(jobId);
        transportFile.setFileState(TransportFileStateConst.UPLOAD_FAILURE);
        transportFile.setFileType(fileType);
        if ("2".equals(fileType)) {
            transportFile.setDataType(dataType);
            transportFile.setGenNum(genNum);
        }

        return saveTransportFile(transportFile);
    }

    /**
     * 根据Job、文件名查询数量
     */
    public int selectCountByJobFileName(String jobId, String fileName) {
        return transportFileService.selectCountByJobFileName(jobId, fileName);
    }

    /**
     * 根据fileId查询
     */
    public TransportFile findOneByGuid(String guid) {
        return transportFileService.findById(guid);
    }

    /**
     * 判断job下所有文件都已经处理
     */
    public Map<String, Object> fileStateConst(String jobId, String fileType) {
        TransportFile transportFile = new TransportFile();
        transportFile.setJobGuid(jobId);
        transportFile.setFileType(fileType);
        List<TransportFileDTO> transportFileDTOList = findAllByWhere(transportFile);
        if (transportFileDTOList.size() > 0) {
            for (TransportFileDTO transportFileDTO : transportFileDTOList) {
                if (transportFileDTO.getFileState() != TransportFileStateConst.CHECK_SUCCESS) {
                    return this.basicReturnFailure("当前事件下有文件未处理无法提交");
                }
            }
        }
        return null;
    }

    /**
     * 条件查询
     */
    public List<TransportFileDTO> findAllByWhere(TransportFile transportFile) {
        List<TransportFile> transportFileList = transportFileService.findAllByWhere(transportFile);
        List<TransportFileDTO> transportFileDTOList = new ArrayList<>();

        for (TransportFile transportFileR : transportFileList) {
            TransportFileDTO transportFileDTO = new TransportFileDTO();
            BeanUtils.copyProperties(transportFileR, transportFileDTO);
            transportFileDTOList.add(transportFileDTO);
        }
        return transportFileDTOList;
    }

    /**
     * 根据guid删除
     */
    public JSONObject deleteByGuid(String guid) {
        if (transportFileService.delete(guid) > 0) {
            return JSONResult.getSuccessJson("删除成功");
        }
        return JSONResult.getFailureJson("删除失败，无数据");
    }

    /**
     * 根据id修改状态
     */
    public String updateFileStateByFileId(String guid, Long state) {
        TransportFile transportFile = new TransportFile();
        transportFile.setGuid(guid);
        transportFile.setFileState(state);
        transportFile.setGmtModified(new Date());
        return transportFileService.save(transportFile);
    }

    public List<TransportFileDTO> findByJobGuid(String jobGuid, String dataType, String genNum, String fileType) {
        return transportFileService.findByJobGuid(jobGuid, dataType, genNum, fileType);
    }

    public List<TransportFileDTO> findByGuidType(String jobGuid, String fileType) {
        return transportFileService.findByGuidType(jobGuid, fileType);
    }

    public Map<String, Object> findFileDetailByJobId(String jobGuid, String fileType) {
        List<TransportFileDTO> fileDTOList = transportFileService.findFileDetailByJobId(jobGuid, fileType);
        if (fileDTOList.size() != 0) {
            //按文件类型分组
//            Map<String, List<TransportFileDTO>> btsGroup = fileDTOList.stream().collect(Collectors.groupingBy(TransportFileDTO::getFileType));
            return this.basicReturnSuccess(fileDTOList);
        }
        return this.basicReturnSuccess();
    }
}
