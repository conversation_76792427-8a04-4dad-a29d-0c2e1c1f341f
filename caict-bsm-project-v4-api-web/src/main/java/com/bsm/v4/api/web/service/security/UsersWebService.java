package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.LoginService;
import com.bsm.v4.domain.security.service.security.UsersService;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Login;
import com.bsm.v4.system.model.entity.security.Users;
import com.bsm.v4.system.model.vo.security.UsersSearchVO;
import com.bsm.v4.system.model.vo.security.UsersVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.MD5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Title: UsersWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/15 14:44
 * @description:
 */
@Service
public class UsersWebService extends BasicWebService {
    private static final Logger LOG = LoggerFactory.getLogger(UsersWebService.class);

    @Value("${caict.myPasswordKey}")
    private String myPasswordKey;

    @Autowired
    private UsersService usersService;
    @Autowired
    private LoginService loginService;
    @Autowired
    private RoleUsersWebService roleUsersWebService;
    @Autowired
    private OrgUsersWebService orgUsersWebService;

    /**
     * 注册
     */
    public Map<String, Object> register(UsersVO usersVO) {
        //添加基本信息
        Users users = new Users();
        BeanUtils.copyProperties(usersVO, users);

        users.setUpdateDateTime(new Date());
        var id = usersService.save(users);
        if (id != null) {
            //添加登陆信息
            Login login = new Login();
            BeanUtils.copyProperties(usersVO, login);

//            if(!"".equals(usersVO.getLoginId()) && usersVO.getLoginId() != null){
//                login.setId(usersVO.getLoginId());
//            }
            login.setUserId(String.valueOf(id));
            login.setPassword(MD5Utils.getInstance().encode(usersVO.getPassword(), myPasswordKey));
            return this.basicReturnResultJson(loginService.save(login));
        }
        return null;
    }

    /**
     * 保存
     */
    public Map<String, Object> save(UsersVO usersVO) {
        Users users = new Users();
        BeanUtils.copyProperties(usersVO, users);

        users.setUpdateDateTime(new Date());
        var id = usersService.save(users);

        return this.basicReturnResultJson(id);
    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllPageByWhere(UsersSearchVO usersSearchVO) {
        return this.basicReturnResultJson(new PageHandle(usersSearchVO).buildPage(findAllByWhere(usersSearchVO)));
    }

    /**
     * 条件查询
     */
    private List<UsersDTO> findAllByWhere(UsersSearchVO usersSearchVO) {
        return usersService.findAllByWhere(usersSearchVO);
    }

    /**
     * 查询
     */
    public Map<String, Object> findOne(String id) {
        return this.basicReturnResultJson(usersService.findById(id));
    }

    /**
     * 绑定用户
     */
    public Map<String, Object> setUsersRole(String roleId, String usersId) {
        String[] usersIds = new String[]{usersId};
        return this.basicReturnResultJson(roleUsersWebService.setUsersRole(roleId, usersIds));
    }

    /**
     * 绑定组织机构
     */
    public Map<String, Object> setUsersOrg(String organizationId, String usersId) {
        String[] usersIds = new String[]{usersId};
        return this.basicReturnResultJson(orgUsersWebService.setUsersOrg(organizationId, usersIds));
    }

    /**
     * 修改所属区域
     */
    public int updateRegion(String usersId, String regionId) {
        Users users = new Users();
        users.setId(usersId.toString());
        users.setRegionId(String.valueOf(regionId));
        return usersService.update(users);
    }

    /**
     * 删除用户
     */
    @Transactional
    public Map<String, Object> delete(String usersId) {
        //删除组织机构关联
        orgUsersWebService.deleteAllByUserId(usersId);
        //删除角色关联
        roleUsersWebService.deleteByUsersId(usersId);
        //删除登录信息
        loginService.deleteByUser(usersId);

        return this.basicReturnResultJson(usersService.delete(usersId));
    }

    /**
     * 批量删除用户
     */
    @Transactional
    public Map<String, Object> deletesUsers(List<String> usersIds) {
        List<Object> idList = new ArrayList<Object>();
        for (String usersId : usersIds) {
            idList.add(usersId);
            //删除组织机构关联
            orgUsersWebService.deleteAllByUserId(usersId);
            //删除角色关联
            roleUsersWebService.deleteByUsersId(usersId);
        }
        return this.basicReturnResultJson(usersService.deleteBatch(idList));
    }
}
