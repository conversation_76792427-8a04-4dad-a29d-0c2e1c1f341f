package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.ApprovalTransportJobWebService;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by dengsy on 2020-05-08.
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/approvalTransportJob")
@Api(tags = "web端审核任务管理接口")
public class ApprovalTransportJobController extends BasicController {

    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;

    @ApiOperation(value = "创建任务", notes = "创建任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/createApproveJob")
    public JSONObject createApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.createApproveJob(dto));
    }

    @ApiOperation(value = "文件和appJob的关联", notes = "文件和appJob的关联接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/setFileJobId")
    public void setFileJobId(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        approvalTransportJobWebService.setFileJobId(dto);
    }

    @ApiOperation(value = "校验处理上传文件", notes = "校验处理上传文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/processingApproveJob")
    public JSONObject processingApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.processingApproveJob(dto));

    }

    @ApiOperation(value = "重点台站校验", notes = "重点台站校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/commitCoordinate")
    public JSONObject commitCoordinate(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.commitCoordinate(dto));

    }

    @ApiOperation(value = "处理重点台站上传文件", notes = "处理重点台站上传文件接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/processingCoordinate")
    public JSONObject processingCoordinate(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.processingCoordinate(dto));
    }

    @ApiOperation(value = "重点台站不同意提交", notes = "重点台站不同意提交接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appGuid", value = "任务id", paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/commitApproveJobCoordinate/{appGuid}", method = RequestMethod.GET)
    public JSONObject commitApproveJobCoordinate(@PathVariable("appGuid") String appGuid) {
        return this.basicReturnJson(appGuid, ApprovalTransportJobWebService.class, (map, service) -> approvalTransportJobWebService.commitApproveJobCoordinate(appGuid));
    }

    @ApiOperation(value = "提交任务", notes = "提交任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/commitApproveJob")
    public JSONObject commitApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.commitApproveJob(dto));
    }

    @ApiOperation(value = "审核任务", notes = "审核任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/approveApproveJob")
    public JSONObject approveApproveJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.approveApproveJob(dto));
    }

    @ApiOperation(value = "分页查看对比任务列表", notes = "分页查看对比任务列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findPageCompareJobVOListByPage")
    public JSONObject findTransportJobVOLogPage(@RequestBody ApprovalTransportJobDTO dto) {
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findPageCompareJobVOListByPage(dto));
    }

    @ApiOperation(value = "分页查看异常任务列表", notes = "分页查看异常任务列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findApprovalTransportJobVOLogPage")
    public JSONObject findApprovalTransportJobVOLogPage(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findApprovalTransportJobVOLogPage(dto));
    }

    @ApiOperation(value = "分页查询待办任务", notes = "分页查询待办任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPageByWhere(@RequestBody ApprovalTransportJobDTO dto) {
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findAllPageByWhere(dto));
    }

    @ApiOperation(value = "分页查看审核列表历史记录（无委）", notes = "分页查看审核列表历史记录（无委）接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "approvalTransportJobDTO", value = "approvalTransportJobDTO", required = true, paramType = "body", dataType = "approvalTransportJobDTO")
    })
    @PostMapping(value = "/findAllPageAppTransportJob")
    public JSONObject findAllPageAppTransportJob(@RequestBody ApprovalTransportJobDTO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.findAllPageAppTransportJobByJur(dto));
    }

    @ApiOperation(value = "查询详情", notes = "查询详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appGuid", value = "审核任务guid", paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/approveDetail/{appGuid}", method = RequestMethod.GET)
    public JSONObject approveDetail(@PathVariable("appGuid") String appGuid) {
        return this.basicReturnJson(appGuid, ApprovalTransportJobWebService.class, (param, service) -> approvalTransportJobWebService.approveDetail(appGuid));
    }
}
