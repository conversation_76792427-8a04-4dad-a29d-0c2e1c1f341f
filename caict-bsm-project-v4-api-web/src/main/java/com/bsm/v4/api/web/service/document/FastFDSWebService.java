package com.bsm.v4.api.web.service.document;

import cn.hutool.core.io.IoUtil;
import com.bsm.v4.domain.security.service.business.transfer.TransportFileService;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.bsm.v4.system.model.vo.business.transfer.TransportFileVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.document.service.FastDFSClientService;
import com.github.tobato.fastdfs.service.DefaultFastFileStorageClient;
import lombok.Cleanup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.Map;

/**
 * Created by dengsy on 2023-02-10.
 */
@Service
public class FastFDSWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(FastFDSWebService.class);

    @Autowired
    private FastDFSClientService fastDFSClientService;
    @Autowired
    private TransportFileService transportFileService;
    @Autowired
    private DefaultFastFileStorageClient defaultFastFileStorageClient;

    /**
     * 上传文件到FastFDS
     */
    public Map<String, Object> upload(MultipartFile file, String fileType) {
        try {
            var path = fastDFSClientService.uploadFile(file);
            TransportFileVO transportFileVO = new TransportFileVO();
            transportFileVO.setFileType(fileType);
            transportFileVO.setFilePath(path);
            transportFileVO.setFileState(0L);
            transportFileVO.setGmtCreate(new Date());
            transportFileVO.setFileLocalName(file.getOriginalFilename());

            TransportFile transportFile = new TransportFile();
            BeanUtils.copyProperties(transportFileVO, transportFile);
            String fileGuid = transportFileService.save(transportFile);
            if (fileGuid == null) return this.basicReturnFailure("上传文件失败！");

            transportFileVO.setFileId(fileGuid);
            return this.basicReturnSuccess(transportFileVO);
        } catch (IOException e) {
            LOG.error(e.getMessage());
            return this.basicReturnFailure(e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    public Map<String, Object> delete(String fileId) {
        try {
            TransportFile transportFile = transportFileService.findById(fileId);
            if (transportFile == null) return this.basicReturnFailure("未找到文件信息！");
            fastDFSClientService.deleteFile(transportFile.getFilePath());
            //删除文件记录表
            transportFileService.delete(fileId);
            return this.basicReturnSuccess("删除成功");
        } catch (Exception e) {
            LOG.error(e.getMessage());
            return this.basicReturnFailure(e.getMessage());
        }
    }

    /**
     * 下载FastFDS文件
     */
//    public Map<String, Object> downLoad(FastFdsVO fastFdsVO, HttpServletResponse response) {
//        try {
//            fastDFSClientService.download(fastFdsVO.getFilePath(), fastFdsVO.getFileName(), response);
//            return this.basicReturnSuccess("下载成功");
//        } catch (IOException e) {
//            LOG.error(e.getMessage());
//            return this.basicReturnFailure(e.getMessage());
//        }
//    }

    /**
     * 下载FastFDS文件
     */
    public Map<String, Object> downLoad(String fileId, HttpServletResponse response) {
        TransportFile transportFile = transportFileService.findById(fileId);
        try {
            String filePath = transportFile.getFilePath();
            @Cleanup InputStream inputStream = defaultFastFileStorageClient.downloadFile(filePath.substring(0, filePath.indexOf("/")), filePath.substring(filePath.indexOf("/") + 1), ins -> ins);
            @Cleanup OutputStream out = response.getOutputStream();
            out.write(IoUtil.readBytes(inputStream));
            out.flush();
            return this.basicReturnSuccess("下载成功");
        } catch (Exception e) {
            LOG.error(e.getMessage());
//            return this.basicReturnFailure(e.getMessage());
        }
        return this.basicReturnFailure("下载失败！");
    }

    /**
     * 获取文件流
     */
    public InputStream getFileInputStream(String fileId) {
        TransportFile transportFile = transportFileService.findById(fileId);
        try {
            String filePath = transportFile.getFilePath();
            @Cleanup InputStream inputStream = defaultFastFileStorageClient.downloadFile(filePath.substring(0, filePath.indexOf("/")), filePath.substring(filePath.indexOf("/") + 1), ins -> ins);
            return inputStream;
        } catch (Exception e) {
            LOG.error(e.getMessage());
        }
        return null;
    }

}
