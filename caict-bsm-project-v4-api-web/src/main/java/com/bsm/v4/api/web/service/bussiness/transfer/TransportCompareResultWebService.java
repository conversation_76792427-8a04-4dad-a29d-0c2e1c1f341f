package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.domain.security.service.business.transfer.TransportCompareResultService;
import com.bsm.v4.system.model.dto.business.transfer.TransportCompareResultDTO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TransportCompareResultWebService extends BasicWebService {

    @Autowired
    private TransportCompareResultService transportCompareResultService;

    /**
     * 返回各比对结果数据的条数
     */
    public Map<String, Object> getCount(String jobId) {
        Map<String, Integer> countMap = new HashMap();
        List<Integer> count = transportCompareResultService.getCount(jobId);
        for (int i = 0; i < count.size(); i++) {
            switch (i) {
                case 0:
                    countMap.put("add", count.get(i));
                    break;
                case 1:
                    countMap.put("edit", count.get(i));
                    break;
                case 2:
                    countMap.put("del", count.get(i));
                    break;
                case 3:
                    countMap.put("nor", count.get(i));
                    break;
            }
        }
        return this.basicReturnSuccess(countMap);
    }

    /**
     * 审核后查看比对详情
     */
    public Map<String, Object> findOneDetail(String guid) {
        return this.basicReturnSuccess(transportCompareResultService.findOneDetail(guid));
    }

    /**
     * 审核前查看比对详情
     */
    public Map<String, Object> findDetail(String guid) {
        return this.basicReturnSuccess(transportCompareResultService.findDetail(guid));
    }

    /**
     * 分页查看比对结果列表
     */
    public Map<String, Object> getCompareVOListByPage(TransportCompareResultDTO dto) {
        PageInfo<TransportCompareResultDTO> transportCompareResultDTOList = new PageInfo<>();
        if ("scheduleLog".equals(dto.getType()))
            transportCompareResultDTOList = new PageHandle(dto).buildPage(transportCompareResultService.findAllPageByAppGuid(dto.getAppGuid(), dto.getResultType()));
        if ("schedule".equals(dto.getType()))
            transportCompareResultDTOList = new PageHandle(dto).buildPage(transportCompareResultService.findAllPageScheduleByAppGuid(dto.getAppGuid(), dto.getResultType()));
        return this.basicReturnSuccess(transportCompareResultDTOList);
    }

    /**
     * 根据jobId删除数据
     */
    public int deleteByJobGuid(String jobGuid) {
        return transportCompareResultService.deleteByJobGuid(jobGuid);
    }
}
