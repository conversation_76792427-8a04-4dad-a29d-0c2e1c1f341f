package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtStationTService;
import com.bsm.v4.system.model.entity.business.station.RsbtStationT;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtStationTWebService extends BasicWebService {

    @Autowired
    private RsbtStationTService rsbtStationTService;

    /**
     * 批量添加
     */
    public void insertBatch(List<RsbtStationT> rsbtStationTList) {
        if (rsbtStationTList.size() > 30) {
            int pageNum = rsbtStationTList.size() % 30 == 0 ? rsbtStationTList.size() / 30 : rsbtStationTList.size() / 30 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtStationT> stations;
                if (i != pageNum) {
                    stations = rsbtStationTList.subList((i - 1) * 30, i * 30);
                } else {
                    stations = rsbtStationTList.subList((i - 1) * 30, rsbtStationTList.size());
                }
                rsbtStationTService.insertBatch(stations);
            }
        } else {
            rsbtStationTService.insertBatch(rsbtStationTList);
        }
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtStationT> rsbtStationTList) {
        return rsbtStationTService.updateBatch(rsbtStationTList);
    }
}
