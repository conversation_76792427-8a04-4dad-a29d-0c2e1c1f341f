package com.bsm.v4.api.web.service.bussiness.transfer_in;

import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transfer.TransportJobService;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.TransportJobInDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
@Service
public class TransportJobInWebService extends BasicWebService {

    @Autowired
    private TransportJobService transportJobService;
    @Autowired
    private AuthWebService authWebService;

    /**
     * 分页查询
     */
    public Map<String, Object> findAllPage(TransportJobInDTO transportJobInDTO, String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        if ("5200".equals(usersDTO.getRegionId())) {
            usersDTO.setRegionId("");
        }
        return this.basicReturnResultJson(new PageHandle(transportJobInDTO).buildPage(transportJobService.findInAllPage(transportJobInDTO, usersDTO)));
    }

    /**
     * 申请表管理
     *
     * @param token token
     * @return list
     */
    public Map<String, Object> findApplicationListByPage(String token) {
        TransportJobDTO transportJobDTO = new TransportJobDTO();
        if (StringUtils.isNotEmpty(token)) {
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            PageInfo<TransportJobDTO> transportJobDTOList;
            if ("wuwei".equals(usersDTO.getType())) {
                // 无委
                if ("2".equals(usersDTO.getRoleDTO().getType())) {
                    // 地市无委
                    transportJobDTOList = new PageHandle(transportJobDTO).buildPage(transportJobService.findJobPageByBranchCode(usersDTO.getOrgDTO().getOrgAreaCode()));
                } else {
                    // 省级无委
                    transportJobDTOList = new PageHandle(transportJobDTO).buildPage(transportJobService.findAllPage(transportJobDTO, null));
                }
            } else {
                // 运营商
                transportJobDTOList = new PageHandle(transportJobDTO).buildPage(transportJobService.findAllPage(transportJobDTO, usersDTO.getUserId()));
            }
            return this.basicReturnResultJson(transportJobDTOList);
        }
        return this.basicReturnFailure("操作失败,请重新登录");
    }
}
