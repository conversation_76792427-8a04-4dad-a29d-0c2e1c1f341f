package com.bsm.v4.api.web.utils;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;
import com.bsm.v4.system.model.enums.AntennaModelEnum;
import com.bsm.v4.system.model.enums.PolarizationModeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class BtsDataUtil {

    /**
     * 检查导入一条csv数据是否有效
     *
     * @param transportRawBts
     * @return 具体错误原因
     */
    public static Map<String, Object> validParameter(TransportRawBts transportRawBts, JSONObject netTsJson, String orgRegionJson) {
        Map<String, Object> map = new HashMap<>();
        List<String> list = new ArrayList<>();
        String isValid = "1";

        //获取所有区域
        List<RegionDTO> orgRegionList = JSONObject.parseArray(orgRegionJson, RegionDTO.class);

        Pattern isNum = Pattern.compile("^\\d+(\\.\\d+)?$");
        if (transportRawBts.getCellName() != null && !"".equals(transportRawBts.getCellName().trim())) {
            if (transportRawBts.getCellName().length() > 40) {
                list.add("CellName(" + transportRawBts.getCellName() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("CellName不能为空");
            isValid = "4";
        }

        if (transportRawBts.getCellId() != null && !"".equals(transportRawBts.getCellId().trim())) {
            //组成：MCC－MNC－LAC－CI   xxx-xx-xxxxx-xxxxx
            /*String[] split = transportRawBts.getCellId().split("-");
            if(split.length<4){
                list.add("CellId(" + transportRawBts.getCellId() + ")组成规则不符合国家规定");
                isValid = "4";
            }
            if(split[0].length()!=3){
                list.add("CellId(" + transportRawBts.getCellId() + ")的MCC组成规则不符合国家规定");
                isValid = "4";
            }
            if(split[1].length()!=2){
                list.add("CellId(" + transportRawBts.getCellId() + ")的MNC组成规则不符合国家规定");
                isValid = "4";
            }
            if(split[2].length()>5){
                list.add("CellId(" + transportRawBts.getCellId() + ")的LAC组成规则不符合国家规定");
                isValid = "4";
            }
            if(split[3].length()>5){
                list.add("CellId(" + transportRawBts.getCellId() + ")的CI组成规则不符合国家规定");
                isValid = "4";
            }*/
            if (transportRawBts.getCellId().length() > 50) {
                list.add("CellId(" + transportRawBts.getCellId() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("CellId不能为空");
            isValid = "4";
        }

        if (transportRawBts.getBtsName() != null && !"".equals(transportRawBts.getBtsName().trim())) {
            if (transportRawBts.getBtsName().length() > 40) {
                String btsName = transportRawBts.getBtsName().substring(0, 40);
                transportRawBts.setBtsName(btsName);
                list.add("BtsName(" + transportRawBts.getBtsName() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("BtsName不能为空");
            isValid = "4";
        }

        if (transportRawBts.getBtsId() != null && !"".equals(transportRawBts.getBtsId().trim())) {
            if (transportRawBts.getBtsId().length() > 50) {
                list.add("BtsId(" + transportRawBts.getBtsId() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("BtsId不能为空");
            isValid = "4";
        }

        if (transportRawBts.getTechType() != null && !"".equals(transportRawBts.getTechType().trim())) {
            if (transportRawBts.getTechType().length() > 60) {
                list.add("techType(" + transportRawBts.getTechType() + ")长度超过数据库规定长度");
                isValid = "4";
            }
            if (netTsJson.getString(transportRawBts.getTechType()) == null || "".equals(netTsJson.getString(transportRawBts.getTechType()))) {
                list.add("techType(" + transportRawBts.getTechType() + ")为非法值");
                isValid = "4";
            }
        } else {
            list.add("techType不能为空");
            isValid = "4";
        }

        if (transportRawBts.getLocation() != null && !"".equals(transportRawBts.getLocation().trim())) {
            if (transportRawBts.getLocation().length() > 70) {
                list.add("Location(" + transportRawBts.getLocation() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("Location不能为空");
            isValid = "4";
        }

        if (transportRawBts.getLongitude() != null && !"".equals(transportRawBts.getLongitude().trim())) {
            if (transportRawBts.getLongitude().length() > 70) {
                list.add("Longitude(" + transportRawBts.getLongitude() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getLongitude()).matches()) {
                list.add("Longitude(" + transportRawBts.getLongitude() + ")中出现非数字字符");
                isValid = "4";
            } else if (transportRawBts.getLongitude().contains("\\.") && transportRawBts.getLongitude().split("\\.")[1].length() > 7) {
                list.add("Longitude(" + transportRawBts.getLongitude() + ")小数长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("Longitude不能为空");
            isValid = "4";
        }

        if (transportRawBts.getLatitude() != null && !"".equals(transportRawBts.getLatitude().trim())) {
            if (transportRawBts.getLatitude().length() > 70) {
                list.add("Latitude(" + transportRawBts.getLatitude() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getLatitude()).matches()) {
                list.add("Latitude(" + transportRawBts.getLatitude() + ")中出现非数字字符");
                isValid = "4";
            } else if (transportRawBts.getLatitude().contains("\\.") && transportRawBts.getLatitude().split("\\.")[1].length() > 7) {
                list.add("Latitude(" + transportRawBts.getLatitude() + ")小数长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("Latitude不能为空");
            isValid = "4";
        }

        if (transportRawBts.getSendStartFreq() != null && !"".equals(transportRawBts.getSendStartFreq())) {
            if (transportRawBts.getSendStartFreq().length() > 50) {
                list.add("SendStartFreq长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getSendStartFreq()).matches()) {
                list.add("SendStartFreq中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("SendStartFreq不能为空");
            isValid = "4";
        }

        if (transportRawBts.getSendStartFreq() != null && !"".equals(transportRawBts.getSendStartFreq().trim())) {
            if (transportRawBts.getSendStartFreq().length() > 50) {
                list.add("SendStartFreq(" + transportRawBts.getSendStartFreq() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getSendStartFreq()).matches()) {
                list.add("SendStartFreq(" + transportRawBts.getSendStartFreq() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("SendStartFreq不能为空");
            isValid = "4";
        }

        if (transportRawBts.getSendEndFreq() != null && !"".equals(transportRawBts.getSendEndFreq().trim())) {
            if (transportRawBts.getSendEndFreq().length() > 50) {
                list.add("SendEndFreq(" + transportRawBts.getSendEndFreq() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getSendEndFreq()).matches()) {
                list.add("SendEndFreq(" + transportRawBts.getSendEndFreq() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("SendEndFreq不能为空");
            isValid = "4";
        }

        if (transportRawBts.getAccStartFreq() != null && !"".equals(transportRawBts.getAccStartFreq().trim())) {
            if (transportRawBts.getAccStartFreq().length() > 50) {
                list.add("AccStartFreq(" + transportRawBts.getAccStartFreq() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getAccStartFreq()).matches()) {
                list.add("AccStartFreq(" + transportRawBts.getAccStartFreq() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("AccStartFreq不能为空");
            isValid = "4";
        }

        if (transportRawBts.getAccEndFreq() != null && !"".equals(transportRawBts.getAccEndFreq().trim())) {
            if (transportRawBts.getAccEndFreq().length() > 50) {
                list.add("AccEndFreq(" + transportRawBts.getAccEndFreq() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getAccEndFreq()).matches()) {
                list.add("AccEndFreq(" + transportRawBts.getAccEndFreq() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("AccEndFreq不能为空");
            isValid = "4";
        }

        if (transportRawBts.getMaxEmissivePower() != null && !"".equals(transportRawBts.getMaxEmissivePower())) {
            if (transportRawBts.getMaxEmissivePower().length() > 50) {
                list.add("MaxEmissivePower(" + transportRawBts.getMaxEmissivePower() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getMaxEmissivePower()).matches()) {
                list.add("MaxEmissivePower(" + transportRawBts.getMaxEmissivePower() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("MaxEmissivePower不能为空");
            isValid = "4";
        }

        if (transportRawBts.getHeight() != null && !"".equals(transportRawBts.getHeight())) {
            if (transportRawBts.getHeight().length() > 50) {
                list.add("Height(" + transportRawBts.getHeight() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getHeight()).matches()) {
                list.add("Height(" + transportRawBts.getHeight() + ")中出现非数字字符");
                isValid = "4";
            } else if (isNum.matcher(transportRawBts.getHeight()).matches() &&
                    (Double.parseDouble(transportRawBts.getHeight()) < 0 ||
                            Double.parseDouble(transportRawBts.getHeight()) > 200)) {
                list.add("Height(" + transportRawBts.getHeight() + ")不在规定范围内");
                isValid = "4";
            }
        } else {
            list.add("Height不能为空");
            isValid = "4";
        }

        String county = transportRawBts.getCounty();
        if (county != null && !"".equals(county)) {
            if (transportRawBts.getCounty().length() > 25) {
                list.add("County(" + transportRawBts.getCounty() + ")长度超过数据库规定长度");
                isValid = "4";
            }
            //检验地区，全称转换
            String checkoutCounty = "地区(" + transportRawBts.getCounty() + ")未识别";
            for (RegionDTO regionDTO : orgRegionList) {
                String regionName = regionDTO.getName();
                if (regionName.contains(county)) {
                    checkoutCounty = regionName;
                    transportRawBts.setCounty(regionName);
                    break;
                }
            }
            if (("地区(" + transportRawBts.getCounty() + ")未识别").equals(checkoutCounty)) {
                list.add(checkoutCounty);
                isValid = "4";
            }
        } else {
            list.add("County不能为空");
            isValid = "4";
        }

        if (transportRawBts.getVendorName().length() > 50) {
            list.add("VendorName(" + transportRawBts.getVendorName() + ")长度超过数据库规定长度");
            isValid = "4";
        }

        if (transportRawBts.getDeviceModel().length() > 50) {
            list.add("DeviceModel(" + transportRawBts.getDeviceModel() + ")长度超过数据库规定长度");
            isValid = "4";
        }

        if (StringUtils.isNotEmpty(transportRawBts.getModelCode())) {

            if (transportRawBts.getModelCode().length() == 10 || transportRawBts.getModelCode().length() == 11) {
                // 生产年份
                String year = transportRawBts.getModelCode().substring(0, 4);
                // 设备类型
                String deviceCode = transportRawBts.getModelCode().substring(4, 5);
                // 产地标识
                String chandiCode = transportRawBts.getModelCode().substring(5, 6);
                // 序号
                String no = transportRawBts.getModelCode().substring(6);

                String[] codes = new String[]{"A", "C", "D", "F", "G", "H", "L", "S", "W", "Z"};
                List<String> codeList = Arrays.asList(codes);
                // 判断
                if (!isNumeric(year) || !isNumeric(no) || (!chandiCode.equals("J") && !chandiCode.equals("P"))
                        || !codeList.contains(deviceCode)) {
                    list.add("ModelCode(" + transportRawBts.getModelCode() + ")不符合标准");
                    isValid = "4";
                }
            } else {
                list.add("ModelCode(" + transportRawBts.getModelCode() + ")长度不符合规定");
                isValid = "4";
            }
        } else {
            list.add("ModelCode不能为空");
            isValid = "4";
        }

        if (transportRawBts.getAntennaGain() != null && !"".equals(transportRawBts.getAntennaGain())) {
            if (transportRawBts.getAntennaGain().length() > 50) {
                list.add("AntennaGain(" + transportRawBts.getAntennaGain() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getAntennaGain()).matches()) {
                list.add("AntennaGain(" + transportRawBts.getAntennaGain() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("AntennaGain不能为空");
            isValid = "4";
        }

        if (transportRawBts.getAntennaModel() != null && !"".equals(transportRawBts.getAntennaModel())) {
            if (transportRawBts.getAntennaModel().length() > 36) {
                list.add("AntennaModel(" + transportRawBts.getAntennaModel() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("AntennaModel不能为空");
            isValid = "4";
        }

        if (transportRawBts.getAntennaFactory() != null && !"".equals(transportRawBts.getAntennaFactory())) {
            if (transportRawBts.getAntennaFactory().length() > 36) {
                list.add("AntennaFactory(" + transportRawBts.getAntennaFactory() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        }

        if (transportRawBts.getPolarizationMode() != null && !"".equals(transportRawBts.getPolarizationMode())) {
            if (transportRawBts.getPolarizationMode().length() > 36) {
                list.add("PolarizationMode(" + transportRawBts.getPolarizationMode() + ")长度超过数据库规定长度");
                isValid = "4";
            }
        } else {
            list.add("PolarizationMode不能为空");
            isValid = "4";
        }

        if (transportRawBts.getAntennaAzimuth() != null && !"".equals(transportRawBts.getAntennaAzimuth())) {
            if (transportRawBts.getAntennaAzimuth().length() > 36) {
                list.add("AntennaAzimuth(" + transportRawBts.getAntennaAzimuth() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getAntennaAzimuth()).matches()) {
                list.add("AntennaAzimuth(" + transportRawBts.getAntennaAzimuth() + ")中出现非数字字符");
                isValid = "4";
            }
        }

        if (transportRawBts.getFeederLoss() != null && !"".equals(transportRawBts.getFeederLoss())) {
            if (transportRawBts.getFeederLoss().length() > 36) {
                list.add("FeederLoss(" + transportRawBts.getFeederLoss() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getFeederLoss()).matches()) {
                list.add("FeederLoss(" + transportRawBts.getFeederLoss() + ")中出现非数字字符");
                isValid = "4";
            }
        }

        if (transportRawBts.getAltitude() != null && !"".equals(transportRawBts.getAltitude())) {
            if (transportRawBts.getAltitude().length() > 36) {
                list.add("Altitude(" + transportRawBts.getAltitude() + ")长度超过数据库规定长度");
                isValid = "4";
            } else if (!isNum.matcher(transportRawBts.getAltitude()).matches()) {
                list.add("Altitude(" + transportRawBts.getAltitude() + ")中出现非数字字符");
                isValid = "4";
            }
        } else {
            list.add("Altitude不能为空");
            isValid = "4";
        }

//        台址	VARCHAR2(80)	暂无
//        经度	NUMBER（10,7）	暂无
//        纬度	NUMBER（10,7）	暂无

//        最大发射功率	NUMBER（14,7）	小于100W
        if (!StringUtils.isBlank(transportRawBts.getMaxEmissivePower())) {
            try {
                double power = Double.valueOf(transportRawBts.getMaxEmissivePower());
                if (power < 0 || power > 500) {
                    list.add("最大发射功率(" + transportRawBts.getMaxEmissivePower() + ")错误，0-500");
                    isValid = "4";
                }
            } catch (Exception e) {
                list.add("最大发射功率(" + transportRawBts.getMaxEmissivePower() + ")错误，0-500");
                isValid = "4";
            }
        }
//        天线距地高度	NUMBER（7,3）	暂无
//        台站类别	VARCHAR2(8)	暂无（蜂窝网基站）
//        对应频率许可证编号	VARCHAR2(40)	暂无
//        起始日期	date	暂无
//        截止日期	date	暂无

        //修改类型只能为一位数字
//        try {
//            Integer dataType = Integer.parseInt(transportRawBts.getDataType());
//            if (!TransportBtsDataTypeConst.isValid(dataType)) {
//                return "数据变更类型错误，1：新增；2：变更；3：注销。";
//            }
//        } catch (Exception e) {
//            return "数据变更类型错误，1：新增；2：变更；3：注销。";
//        }

        //天线类型 判断并转义
        int isAntennaModel = 0;
        if (!StringUtils.isBlank(transportRawBts.getAntennaModel())) {
            for (AntennaModelEnum antennaModelEnum : AntennaModelEnum.values()) {
                if (antennaModelEnum.getType().equalsIgnoreCase(transportRawBts.getAntennaModel()) || antennaModelEnum.getName().equals(transportRawBts.getAntennaModel())) {
                    transportRawBts.setAntennaModel(antennaModelEnum.getType());
                    isAntennaModel = 1;
                }
            }
        }
        if (isAntennaModel == 0) {
            list.add("天线类型数据格式不正确");
            isValid = "4";
        }

        //极化方式 判断并转义
        int isPolarizationModel = 0;
        if (!StringUtils.isBlank(transportRawBts.getPolarizationMode())) {
            for (PolarizationModeEnum polarizationModeEnum : PolarizationModeEnum.values()) {
                if (polarizationModeEnum.getType().equalsIgnoreCase(transportRawBts.getPolarizationMode()) || polarizationModeEnum.getName().equals(transportRawBts.getPolarizationMode())) {
                    transportRawBts.setPolarizationMode(polarizationModeEnum.getType());
                    isPolarizationModel = 1;
                }
            }
        }
        if (isPolarizationModel == 0) {
            list.add("极化方式数据格式不正确");
            isValid = "4";
        }

        map.put("isValid", isValid);
        map.put("parameterErrorMessage", list);
        return map;
    }

    private static boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

}
