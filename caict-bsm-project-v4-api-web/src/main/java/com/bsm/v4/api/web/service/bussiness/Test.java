package com.bsm.v4.api.web.service.bussiness;

import com.bsm.v4.api.web.service.FlowBean;
import com.caictframework.bigdata.flink.FlinkClient;
import org.apache.hadoop.io.Text;
import org.apache.hadoop.mapreduce.Partitioner;

/**
 * @author: ycp
 * @createTime: 2023/09/06 16:30
 * @company: 成渝（成都）信息通信研究院
 * @description:
 */
public class Test extends Partitioner<Text, FlowBean> {

    @Override
    public int getPartition(Text text, FlowBean flowBean, int i) {
        return 0;
    }

    public void aaa(){
        FlinkClient flinkClient = new FlinkClient();
    }
}
