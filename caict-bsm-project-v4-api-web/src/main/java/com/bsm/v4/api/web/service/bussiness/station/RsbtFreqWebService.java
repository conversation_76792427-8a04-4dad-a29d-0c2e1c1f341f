package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtFreqService;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtFreqWebService extends BasicWebService {

    @Autowired
    private RsbtFreqService rsbtFreqService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtFreq> rsbtFreqListInsert) {
        return rsbtFreqService.insertBatch(rsbtFreqListInsert);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtFreq> rsbtFreqListUpdate) {
        return rsbtFreqService.updateBatch(rsbtFreqListUpdate);
    }

    /**
     * 批量删除
     */
    public int deleteBatch(List<RsbtFreq> rsbtFreqListUpdate) {
        return rsbtFreqService.deleteBatch(Collections.singletonList(rsbtFreqListUpdate));
    }


    /**
     * 根据基站guid和扇区id查询
     */
    public RsbtFreq findOneByStationCell(String stationGuid, String cellId) {
        return rsbtFreqService.findOneByStationCell(stationGuid, cellId);
    }
}
