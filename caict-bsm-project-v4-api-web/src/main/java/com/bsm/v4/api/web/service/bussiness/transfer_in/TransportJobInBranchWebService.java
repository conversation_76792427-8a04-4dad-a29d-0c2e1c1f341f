package com.bsm.v4.api.web.service.bussiness.transfer_in;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.*;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.api.web.utils.ExcelHelperUtil;
import com.bsm.v4.api.web.utils.ZipCompressor;
import com.bsm.v4.domain.security.service.business.license.*;
import com.bsm.v4.domain.security.service.business.station.*;
import com.bsm.v4.domain.security.service.business.transfer.*;
import com.bsm.v4.domain.security.service.security.RegionService;
import com.bsm.v4.system.model.contrust.transfer.ApplyLinkStateConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobInBranchConst;
import com.bsm.v4.system.model.contrust.transfer.TransportJobInConst;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import com.bsm.v4.system.model.dto.business.applytable.PointDTO;
import com.bsm.v4.system.model.dto.business.applytable.PointDTO2;
import com.bsm.v4.system.model.dto.business.station.SectionDTO;
import com.bsm.v4.system.model.dto.business.station.StationBakDTO;
import com.bsm.v4.system.model.dto.business.station.StationCountyNumDTO;
import com.bsm.v4.system.model.dto.business.station.StationDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.PackageDataDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.SchedualDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.TransportJobInBranchDTO;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import com.caictframework.utils.util.VerificationCode;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */
@Service
public class TransportJobInBranchWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(TransportJobInBranchWebService.class);

    @Autowired
    private TransportJobWebService transportJobWebService;
//    @Autowired
//    private StanderSyncWebService standerSyncWebService;
    @Autowired
    private TransportJobBranchService transportJobBranchService;
    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;
    @Autowired
    private ApprovalScheduleLogWebService approvalScheduleLogWebService;
    @Autowired
    private RsbtStationBakService stationBakService;
    @Autowired
    private RsbtStationService stationService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private ApplyLinkWebService applyLinkWebService;
    @Autowired
    private AuthWebService authWebService;
    @Autowired
    private ApplyJobInWebService applyJobInWebService;
    @Autowired
    private RegionService fsaRegionService;
    @Autowired
    private AsyncRawBtsWebService asyncRawBtsWebService;
    @Autowired
    private TransportFileWebService transportFileWebService;
    @Autowired
    private TransportJobService transportJobService;
    @Value("${caict.myFileExportPath}")
    private String serverExportPath;

    /**
     * 无委代办查询
     *
     * @param token token
     * @return json
     */
    public Map<String, Object> findSchedual(String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        List<SchedualDTO> schedualDTOs;
        if ("wuwei".equals(usersDTO.getType()) && "32".equals(usersDTO.getRegionId())) {
            schedualDTOs = transportJobBranchService.findSchedualP(usersDTO.getRegionId());
        } else {
            schedualDTOs = transportJobBranchService.findSchedual(usersDTO.getRegionId());
        }
        if (schedualDTOs != null && schedualDTOs.size() != 0) {
            schedualDTOs.removeIf(schedualDTO -> schedualDTO.getTransportJobBranchList().size() == 0);
        }
        return this.basicReturnSuccess(schedualDTOs);
    }

    /**
     * 点击待办菜单时与上一个任务组装数据
     */
    public JSONObject packageData() {
        //首先查询当前正在处理的job
        List<PackageDataDTO> schedualDTOs = transportJobBranchService.findSchedualPackage();
        if (schedualDTOs != null && schedualDTOs.size() != 0) {
            //用userType进行分组
            Map<String, List<PackageDataDTO>> collect = schedualDTOs.stream().collect(Collectors.groupingBy(PackageDataDTO::getUserType));
            List<TransportJob> transportJobList = new ArrayList<>();
            for (Map.Entry<String, List<PackageDataDTO>> entry : collect.entrySet()) {
                PackageDataDTO value = entry.getValue().get(0);
                List<ApprovalTransportJob> notPassList = approvalTransportJobWebService.findNotPassList(String.valueOf(value.getGuid()), null);
                TransportJob transportJob = new TransportJob();
                if (notPassList.size() == 0) {
                    BeanUtils.copyProperties(value, transportJob);
                    transportJob.setIsCompare(TransportJobInConst.JOBIN_COMPLETE);
                    transportJobList.add(transportJob);
                }
            }

            //查询上一次已处理完成的并且未合并过数据的job
            List<PackageDataDTO> schedualPackage2 = transportJobBranchService.findSchedualPackage2();
            if (schedualPackage2 != null && schedualPackage2.size() != 0) {
                for (PackageDataDTO packageDataDTO : schedualPackage2) {
                    //查询各运营商下分任务是否有审核不同意的
                    List<ApprovalTransportJob> notPassList = approvalTransportJobWebService.findNotPassList(String.valueOf(packageDataDTO.getGuid()), "1");
                    List<PackageDataDTO> packageDataDTOS = collect.get(packageDataDTO.getUserType());
                    //如果有审核不通过的数据
                    if (notPassList != null && notPassList.size() != 0) {
                        for (ApprovalTransportJob approvalTransportJob : notPassList) {
                            //获取审核不通过的基站数据
                            List<RsbtStationBak> stationBaks = stationBakService.findNotPassData(approvalTransportJob.getGuid());
                            //判断生成新的approval_transport_job
                            createNewApprovalJob(String.valueOf(packageDataDTOS.get(0).getGuid()), approvalTransportJob, stationBaks);
                        }
                        //数据处理完成后，将本次处理任务进行已处理标记
                        transportJobWebService.updateIsDeleted(String.valueOf(packageDataDTO.getGuid()), "1");
                    }
                }
            }
            //处理完成之后判断是否全为不变数据，如果全为不变数据则将transport_job状态改为完成
            if (transportJobList.size() != 0) {
                transportJobService.updateBatch(transportJobList);
            }
        }
        return JSONResult.getSuccessJson("数据处理完成");
    }

    private void createNewApprovalJob(String jobGuid, ApprovalTransportJob approvalTransportJob, List<RsbtStationBak> stationBaks) {
        //先查询本次任务有无跟上次审核不通过的任务相同的运营商地区代数，有则直接添加，无则新创建approvalJob
        ApprovalTransportJob approvalJobNow = approvalTransportJobWebService.findByDetail(jobGuid, approvalTransportJob.getDataType(), approvalTransportJob.getGenNum(), approvalTransportJob.getRegionCode(), approvalTransportJob.getTechType());
        List<RsbtStationBak> insertList = new ArrayList<>();
        List<RsbtStationBak> updateList = new ArrayList<>();
        if (approvalJobNow == null) {
            ApprovalTransportJob approvalTransportJob1 = new ApprovalTransportJob();
            BeanUtils.copyProperties(approvalTransportJob, approvalTransportJob1);
            String appGuid = VerificationCode.myUUID();
            approvalTransportJob1.setGuid(appGuid);
            approvalTransportJob1.setJobGuid(jobGuid);
            approvalTransportJob1.setGmtCreate(new Date());
            approvalTransportJob1.setGmtModified(new Date());
            approvalTransportJob1.setOpUser("1");
            approvalTransportJob1.setOpDetail("运营商未修改不同意的数据");
            String appCode = VerificationCode.codeGet(10);
            approvalTransportJob1.setAppCode(appCode);
            approvalTransportJob1.setIsCompare("19");
            for (RsbtStationBak stationBak : stationBaks) {
                stationBak.setGuid(VerificationCode.myUUID());
                stationBak.setIsShow("3");
                stationBak.setAppGuid(appGuid);
                stationBak.setAppCode(appCode);
                stationBak.setBakDate(new Date());
                stationBak.setStatDateStart(new Date());
                insertList.add(stationBak);
            }
            approvalTransportJobWebService.save(approvalTransportJob1);
            // 添加job_branch
            TransportJobBranch transportJobBranch = new TransportJobBranch();
            BeanUtils.copyProperties(approvalTransportJob1, transportJobBranch);
            transportJobBranch.setCellCount(String.valueOf(approvalTransportJob1.getIdenCount()));
            transportJobBranch.setIsCompare("20");
            transportJobBranchService.insert(transportJobBranch);
            addScheduleLog(approvalTransportJob1.getGuid(), approvalTransportJob.getGuid());
        } else {
            //再判断此次待办任务中是否包含上次审核不通过数据
            for (RsbtStationBak stationBak : stationBaks) {
                RsbtStationBak rsbtStationBak = stationBakService.findByCondition(approvalJobNow.getGuid(), stationBak.getStCCode());
                if (rsbtStationBak == null) {
                    stationBak.setGuid(VerificationCode.myUUID());
                    stationBak.setAppGuid(approvalJobNow.getGuid());
                    insertList.add(stationBak);
                } else {
                    rsbtStationBak.setIsShow("3");
                    updateList.add(rsbtStationBak);
                }
            }

        }
        if (insertList.size() != 0) {
            stationBakService.insertBatch(insertList);
        }
        if (updateList.size() != 0) {
            stationBakService.updateBatch(updateList);
        }

    }

    /**
     * 根据appGuid插入log数据 解决审核变更对比扇区信息不全
     *
     * @param newAppGuid 新生成的appGuid
     * @param oldAppGuid 以前存在的appGuid
     */
    private void addScheduleLog(String newAppGuid, String oldAppGuid) {
        approvalTransportJobWebService.insertApprovalScheduleLog(newAppGuid, oldAppGuid);
    }

    public Map<String, Object> findListByPage(TransportJobInBranchDTO transportJobInBranchDTO, String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        return this.basicReturnSuccess(new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findListByPage(transportJobInBranchDTO, usersDTO)));
    }


    public JSONObject findDetail(String guid) {
        TransportJobInBranchDTO transportJobInBranchDTO = transportJobBranchService.findDetail(guid);
        //点击查看详情的时候先生成申请表
//        List<RsbtApplyDTO> applyDTOs = createApply(transportJobInBranchDTO);
//        transportJobInBranchDTO.setApplyDTOList(applyDTOs);
        return JSONResult.getSuccessJson(transportJobInBranchDTO);
    }

    public Map<String, Object> findStationListByGuid(TransportJobInBranchDTO transportJobInBranchDTO) {
        PageInfo<StationBakDTO> transportJobInBranchDTOList = new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findStationListByGuid(transportJobInBranchDTO));
        if (transportJobInBranchDTOList.getList() != null) {
            List<StationBakDTO> list = transportJobInBranchDTOList.getList();
            for (StationBakDTO stationBakDTO : list) {
                if (!stationBakDTO.getStatAddr().contains(stationBakDTO.getCounty().substring(0, stationBakDTO.getCounty().length() - 1))) {
                    stationBakDTO.setStatAddr(stationBakDTO.getCounty() + stationBakDTO.getStatAddr());
                }
            }
            transportJobInBranchDTOList.setList(list);
        }
        return this.basicReturnSuccess(transportJobInBranchDTOList);
    }

    public Map<String, Object> findUpdateList(StationBakDTO stationBakDTO) {
        List<StationBakDTO> updateList = transportJobBranchService.findUpdateList(stationBakDTO);
        if (updateList.size() != 0) {
            for (StationBakDTO stationBak : updateList) {
                List<SectionDTO> sectionList = approvalScheduleLogWebService.findSectionList(stationBak.getAppGuid(), stationBak.getStCCode());
                stationBak.setSectionDTOS(sectionList);
            }
        }
        return this.basicReturnSuccess(updateList);
    }

    public JSONObject commit(TransportJobInBranchDTO transportJobInBranchDTO) {
        try {
            //获得频率台站库最大的申请表编号
            // todo
//            String appCode = standerSyncWebService.getAppCode();
            //判断任务状态是否是待提交
            TransportJobBranch transportJobInBranch = new TransportJobBranch();
            transportJobInBranch.setGuid(transportJobInBranchDTO.getGuid());
            TransportJobBranch transportJobInBranchServiceById = transportJobBranchService.findById(transportJobInBranch);
            if (!TransportJobInBranchConst.BRANCHIN_APPLY_WAIT.equals(transportJobInBranchServiceById.getIsCompare())) {
                return JSONResult.getSuccessJson("当前任务不可提交！");
            }
            //将需要审核的任务修改为生成申请表中
            transportJobInBranchServiceById.setIsCompare(TransportJobInBranchConst.BRANCHIN_APPLYING);
            transportJobInBranchServiceById.setOpDetail(transportJobInBranchDTO.getPoinion());
            transportJobBranchService.update(transportJobInBranchServiceById);
            //将审核的数据放入rabbitmq中 todo
//            sendRabbit(taskData, "数据推送", transportJobInBranchServiceById.getAppCode(), null, RabbitMessageConst.APPCODE_UPDATE, appCode);
            return JSONResult.getSuccessJson("已提交处理，请稍后");
        } catch (Exception e) {
            //失败时将任务修改为生成申请表失败
            TransportJobBranch transportJobInBranch = new TransportJobBranch();
            transportJobInBranch.setGuid(transportJobInBranchDTO.getGuid());
            transportJobInBranch.setIsCompare(TransportJobInBranchConst.BRANCHIN_APPLY_FAIL);
            transportJobBranchService.update(transportJobInBranch);
            //恢复数据为审核之前的状态
        }
        return null;
    }

    /**
     * 不通过，不生成国申请表
     *
     * @param transportJobInBranchDTO transportJobInBranchDTO
     * @return json
     */
    public JSONObject failToApply(TransportJobInBranchDTO transportJobInBranchDTO) {
        //获取当前任务
        ApprovalTransportJob approvalTransportJob = approvalTransportJobWebService.findById(transportJobInBranchDTO.getAppGuid());
        approvalTransportJob.setGmtModified(new Date());
        // 未审核
        int unCheckCount = stationBakService.findUnCheckNumByAppGuidAndIsShow(transportJobInBranchDTO.getAppGuid());
        // 总数
        int allCount = stationBakService.findAllNumByAppGuidAndIsShow(transportJobInBranchDTO.getAppGuid());
        // 已审核
        int checkCount = stationBakService.findCheckByAppGuidAndIsShow(transportJobInBranchDTO.getAppGuid());
        // 更新主任务状态
        TransportJob transportJob = transportJobService.findById(approvalTransportJob.getJobGuid());
        // 是否有不通过 标识 1,是  2,否
        approvalTransportJob.setOpUser("1");
        List<RsbtStationBak> stationBakList;
        if ((transportJobInBranchDTO.getGuids() == null || transportJobInBranchDTO.getGuids().size() == 0)) {
            TransportJobBranchDTO jobBranchDTO = transportJobBranchService.findOneByJobUserTypeGen(approvalTransportJob.getJobGuid(), null,
                    approvalTransportJob.getDataType(), approvalTransportJob.getGenNum(), approvalTransportJob.getRegionCode(), approvalTransportJob.getTechType());
            TransportJobBranch branch = new TransportJobBranch();
            BeanUtils.copyProperties(jobBranchDTO, branch);
            if (unCheckCount + checkCount == allCount) {
                if (checkCount != 0) {
                    branch.setIsCompare(TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
                    approvalTransportJob.setIsCompare(TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
                } else {
                    branch.setIsCompare(TransportJobInBranchConst.BRANCHIN_GIVEUP);
                    approvalTransportJob.setOpDetail("无委审核不通过");
                    approvalTransportJob.setIsCompare(TransportJobInBranchConst.BRANCHIN_GIVEUP);
                }
            }
            transportJobBranchService.update(branch);
            LOG.info("无委审核全部不通过.");
            stationBakList = stationBakService.findByAppGuidAndIsShow(approvalTransportJob.getGenNum(), approvalTransportJob.getDataType(), transportJobInBranchDTO.getAppGuid());
        } else {
            TransportJobBranchDTO jobBranchDTO = transportJobBranchService.findOneByJobUserTypeGen(approvalTransportJob.getJobGuid(), null,
                    approvalTransportJob.getDataType(), approvalTransportJob.getGenNum(), approvalTransportJob.getRegionCode(), approvalTransportJob.getTechType());
            TransportJobBranch branch = new TransportJobBranch();
            BeanUtils.copyProperties(jobBranchDTO, branch);
            if (transportJobInBranchDTO.getGuids().size() == unCheckCount) {
                if (approvalTransportJob.getIsCompare().equals("12")) {
                    branch.setIsCompare(TransportJobInBranchConst.BRANCHIN_GIVEUP);
                    approvalTransportJob.setOpDetail("无委审核不通过");
                    approvalTransportJob.setIsCompare(TransportJobInBranchConst.BRANCHIN_GIVEUP);
                } else if (checkCount != 0 && (checkCount + unCheckCount == allCount)) {
                    branch.setIsCompare(TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
                    approvalTransportJob.setIsCompare(TransportJobInBranchConst.BRANCHIN_CHECK_SUCCESS);
                }
                transportJobBranchService.update(branch);
            }
            LOG.info("无委审核部分不通过,条数: " + transportJobInBranchDTO.getGuids().size());
            stationBakList = stationBakService.findByGuids(transportJobInBranchDTO.getGuids(), transportJobInBranchDTO.getAppGuid());
        }
        approvalTransportJobWebService.save(approvalTransportJob);
        if (stationBakList != null && stationBakList.size() > 0) {
            if (transportJob.getIsCompare().equals("10")) {
                transportJob.setIsCompare(TransportJobInConst.JOBIN_CHECKING);
            }
            for (RsbtStationBak rsbtStationBak : stationBakList) {
                // 3 -- 不通过
                rsbtStationBak.setIsShow("3");
            }
            stationBakService.updateBatch(stationBakList);
            // 获取所有的bts_id
            List<String> btsIds = stationBakList.stream().map(RsbtStationBak::getStCCode).collect(Collectors.toList());
            // 删除总表数据
            asyncRawBtsWebService.deleteByBtsIds(btsIds, approvalTransportJob.getJobGuid());
//            // TODO 删除station和license数据
//            List<String> stationIds = stationBakList.stream().map(RsbtStationBak::getUserGuid).collect(Collectors.toList());
//            stationService.deleteByBtsIds(stationIds);
//            licenseService.deleteByStationIds(stationIds);
        }
        // 查询是否存在任务状态为未完结的分任务
        Integer count3 = approvalTransportJobWebService.findByJobGuidAndIsCompareCount(approvalTransportJob.getJobGuid(), approvalTransportJob.getIsCompare());
        if (count3 == 0) {
            transportJob.setIsCompare(TransportJobInConst.JOBIN_COMPLETE);
        }
        transportJob.setGmtModified(new Date());
        transportJobService.update(transportJob);
        return JSONResult.getSuccessJson("不通过成功");
    }

    /**
     * 通过，生成国家申请表
     *
     * @param transportJobInBranchDTO transportJobInBranchDTO
     * @return list
     */
    public JSONObject commitApply(TransportJobInBranchDTO transportJobInBranchDTO) {

//        String appCode = "T652920210001";
        //获取当前任务
        ApprovalTransportJob approvalTransportJob = approvalTransportJobWebService.findById(transportJobInBranchDTO.getAppGuid());
        //获取申请表编号
        String appCode = "";
        if (approvalTransportJob != null) {
            //获取申请表编号
            appCode = getAppCode(approvalTransportJob.getRegionCode());
            transportJobInBranchDTO.setUserGuid(approvalTransportJob.getUserGuid());
            transportJobInBranchDTO.setAppCode(approvalTransportJob.getAppCode());
            transportJobInBranchDTO.setDataType(approvalTransportJob.getDataType());
            transportJobInBranchDTO.setGenNum(approvalTransportJob.getGenNum());
            transportJobInBranchDTO.setRegionCode(approvalTransportJob.getRegionCode());
            transportJobInBranchDTO.setTechType(approvalTransportJob.getTechType());
            //修改任务为生成申请表中
            approvalTransportJob.setIsCompare(TransportJobInBranchConst.BRANCHIN_APPLYING);
            approvalTransportJobWebService.save(approvalTransportJob);
            TransportJobBranchDTO jobBranchDTO = transportJobBranchService.findOneByJobUserTypeGen(approvalTransportJob.getJobGuid(), null,
                    approvalTransportJob.getDataType(), approvalTransportJob.getGenNum(), approvalTransportJob.getRegionCode(), approvalTransportJob.getTechType());
            TransportJobBranch branch = new TransportJobBranch();
            BeanUtils.copyProperties(jobBranchDTO, branch);
            branch.setIsCompare(TransportJobInBranchConst.BRANCHIN_APPLYING);
            transportJobBranchService.update(branch);
        }
        // todo
//        sendRabbit(taskData, "数据推送", JSONObject.toJSONString(transportJobInBranchDTO), null, RabbitMessageConst.APPCODE_UPDATE, appCode);
        return JSONResult.getSuccessJson("已提交处理，请稍后");
    }

//    public JSONObject checkApplyCode(String appCode) {
//        String resultJson = myRestRepository.getForStringNpList("http://127.0.0.1:8087/syncApi/transfer/stander/checkAppCode/" + appCode);
//        LOG.warn(resultJson);
//        return JSONObject.parseObject(resultJson);
//    }

    /**
     * 推送申请表入波尔台站库
     */
    public JSONObject dataPush(ApplyJobInDTO applyJobInDTO) {
        if (applyJobInDTO.getAppCodes() != null) {
            for (String appCode : applyJobInDTO.getAppCodes()) {
                //将分任务申请表状态改为推送中
                applyLinkWebService.updateByAppCode(appCode, "1");
                //查询出需提交的任务
                ApplyJobInDTO jobInDTO = applyJobInWebService.selectByAppCode(appCode);
                applyJobInWebService.updateByAppCode(jobInDTO.getAppGuid(), jobInDTO.getAppCode(), ApplyLinkStateConst.APPLY_SYNCING);
                // 删除基站信息和执照信息
                if ("3".equals(jobInDTO.getDataType())) {
                    RsbtStationBak stationBak = new RsbtStationBak();
                    stationBak.setAppCode(jobInDTO.getAppCode());
                    stationBak.setAppGuid(jobInDTO.getAppGuid());
                    List<RsbtStationBak> stationBaks = stationBakService.findAllByWhere(stationBak);
                    List<String> stationIds = stationBaks.stream().map(RsbtStationBak::getUserGuid).collect(Collectors.toList());

                    if (stationIds.size() > 0) {
                        // 删除station
                        stationService.deleteByStationIds(stationIds);
                        // 删除license
                        licenseService.deleteByStationIds(stationIds);
                    }
                    // 修改bak is_show 状态
                    if (stationBaks.size() > 0) {
                        for (RsbtStationBak bak : stationBaks) {
                            bak.setIsShow("2");
                        }
                        stationBakService.updateBatch(stationBaks);
                    }
                }
                //获取需推送的任务放入rabbitmq中 todo

//                sendRabbit(syncData, "数据推送入库", jobInDTO.getAppCode(), jobInDTO.getAppGuid(), RabbitMessageConst.APPLY_COMMIT, jobInDTO.getDataType());
            }
            return JSONResult.getSuccessJson("已提交处理，请稍后");
        }
        return JSONResult.getFailureJson("未接收到需提交的申请表编号！");

    }


//    //发送rabbit
//    protected void sendRabbit(String key, String title, String content, String remark, String type, String from) {
//        RabbitMessage rabbitMessage = new RabbitMessage(VerificationCode.idGet("rabbit", 4), title, content, DateUtils.getDateTime(), from, remark, type);
//        rabbitService.sendSimple(key, JSONObject.toJSONString(rabbitMessage));
//    }

    public Map<String, Object> findHistoryByGuid(TransportJobInBranchDTO transportJobInBranchDTO) {
        TransportJobInBranchDTO transportJobInBranch = transportJobBranchService.findDetail(transportJobInBranchDTO.getGuid().toString());
        PageInfo<StationDTO> transportJobInBranchDTOList = new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findHistoryByGuid(transportJobInBranchDTO));
        if (transportJobInBranchDTOList.getList() != null) {
            transportJobInBranch.setTotal((int) transportJobInBranchDTOList.getTotal());
            transportJobInBranch.setStationDTOList(transportJobInBranchDTOList.getList());
            List<TransportFileDTO> fileDTOList = transportFileWebService.findByGuidType(transportJobInBranchDTO.getGuid().toString(), "2");
            transportJobInBranch.setTransportFileAttachedDTOList(fileDTOList);
        }
        return this.basicReturnResultJson(transportJobInBranch);
    }

    /**
     * 用户分页查看所有申请表
     */
    public Map<String, Object> findAllApply(TransportJobInBranchDTO transportJobInBranchDTO) {
        return this.basicReturnResultJson(new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findAllApply(transportJobInBranchDTO)));
    }

    /*//获取申请表编号
    public String getAppCode(){
        //获得频率台站库最大的申请表编号
        String appCode = myRestRepository.getForStringNpList("http://127.0.0.1:8087/syncApi/transfer/stander/getAppCode");
        String appCode1 = appCode.substring(1,appCode.length());
        //判断appCode是否被占用
        String redisAppCode = redisService.get("caictAppCode");
        String redisAppCode1 = redisAppCode.substring(1,redisAppCode.length());
        if(Long.parseLong(appCode1)<Long.parseLong(redisAppCode1)){
            appCode = redisAppCode;
        }else {
            redisService.set("caictAppCode",appCode);
        }
        return appCode;
    }
*/

    //获取申请表编号
    public String getAppCode(String areaCode) {
        //获得频率台站库最大的申请表编号
        RegionDTO fsaRegion = fsaRegionService.findOneByCode(areaCode);
        if (fsaRegion != null) {
            return fsaRegion.getRegionDetails();
        }
        return null;
    }

    /**
     * 根据regionCode和jobGuid 查询branch信息
     *
     * @param transportJobInBranchDTO transportJobInBranchDTO
     * @param token                   token
     * @return list
     */
    public Map<String, Object> findApplicationDetailListByPage(TransportJobInBranchDTO transportJobInBranchDTO, String token) {
        if (StringUtils.isNotEmpty(token)) {
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            PageInfo<TransportJobDTO> transportJobDTOList;
            if ("wuwei".equals(usersDTO.getType())) {
                // 无委
                if ("2".equals(usersDTO.getRoleDTO().getType())) {
                    // 地市无委
                    transportJobDTOList = new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findOneByJobUserTypeGen1
                            (transportJobInBranchDTO.getJobGuid(), null, null, null, null, usersDTO.getOrgDTO().getOrgAreaCode()));
                } else {
                    // 省级无委
                    transportJobDTOList = new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findOneByJobUserTypeGen1
                            (transportJobInBranchDTO.getJobGuid(), null, null, null, null, null));
                }
            } else {
                // 运营商
                transportJobDTOList = new PageHandle(transportJobInBranchDTO).buildPage(transportJobBranchService.findOneByJobUserTypeGen1
                        (transportJobInBranchDTO.getJobGuid(), null, null, null, null, null));
            }
            return this.basicReturnSuccess(transportJobDTOList);
        }
        return this.basicReturnFailure("操作失败,请重新登录");
    }

    /**
     * 查询经纬度范围内的台站
     *
     * @param pointDto pointDto
     * @return json
     */
    public JSONObject findAllBtsInMap(PointDTO2 pointDto) {
        double lowLng = Double.MAX_VALUE, lowLat = Double.MAX_VALUE, highLng = Double.MIN_VALUE, highLat = Double.MIN_VALUE;

        for (PointDTO point : pointDto.getPointList()) {
            if (point.getLongitude() < lowLng) lowLng = point.getLongitude();
            if (point.getLongitude() > highLng) highLng = point.getLongitude();
            if (point.getLatitude() < lowLat) lowLat = point.getLatitude();
            if (point.getLatitude() > highLat) highLat = point.getLatitude();
        }
        List<StationDTO> list = stationBakService.findAllBtsInMap(lowLng, lowLat, highLng, highLat, pointDto.getAppGuid());
        return JSONResult.getSuccessJson(list, "经纬度范围内的台站");
    }

    /**
     * 查询经纬度范围内的台站数量
     *
     * @param appGuid appGuid
     * @return json
     */
    public JSONObject countByBtsInMap(String appGuid) {
        List<StationCountyNumDTO> list = stationBakService.countByBtsInMap(appGuid);
        return JSONResult.getSuccessJson(list, "经纬度范围内的台站数量");
    }

    /**
     * 查看基站信息
     *
     * @param appGuid appGuid
     * @param guid    guid
     * @return json
     */
    public JSONObject getStationInfo(String guid, String appGuid) {
        return JSONResult.getSuccessJson(stationBakService.getStationInfo(guid, appGuid), "查看基站信息");
    }

    /**
     * 查询基站信息
     *
     * @param transportJobInBranchDTO transportJobInBranchDTO
     * @return json
     */
    public JSONObject exportStationExcel(TransportJobInBranchDTO transportJobInBranchDTO) {
        // 当前年月日
        String timeStr = new Timestamp(System.currentTimeMillis()).toString().replaceAll("\\.", "_")
                .replace(" ", "_").replaceAll("-", "").replaceAll(":", "")
                .replaceAll("\\\\", "");
        // 打包后的文件路径
        String filePath = serverExportPath + timeStr + File.separator;
        List<ApprovalScheduleLog> scheduleLogList = approvalScheduleLogWebService.findByAppGuid(transportJobInBranchDTO.getAppGuid());
        List<String[]> dataList = new ArrayList<>();
        dataList.add(getHead());
        dataList.addAll(getBody(scheduleLogList));
        String[][] datas = new String[dataList.size()][dataList.get(0).length];
        for (int i = 0; i < dataList.size(); i++) {
            System.arraycopy(dataList.get(i), 0, datas[i], 0, dataList.get(0).length);
        }
        ApprovalScheduleLog log = scheduleLogList.get(0);
        // 打包文件名
        String fileName = "_" + log.getCounty() + "-" + log.getGenNum() + "G-" + log.getTechType();
        String outZipPath = filePath + timeStr + fileName + ".zip";
        ExcelHelperUtil helper = new ExcelHelperUtil();
        String msg = helper.importDataToExcel(datas, filePath, fileName);
        LOG.warn(msg);
        File fileServer = new File(filePath);
        // 打包文件夹
        packageFiles(outZipPath, fileServer);

        return JSONResult.getSuccessJson(outZipPath.split("/file/")[1], "下载成功");
    }

    /**
     * 打包文件夹
     *
     * @param outZipPath zip文件地址
     * @param fileServer Excel文件路劲
     */
    private void packageFiles(String outZipPath, File fileServer) {
        File[] fileArray = fileServer.listFiles();
        if (fileArray != null) {
            List<File> files = new ArrayList<>(Arrays.asList(fileArray));
            File outFile = new File(outZipPath);
            LOG.warn(new Timestamp(System.currentTimeMillis()) + "\t导出.outFile.out.-->:" + outZipPath);
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(outFile);
                ZipCompressor.toZip(files, fos);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取excel数据
     *
     * @param listData data list
     * @return List'<String[]>
     */
    private List<String[]> getBody(List<ApprovalScheduleLog> listData) {
        List<String[]> list = new ArrayList<>();

        for (ApprovalScheduleLog dto : listData) {
            String[] data = new String[24];
            data[0] = dto.getCellName();
            data[1] = dto.getCellId();
            data[2] = dto.getBtsName();
            data[3] = dto.getBtsId();
            data[4] = dto.getTechType();
            data[5] = dto.getLocation();
            data[6] = dto.getLongitude();
            data[7] = dto.getLatitude();
            data[8] = dto.getCounty();
            data[9] = dto.getDeviceModel();
            data[10] = dto.getVendorName();
            data[11] = dto.getModelCode();
            data[12] = dto.getSendStartFreq() + "~" + dto.getSendEndFreq();
            data[13] = dto.getAccStartFreq() + "~" + dto.getAccEndFreq();
            data[14] = dto.getHeight();
            data[15] = dto.getAntennaFactory();
            data[16] = dto.getAntennaModel();
            data[17] = dto.getAntennaAzimuth();
            data[18] = dto.getAntennaGain();
            data[19] = dto.getMaxEmissivePower();
            data[20] = dto.getPolarizationMode();
            data[21] = dto.getFeederLoss();
            data[22] = dto.getAltitude();
            data[23] = dto.getGenNum();

            list.add(data);
        }
        return list;
    }

    /**
     * 获取excel表头
     *
     * @return String[]
     */
    private String[] getHead() {
        String[] head = new String[24];
        head[0] = "CELL_NAME/扇区名称";
        head[1] = "CELL_ID/扇区识别码";
        head[2] = "BTS_NAME/基站名称";
        head[3] = "BTS_ID/基站识别码";
        head[4] = "TECH_TYPE/技术体制";
        head[5] = "LOCATION/台址";
        head[6] = "LONGITUDE/经度";
        head[7] = "LATITUDE纬度";
        head[8] = "COUNTY/所属地区";
        head[9] = "DEVICE_MODEL/设备型号";
        head[10] = "DEVICE_FACTORY/设备生产厂家";
        head[11] = "MODEL_CODE/型号核准代码";
        head[12] = "SEND_FREQ/发射频率/频段(MHz)";
        head[13] = "ACC_FREQ/接收频率/频段(MHz)";
        head[14] = "HEIGHT/天线距地高度（M）";
        head[15] = "DEVICE_FACTORY/天线生产厂家";
        head[16] = "ANTENNA_MODEL/天线类型";
        head[17] = "ANTENNA_AZIMUTH/天线方位角(°)";
        head[18] = "ANTENNA_GAIN/天线增益(dBi)";
        head[19] = "MAX_EMISSIVE_POWER/最大发射功率(W)";
        head[20] = "POLARIZATION_MODE/极化方式";
        head[21] = "FEEDER_LOSS/馈线系统总损耗(dB)";
        head[22] = "ALTITUDE/海拔高度(m)";
        head[23] = "GEN_NUM/代数";

        return head;
    }
}
