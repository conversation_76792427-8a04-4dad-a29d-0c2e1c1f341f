package com.bsm.v4.api.web.service.bussiness.applytable;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.station.RsbtStationWebService;
import com.bsm.v4.api.web.service.bussiness.transfer_in.ApplyJobInWebService;
import com.bsm.v4.api.web.service.bussiness.transfer_in.ApplyLinkWebService;
import com.bsm.v4.api.web.service.rule.FsaCheckRuleWebService;
import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.applytable.RsbtApplyAppendixService;
import com.bsm.v4.domain.security.service.business.applytable.RsbtApplyService;
import com.bsm.v4.domain.security.service.business.license.LicenseService;
import com.bsm.v4.domain.security.service.business.station.RsbtFreqService;
import com.bsm.v4.domain.security.service.business.station.RsbtNetService;
import com.bsm.v4.domain.security.service.business.transfer.TransportJobBranchService;
import com.bsm.v4.domain.security.service.security.OrgService;
import com.bsm.v4.system.model.dto.business.applytable.*;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApplyAppendix;
import com.bsm.v4.system.model.entity.business.station.RsbtNet;
import com.bsm.v4.system.model.vo.business.applytable.ApplyTableVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.document.service.PdfExportService;
import com.caictframework.document.util.QRCodeUtil;
import com.caictframework.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RsbtApplyWebService extends BasicWebService {

    @Autowired
    private RsbtApplyService bsmApplyTableService;
    @Autowired
    private RsbtApplyAppendixService applyAppendixService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private RsbtNetService netService;
    @Autowired
    private RsbtFreqService freqService;
    @Autowired
    private RsbtStationWebService stationWebService;
    @Autowired
    private LicenseService licenseWebService;
    @Autowired
    private ApplyLinkWebService applyLinkWebService;
    @Autowired
    private FsaCheckRuleWebService fsaCheckRuleWebService;
    @Autowired
    private AuthWebService authWebService;
    @Autowired
    private ApplyJobInWebService applyJobInWebService;
    @Autowired
    private TransportJobBranchService transportJobBranchService;
    @Autowired
    private PdfExportService pdfExportService;

    @Value("${caict.myFileExportPath}")
    private String myFileExportPath;
    @Value("${caict.applyTemplate}")
    private String applyTemplate;

    /**
     * 分页条件查询
     */
    public JSONObject findAllByWhere(RsbtApplyDTO bsmApplytableDTO) {

        PageInfo<RsbtApplyDTO> bsmApplytableDTOList = new PageHandle(bsmApplytableDTO).buildPage(bsmApplyTableService.findAllByWhere(bsmApplytableDTO));

        if (bsmApplytableDTOList.getList() != null) {
            return JSONResult.getSuccessJson(bsmApplytableDTOList);
        }
        return JSONResult.getPageFailureJson(bsmApplytableDTO.getPage(), bsmApplytableDTO.getPage() + 1, bsmApplytableDTO.getRows(), 0, "操作失败");
    }

    public Map<String,Object> findApplyList(ApplyTableVO vo,String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        vo.setUserId(usersDTO.getId());
        return this.basicReturnSuccess(new PageHandle(vo).buildPage(bsmApplyTableService.findDealDataList(vo)));
    }

    public Map<String,Object> findApplyDetailList(String jobId) {
        List<TransportJobBranchDTO> transportJobBranchDTOS = bsmApplyTableService.findApplyDetailList(jobId);
        if (transportJobBranchDTOS!=null){
            Map<String, List<TransportJobBranchDTO>> collect = transportJobBranchDTOS.stream().collect(Collectors.groupingBy(TransportJobBranchDTO::getDataType));
            return this.basicReturnSuccess(collect);
        }
        return this.basicReturnSuccess();
    }

    public Map<String,Object> printPdf(String guid) {
        //先判断文件是否存在，存在则直接返回
        File file = new File(myFileExportPath+File.separator + guid + "pdf.pdf");
        if (file.exists()){
            return basicReturnSuccess("export/"+ guid + "pdf.pdf");
        }
        BsmApplyTemplateDTO bsmApplyTemplateDTO = transportJobBranchService.findBsmApplyTemplateByGuid(guid);
        if (bsmApplyTemplateDTO != null){
            //表单数据BsmApplyTemplateDTO
            Map<String, String> map = new LinkedHashMap<>();
            //生成申请表单数据
            setApplyPrintData(map,bsmApplyTemplateDTO);
            //打印表单
            String padPath = pdfExportService.savePdf(myFileExportPath,applyTemplate,guid,map,null,null);
            List<BsmApplyTableTemplateDTO> bsmApplyTableTemplateDTOList = transportJobBranchService.findBsmApplyTableTemplateByJobUserDataType(bsmApplyTemplateDTO.getJobGuid(), bsmApplyTemplateDTO.getDataType(), bsmApplyTemplateDTO.getRegionCode(), bsmApplyTemplateDTO.getGenNum());
            if (bsmApplyTableTemplateDTOList != null && bsmApplyTableTemplateDTOList.size() != 0){
                Paragraph paragraph = setApplyTableData(bsmApplyTableTemplateDTOList);
                //打印最终申请表
                return basicReturnSuccess("export/" + pdfExportService.savePdf(myFileExportPath,myFileExportPath + padPath,guid,paragraph));
            }
        }
        return this.basicReturnFailure("打印申请表失败！");
    }

    /**
     * 申请表数据生成
     * map;表单数据
     * codeMap：二维码数据
     * bsmApplyTemplateDTO：数据源
     * */
    protected void setApplyPrintData(Map<String, String> map, BsmApplyTemplateDTO bsmApplyTemplateDTO){
        map.put("app_code", bsmApplyTemplateDTO.getAppCode()); //编号
        map.put("org_name", bsmApplyTemplateDTO.getOrgName()); //台站设置使用人
        map.put("org_addr", bsmApplyTemplateDTO.getOrgAddr()); //通信地址
        map.put("org_code", bsmApplyTemplateDTO.getOrgCode()); //统一社会信用代码
        map.put("org_post", bsmApplyTemplateDTO.getOrgPost()); //邮政编码
        map.put("org_link_person", bsmApplyTemplateDTO.getOrgLinkPerson()); //联系人
        map.put("org_phone", bsmApplyTemplateDTO.getOrgPhone()); //联系电话
        map.put("org_mail", bsmApplyTemplateDTO.getOrgMail()); //电子邮箱
        //申请类型
        //初始化类型值
        map.put("data_type_new", "Off");
        map.put("data_type_update", "Off");
        map.put("data_type_delete", "Off");
        map.put("data_type_on", "Off");

        if ("1".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_new", "On");
        else if ("2".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_update", "On");
        else if ("3".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_delete", "On");
        else if ("4".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_on", "On");
        map.put("station_count", bsmApplyTemplateDTO.getStationCount()); //基站数量
    }

    /**
     * 生成pdf表格
     * */
    protected Paragraph setApplyTableData(List<BsmApplyTableTemplateDTO> bsmApplyTableTemplateDTOList){
        try{
            BaseFont bfChinese = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font fontChinese = new Font(bfChinese, 10.6F, Font.NORMAL);// 五号
            Paragraph ret = new Paragraph();
            PdfPTable tableBox = new PdfPTable(11);
            tableBox.setWidths(new float[] { 0.05f,0.15f,0.15f,0.05f,0.1f,0.1f,0.1f,0.1f,0.1f,0.05f,0.05f });// 每个单元格占多宽
            tableBox.setWidthPercentage(100);
            //设置表头
            tableBox.addCell(setCell(new Phrase("技术资料信息",fontChinese),11,1, Element.ALIGN_LEFT ,new BaseColor(184,204,228)));
            tableBox.addCell(setCell(new Phrase("序号",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("台站名称",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("基站/小区识别码",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("技术体制",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("台址（编号）",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("经度",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("纬度",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("发射频率（MHz）",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("接收频率（MHz）",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("最大发射功率（W）",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            tableBox.addCell(setCell(new Phrase("天线距地高度（m）",fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            //设置表体
            for (BsmApplyTableTemplateDTO bsmApplyTableTemplateDTO : bsmApplyTableTemplateDTOList){
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getId(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getBtsName(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getBtsId(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getTechType(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getCounty(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getLongitude(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getLatitude(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getSendFreq(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getAccFreq(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getMaxEmissivePower(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getHeight(),fontChinese),1,1, Element.ALIGN_CENTER ,BaseColor.WHITE));
            }
            ret.add(tableBox);
            return ret;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 每个cell设置
     * phrase:phrase
     * colSpan:合并列
     * rowSpan：合并行
     *
     * */
    protected PdfPCell setCell(Phrase phrase, int colSpan, int rowSpan, int alignment, BaseColor backgroundColor){
        PdfPCell cells = new PdfPCell(phrase);
        cells.setUseAscender(true);
        cells.setMinimumHeight(20f);
        cells.setHorizontalAlignment(alignment);
        cells.setVerticalAlignment(5);
        cells.setColspan(colSpan);
        cells.setRowspan(rowSpan);
        cells.setNoWrap(false);
        cells.setBackgroundColor(backgroundColor);
        return cells;
    }

    /**
     * 内网分页条件查询
     */
    public JSONObject findAllInByWhere(RsbtApplyDTO bsmApplytableDTO, String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        if ("2".equals(usersDTO.getRoleDTO().getType())) {
            bsmApplytableDTO.setRegionId(usersDTO.getRegionId());
        }
        PageInfo<RsbtApplyDTO> bsmApplytableDTOList = new PageHandle(bsmApplytableDTO).buildPage(bsmApplyTableService.findAllInByWhere(bsmApplytableDTO));

        if (bsmApplytableDTOList.getList() != null) {
            return JSONResult.getSuccessJson(bsmApplytableDTOList);
        }
        return JSONResult.getPageFailureJson(bsmApplytableDTO.getPage(), bsmApplytableDTO.getPage() + 1, bsmApplytableDTO.getRows(), 0, "操作失败");
    }

    /**
     * 根据guid查询详情DTO
     */
    public JSONObject findOneByGuid(String guid) {
        return JSONResult.getSuccessJson(bsmApplyTableService.findOneByGuid(guid), "查询成功");
    }

    /**
     * 内网端查询技术资料表
     */
    public JSONObject findTechTable(RsbtApplyDTO applyDTO) {
        return JSONResult.getSuccessJson(bsmApplyTableService.findTechTable(applyDTO), "查询成功");
    }

    /**
     * 内网申请表详情查看
     */
    public JSONObject findApplyDetail(String guid) {
        AddApplyDTO addApplyDTO = new AddApplyDTO();
        RsbtApply apply = bsmApplyTableService.findById(guid);
        if (apply == null) {
            JSONResult.getFailureJson("未查询到相关申请表");
        }
        RsbtApplyAppendix applyAppendix = applyAppendixService.findById(guid);
        addApplyDTO.setAppCode(apply.getAppCode());
        addApplyDTO.setGuid(guid);
        //申请表类型
        addApplyDTO.setAppType(apply.getAppType());
        //设台单位信息
        OrgDTO oneOrg = orgService.findOneDetails(apply.getOrgGuid());
        AppOrgDetailDTO appOrgDetailDTO = new AppOrgDetailDTO();
        BeanUtils.copyProperties(oneOrg, appOrgDetailDTO);
        //缴费单位信息
        AppFeeOrgDetailDTO appFeeOrgDetailDTO = new AppFeeOrgDetailDTO();
        BeanUtils.copyProperties(oneOrg, appFeeOrgDetailDTO);
        addApplyDTO.setAppFeeOrgDetailDTO(appFeeOrgDetailDTO);
        //申请类型
        appOrgDetailDTO.setAppSubType(apply.getAppSubType());
        addApplyDTO.setAppOrgDetailDTO(appOrgDetailDTO);

        //频率信息
        RsbtNet net = netService.findById(apply.getNetGuid());
        AppNetDetailDTO appNetDetailDTO = new AppNetDetailDTO();
        List<FsaCheckRuleDTO> fsaCheckRuleDTOs = fsaCheckRuleWebService.findByOrgGuidAndTechType(oneOrg.getType(), net.getNetTs());
        AppFreqDetailDTO appFreqDetailDTO = new AppFreqDetailDTO();
        List<String> appFileNos = new ArrayList<>();
        List<FreqListDTO> applyFreqDTOs = new ArrayList<>();
        for (FsaCheckRuleDTO fsaCheckRuleDTO : fsaCheckRuleDTOs) {
            appFileNos.add(fsaCheckRuleDTO.getFileNo());
            FreqListDTO applyFreqDTO = new FreqListDTO();
            applyFreqDTO.setFreqEfb(fsaCheckRuleDTO.getFreqEfb());
            applyFreqDTO.setFreqEfe(fsaCheckRuleDTO.getFreqEfe());
            applyFreqDTO.setFreqRfb(fsaCheckRuleDTO.getFreqRfb());
            applyFreqDTO.setFreqRfe(fsaCheckRuleDTO.getFreqRfe());
            applyFreqDTOs.add(applyFreqDTO);
        }
        appFreqDetailDTO.setFreqList(applyFreqDTOs);
        appFreqDetailDTO.setFreqType("2");
        addApplyDTO.setAppFreqDetailDTO(appFreqDetailDTO);
        BeanUtils.copyProperties(net, appNetDetailDTO);
        appNetDetailDTO.setNetFileNo(appFileNos);
        addApplyDTO.setAppNetDetailDTO(appNetDetailDTO);

        AppDataDTO appDataDTO = new AppDataDTO();
        appDataDTO.setAppDate(apply.getAppDate());
        appDataDTO.setAppPerName(applyAppendix.getApppUserGuid());
        addApplyDTO.setAppDataDTO(appDataDTO);



        /*Map<String,Object> map = new HashMap<>();
        //组装机构相关信息
        RsbtApply applytable = bsmApplyTableService.findOne(guid);
        map.put("appSubType",applytable.getAppSubType());
        ApplyOrgDTO appOrgDetailDTO = new ApplyOrgDTO();
        OrgDTO oneOrg = orgService.findOneDetails(applytable.getOrgGuid());
        BeanUtils.copyProperties(oneOrg,appOrgDetailDTO);
        map.put("appType",applytable.getAppType());
        map.put("appOrgDetailDTO",appOrgDetailDTO);
        //组装共性数据
        ApplyNetDTO appNetDetailDTO = new ApplyNetDTO();
        RsbtNet net = netService.findById(applytable.getNetGuid());
        map.put("appFeeOrgDetailDTO",appOrgDetailDTO);
        BeanUtils.copyProperties(net,appNetDetailDTO);

        //组装频率数据
        List<FsaCheckRuleDTO> fsaCheckRuleDTOs =fsaCheckRuleWebService.findByOrgGuidAndTechType(oneOrg.getType(),net.getNetTs());
        StringBuffer buffer = new StringBuffer();
//        AppFreqDetailDTO appFreqDetailDTO = new AppFreqDetailDTO();
        List<ApplyFreqDTO> applyFreqDTOs= new ArrayList<>();
        for (int i = 0;i<fsaCheckRuleDTOs.size();i++) {
            buffer.append(fsaCheckRuleDTOs.get(i).getFileNo());
            if (i<fsaCheckRuleDTOs.size()-1){
                buffer.append(",");
            }
            ApplyFreqDTO applyFreqDTO = new ApplyFreqDTO();
            applyFreqDTO.setFreqEfb(fsaCheckRuleDTOs.get(i).getFreqEfb());
            applyFreqDTO.setFreqEfe(fsaCheckRuleDTOs.get(i).getFreqEfe());
            applyFreqDTO.setFreqRfb(fsaCheckRuleDTOs.get(i).getFreqRfb());
            applyFreqDTO.setFreqRfe(fsaCheckRuleDTOs.get(i).getFreqRfe());
            applyFreqDTOs.add(applyFreqDTO);
        }
        appFreqDetailDTO.setFreqType("2");
        appFreqDetailDTO.setApplyFreqDTOs(applyFreqDTOs);
        appNetDetailDTO.setNetFileNo(buffer.toString());
        map.put("appFreqDetailDTO",appFreqDetailDTO);
        map.put("appNetDetailDTO",appNetDetailDTO);
        map.put("guid",guid);
        //map.put("freqType",appFreqList.get(0).getAppFreqType());
        Table2ApplyTableDTO appDataDTO = new Table2ApplyTableDTO();
        map.put("appCode",applytable.getAppCode());
        appDataDTO.setAppDate(applytable.getAppDate());
        appDataDTO.setAppPerson(oneOrg.getOrgLinkPerson());
        map.put("appDataDTO",appDataDTO);*/
        return JSONResult.getSuccessJson(addApplyDTO, "查询成功");
    }

    public JSONObject editApply(AddApplyDTO addApplyDTO) {
        try {
            RsbtApply apply = bsmApplyTableService.findById(addApplyDTO.getGuid());
            if (apply == null) {
                return JSONResult.getFailureJson("保存失败");
            }
            RsbtNet net = netService.findById(apply.getNetGuid());
            BeanUtils.copyProperties(addApplyDTO.getAppNetDetailDTO(), net);
            netService.update(net);
            RsbtApplyAppendix appendix = applyAppendixService.findById(apply.getGuid());
            //修改申请表信息
            apply.setAppCode(addApplyDTO.getAppCode());
            apply.setAppType(addApplyDTO.getAppType());
            apply.setAppSubType(addApplyDTO.getAppOrgDetailDTO().getAppSubType());
            //修改台站申请表编号
            stationWebService.updateAppCode(apply.getAppCode(), addApplyDTO.getAppCode());
            //修改执照表申请表编号
            licenseWebService.updateAppCode(apply.getAppCode(), addApplyDTO.getAppCode());
            //修改apply_link表申请表编号
            applyLinkWebService.updateAppCode(apply.getAppCode(), addApplyDTO.getAppCode());
            //修改申请表任务表
            applyJobInWebService.updateAppCode(apply.getAppCode(), addApplyDTO.getAppCode());
            //修改申请表
            bsmApplyTableService.update(apply);
            //修改申请表关联表
            applyAppendixService.update(appendix);

            return JSONResult.getSuccessJson("修改成功！");
        } catch (Exception e) {
            return JSONResult.getFailureJson("修改失败！");
        }
    }
}
