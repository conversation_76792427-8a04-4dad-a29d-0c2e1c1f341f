package com.bsm.v4.api.web.config;

import com.bsm.v4.api.web.service.rule.FsaCheckRuleWebService;
import com.bsm.v4.api.web.service.security.RegionWebService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 项目启动后执行
 * */
@Component
public class InitWebProject implements ApplicationRunner {

    private static final Logger LOG = LoggerFactory.getLogger(InitWebProject.class);

    @Autowired
    private FsaCheckRuleWebService fsaCheckRuleWebService;

    @Autowired
    private RegionWebService regionWebService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        LOG.info("获取规则放在内存中");
        fsaCheckRuleWebService.findAllCheckRuleByIsDeleted();
        fsaCheckRuleWebService.findAllCheckRuleNetTsByIsDeleted();
        regionWebService.findAllByOrg("500000");
    }
}
