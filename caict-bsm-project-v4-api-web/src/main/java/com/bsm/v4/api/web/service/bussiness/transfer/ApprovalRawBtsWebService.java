package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.business.transfer.ApprovalRawBtsService;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalRawBts;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.util.JSONResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class ApprovalRawBtsWebService extends BasicWebService {

    @Autowired
    private ApprovalRawBtsService approvalRawBtsService;

    //根据fileId删除所有数据
    public JSONObject deleteAllByFileId(String fileId) {
        if (approvalRawBtsService.deleteAllByFileId(fileId) > 0) {
            return JSONResult.getSuccessJson("删除成功");
        }
        return JSONResult.getFailureJson("删除失败，无数据");
    }

    /**
     * 根据appGuid查询总数
     */
    public int selectCountAllByAppGuid(String appGuid) {
        return approvalRawBtsService.selectCountAllByAppGuid(appGuid);
    }

    /**
     * 根据appGuid删除
     */
    public int deleteByAppGuidDataTypeGenNum(String appGuid, String dataType, String genNum) {
        return approvalRawBtsService.deleteByAppGuidDataTypeGenNum(appGuid, dataType, genNum);
    }

    /**
     * 3.0自动生成待审核数据
     */
    public int insertBySchedule(String jobGuid, String userGuid, String dataType, String genNum, String appGuid, String regionCode) {
        return approvalRawBtsService.insertBySchedule(jobGuid, userGuid, dataType, genNum, appGuid, regionCode);
    }

    /**
     * 保存（如果Id为空或者为“”则为添加，否则修改）
     *
     * @param approvalRawBts approvalRawBts
     * @return map
     */
    public Map<String, Object> save(ApprovalRawBts approvalRawBts) {
        return this.basicReturnResultJson(approvalRawBtsService.save(approvalRawBts));
    }
}
