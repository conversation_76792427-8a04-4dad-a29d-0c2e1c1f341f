package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.api.web.service.security.AuthWebService;
import com.bsm.v4.domain.security.service.business.transfer.TransportJobBranchService;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDealLog;
import com.caictframework.data.util.PageHandle;
import com.caictframework.document.service.PdfExportService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Service
public class TransportJobBranchWebService {

    @Autowired
    private TransportJobBranchService transportJobBranchService;
    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;
    @Autowired
    private AuthWebService authWebService;
    @Autowired
    private ApprovalRawBtsWebService approvalRawBtsWebService;
    @Autowired
    private PdfExportService pdfExportService;

    @Value("${caict.myFileExportPath}")
    private String myFileExportPath;
    @Value("${caict.applyTemplate}")
    private String applyTemplate;

    /**
     * 3.0版本自动提交处理任务
     */
    public void commit(String userId, String guid, String token) {
        //修改申请表状态为已提交
        TransportJobBranch transportJobBranch = transportJobBranchService.findById(guid);
        //创建审核任务
        ApprovalTransportJobDTO model = new ApprovalTransportJobDTO();
        model.setToken(token);
        model.setGuid(guid);
        model.setJobGuid(transportJobBranch.getJobGuid());
        model.setDataType(transportJobBranch.getDataType());
        model.setGenNum(transportJobBranch.getGenNum());
        Map approvalTransportJobDTOMap = approvalTransportJobWebService.createApproveJob(model);
        ApprovalTransportJobDTO approvalTransportJobDTO = (ApprovalTransportJobDTO) approvalTransportJobDTOMap.get("data");
        //生成待审核数据
        approvalRawBtsWebService.insertBySchedule(transportJobBranch.getJobGuid(), approvalTransportJobDTO.getUserGuid(), transportJobBranch.getDataType(), transportJobBranch.getGenNum(), approvalTransportJobDTO.getGuid(), transportJobBranch.getRegionCode());

        //transportJobBranch.setGuid(guid);
        transportJobBranch.setState("2");
        transportJobBranch.setOpDetail("审核通过");
        transportJobBranchService.update(transportJobBranch);

        //提交审核数据
        approvalTransportJobWebService.commitApproveJob(model);
    }

    public PageInfo<TransportJobBranchDTO> findAllBranch(TransportJobBranchDTO transportJobBranchDTO) {
        return new PageHandle(transportJobBranchDTO).buildPage(transportJobBranchService.findAllBranch(transportJobBranchDTO));
    }

    /**
     * 用户分页查看所有申请表
     */
    public PageInfo<TransportJobBranchDTO> findAllPageByUsers(String token, TransportJobBranchDTO transportJobBranchDTO) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        return new PageHandle(transportJobBranchDTO).buildPage(transportJobBranchService.findAllByUser(usersDTO.getUserId()));
    }

//    public int updateByConfirm(String jobGuid,String dataType,String genNum,String isCompare){
//        return transportJobBranchService.updateByConfirm(jobGuid,dataType,genNum,isCompare);
//    }

    public int updateByConfirm(String jobGuid, String dataType, String genNum, String techType, String isCompare) {
        return transportJobBranchService.updateByConfirm(jobGuid, dataType, genNum, techType, isCompare);
    }

    /**
     * 判断此job下是否已经全部确认完
     */
    public int judgeAllConfirm(String jobGuid) {
        return transportJobBranchService.judgeAllConfirm(jobGuid);
    }

    /**
     * 根据jobid删除
     */
    public int deleteByJobId(String jobId) {
        return transportJobBranchService.deleteByJobId(jobId);
    }

    /**
     * 根据任务guid、状态查询所有数据
     */
    public List<TransportJobBranchDTO> findAllByJobState(String jobGuid, String state) {
        TransportJobBranch transportJobBranch = new TransportJobBranch();
        transportJobBranch.setJobGuid(jobGuid);
        transportJobBranch.setState(state);

        List<TransportJobBranch> transportJobBranchList = transportJobBranchService.findAllByWhere(transportJobBranch);
        if (transportJobBranchList != null && transportJobBranchList.size() > 0) {
            List<TransportJobBranchDTO> transportJobBranchDTOList = new ArrayList<>();
            for (TransportJobBranch transportJobBranchR : transportJobBranchList) {
                TransportJobBranchDTO transportJobBranchDTO = new TransportJobBranchDTO();
                BeanUtils.copyProperties(transportJobBranchR, transportJobBranchDTO);
                transportJobBranchDTOList.add(transportJobBranchDTO);
            }
            return transportJobBranchDTOList;
        }
        return null;
    }

//    /**
//     * 申请表打印
//     */
//    public String printPdf(String guid) {
//        BsmApplyTemplateDTO bsmApplyTemplateDTO = transportJobBranchService.findBsmApplyTemplateByGuid(guid);
//        if (bsmApplyTemplateDTO != null) {
//            //表单数据
//            Map<String, String> map = new LinkedHashMap<>();
//            //生成申请表单数据
//            setApplyPrintData(map, bsmApplyTemplateDTO);
//            Map in = new HashMap();
//            in.put("template", "template");
//            //打印表单
//            String padPath = pdfExportService.savePdf(myFileExportPath, applyTemplate, guid, in, map, null);
//            List<BsmApplyTableTemplateDTO> bsmApplyTableTemplateDTOList = transportJobBranchService.findBsmApplyTableTemplateByJobUserDataType(bsmApplyTemplateDTO.getJobGuid(), bsmApplyTemplateDTO.getDataType(), bsmApplyTemplateDTO.getRegionCode(), bsmApplyTemplateDTO.getGenNum());
//            if (bsmApplyTableTemplateDTOList != null && bsmApplyTableTemplateDTOList.size() != 0) {
//                Paragraph paragraph = setApplyTableData(bsmApplyTableTemplateDTOList);
//                //打印最终申请表
//                return pdfExportService.savePdf(myFileExportPath, myFileExportPath + padPath, guid, paragraph);
//            }
//            return padPath;
//        }
//        return null;
//    }

//    public String printStaPdf(String guid) {
//        TransportJobInBranchDTO transportJobInBranchDTO = transportJobBranchService.findBsmApplyTemplateByAppGuid(guid);
//        if (transportJobInBranchDTO != null) {
//            return printPdf(transportJobInBranchDTO.getGuid().toString());
//        }
//        return null;
//    }

//    /**
//     * 申请表数据生成
//     * map;表单数据
//     * codeMap：二维码数据
//     * bsmApplyTemplateDTO：数据源
//     */
//    protected void setApplyPrintData(Map<String, String> map, BsmApplyTemplateDTO bsmApplyTemplateDTO) {
//        map.put("app_code", bsmApplyTemplateDTO.getAppCode()); //编号
//        map.put("org_name", bsmApplyTemplateDTO.getOrgName()); //台站设置使用人
//        map.put("org_addr", bsmApplyTemplateDTO.getOrgAddr()); //通信地址
//        map.put("org_code", bsmApplyTemplateDTO.getOrgCode()); //统一社会信用代码
//        map.put("org_post", bsmApplyTemplateDTO.getOrgPost()); //邮政编码
//        map.put("org_link_person", bsmApplyTemplateDTO.getOrgLinkPerson()); //联系人
//        map.put("org_phone", bsmApplyTemplateDTO.getOrgPhone()); //联系电话
//        map.put("org_mail", bsmApplyTemplateDTO.getOrgMail()); //电子邮箱
//        //申请类型
//        //初始化类型值
//        map.put("data_type_new", "Off");
//        map.put("data_type_update", "Off");
//        map.put("data_type_delete", "Off");
//        map.put("data_type_on", "Off");
//
//        if ("1".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_new", "On");
//        else if ("2".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_update", "On");
//        else if ("3".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_delete", "On");
//        else if ("4".equals(bsmApplyTemplateDTO.getDataType())) map.put("data_type_on", "On");
//        map.put("station_count", bsmApplyTemplateDTO.getStationCount()); //基站数量
//    }
//
//    /**
//     * 生成pdf表格
//     */
//    protected Paragraph setApplyTableData(List<BsmApplyTableTemplateDTO> bsmApplyTableTemplateDTOList) {
//        try {
//            BaseFont bfChinese = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
//            Font fontChinese = new Font(bfChinese, 10.6F, Font.NORMAL);// 五号
//            Paragraph ret = new Paragraph();
//            PdfPTable tableBox = new PdfPTable(11);
//            tableBox.setWidths(new float[]{0.05f, 0.15f, 0.15f, 0.05f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.05f, 0.05f});// 每个单元格占多宽
//            tableBox.setWidthPercentage(100);
//            //设置表头
//            tableBox.addCell(setCell(new Phrase("技术资料信息", fontChinese), 11, 1, Element.ALIGN_LEFT, new BaseColor(184, 204, 228)));
//            tableBox.addCell(setCell(new Phrase("序号", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("台站名称", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("基站/小区识别码", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("技术体制", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("台址（编号）", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("经度", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("纬度", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("发射频率（MHz）", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("接收频率（MHz）", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("最大发射功率（W）", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            tableBox.addCell(setCell(new Phrase("天线距地高度（m）", fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            //设置表体
//            for (BsmApplyTableTemplateDTO bsmApplyTableTemplateDTO : bsmApplyTableTemplateDTOList) {
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getId(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getBtsName(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getBtsId(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getTechType(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getCounty(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getLongitude(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getLatitude(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getSendFreq(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getAccFreq(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getMaxEmissivePower(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//                tableBox.addCell(setCell(new Phrase(bsmApplyTableTemplateDTO.getHeight(), fontChinese), 1, 1, Element.ALIGN_CENTER, BaseColor.WHITE));
//            }
//            ret.add(tableBox);
//            return ret;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * 每个cell设置
//     * phrase:phrase
//     * colSpan:合并列
//     * rowSpan：合并行
//     */
//    protected PdfPCell setCell(Phrase phrase, int colSpan, int rowSpan, int alignment, BaseColor backgroundColor) {
//        PdfPCell cells = new PdfPCell(phrase);
//        cells.setUseAscender(true);
//        cells.setMinimumHeight(20f);
//        cells.setHorizontalAlignment(alignment);
//        cells.setVerticalAlignment(5);
//        cells.setColspan(colSpan);
//        cells.setRowspan(rowSpan);
//        cells.setNoWrap(false);
//        cells.setBackgroundColor(backgroundColor);
//        return cells;
//    }

    /**
     *
     */
    public PageInfo<TransportRawBtsDealLog> findAllDetailByUser(TransportJobBranchDTO transportJobBranchDTO, String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        return new PageHandle(transportJobBranchDTO).buildPage(transportJobBranchService.findAllDetailByUser(transportJobBranchDTO.getGuid().toString(), usersDTO.getUserId()));
    }

    /**
     * 根据任务、运营商、类型、制式查询
     */
    public TransportJobBranchDTO findOneByJobUserTypeGen(String jobGuid, String userGuid, String dataType, String genNum, String regionCode, String techType) {
        return transportJobBranchService.findOneByJobUserTypeGen(jobGuid, userGuid, dataType, genNum, regionCode, techType);
    }

    public void updateByJobId(String jobId, String dataType, String genNum, String isCompare) {
        transportJobBranchService.updateByJobId(jobId, dataType, genNum, isCompare);
    }

    /**
     * 判断此job下是否已经全部确认完
     */
    public int judgeAllConfirmFail(String jobGuid) {
        return transportJobBranchService.judgeAllConfirmFail(jobGuid);
    }
}
