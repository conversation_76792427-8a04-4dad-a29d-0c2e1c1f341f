//package com.bsm.v4.api.web.config;
//
//import com.caictframework.bigdata.hadoop.hdfs.HdfsClient;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.hadoop.fs.FileSystem;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * @author: ycp
// * @createTime: 2023/09/01 10:22
// * @company: 成渝（成都）信息通信研究院
// * @description: hdfs配置类
// */
//@Configuration
//@ConditionalOnProperty(name="hadoop.url")
//@Slf4j
//public class HadoopConfig {
//
//    @Value("${hadoop.url}")
//    private String nameNode;
//
//    @Value("${hadoop.user}")
//    private String user;
//
//    /**
//     * Configuration conf=new Configuration（）；
//     * 创建一个Configuration对象时，其构造方法会默认加载hadoop中的两个配置文件，
//     * 分别是hdfs-site.xml以及core-site.xml，这两个文件中会有访问hdfs所需的参数值，
//     * 主要是fs.default.name，指定了hdfs的地址，有了这个地址客户端就可以通过这个地址访问hdfs了。
//     * 即可理解为configuration就是hadoop中的配置信息。
//     * @return
//     */
//    @Bean("fileSystem")
//    public FileSystem createFs() throws Exception{
//        //读取配置文件
////        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
////
////        conf.set("fs.defalutFS", nameNode);
////        conf.set("dfs.replication", "3");
////        conf.set("dfs.client.use.datanode.hostname", "true");
//        FileSystem fs = null;
//
//        // 返回指定的文件系统,如果在本地测试，需要使用此种方法获取文件系统
//        try {
//            fs = new HdfsClient(nameNode,user).get();
//        } catch (Exception e) {
//            log.error("", e);
//        }
//        return  fs;
//    }
//}
