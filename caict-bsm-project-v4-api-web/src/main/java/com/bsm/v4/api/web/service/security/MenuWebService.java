package com.bsm.v4.api.web.service.security;

import com.bsm.v4.system.model.dto.security.MenuDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.caictframework.data.service.BasicWebService;
import com.bsm.v4.domain.security.service.security.MenuService;
import com.bsm.v4.system.model.entity.security.Menu;
import com.bsm.v4.system.model.vo.security.MenuVO;
import com.caictframework.data.util.SnowflakeManager;
import com.caictframework.utils.util.TreeBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by dengsy on 2023-02-10.
 */
@Service
public class MenuWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(MenuWebService.class);

    @Autowired
    private MenuService menuService;

    @Autowired
    private RoleMenuWebService roleMenuWebService;

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private SnowflakeManager snowflakeManager;

    /**
     * 保存
     */
    public Map<String, Object> save(MenuVO menuVO) {
        var menu = new Menu();
        BeanUtils.copyProperties(menuVO, menu);

        menu.setUpdateDateTime(new Date());
        var id = menuService.save(menu);

        return this.basicReturnResultJson(id);
    }

    /**
     * 批量添加
     */
    public Map<String, Object> insertBatch(List<MenuVO> menuVOList) {
        var list = new ArrayList<Menu>();
        menuVOList.forEach(menuVO -> {
            var menu = new Menu();
            BeanUtils.copyProperties(menuVO, menu);
            menu.setUpdateDateTime(new Date());
            var id = snowflakeManager.nextValue();
            menu.setId(String.valueOf(id));
            list.add(menu);
        });
        menuService.insertBatch(list);

        return this.basicReturnResultJson("批量添加成功");
    }

    /**
     * 查询全部
     */
    public Map<String, Object> findAll() {
        return this.basicReturnResultJson(menuService.findAll());
    }

    /**
     * 查询所有菜单（树形结构）
     */
    public Map<String, Object> findAllTree() {
        List<MenuDTO> menuList = menuService.findAllDto();
        if (menuList != null)
            return this.basicReturnResultJson(new TreeBuilder<MenuDTO>(menuList).buildTree());
        return null;
    }

    /**
     * 查询单个菜单（根据ID）
     */
    public Map<String, Object> findOne(String id) {
        MenuDTO menuDTO = new MenuDTO();
        Menu menu = menuService.findById(id);
        if (menu != null) {
            BeanUtils.copyProperties(menu, menuDTO);
        }
        return this.basicReturnResultJson(menuDTO);
    }

    /**
     * 查询所有菜单（根据菜单类型）
     */
    public Map<String, Object> findAllByType(String type) {
        List<MenuDTO> menuDTOList = menuService.findAllByType(type);
        if (menuDTOList != null) return this.basicReturnResultJson(menuDTOList);
        return null;

    }

    /**
     * 查询所有菜单（根据父级菜单Id）
     */
    public Map<String, Object> findAllByParentId(String parentId) {
        List<MenuDTO> menuDTOList = menuService.findAllByParentId(parentId);
        if (menuDTOList != null) return this.basicReturnResultJson(menuDTOList);
        return null;
    }

    /**
     * 查询所有菜单（根据父级菜单Id）（树形结构）
     */
    public Map<String, Object> findAllByParentIdTree(String parentId) {
        List<MenuDTO> menuDTOList = menuService.findAllByParentId(parentId);
        if (menuDTOList != null)
            return this.basicReturnResultJson(new TreeBuilder<MenuDTO>(menuDTOList).buildTree(parentId));
        return null;
    }

    /**
     * 删除菜单（根据id）
     */
    @Transactional
    public Map<String, Object> delete(String id) {
        Menu menu = menuService.findById(id);
        //跟新父级
        menuService.updateParent(id, menu.getParentId());
        //删除角色菜单关联
        roleMenuWebService.deleteAllByMenu(id);
        //删除
        return this.basicReturnResultJson(menuService.delete(id));
    }

    /**
     * 根据用户查询菜单（树形结构）
     */
    public Map<String, Object> findAllByUserTree(String token) {
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        if (usersDTO == null) return null;
        List<MenuDTO> menuDTOList = menuService.findAllByUser(usersDTO.getUserId());
        if (menuDTOList != null) return this.basicReturnResultJson(new TreeBuilder<MenuDTO>(menuDTOList).buildTree());
        return null;
    }
}
