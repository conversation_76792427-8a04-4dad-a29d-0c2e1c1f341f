package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtEquTService;
import com.bsm.v4.system.model.entity.business.station.RsbtEquT;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class RsbtEquTWebService extends BasicWebService {

    @Autowired
    private RsbtEquTService rsbtEquTService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtEquT> rsbtEquTList) {
        return rsbtEquTService.insertBatch(rsbtEquTList);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtEquT> rsbtEquTList) {
        return rsbtEquTService.updateBatch(rsbtEquTList);
    }

    /**
     * 批量删除
     */
    public int deleteBatch(List<RsbtEquT> rsbtEquTList) {
        return rsbtEquTService.deleteBatch(Collections.singletonList(rsbtEquTList));
    }
}
