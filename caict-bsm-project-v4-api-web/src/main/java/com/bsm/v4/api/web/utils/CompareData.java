package com.bsm.v4.api.web.utils;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDeal;
import com.bsm.v4.system.model.entity.business.transfer.TransportSchedule;
import com.caictframework.utils.util.JSONResult;

public class CompareData {

    public static boolean compareFilds(TransportRawBtsDeal transportRawBts, AsyncRawBts asyncRawBts) {
        if (!transportRawBts.getCellName().equals(asyncRawBts.getCellName())) {
            return false;
        }
        if(asyncRawBts.getBtsName().contains("(拉远站)")){
            String originalBtsName = asyncRawBts.getBtsName().replace("(拉远站)", "");
            if (!transportRawBts.getBtsName().equals(originalBtsName)) {
                return false;
            }
        }

        if (!transportRawBts.getTechType().equals(asyncRawBts.getTechType())) {
            return false;
        }
        if (!transportRawBts.getLocation().equals(asyncRawBts.getLocation())) {
            return false;
        }
        if (!transportRawBts.getLongitude().equals(asyncRawBts.getLongitude())) {
            return false;
        }
        if (!transportRawBts.getLatitude().equals(asyncRawBts.getLatitude())) {
            return false;
        }
        if (!transportRawBts.getSendStartFreq().equals(asyncRawBts.getSendStartFreq())) {
            return false;
        }
        if (!transportRawBts.getSendEndFreq().equals(asyncRawBts.getSendEndFreq())) {
            return false;
        }
        if (!transportRawBts.getAccStartFreq().equals(asyncRawBts.getAccStartFreq())) {
            return false;
        }
        if (!transportRawBts.getAccEndFreq().equals(asyncRawBts.getAccEndFreq())) {
            return false;
        }
        if (!transportRawBts.getMaxEmissivePower().equals(asyncRawBts.getMaxEmissivePower())) {
            return false;
        }
        if (!transportRawBts.getHeight().equals(asyncRawBts.getHeight())) {
            return false;
        }
        if (!transportRawBts.getCounty().equals(asyncRawBts.getCounty())) {
            return false;
        }
        return true;
    }

    public static JSONObject comparison(TransportRawBts transportRawBts, TransportSchedule transportSchedule) {
        if (!transportRawBts.getCellName().equals(transportSchedule.getCellName())) {
            return JSONResult.getFailureJson(false, "小区名称不一致，上报数据为：" + transportSchedule.getCellName() + "，对比数据为：" + transportRawBts.getCellName());
        }
        if (!transportRawBts.getBtsName().equals(transportSchedule.getBtsName())) {
            return JSONResult.getFailureJson(false, "基站名称不一致，上报数据为：" + transportSchedule.getBtsName() + "，对比数据为：" + transportRawBts.getBtsName());
        }
        if (!transportRawBts.getBtsId().equals(transportSchedule.getBtsId())) {
            return JSONResult.getFailureJson(false, "基站识别码不一致，上报数据为：" + transportSchedule.getBtsId() + "，对比数据为：" + transportRawBts.getBtsId());
        }
        if (!transportRawBts.getTechType().equals(transportSchedule.getTechType())) {
            return JSONResult.getFailureJson(false, "技术体制不一致，上报数据为：" + transportSchedule.getTechType() + "，对比数据为：" + transportRawBts.getTechType());
        }
        if (!transportRawBts.getLocation().equals(transportSchedule.getLocation())) {
            return JSONResult.getFailureJson(false, "台址不一致，上报数据为：" + transportSchedule.getLocation() + "，对比数据为：" + transportRawBts.getLocation());
        }
        if (!transportRawBts.getLongitude().equals(transportSchedule.getLongitude())) {
            return JSONResult.getFailureJson(false, "经度不一致，上报数据为：" + transportSchedule.getLongitude() + "，对比数据为：" + transportRawBts.getLongitude());
        }
        if (!transportRawBts.getLatitude().equals(transportSchedule.getLatitude())) {
            return JSONResult.getFailureJson(false, "纬度不一致，上报数据为：" + transportSchedule.getLatitude() + "，对比数据为：" + transportRawBts.getLatitude());
        }
        if (!transportRawBts.getSendStartFreq().equals(transportSchedule.getSendStartFreq())) {
            return JSONResult.getFailureJson(false, "发射起始频率不一致，上报数据为：" + transportSchedule.getSendStartFreq() + "，对比数据为：" + transportRawBts.getSendStartFreq());
        }
        if (!transportRawBts.getSendEndFreq().equals(transportSchedule.getSendEndFreq())) {
            return JSONResult.getFailureJson(false, "发射终止频率不一致，上报数据为：" + transportSchedule.getSendEndFreq() + "，对比数据为：" + transportRawBts.getSendEndFreq());
        }
        if (!transportRawBts.getAccStartFreq().equals(transportSchedule.getAccStartFreq())) {
            return JSONResult.getFailureJson(false, "接收起始频率不一致，上报数据为：" + transportSchedule.getAccStartFreq() + "，对比数据为：" + transportRawBts.getAccStartFreq());
        }
        if (!transportRawBts.getAccEndFreq().equals(transportSchedule.getAccEndFreq())) {
            return JSONResult.getFailureJson(false, "接收终止频率不一致，上报数据为：" + transportSchedule.getAccEndFreq() + "，对比数据为：" + transportRawBts.getAccEndFreq());
        }
        if (!transportRawBts.getMaxEmissivePower().equals(transportSchedule.getMaxEmissivePower())) {
            return JSONResult.getFailureJson(false, "最大发射功率不一致，上报数据为：" + transportSchedule.getMaxEmissivePower() + "，对比数据为：" + transportRawBts.getMaxEmissivePower());
        }
        if (!transportRawBts.getHeight().equals(transportSchedule.getHeight())) {
            return JSONResult.getFailureJson(false, "高度不一致，上报数据为：" + transportSchedule.getHeight() + "，对比数据为：" + transportRawBts.getHeight());
        }
        if (!transportRawBts.getCounty().equals(transportSchedule.getCounty())) {
            return JSONResult.getFailureJson(false, "所在市位置不一致，上报数据为：" + transportSchedule.getCounty() + "，对比数据为：" + transportRawBts.getCounty());
        }
        if (!transportRawBts.getDataType().equals(transportSchedule.getDataType())) {
            return JSONResult.getFailureJson(false, "数据类型不一致，上报数据为：" + transportSchedule.getDataType() + "，对比数据为：" + transportRawBts.getDataType());
        }
        return JSONResult.getSuccessJson(true, "比对信息一致！");
    }
}
