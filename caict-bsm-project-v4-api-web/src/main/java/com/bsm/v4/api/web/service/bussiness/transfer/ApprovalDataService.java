package com.bsm.v4.api.web.service.bussiness.transfer;

import com.bsm.v4.api.web.service.bussiness.station.*;
import com.bsm.v4.system.model.contrust.DBBoolConst;
import com.bsm.v4.system.model.dto.business.station.*;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.business.stationbak.*;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.business.station.*;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalSchedule;
import com.caictframework.utils.util.VerificationCode;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ApprovalDataService {

    @Autowired
    private RsbtStationWebService rsbtStationWebService;
    @Autowired
    private ApprovalScheduleWebService approvalScheduleWebService;
    @Autowired
    private AsyncRawBtsWebService asyncRawBtsWebService;
    @Autowired
    private RsbtEquWebService rsbtEquWebService;
    @Autowired
    private RsbtFreqWebService rsbtFreqWebService;
    @Autowired
    private RsbtAntfeedWebService rsbtAntfeedWebService;
    @Autowired
    private RsbtNetWebService rsbtNetWebService;

    //批量数据处理
    public void HandleAdded(String appGuid, OrgDTO rsbtOrg, StationScheduleDataDTO stationScheduleDataDTO, List<RsbtNet> rsbtNetList, List<RsbtTraffic> rsbtTrafficList,
                            List<RsbtStation> rsbtStationList, List<RsbtStationAppendix> rsbtStationAppendixList, List<RsbtStationT> rsbtStationTList,
                            List<RsbtStationBak> rsbtStationBakList, List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList,
                            List<RsbtAntfeedT> rsbtAntfeedTList, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList,
                            List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList,
                            List<AsyncRawBts> asyncRawBtsListInsert, String appCode) {
        //构建台站,台站->申请表关系,台站，扇区，频率
        addStation(appGuid, rsbtOrg, stationScheduleDataDTO, rsbtTrafficList, rsbtNetList, rsbtStationList, rsbtStationAppendixList, rsbtStationTList, rsbtStationBakList,
                rsbtAntfeedList, rsbtAntfeedAppendixList, rsbtAntfeedTList, rsbtEquList, rsbtEquAppendixList, rsbtEquTList,
                rsbtFreqList, rsbtFreqAppendixList, rsbtFreqTList, rsbtEafList, asyncRawBtsListInsert, appCode);
    }

    /**
     * 新增台站、扇区、台站、申请表关系
     */
    protected void addStation(String appGuid, OrgDTO rsbtOrg, StationScheduleDataDTO stationScheduleDataDTO, List<RsbtTraffic> rsbtTrafficList, List<RsbtNet> rsbtNetList,
                              List<RsbtStation> rsbtStationList, List<RsbtStationAppendix> rsbtStationAppendixList, List<RsbtStationT> rsbtStationTList,
                              List<RsbtStationBak> rsbtStationBakList, List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList,
                              List<RsbtAntfeedT> rsbtAntfeedTList, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList,
                              List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList,
                              List<AsyncRawBts> asyncRawBtsList, String appCode) {
        RsbtStation rsbtStation = new RsbtStation();
        RsbtStationAppendix rsbtStationAppendix = new RsbtStationAppendix();
        RsbtStationT rsbtStationT = new RsbtStationT();
        RsbtStationBak rsbtStationBak = new RsbtStationBak();
        RsbtNet rsbtNet = new RsbtNet();

        //构建net
        buildRsbtNet(rsbtNet, rsbtOrg, stationScheduleDataDTO);
        rsbtStation.setNetGuid(rsbtNet.getGuid());
        rsbtNetList.add(rsbtNet);
        //构建station
        buildRsbStation(rsbtStation, rsbtStationAppendix, rsbtStationT, rsbtStationBak, rsbtOrg, appCode, appGuid, stationScheduleDataDTO);
        //添加专网申请表编号，用于专网和内网申请表关联
        rsbtStation.setAppCode(appCode);


        List<ApprovalSchedule> approvalScheduleListRe = approvalScheduleWebService.findAllByAppGuid(appGuid, stationScheduleDataDTO.getBtsId(),
                stationScheduleDataDTO.getExpandStation(), stationScheduleDataDTO.getDataType());
        if (approvalScheduleListRe != null && approvalScheduleListRe.size() != 0) {
            for (ApprovalSchedule approvalSchedule : approvalScheduleListRe) {
                rsbtStationBak.setIsValid("1");
                if (approvalSchedule.getIsValid() != 1) {
                    rsbtStationBak.setIsValid("3");
                }
                AsyncRawBts asyncRawBts = getAsyncRawBts(approvalSchedule, approvalSchedule.getDataType());
                addOther(approvalSchedule.getDataType(), new StationSecDTO(rsbtStation.getGuid(), asyncRawBts), rsbtAntfeedList, rsbtAntfeedAppendixList, rsbtAntfeedTList,
                        rsbtEquList, rsbtEquAppendixList, rsbtEquTList, rsbtFreqList, rsbtFreqAppendixList, rsbtFreqTList, rsbtEafList, rsbtTrafficList);

                asyncRawBtsList.add(asyncRawBts);
            }
        }

        rsbtStationList.add(rsbtStation);
        rsbtStationAppendixList.add(rsbtStationAppendix);
        rsbtStationTList.add(rsbtStationT);
        rsbtStationBakList.add(rsbtStationBak);

    }

    /**
     * 构建Net表
     */
    protected void buildRsbtNet(RsbtNet rsbtNet, OrgDTO rsbtOrg, StationScheduleDataDTO stationScheduleDataDTO) {
        rsbtNet.setGuid(VerificationCode.myUUID());
        // 批准日期
        if ("1".equals(stationScheduleDataDTO.getDataType())) {
            rsbtNet.setNetArea("4");  // 4 省内
            rsbtNet.setNetStartDate(new Date());    // 启用日期
        } else {
            RsbtNet rsbtNetUpdate = rsbtNetWebService.getRsbtNetByStationGuid(stationScheduleDataDTO.getStationGuid());
            if (rsbtNetUpdate != null) {
                rsbtNet.setGuid(rsbtNetUpdate.getGuid());
            }
        }
        rsbtNet.setNetConfirmDate(new Date()); // 批准日期
        rsbtNet.setOrgGuid(rsbtOrg.getGuid());
        rsbtNet.setOrgCode(rsbtOrg.getOrgCode());
        rsbtNet.setNetTs(stationScheduleDataDTO.getTechType());

    }

    /**
     * 构建新的RsbtStation对象
     *
     * @param stationScheduleDataDTO
     * @return
     */
    protected void buildRsbStation(RsbtStation rsbtStation, RsbtStationAppendix rsbtStationAppendix, RsbtStationT rsbtStationT, RsbtStationBak rsbtStationBak,
                                   OrgDTO rsbtOrg, String appCode, String appGuid, StationScheduleDataDTO stationScheduleDataDTO) {
        RsbtStation rsbtStationUpdate = null;
        rsbtStation.setGuid(stationScheduleDataDTO.getStationGuid());
        if ("1".equals(stationScheduleDataDTO.getDataType())) {
            rsbtStation.setStatAppType("C"); // T 无线电台（站）设置申请表
            rsbtStation.setStatStatus("1");  // 1 在用
            rsbtStation.setStatDateStart(new Date());
            rsbtStation.setStatEquSum(0);
            // 036 中华人民共和国
            rsbtStation.setMemo("036");

            rsbtStationAppendix.setIsLicense("0");
            rsbtStationAppendix.setIsApply("0");
            rsbtStationAppendix.setGmtCreate(new Date());
            rsbtStationAppendix.setGmtModified(new Date());
            rsbtStationAppendix.setIsDeleted(DBBoolConst.FALSE);

        } else {
            rsbtStationUpdate = rsbtStationWebService.querySattionByBts(stationScheduleDataDTO.getBtsId(), stationScheduleDataDTO.getTechType(), rsbtOrg.getOrgCode());
            if (rsbtStationUpdate != null) {
                rsbtStation.setGuid(rsbtStationUpdate.getGuid());
            }
            rsbtStationAppendix.setGmtModified(new Date());
        }
        rsbtStationBak.setIsSync("0");
        rsbtStationBak.setIsShow("0");
        rsbtStation.setOrgCode(rsbtOrg.getOrgCode());
        rsbtStation.setStatName(stationScheduleDataDTO.getBtsName());
        rsbtStation.setStatAddr(stationScheduleDataDTO.getLocation().length() > 40 ? stationScheduleDataDTO.getLocation().substring(0, 40) : stationScheduleDataDTO.getLocation());
        rsbtStation.setStatAreaCode(stationScheduleDataDTO.getRegionCode());

        if (!("".equals(stationScheduleDataDTO.getAltitude()) || stationScheduleDataDTO.getAltitude() == null || "null".equals(stationScheduleDataDTO.getAltitude()))) {
            rsbtStation.setStatAt(Double.parseDouble(stationScheduleDataDTO.getAltitude())); //海拔
            rsbtStationBak.setStatAt(Double.parseDouble(stationScheduleDataDTO.getAltitude())); //海拔
        } else {
            rsbtStation.setStatAt(0d);
            rsbtStationBak.setStatAt(0d);
        }

        if (null != stationScheduleDataDTO.getLongitude()) {
            rsbtStation.setStatLg(stationScheduleDataDTO.getLongitude());
        } else {
            rsbtStation.setStatLg(0.0);
        }

        if (null != stationScheduleDataDTO.getLatitude()) {
            rsbtStation.setStatLa(stationScheduleDataDTO.getLatitude());
        } else {
            rsbtStation.setStatLa(0.0);
        }

        //构建备份表
        BeanUtils.copyProperties(rsbtStation, rsbtStationBak);
        rsbtStationBak.setStatDateStart(new Date());
        rsbtStationBak.setGuid(VerificationCode.myUUID());
        rsbtStationBak.setStationGuid(rsbtStation.getGuid());
        rsbtStationBak.setUserGuid(rsbtOrg.getUsersId());
        rsbtStationBak.setIsShow("0");
        // RSBT_STATION_T
        rsbtStationT.setGuid(rsbtStation.getGuid());  // GUID
        rsbtStationT.setStDTecType(stationScheduleDataDTO.getTechType()); // 技术体制
        rsbtStationT.setStCCode(stationScheduleDataDTO.getBtsId());  // 基站编号
        if (stationScheduleDataDTO.getStServR() != null) {
            rsbtStationT.setStServR(Double.valueOf(stationScheduleDataDTO.getStServR()));  // 服务半径
        }
        rsbtStationBak.setStCCode(stationScheduleDataDTO.getBtsId());  // 基站编号

        // RsbtStationAppendix
        rsbtStationAppendix.setGuid(rsbtStation.getGuid());
        //信号计算
        rsbtStationAppendix.setGenNum(stationScheduleDataDTO.getGenNum());
        rsbtStationBak.setGenNum(stationScheduleDataDTO.getGenNum());
        rsbtStationAppendix.setUserGuid(stationScheduleDataDTO.getUserGuid());
        rsbtStationAppendix.setCounty(stationScheduleDataDTO.getCounty());
        rsbtStationAppendix.setExpandStation(stationScheduleDataDTO.getExpandStation());
        rsbtStationBak.setExpandStation(stationScheduleDataDTO.getExpandStation());
        rsbtStationAppendix.setAttributeStation(stationScheduleDataDTO.getAttributeStation());
        /*if (rsbtStationWebService.judgeIsArea(regionCode, stationScheduleDataDTO.getLatitude(), stationScheduleDataDTO.getLongitude())){
            rsbtStationAppendix.setIsArea("1");
        }else {
            rsbtStationAppendix.setIsArea("2");
        }*/
        rsbtStationBak.setCounty(stationScheduleDataDTO.getCounty());
        rsbtStationAppendix.setTechType(stationScheduleDataDTO.getTechType());
        rsbtStationBak.setNetTs(stationScheduleDataDTO.getTechType());
        rsbtStationAppendix.setOrgType(rsbtOrg.getType());
        rsbtStationBak.setOrgType(rsbtOrg.getType());
        rsbtStationBak.setDataType(stationScheduleDataDTO.getDataType());
        rsbtStationBak.setAppCode(appCode);
        rsbtStationBak.setAppGuid(appGuid);
        //增加校验状态
        rsbtStationBak.setIsValid(stationScheduleDataDTO.getIsValid());
        rsbtStationBak.setBakDate(new Date());
        if ("3".equals(stationScheduleDataDTO.getDataType())) {
            if (rsbtStationUpdate != null) {
                BeanUtils.copyProperties(rsbtStationUpdate, rsbtStationBak);
                rsbtStationBak.setGuid(VerificationCode.myUUID());
                rsbtStationBak.setDataType(stationScheduleDataDTO.getDataType());
                rsbtStationBak.setAppGuid(appGuid);
                rsbtStationBak.setAppCode(appCode);
                rsbtStationBak.setStationGuid(rsbtStation.getGuid());
                rsbtStationBak.setUserGuid(rsbtStation.getGuid());
                //增加校验状态 删除 3
                rsbtStationBak.setIsSync("0");
                rsbtStationBak.setIsShow("0");
            }
        }
    }

    /**
     * 添加一个扇区、频率、天线
     *
     * @param stationSecDto
     */
    protected void addOther(String dataType, StationSecDTO stationSecDto, List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList,
                            List<RsbtAntfeedT> rsbtAntfeedTList, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList,
                            List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList,
                            List<RsbtTraffic> rsbtTrafficList) {

        String guid = VerificationCode.myUUID();
        RsbtEaf rsbtEaf = new RsbtEaf();
        rsbtEaf.setGuid(guid);
        addEqu(dataType, stationSecDto, rsbtEquList, rsbtEquAppendixList, rsbtEquTList, rsbtEaf);

        addFreq(dataType, stationSecDto, rsbtFreqList, rsbtFreqAppendixList, rsbtFreqTList, rsbtEaf);

        addAntfeed(dataType, stationSecDto, rsbtAntfeedList, rsbtAntfeedAppendixList, rsbtAntfeedTList, rsbtEaf, rsbtTrafficList);
        rsbtEaf.setStationGuid(stationSecDto.getStationGuid());
        if ("1".equals(dataType)) {
            rsbtEafList.add(rsbtEaf);
        }

    }

    //添加天线数据
    protected void addAntfeed(String dataType, StationSecDTO stationSecDto, List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList,
                              List<RsbtAntfeedT> rsbtAntfeedTList, RsbtEaf rsbtEaf, List<RsbtTraffic> rsbtTrafficList) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtAntfeed rsbtAntfeedUpdate = rsbtAntfeedWebService.findOneByStationCell(stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts().getCellId());
            guid = rsbtAntfeedUpdate.getGuid();
        }
        RsbtAntfeed rsbtAntfeed = new RsbtAntfeed();
        RsbtAntfeedAppendix rsbtAntfeedAppendix = new RsbtAntfeedAppendix();
        RsbtAntfeedT rsbtAntfeedT = new RsbtAntfeedT();
        buidAntFeed(guid, stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts(), rsbtAntfeed, rsbtAntfeedAppendix, rsbtAntfeedT);

        rsbtAntfeedList.add(rsbtAntfeed);
        rsbtAntfeedAppendixList.add(rsbtAntfeedAppendix);
        rsbtAntfeedTList.add(rsbtAntfeedT);
        rsbtEaf.setAntGuid(guid);
        RsbtTraffic rsbtTraffic = buildTraffic(guid, stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts());
        rsbtTrafficList.add(rsbtTraffic);
    }

    //构造天线数据

    /**
     * @param guid        RsbtAntfeed
     * @param stationGuid 台站GUID
     * @param asyncRawBts 基本信息
     */
    protected void buidAntFeed(String guid, String stationGuid, AsyncRawBts asyncRawBts, RsbtAntfeed rsbtAntfeed,
                               RsbtAntfeedAppendix rsbtAntfeedAppendix, RsbtAntfeedT rsbtAntfeedT) {

        rsbtAntfeed.setGuid(guid);
        rsbtAntfeed.setStationGuid(stationGuid);
        rsbtAntfeed.setAntWorkType("");
        //天线据地高度
        if (!("".equals(asyncRawBts.getHeight()) || asyncRawBts.getHeight() == null)) {
            rsbtAntfeed.setAntHight(Double.parseDouble(asyncRawBts.getHeight()));
        } else {
            rsbtAntfeed.setAntHight(0d);
        }
        //天线增益
        if (!("".equals(asyncRawBts.getAntennaGain()) || asyncRawBts.getAntennaGain() == null || "null".equals(asyncRawBts.getAntennaGain()))) {
            rsbtAntfeed.setAntGain(Double.parseDouble(asyncRawBts.getAntennaGain()));
        } else {
            rsbtAntfeed.setAntGain(0d);
        }

        //天线方位角
        if (!("".equals(asyncRawBts.getAntennaAzimuth()) || asyncRawBts.getAntennaAzimuth() == null || "null".equals(asyncRawBts.getAntennaAzimuth()))) {
            rsbtAntfeed.setAntAngle(Double.parseDouble(asyncRawBts.getAntennaAzimuth()));
        } else {
            rsbtAntfeed.setAntAngle(0d);
        }
        //馈线系统总损耗
        if (!("".equals(asyncRawBts.getFeederLoss()) || asyncRawBts.getFeederLoss() == null || "null".equals(asyncRawBts.getFeederLoss()))) {
            rsbtAntfeed.setFeedLose(Double.parseDouble(asyncRawBts.getFeederLoss()));
        } else {
            rsbtAntfeed.setFeedLose(0d);
        }

        //天线生产厂家
        rsbtAntfeed.setAntMenu(asyncRawBts.getAntennaFactory());
        //天线类型
        rsbtAntfeed.setAntType(asyncRawBts.getAntennaModel());
        //极化方式
        rsbtAntfeed.setAntPole(asyncRawBts.getPolarizationMode());
        rsbtAntfeed.setAntRpole(asyncRawBts.getPolarizationMode());
        rsbtAntfeed.setAntEpole(asyncRawBts.getPolarizationMode());

        rsbtAntfeedAppendix.setGuid(guid);
        rsbtAntfeedAppendix.setGmtCreate(new Date());
        rsbtAntfeedAppendix.setGmtModified(new Date());
        rsbtAntfeedAppendix.setIsDeleted(DBBoolConst.FALSE);

        rsbtAntfeedT.setGuid(guid);
        rsbtAntfeedT.setAtCcode(asyncRawBts.getCellId());
        //收倾角
        if (!"".equals(asyncRawBts.getAtRang()) && asyncRawBts.getAtRang() != null) {
            rsbtAntfeedT.setAtRang(Double.valueOf(asyncRawBts.getAtRang()));
        }
        //发倾角
        if (!"".equals(asyncRawBts.getAtEang()) && asyncRawBts.getAtEang() != null) {
            rsbtAntfeedT.setAtEang(Double.valueOf(asyncRawBts.getAtEang()));
        }
    }

    //构造参数
    protected RsbtTraffic buildTraffic(String guid, String stationGuid, AsyncRawBts asyncRawBts) {
        RsbtTraffic rsbtTraffic = new RsbtTraffic();
        rsbtTraffic.setTrafficGuid(guid);
        rsbtTraffic.setStationGuid(stationGuid);
        rsbtTraffic.setSectionCode(asyncRawBts.getCellId());
        rsbtTraffic.setFreqEfbC(Double.parseDouble(asyncRawBts.getSendEndFreq()));
        rsbtTraffic.setFreqEfeC(Double.parseDouble(asyncRawBts.getSendStartFreq()));
        rsbtTraffic.setNetTs(asyncRawBts.getTechType());
        rsbtTraffic.setTrfDate(asyncRawBts.getTrfDate());
        rsbtTraffic.setTrfUser(asyncRawBts.getTrfUser() != null ? asyncRawBts.getTrfUser() : 0d);
        rsbtTraffic.setTrfData(asyncRawBts.getTrfData() != null ? asyncRawBts.getTrfData() : 0d);
        return rsbtTraffic;
    }

    //添加参数数据
    protected void addFreq(String dataType, StationSecDTO stationSecDto, List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, RsbtEaf rsbtEaf) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtFreq rsbtFreqUpdate = rsbtFreqWebService.findOneByStationCell(stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts().getCellId());
            guid = rsbtFreqUpdate.getGuid();
        }
        RsbtFreq rsbtFreq = new RsbtFreq();
        RsbtFreqAppendix rsbtFreqAppendix = new RsbtFreqAppendix();
        RsbtFreqT rsbtFreqT = new RsbtFreqT();
        buildFreq(guid, stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts(), rsbtFreq, rsbtFreqAppendix, rsbtFreqT);

        rsbtFreqList.add(rsbtFreq);
        rsbtFreqAppendixList.add(rsbtFreqAppendix);
        rsbtFreqTList.add(rsbtFreqT);
        rsbtEaf.setFreqGuid(guid);
    }

    //构造参数
    protected void buildFreq(String freqGuid, String stationGuid, AsyncRawBts asyncRawBts, RsbtFreq rsbtFreq, RsbtFreqAppendix rsbtFreqAppendix, RsbtFreqT rsbtFreqT) {

        rsbtFreq.setGuid(freqGuid);
        rsbtFreq.setStationGuid(stationGuid);
        //发射频率下线
        rsbtFreq.setFreqEfb(Double.parseDouble((asyncRawBts.getSendStartFreq() == null || "".equals(asyncRawBts.getSendStartFreq())) ? "0" : asyncRawBts.getSendStartFreq()));
        //发射频率上限
        rsbtFreq.setFreqEfe(Double.parseDouble((asyncRawBts.getSendEndFreq() == null || "".equals(asyncRawBts.getSendEndFreq())) ? "0" : asyncRawBts.getSendEndFreq()));
        //接受频率上限
        rsbtFreq.setFreqRfb(Double.parseDouble((asyncRawBts.getAccStartFreq() == null || "".equals(asyncRawBts.getAccStartFreq())) ? "0" : asyncRawBts.getAccStartFreq()));
        //接受频率下线
        rsbtFreq.setFreqRfe(Double.parseDouble((asyncRawBts.getAccEndFreq() == null || "".equals(asyncRawBts.getAccEndFreq())) ? "0" : asyncRawBts.getAccEndFreq()));
        //频率频点
        rsbtFreq.setFreqType("");
        //发射必要带宽
        rsbtFreq.setFreqEBand(0.0);
        //接收必要带宽
        rsbtFreq.setFreqRBand(0.0);

        rsbtFreqAppendix.setGuid(freqGuid);
        rsbtFreqAppendix.setGmtCreate(new Date());
        rsbtFreqAppendix.setGmtModified(new Date());
        rsbtFreqAppendix.setIsDeleted(DBBoolConst.FALSE);
        rsbtFreqAppendix.setSectionName(asyncRawBts.getCellName());

        rsbtFreqT.setGuid(freqGuid);
        rsbtFreqT.setFtFreqCcode(asyncRawBts.getCellId());
    }

    //添加设备数据
    protected void addEqu(String dataType, StationSecDTO stationSecDTO, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList, RsbtEaf rsbtEaf) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtEqu rsbtEquUpdate = rsbtEquWebService.findOneByStationSection(stationSecDTO.getStationGuid(), stationSecDTO.getAsyncRawBts().getCellId());
            guid = rsbtEquUpdate.getGuid();
        }
        RsbtEqu rsbtEqu = new RsbtEqu();
        RsbtEquAppendix rsbtEquAppendix = new RsbtEquAppendix();
        RsbtEquT rsbtEquT = new RsbtEquT();
        buidRsbtEqu(guid, stationSecDTO.getStationGuid(), stationSecDTO.getAsyncRawBts(), rsbtEqu, rsbtEquAppendix, rsbtEquT);

        rsbtEquList.add(rsbtEqu);
        rsbtEquAppendixList.add(rsbtEquAppendix);
        rsbtEquTList.add(rsbtEquT);
        rsbtEaf.setEquGuid(guid);
    }


    //构造设备数据
    protected void buidRsbtEqu(String equGuid, String stationGuid, AsyncRawBts asyncRawBts, RsbtEqu rsbtEqu, RsbtEquAppendix rsbtEquAppendix, RsbtEquT rsbtEquT) {
        rsbtEqu.setGuid(equGuid);
        rsbtEqu.setStationGuid(stationGuid);
        //设备型号
        rsbtEqu.setEquModel(asyncRawBts.getDeviceModel());
        //型号核准代码
        if (asyncRawBts.getModelCode() != null) {
            int len = asyncRawBts.getModelCode().getBytes().length;  // 长度
            // 字节长度 > 40 ? 把中文去掉
            String auth = len > 40 ? asyncRawBts.getModelCode().replaceAll("[^\\x00-\\xff]", "") : asyncRawBts.getModelCode();

            rsbtEqu.setEquAuth(auth.length() > 40 ? auth.substring(auth.length() - 40) : auth);

        }
        //设备生产厂家
        rsbtEqu.setEquMenu(asyncRawBts.getVendorName());

        rsbtEquAppendix.setGuid(equGuid);
        rsbtEquAppendix.setGmtCreate(new Date());
        rsbtEquAppendix.setGmtModified(new Date());
        rsbtEquAppendix.setIsDeleted(DBBoolConst.FALSE);

        rsbtEquT.setGuid(equGuid);
        //上行发射功率
        if (null != asyncRawBts.getMaxEmissivePower()) {
            rsbtEquT.setEtEquUpow(Double.parseDouble(asyncRawBts.getMaxEmissivePower()));
        } else {
            rsbtEquT.setEtEquUpow(0.0);
        }
        //下行发射功率
        rsbtEquT.setEtEquDpow(0.0);

        rsbtEquT.setEtEquCcode(asyncRawBts.getCellId());

    }

    protected AsyncRawBts getAsyncRawBts(ApprovalSchedule approvalSchedule, String dataType) {
        AsyncRawBts asyncRawBts = new AsyncRawBts();
        BeanUtils.copyProperties(approvalSchedule, asyncRawBts);
        if ("1".equals(dataType)) {
            asyncRawBts.setGuid(VerificationCode.myUUID());
        } else {
            AsyncRawBts asyncRawBtsUpdate = asyncRawBtsWebService.getOneByCellId(asyncRawBts.getBtsId(), asyncRawBts.getCellId(), asyncRawBts.getTechType());
            // 基站变更里边的扇区增加，需要给一个新的ID,否则把旧ID赋值给它
            if (!"1".equals(approvalSchedule.getDataType())) {
                asyncRawBts.setGuid(asyncRawBtsUpdate.getGuid());
            }
        }

        return asyncRawBts;
    }
}
