package com.bsm.v4.api.web.service.security;

import com.bsm.v4.domain.security.service.security.OrgUsersService;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.entity.security.RsbtOrgUsers;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Title: OrgUsersWebService
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.service.security
 * @Date 2023/8/15 10:05
 * @description:
 */
@Service
public class OrgUsersWebService extends BasicWebService {

    @Autowired
    private OrgUsersService orgUsersService;
    @Autowired
    private UsersWebService usersWebService;
    @Autowired
    private OrgWebService orgWebService;

    /**
     * 绑定组织机构
     */
    @Transactional
    public int setUsersOrg(String orgId, String[] usersIds) {
        List<RsbtOrgUsers> list = new ArrayList<>();
        OrgDTO orgDTO = orgWebService.findOne(orgId);
        //删除已绑定用户
        deleteAllByOrg(orgId);
        //绑定
        for (String usersId : usersIds) {
            RsbtOrgUsers orgUsers = new RsbtOrgUsers();
            orgUsers.setId(VerificationCode.myUUID());
            orgUsers.setOrgGuid(orgId);
            orgUsers.setUsersId(usersId);
            list.add(orgUsers);
            //修改登录账号所属区域
            if (orgDTO != null && orgDTO.getRegionDTO() != null)
                usersWebService.updateRegion(usersId, String.valueOf(orgDTO.getRegionDTO().getId()));
        }
        return orgUsersService.insertBatch(list);
    }

    /**
     * 根据组织机构删除所有
     */
    public int deleteAllByOrg(String orgId) {
        return orgUsersService.deleteAllByOrg(orgId);
    }

    /**
     * 根据userId删除
     */
    public int deleteAllByUserId(String usersId) {
        return orgUsersService.deleteAllByUserId(usersId);
    }
}
