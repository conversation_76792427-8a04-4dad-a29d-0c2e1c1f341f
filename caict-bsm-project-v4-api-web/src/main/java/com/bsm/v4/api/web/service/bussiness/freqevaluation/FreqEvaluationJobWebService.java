package com.bsm.v4.api.web.service.bussiness.freqevaluation;

import com.bsm.v4.domain.security.service.business.freqevaluation.FreqEvaluationJobService;
import com.bsm.v4.system.model.dto.business.freqevaluation.FreqEvaluationJobDTO;
import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationJobVO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationJobWebService extends BasicWebService {

    @Autowired
    private FreqEvaluationJobService service;

    /**
     * 添加、修改组织机构
     */
    public Map<String, Object> save(FreqEvaluationJobVO freqEvaluationJobVO) {
        // todo fileType TransportFileStateConst.FREQ = 5
        var freqEvaluationJob = new FreqEvaluationJob();
        BeanUtils.copyProperties(freqEvaluationJobVO, freqEvaluationJob);

        var id = service.save(freqEvaluationJob);
        return this.basicReturnResultJson(id);
    }

    /**
     * 删除组织机构（根据guid）
     */
    public Map<String, Object> delete(String guid) {
        //删除用户关联
        service.delete(guid);
        return this.basicReturnResultJson(guid);
    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllByWhere(FreqEvaluationJobVO vo) {
        return this.basicReturnResultJson(new PageHandle(vo).buildPage(service.findAllByWhere(vo)));
    }

    /**
     * 根据id查询详情
     */
    public Map<String, Object> findOne(String guid) {
        var freqEvaluationJob = service.findById(guid);
        if (freqEvaluationJob != null) {
            var dto = new FreqEvaluationJobDTO();
            BeanUtils.copyProperties(freqEvaluationJob, dto);
            return this.basicReturnResultJson(dto);
        }
        return this.basicReturnFailure("未查询到数据");
    }

}
