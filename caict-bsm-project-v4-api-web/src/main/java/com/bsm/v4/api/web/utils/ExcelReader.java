package com.bsm.v4.api.web.utils;

import com.bsm.v4.system.model.entity.business.rule.FsaCheckRule;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class ExcelReader {

    private static Logger logger = Logger.getLogger(ExcelReader.class.getName()); // 日志打印类
    private static final String XLS = "xls";
    private static final String XLSX = "xlsx";


    /**
     * 根据文件后缀名类型获取对应的工作簿对象
     *
     * @param inputStream 读取文件的输入流
     * @param fileType    文件后缀名类型（xls或xlsx）
     * @return 包含文件数据的工作簿对象
     * @throws IOException
     */
    public static Workbook getWorkbook(InputStream inputStream, String fileType) throws IOException {
        Workbook workbook = null;
        if (fileType.equalsIgnoreCase(XLS)) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (fileType.equalsIgnoreCase(XLSX)) {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }


    /**
     * 读取Excel文件内容
     *
     * @param file 上传的Excel文件
     * @return 读取结果列表，读取失败时返回null
     */
    public static List<FsaCheckRule> readExcel(MultipartFile file) {

        Workbook workbook = null;

        try {
            // 获取Excel后缀名
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.isEmpty() || fileName.lastIndexOf(".") < 0) {
                logger.warning("解析Excel失败，因为获取到的Excel文件名非法！");
                return null;
            }
            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);

            // 获取Excel工作簿
            workbook = getWorkbook(file.getInputStream(), fileType);

            // 读取excel中的数据
            return parseExcel(workbook);
        } catch (Exception e) {
            logger.warning("解析Excel失败，文件名：" + file.getOriginalFilename() + " 错误信息：" + e.getMessage());
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
            } catch (Exception e) {
                logger.warning("关闭数据流出错！错误信息：" + e.getMessage());
            }
        }
        return null;
    }


    /**
     * 解析Excel数据
     *
     * @param workbook Excel工作簿对象
     * @return 解析结果
     */
    private static List<FsaCheckRule> parseExcel(Workbook workbook) {
        List<FsaCheckRule> resultDataList = new ArrayList<>();
        // 解析sheet
        for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
            Sheet sheet = workbook.getSheetAt(sheetNum);

            // 校验sheet是否合法
            if (sheet == null) {
                continue;
            }

            // 获取第一行数据
            int firstRowNum = sheet.getFirstRowNum();
            Row firstRow = sheet.getRow(firstRowNum);
//            if (null == firstRow) {
//                logger.warning("解析Excel失败，在第一行没有读取到任何数据！");
//            }

            // 解析每一行的数据，构造数据对象
            int rowStart = firstRowNum + 1;
            int rowEnd = sheet.getPhysicalNumberOfRows();
            for (int rowNum = rowStart; rowNum < rowEnd; rowNum++) {
                Row row = sheet.getRow(rowNum);

                if (null == row) {
                    continue;
                }

                FsaCheckRule fsaCheckRule = convertRowToData(row);
                if (null == fsaCheckRule) {
                    logger.warning("第 " + row.getRowNum() + "行数据不合法，已忽略！");
                    continue;
                }
                resultDataList.add(fsaCheckRule);
            }
        }

        return resultDataList;
    }


    /**
     * 提取每一行中需要的数据，构造成为一个结果数据对象
     * <p>
     * 当该行中有单元格的数据为空或不合法时，忽略该行的数据
     *
     * @param row 行数据
     * @return 解析后的行数据对象，行数据错误时返回null
     */
    private static FsaCheckRule convertRowToData(Row row) {
        FsaCheckRule fsaCheckRule = new FsaCheckRule();
//        String id = VerificationCode.myUUID();
//        fsaCheckRule.setId(id);
        fsaCheckRule.setState("0");

        Cell cell;
        int cellNum = 0;
        //获取组织机构guid
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.STRING);
        fsaCheckRule.setOrgGuid(cell.getStringCellValue());
        //获取技术制式
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.STRING);
        fsaCheckRule.setNetTs(cell.getStringCellValue());
        //获取代数
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.STRING);
        fsaCheckRule.setGenNum(cell.getStringCellValue());
        //获取发射频率下限
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.NUMERIC);
        fsaCheckRule.setFreqEfb(cell.getNumericCellValue());
        //获取发射频率上限
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.NUMERIC);
        fsaCheckRule.setFreqEfe(cell.getNumericCellValue());
        //获取接受频率下限
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.NUMERIC);
        fsaCheckRule.setFreqRfb(cell.getNumericCellValue());
        //获取接受频率上限
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.NUMERIC);
        fsaCheckRule.setFreqRfe(cell.getNumericCellValue());
        //频率使用许可证号或批准文号
        cell = row.getCell(cellNum++);
        cell.setCellType(CellType.STRING);
        fsaCheckRule.setFileNo(cell.getStringCellValue());
        return fsaCheckRule;
    }


}

