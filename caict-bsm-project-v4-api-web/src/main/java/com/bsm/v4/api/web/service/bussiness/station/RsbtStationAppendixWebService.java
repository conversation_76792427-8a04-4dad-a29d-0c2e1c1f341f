package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtStationAppendixService;
import com.bsm.v4.system.model.entity.business.station.RsbtStationAppendix;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/23
 */
@Service
public class RsbtStationAppendixWebService extends BasicWebService {

    @Autowired
    private RsbtStationAppendixService rsbtStationAppendixService;

    /**
     * 批量添加
     */
    public void insertBatch(List<RsbtStationAppendix> rsbtStationAppendixListInsert) {
        if (rsbtStationAppendixListInsert.size() > 30) {
            int pageNum = rsbtStationAppendixListInsert.size() % 30 == 0 ? rsbtStationAppendixListInsert.size() / 30 : rsbtStationAppendixListInsert.size() / 30 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<RsbtStationAppendix> stations;
                if (i != pageNum) {
                    stations = rsbtStationAppendixListInsert.subList((i - 1) * 30, i * 30);
                } else {
                    stations = rsbtStationAppendixListInsert.subList((i - 1) * 30, rsbtStationAppendixListInsert.size());
                }
                rsbtStationAppendixService.insertBatch(stations);
            }
        } else {
            rsbtStationAppendixService.insertBatch(rsbtStationAppendixListInsert);
        }
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtStationAppendix> rsbtStationAppendixListUpdate) {
        return rsbtStationAppendixService.updateBatch(rsbtStationAppendixListUpdate);
    }

    /**
     * 获取需要注销的基站数据
     */
    public List<RsbtStationAppendix> findAllByNotSection() {
        return rsbtStationAppendixService.findAllByNotSection();
    }

    /**
     * 批量删除（修改状态）
     */
    public int deleteBatchByStationGuid(List<RsbtStationAppendix> rsbtStationListDeleted) {
        for (RsbtStationAppendix rsbtStationAppendix : rsbtStationListDeleted) {
            String guid = rsbtStationAppendix.getGuid();
            rsbtStationAppendix = new RsbtStationAppendix();
            rsbtStationAppendix.setGuid(guid);
            rsbtStationAppendix.setIsDeleted(1L);
        }
        return rsbtStationAppendixService.updateBatch(rsbtStationListDeleted);
    }
}
