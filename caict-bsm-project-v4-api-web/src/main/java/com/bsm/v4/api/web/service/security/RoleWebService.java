package com.bsm.v4.api.web.service.security;

import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.bsm.v4.domain.security.service.security.RoleService;
import com.bsm.v4.system.model.dto.security.RoleDTO;
import com.bsm.v4.system.model.vo.security.RoleSearchVO;
import com.bsm.v4.system.model.entity.security.Role;
import com.bsm.v4.system.model.vo.security.RoleVO;
import com.caictframework.data.util.SnowflakeManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by dengsy on 2023-02-10.
 */
@Service
public class RoleWebService extends BasicWebService {

    private static final Logger LOG = LoggerFactory.getLogger(RoleWebService.class);

    @Autowired
    private RoleService roleService;
    @Autowired
    private RoleUsersWebService roleUsersWebService;
    @Autowired
    private RoleMenuWebService roleMenuWebService;
    @Autowired
    private SnowflakeManager snowflakeManager;

    /**
     * 保存
     */
    public Map<String, Object> save(RoleVO roleVO) {
        var role = new Role();
        BeanUtils.copyProperties(roleVO, role);

        role.setUpdateDateTime(new Date());
        var id = roleService.save(role);

        return this.basicReturnResultJson(id);
    }

    /**
     * 批量添加
     */
    public Map<String, Object> insertBatch(List<RoleVO> roleVOList) {
        var list = new ArrayList<Role>();
        roleVOList.forEach(vo -> {
            var role = new Role();
            BeanUtils.copyProperties(vo, role);
            role.setUpdateDateTime(new Date());
            var id = snowflakeManager.nextValue();
            role.setId(String.valueOf(id));
            list.add(role);
        });
        roleService.insertBatch(list);

        return this.basicReturnResultJson("批量添加成功");
    }

    /**
     * 批量修改
     */
    public Map<String, Object> updateBatch(List<RoleVO> roleVOList) {
        var list = new ArrayList<Role>();
        roleVOList.forEach(vo -> {
            var role = new Role();
            BeanUtils.copyProperties(vo, role);
            role.setUpdateDateTime(new Date());

            list.add(role);
        });
        roleService.updateBatch(list);

        return this.basicReturnResultJson("批量修改成功");
    }

    /**
     * 删除
     */
    public Map<String, Object> delete(String roleId) {
        //删除角色菜单关联
        roleMenuWebService.deleteAllByRole(roleId);
        //删除角色用户关联
        roleUsersWebService.deleteByRoleId(roleId);
        return this.basicReturnResultJson(roleService.delete(roleId));
    }

    /**
     * 批量删除
     */
//    public Map<String,Object> deleteBatch(List<String> idList){
//        return this.basicReturnResultJson(roleService.deleteBatch(Collections.singletonList(idList)),idList);
//    }
    public Map<String, Object> deleteBatch(List<String> idList) {
        List<Object> idObjects = new ArrayList<>();
        for (String id : idList) {
            idObjects.add((Object) id);
        }
        return this.basicReturnResultJson(roleService.deleteBatch(idObjects), idList);
    }


    /**
     * 查询
     */
    public Map<String, Object> findOne(String id) {
        return this.basicReturnResultJson(roleService.findById(id));
    }

    /**
     * 查询全部
     */
    public Map<String, Object> findAll() {
        return this.basicReturnResultJson(roleService.findAll());
    }

    /**
     * 条件查询
     */
    public List<RoleDTO> findAllByWhere(RoleSearchVO roleSearchVO) {
        return roleService.findAllByWhere(roleSearchVO);
    }

    /**
     * 分页条件查询
     */
    public Map<String, Object> findAllPageByWhere(RoleSearchVO roleSearchVO) {
        return this.basicReturnResultJson(new PageHandle(roleSearchVO).buildPage(findAllByWhere(roleSearchVO)));
    }

    /**
     * 查询单个角色（根据id）并附带出所属用户信息
     */
    public Map<String, Object> findOneRoleDTO(String roleId) {
        return this.basicReturnResultJson(roleService.findOneRoleDTO(roleId));
    }
}
