package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtEquService;
import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtEquWebService extends BasicWebService {

    @Autowired
    private RsbtEquService rsbtEquService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtEqu> rsbtEquListInsert) {
        return rsbtEquService.insertBatch(rsbtEquListInsert);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtEqu> rsbtEquListUpdate) {
        return rsbtEquService.updateBatch(rsbtEquListUpdate);
    }

    /**
     * 批量删除
     */
    public int deleteBatch(List<RsbtEqu> rsbtEquListUpdate) {
        return rsbtEquService.deleteBatch(Collections.singletonList(rsbtEquListUpdate));
    }

    /**
     * 根据基站id和扇区id查询
     */
    public RsbtEqu findOneByStationSection(String stationGuid, String cellId) {
        return rsbtEquService.findOneByStationSection(stationGuid, cellId);
    }
}
