package com.bsm.v4.api.web.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2019/12/11
 */
public class ZipUtil {

    private static final Logger LOG = LoggerFactory.getLogger(ZipUtil.class);

    /**
     * 将多个Excel打包成zip文件
     *
     * @param srcFile 该路径下的所有需要打成Zip的文件
     * @param zipFile 压缩的Zip文件
     */
    public String zipFiles(String zipFile, List<String> srcFile) {
        try {

            String fileName = new Date().getTime() + ".zip";//使用时间戳作为名称
            Path targetFilePath = Paths.get(zipFile + File.separator + fileName);
            File targetFile = targetFilePath.toFile();

            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(targetFile));
            ZipOutputStream zos = new ZipOutputStream(bos);
            ZipEntry ze;
            File licenseFile;
            Path licenseFilePath;
            BufferedInputStream bis;
            byte[] bytes = new byte[2048];
            LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  打包开始");
            for (String file : srcFile){
                licenseFilePath = Paths.get(file);
                licenseFile = licenseFilePath.toFile();

                bis = new BufferedInputStream(new FileInputStream(licenseFile));
                ze = new ZipEntry(licenseFile.getName());
                zos.putNextEntry(ze);
                int length;
                while ((length = bis.read(bytes)) != -1) {
                    zos.write(bytes, 0, length);
                }
                bis.close();
            }
            zos.flush();
            zos.close();
            LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  打包完成");
            return fileName;
        }catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 将多个Excel打包成zip文件
     *
     * @param srcFile 该路径下的所有需要打成Zip的文件
     * @param zipFile 压缩的Zip文件
     */
    public String zipFiles1(String zipFile, String name, List<String> srcFile) {
        try {

            String fileName = name + new Date().getTime() + ".zip";//使用时间戳作为名称
            Path targetFilePath = Paths.get(zipFile + File.separator + fileName);
            File targetFile = targetFilePath.toFile();

            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(targetFile));
            ZipOutputStream zos = new ZipOutputStream(bos);
            ZipEntry ze;
            File licenseFile;
            Path licenseFilePath;
            BufferedInputStream bis;
            byte[] bytes = new byte[2048];
            LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  打包开始");
            for (String file : srcFile){
                licenseFilePath = Paths.get(file);
                licenseFile = licenseFilePath.toFile();

                bis = new BufferedInputStream(new FileInputStream(licenseFile));
                ze = new ZipEntry(licenseFile.getName());
                zos.putNextEntry(ze);
                int length;
                while ((length = bis.read(bytes)) != -1) {
                    zos.write(bytes, 0, length);
                }
                bis.close();
            }
            zos.flush();
            zos.close();
            LOG.warn(new Timestamp(System.currentTimeMillis()).toString().substring(0, 19) + "  打包完成");
            return fileName;
        }catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 删除目录下所有的文件;
     *
     * @param file 服务器目录地址 例如:
     */
    public boolean deleteExcelPath(File file) {
        if (file.isDirectory()) {
            String[] children = file.list();
            if (children != null) {
                //递归删除目录中的子目录下
                for (String child : children) {
                    boolean success = deleteExcelPath(new File(file, child));
                    if (!success) {
                        return false;
                    }
                }
            }
        }
        // 目录此时为空，可以删除
        return file.delete();
    }

}
