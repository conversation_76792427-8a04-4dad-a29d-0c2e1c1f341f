package com.bsm.v4.api.web.utils;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by dsy62 on 2017-12-07.
 * 工具类：excel读取导入功能，返回集合
 * filePath:excel路径
 * list:返回的集合；分3层，最外层为excel的tab，中间层为行数，最里层为列数
 */
public class MyExcelImportUtil {

    public static List<List<List<String>>> excelImportByFilePath(String filePath) {
        Workbook wb;
        //判断excel格式
        try {
            if (WDWUtil.isExcel03(filePath)) {
                wb = new HSSFWorkbook(new FileInputStream(filePath));
                return readContent(wb);
            } else if (WDWUtil.isExcel07(filePath)) {
                wb = new XSSFWorkbook(new FileInputStream(filePath));
                return readContent(wb);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static List<List<List<String>>> excelImportByFile(MultipartFile file) {
        Workbook wb;
        //判断excel格式
        if (file == null || file.getOriginalFilename() == null) {
            return null;
        }
        try {
            if (WDWUtil.isExcel03(file.getOriginalFilename())) {
                wb = new HSSFWorkbook(file.getInputStream());
                return readContent(wb);
            } else if (WDWUtil.isExcel07(file.getOriginalFilename())) {
                wb = new XSSFWorkbook(file.getInputStream());
                return readContent(wb);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 读取
     */
    private static List<List<List<String>>> readContent(Workbook wb) {
        List<List<List<String>>> list = new ArrayList<>();
        for (int i = 0; i < wb.getNumberOfSheets(); i++) {
            Sheet sheet = wb.getSheetAt(i);
            List<List<String>> rowList = new ArrayList<>();
            for (Row row : sheet) {
                List<String> cellList = new ArrayList<>();
                for (Cell cell : row) {
                    cellList.add(cell.toString());
                }
                rowList.add(cellList);
            }
            list.add(rowList);
        }
        return list;
    }
}
