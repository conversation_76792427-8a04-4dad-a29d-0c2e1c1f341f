package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.domain.security.service.business.station.RsbtEafService;
import com.bsm.v4.system.model.entity.business.station.RsbtEaf;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtEafWebService extends BasicWebService {

    @Autowired
    private RsbtEafService rsbtEafService;

    /**
     * 批量添加
     */
    public int insertBatch(List<RsbtEaf> rsbtEafList) {
        return rsbtEafService.insertBatch(rsbtEafList);
    }

    /**
     * 批量修改
     */
    public int updateBatch(List<RsbtEaf> rsbtEafList) {
        return rsbtEafService.updateBatch(rsbtEafList);
    }
}
