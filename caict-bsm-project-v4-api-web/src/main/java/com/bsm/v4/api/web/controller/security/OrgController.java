package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.security.OrgWebService;
import com.bsm.v4.system.model.vo.security.OrgVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

/**
 * @Title: OrgController
 * <AUTHOR>
 * @Package com.bsm.v4.api.web.controller.security
 * @Date 2023/8/14 16:40
 * @description: 组织架构
 */
@RestController
@RequestMapping(value = "/apiWeb/security/org")
@Api(value = "web端-系统-组织机构接口", tags = "web端-系统-组织机构接口")
public class OrgController extends BasicController {

    @ApiOperation(value = "添加、修改组织机构", notes = "添加、修改组织机构接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgVO", value = "组织机构对象", required = true, paramType = "body", dataType = "orgVO")
    })
    @PostMapping(value = "/")
    public JSONObject save(@RequestBody OrgVO orgVO) {
        return this.basicReturnJson(orgVO, OrgWebService.class, (vo, service) -> service.save(vo));
    }

    @ApiOperation(value = "删除组织机构", notes = "删除组织机构接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "组织机构id", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/{id}")
    public JSONObject delete(@PathVariable("id") String id) {
        return this.basicReturnJson(id, OrgWebService.class, (vo, service) -> service.delete(vo));
    }

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgVO", value = "组织机构搜索对象", required = true, paramType = "body", dataType = "orgVO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPageByWhere(@RequestBody OrgVO orgVO) {
        return this.basicReturnJson(orgVO, OrgWebService.class, (vo, service) -> service.findAllPageByWhere(vo));
    }

    @ApiOperation(value = "查询组织机构详情", notes = "查询组织机构详情接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "组织机构id", required = true, dataType = "String")
    })
    @GetMapping(value = "/findOneDetails/{id}")
    public JSONObject findOneDetails(@PathVariable("id") String id) {
        return this.basicReturnJson(id, OrgWebService.class, (vo, service) -> service.findOneDetails(vo));
    }

//    @ApiOperation(value = "根据父级查询全部",notes = "根据父级查询全部接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "orgSupCode",value = "父级编号",required = true,dataType = "String")
//    })
//    @GetMapping(value = "/findAllBySupCode/{orgSupCode}")
//    public JSONObject findAllBySupCode(@PathVariable("orgSupCode") String orgSupCode){
//        return this.basicReturnJson(orgSupCode, OrgWebService.class,(vo,service) -> service.findAllBySupCode(vo));
//    }

//    @ApiOperation(value = "根据父ID查询全部", notes = "根据父ID查询全部接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "parentId", value = "父ID", required = true, dataType = "String")
//    })
//    @GetMapping(value = "/findAllByParentId/{parentId}")
//    public JSONObject findAllByParentId(@PathVariable("parentId") String parentId) {
//        return this.basicReturnJson(parentId, OrgWebService.class, (vo, service) -> service.findAllByParentId(vo));
//    }

    @ApiOperation(value = "绑定用户", notes = "绑定用户接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orgGuid", value = "组织机构id", required = true, paramType = "path", dataType = "String")
    })
    @PostMapping(value = "/setUsersOrganization/{orgGuid}")
    public JSONObject setUsersOrganization(@PathVariable("orgGuid") String orgGuid, @RequestBody String[] usersIds) {
        return this.basicReturnJson(orgGuid, OrgWebService.class, (vo, service) -> service.setUsersOrg(orgGuid, usersIds));

    }


}
