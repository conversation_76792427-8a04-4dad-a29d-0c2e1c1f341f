package com.bsm.v4.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.caictframework.data.controller.BasicController;
import com.bsm.v4.api.web.service.security.RoleWebService;
import com.bsm.v4.system.model.vo.security.RoleSearchVO;
import com.bsm.v4.system.model.vo.security.RoleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 */
@RestController
@RequestMapping(value = "/apiWeb/security/role")
@Api(value = "web端-系统-角色接口", tags = "web端-系统-角色接口")
public class RoleController extends BasicController {

    @ApiOperation(value = "保存角色信息", notes = "保存角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleVO", value = "角色对象", required = true, paramType = "body", dataType = "roleVO")
    })
    @PostMapping(value = "/")
    public JSONObject save(@RequestBody RoleVO roleVO) {
        return this.basicReturnJson(roleVO, RoleWebService.class, (vo, service) -> service.save(vo));
    }

    @ApiOperation(value = "批量添加角色信息", notes = "批量添加角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleVOList", value = "角色对象集合", allowMultiple = true, required = true, paramType = "body", dataType = "roleVO")
    })
    @PostMapping(value = "/insertBatch")
    public JSONObject insertBatch(@RequestBody List<RoleVO> roleVOList) {
        return this.basicReturnJson(roleVOList, RoleWebService.class, (vo, service) -> service.insertBatch(vo));
    }

    @ApiOperation(value = "批量修改角色信息", notes = "批量修改角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleVOList", value = "角色对象集合", allowMultiple = true, required = true, paramType = "body", dataType = "roleVO")
    })
    @PostMapping(value = "/updateBatch")
    public JSONObject updateBatch(@RequestBody List<RoleVO> roleVOList) {
        return this.basicReturnJson(roleVOList, RoleWebService.class, (vo, service) -> service.updateBatch(vo));
    }

//    @ApiOperation(value = "删除角色信息", notes = "删除角色信息接口")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id",value = "角色主键",required = true,dataType = "long")
//    })
//    @DeleteMapping(value = "/{id}")
//    public JSONObject delete(@PathVariable("id")long id){
//        return this.basicReturnJson(id, RoleWebService.class,(vo,service) -> service.delete(vo));
//    }

    @ApiOperation(value = "批量删除角色信息", notes = "批量删除角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", value = "角色主键集合", allowMultiple = true, required = true, paramType = "body", dataType = "String")
    })
    @PostMapping(value = "/deleteBatch")
    public JSONObject deleteBatch(@RequestBody List<String> idList) {
        return this.basicReturnJson(idList, RoleWebService.class, (vo, service) -> service.deleteBatch(vo));
    }

    @ApiOperation(value = "查询角色信息", notes = "查询角色信息接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "角色主键", required = true, dataType = "String")
    })
    @GetMapping(value = "/{id}")
    public JSONObject findOne(@PathVariable("id") String id) {
        return this.basicReturnJson(id, RoleWebService.class, (vo, service) -> service.findOne(vo));
    }

    @ApiOperation(value = "查询全部角色信息", notes = "查询全部角色信息接口")
    @GetMapping(value = "/")
    public JSONObject findAll() {
        return this.basicReturnJson(RoleWebService.class, (service) -> service.findAll());
    }

    @ApiOperation(value = "分页条件查询", notes = "分页条件查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleSearchVO", value = "角色搜索对象", required = true, paramType = "body", dataType = "roleSearchVO")
    })
    @PostMapping(value = "/findAllPageByWhere")
    public JSONObject findAllPageByWhere(@RequestBody RoleSearchVO roleSearchVO) {
        return this.basicReturnJson(roleSearchVO, RoleWebService.class, (vo, service) -> service.findAllPageByWhere(vo));
    }

    @ApiOperation(value = "查询单个角色（根据id）并附带出所属用户信息", notes = "查询单个角色（根据id）并附带出所属用户信息")
    @ApiImplicitParam(name = "roleId", value = "角色id", required = true, paramType = "path", dataType = "String")
    @GetMapping(value = "/findOneRoleDTO/{roleId}")
    public JSONObject findOneRoleDTO(@PathVariable String roleId) {
        return this.basicReturnJson(roleId, RoleWebService.class, (vo, service) -> service.findOneRoleDTO(roleId));

    }

    @ApiOperation(value = "清空角色用户", notes = "清空角色用户接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId", value = "角色id", required = true, dataType = "String")
    })
    @DeleteMapping(value = "/delete/{roleId}")
    public JSONObject delete(@PathVariable("roleId") String roleId) {
        return this.basicReturnJson(roleId, RoleWebService.class, (vo, service) -> service.delete(roleId));
    }


}
