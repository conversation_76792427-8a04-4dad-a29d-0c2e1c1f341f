package com.bsm.v4.api.web.service.bussiness.station;

import com.bsm.v4.api.web.service.bussiness.transfer.TransportJobWebService;
import com.bsm.v4.api.web.utils.CalculationUtil;
import com.bsm.v4.domain.security.service.business.station.EarthTableService;
import com.bsm.v4.domain.security.service.business.transfer.ApprovalScheduleLogService;
import com.bsm.v4.system.model.contrust.transfer.TransportJobStateConst;
import com.bsm.v4.system.model.entity.business.station.EarthTable;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caictframework.data.service.BasicWebService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class StationInterfereWebService extends BasicWebService {

    @Autowired
    private EarthTableService earthTableService;
    @Autowired
    private ApprovalScheduleLogService approvalScheduleLogService;
    @Autowired
    private TransportJobWebService transportJobWebService;

    private static int effectiveDistance;

    public void interfere(String jobId, String appGuid) {
        try {
            List<ApprovalScheduleLog> approvalScheduleLogList = approvalScheduleLogService.findByAppGuid(appGuid);
            List<ApprovalScheduleLog> approvalScheduleLogs = Collections.synchronizedList(new ArrayList<>());
            approvalScheduleLogList.parallelStream().forEach(approvalScheduleLog -> {
                //基站经度
                String staLongitude = approvalScheduleLog.getLatitude();
                //基站纬度
                String staLatitude = approvalScheduleLog.getLongitude();

                List<EarthTable> earthTableList = earthTableService.findAll();

                for (EarthTable earthTable : earthTableList) {
                    //地球站经度
                    String earthLongitude = earthTable.getLongitude();
                    //地球站纬度
                    String earthLatitude = earthTable.getLatitude();

                    //地球站与基站经纬度之间的距离
                    double distance = CalculationUtil.getDistance(staLongitude, staLatitude, earthLongitude, earthLatitude);

                    String freqRfb = "";
                    if (earthTable.getFreqRfb().contains(" ")) {
                        String[] s = earthTable.getFreqRfb().split(" ");
                        freqRfb = s[1];
                    }
                    double freq = Double.parseDouble(freqRfb);
                    //是否改造
                    String newTech = "0";

                    double accStartFreq = Double.parseDouble(approvalScheduleLog.getAccStartFreq());
                    double accEndFreq = Double.parseDouble(approvalScheduleLog.getAccEndFreq());
                    if (3400 <= accStartFreq && accEndFreq <= 3600) {
                        if (freq >= 3400 && freq <= 3600) {
                            effectiveDistance = 42500;
                        } else if (freq >= 3600 && freq <= 3700) {
                            effectiveDistance = 4000;
                        } else if (freq >= 3700 && freq <= 4200) {
                            if ("1".equals(newTech)) {
                                effectiveDistance = 100;
                            } else if ("0".equals(newTech)) {
                                effectiveDistance = 2000;
                            }
                        }
                    } else if (4800 <= accStartFreq && accEndFreq <= 4900) {
                        if (freq >= 4500 && freq <= 4800) {
                            if ("1".equals(newTech)) {
                                effectiveDistance = 100;
                            } else if ("0".equals(newTech)) {
                                effectiveDistance = 2000;
                            }
                        }
                    }
                    if (judgeResult(effectiveDistance, distance)) {
                        approvalScheduleLog.setIsHandle("1");
                        approvalScheduleLogs.add(approvalScheduleLog);
                    }
                }
            });

            if (approvalScheduleLogs.size() > 0) {
                approvalScheduleLogService.updateBatch(approvalScheduleLogs);
            }
            transportJobWebService.updateIsCompareByGuid(TransportJobStateConst.INTERFERE_SUCCESS, jobId);
        } catch (Exception e) {
            transportJobWebService.updateIsCompareByGuid(TransportJobStateConst.INTERFERE_FAIL, jobId);
        }

    }

    /**
     * 判断是否在干扰范围之内
     */
    public boolean judgeResult(int effectiveDistance, double distance) {
        return !((double) effectiveDistance >= distance);
    }
}
