package com.bsm.v4.api.web.service.bussiness.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.service.business.transfer.CoordinateScheduleTempService;
import com.bsm.v4.system.model.contrust.transfer.ApprovalTransportJobStateConst;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleTempDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawFileDTO;
import com.caictframework.data.service.BasicWebService;
import com.caictframework.data.util.PageHandle;
import com.caictframework.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CoordinateScheduleTempWebService extends BasicWebService {

    @Autowired
    private CoordinateScheduleTempService coordinateScheduleTempService;
    @Autowired
    private ApprovalTransportJobWebService approvalTransportJobWebService;

    public void deleteBatch(List<String> ids) {
        coordinateScheduleTempService.deleteBatch(ids.stream().map(e -> (Object) e).collect(Collectors.toList()));
    }

    /**
     * 查询干扰协调数量
     */
    public JSONObject findAllCoordinateSchedule() {
        List<String> compares = new ArrayList<>(Arrays.asList(ApprovalTransportJobStateConst.COORDINATE_PROCESSING));
        return JSONResult.getSuccessJson(coordinateScheduleTempService.findAllCoordinateSchedule(compares), "查询成功");
    }

    /**
     * 根据jobId、类型分页查询
     */
    public JSONObject findAllPageByJobIdDataType(CoordinateScheduleTempDTO coordinateScheduleTempDTO) {
        PageInfo<CoordinateScheduleTempDTO> coordinateScheduleDTOList = new PageHandle().buildPage(coordinateScheduleTempService.findAllPageByJobIdDataType(coordinateScheduleTempDTO.getJobGuid(), coordinateScheduleTempDTO.getDataType(), coordinateScheduleTempDTO.getGenNum()));
        if (coordinateScheduleDTOList.getList() != null) {
            TransportRawFileDTO transportRawFileDTO = new TransportRawFileDTO();
            transportRawFileDTO.setCoordinateScheduleTempDTOS(coordinateScheduleDTOList.getList());
            ApprovalTransportJobDTO approvalDTO = approvalTransportJobWebService.findOneByJobGuidUpload(coordinateScheduleTempDTO.getJobGuid(), coordinateScheduleTempDTO.getDataType(), coordinateScheduleTempDTO.getGenNum());
            if (approvalDTO != null) {
                transportRawFileDTO.setAppGuid(approvalDTO.getGuid());
            }
            //int total = coordinateScheduleTempService.selectCountByJobIdDataType(coordinateScheduleTempDTO.getJobGuid(),coordinateScheduleTempDTO.getDataType());
            return JSONResult.getSuccessJson(transportRawFileDTO);
        }
        return JSONResult.getPageFailureJson(coordinateScheduleTempDTO.getPage(), coordinateScheduleTempDTO.getPage() + 1, coordinateScheduleTempDTO.getRows(), 0, "操作失败");
    }

    /**
     * 根据appGuid删除
     */
    public int deleteAllByAppGuid(String appGuid) {
        return coordinateScheduleTempService.deleteAllByAppGuid(appGuid);
    }

    /**
     * 根据appGuid查询数量
     */
    public int selectCountByAppGuid(String appGuid) {
        return coordinateScheduleTempService.selectCountByAppGuid(appGuid);
    }
}
