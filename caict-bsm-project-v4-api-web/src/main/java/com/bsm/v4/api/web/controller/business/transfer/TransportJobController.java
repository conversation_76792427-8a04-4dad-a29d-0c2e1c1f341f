package com.bsm.v4.api.web.controller.business.transfer;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.api.web.service.bussiness.transfer.TransportJobWebService;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobDTO;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobConfirmVO;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.controller.BasicController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;

/**
 * Created by dengsy on 2020-04-22.
 */
@RestController
@RequestMapping(value = "/apiWeb/transfer/transportJob")
@Api(value = "web端任务管理接口", tags = "web端任务管理接口")
public class TransportJobController extends BasicController {

    @ApiOperation(value = "创建任务", notes = "创建任务接口")
    @RequestMapping(value = "/createTransportJob", method = RequestMethod.POST)
    public JSONObject createTransportJob(@RequestBody TransportJobVO dto,@RequestHeader("token")String token) {
        return this.basicReturnJson(dto, TransportJobWebService.class, (model, service) -> service.createTransportJob(dto,token));
    }

    @ApiOperation(value = "提交任务接口", notes = "提交任务接口")
    @RequestMapping(value = "/commitTransportJob", method = RequestMethod.POST)
    public JSONObject commitTransportJob(@RequestBody TransportJobDTO dto,@RequestHeader("token")String token) {
        return this.basicReturnJson(dto, TransportJobWebService.class, (model, service) -> service.commitTransportJob(dto,token));
    }

    @ApiOperation(value = "任务确认操作", notes = "任务确认操作接口")
    @RequestMapping(value = "/confirmCommitTransportJob", method = RequestMethod.POST)
    public JSONObject confirmCommitTransportJob(@RequestBody TransportJobConfirmVO transportJobConfirmVO, @RequestHeader("token") String token) {
        return this.basicReturnJson(transportJobConfirmVO, TransportJobWebService.class, (vo, service) -> service.confirmCommitTransportJob(transportJobConfirmVO, token));
    }

    @ApiOperation(value = "任务情况查询", notes = "任务情况查询")
    @RequestMapping(value = "/queryTransportJob", method = RequestMethod.POST)
    public JSONObject queryTransportJob(@RequestBody TransportJobVO vo,@RequestHeader("token") String token) {
        return this.basicReturnJson(vo, TransportJobWebService.class, (model, service) -> service.queryTransportJob(vo,token));
    }

    @ApiOperation(value = "任务详情查询", notes = "任务详情查询")
    @RequestMapping(value = "/getCurrentTaskOne", method = RequestMethod.POST)
    public JSONObject getCurrentTaskOne(@RequestBody TransportJobVO dto, @RequestHeader("token")String token) {
        return this.basicReturnJson(dto, TransportJobWebService.class, (model, service) -> service.getCurrentTaskOne(dto,token));
    }

    @ApiOperation(value = "删除任务", notes = "删除任务")
    @RequestMapping(value = "/deleteTransportJob", method = RequestMethod.POST)
    public JSONObject deleteTransportJob(@RequestBody TransportJobVO dto, HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        dto.setToken(accessToken.toString());
        return this.basicReturnJson(dto, TransportJobWebService.class, (model, service) -> service.deleteTransportJob(dto));
    }

    @ApiOperation(value = "数据对比测试", notes = "数据对比测试")
    @RequestMapping(value = "/dataDealTest/{jobId}/{userId}", method = RequestMethod.GET)
    public JSONObject dataDealTest(@PathVariable String jobId,@PathVariable String userId, @RequestHeader("token")String token) {
        return this.basicReturnJson(jobId, TransportJobWebService.class, (model, service) -> service.scheduleData(jobId,userId));
    }

    @ApiOperation(value = "待比对任务查询", notes = "待比对任务查询")
    @RequestMapping(value = "/queryTransportJobWaitComparison", method = RequestMethod.POST)
    public JSONObject queryTransportJobWaitComparison(@RequestBody TransportJobVO dto,@RequestHeader("token")String token) {
        return this.basicReturnJson(dto, TransportJobWebService.class, (model, service) -> service.queryTransportJobWaitComparison(dto,token));
    }

    @ApiOperation(value = "待比任务结果详情查询", notes = "待比任务结果详情查询")
    @RequestMapping(value = "/queryTransportJobComparisonDetail", method = RequestMethod.POST)
    public JSONObject queryTransportJobComparisonDetail(@RequestBody TransportJobVO dto, @RequestHeader("token")String token) {
        return this.basicReturnJson(dto, TransportJobWebService.class, (model, service) -> service.queryTransportJobComparisonDetail(dto));
    }

    @ApiOperation(value = "待比任务结果选项", notes = "待比任务结果选项")
    @RequestMapping(value = "/getTransportJobStateList", method = RequestMethod.POST)
    public JSONObject getTransportJobStateList(HttpServletRequest request) {
        Object accessToken = request.getHeader("token");
        return this.basicReturnJson(accessToken.toString(), TransportJobWebService.class, (model, service) -> service.getTransportJobStateList(accessToken.toString()));
    }

    @ApiOperation(value = "待比任务各类结果下数据查询", notes = "待比任务各类结果下数据查询")
    @RequestMapping(value = "/queryTransportJobDataDetail", method = RequestMethod.POST)
    public JSONObject queryTransportJobDataDetail(@RequestBody TransportJobVO vo,@RequestHeader("token")String token) {
        return this.basicReturnJson(vo, TransportJobWebService.class, (model, service) -> service.queryTransportJobDataDetail(vo));
    }
}
