server:
  port: 8011
  tomcat:
    basedir: /home/<USER>/bsm/tomcat

caict:
  myFilePath: /home/<USER>/bsm/4.0/file/
  myFilePathTmp: /home/<USER>/bsm/4.0/tmp/
  generalTemplate: general_template.pdf
  generalTemplateForm: general_template_form.pdf
  applyTemplate: bsm_apply_template.pdf
  myFileExportPath: /home/<USER>/bsm/4.0/file/export/
  myZipExportPath: /home/<USER>/bsm/4.0/file/zipTemp/
  myWordTemplateFile: /home/<USER>/bsm/4.0/file/export/
  myWordExportFile: /home/<USER>/bsm/4.0/file/export/report/
  mySqlInsertsNumber: 50
  mySqlDeleteNumber: 50
  myPasswordKey: caict
  zipPath: /home/<USER>/bsm/4.0/file/zip/
  message:
    url: 127.0.0.1
  replacePath: /bsm_cq

spring:
  application:
    name: caict-bsm-4.0-web
  mvc:
    static-path-pattern: /**
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    url: *************************************
    username: BSM_APP
    password: rzg3S8XvZPXmi
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      cachePrepStmts: true
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      read-only: false
      connection-timeout: 60000
      idle-timeout: 60000
      validation-timeout: 3000
      max-lifetime: 60000
      login-timeout: 5
      maximum-pool-size: 60
      minimum-idle: 10
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 10000
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${caict.myFilePath}
  servlet:
    multipart:
      enabled: true
      max-file-size: 10240000000
      max-request-size: 102400000000
  main:
    allow-circular-references: true
  elasticsearch:
    uris: localhost:9200
hadoop:
  url: hdfs://localhost:8020
  namespace: /caict/
  user: root

mybatis:
  configuration:
    map-underscore-to-camel-case: true