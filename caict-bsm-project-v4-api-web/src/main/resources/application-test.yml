server:
  port: 8011
#  ssl:
#    key-store: ${caict.myFilePath}caict.keystore
#    key-store-password: caict123456
#    key-store-type: JKS
  # 启用http2提速
  http2:
    enabled: true
  tomcat:
    basedir: D:/caict/bsm/tomcat

caict:
  myFilePath: D:/caict/bsm/4.0/file/
  myFilePathTmp: D:/caict/bsm/4.0/tmp/
  generalTemplate: general_template.pdf
  generalTemplateForm: general_template_form.pdf
  myFileExportPath: D:/caict/bsm/4.0/file/export/
  myZipExportPath: D:/caict/bsm/4.0/file/zipTemp/
  myWordTemplateFile: D:/caict/bsm/4.0/file/export/
  myWordExportFile: D:/caict/cradio/affairs/1.0/file/export/report/
  mySqlInsertsNumber: 50
  mySqlDeleteNumber: 50
  myPasswordKey: caict
  zipPath: D:/caict/bsm/4.0/file/zip/

spring:
  application:
    name: caict-bsm-4.0-web
  mvc:
    static-path-pattern: /**
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    url: *****************************************
    username: BSM_CQ
    password: caict
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      cachePrepStmts: true
      prepStmtCacheSize: 250
      prepStmtCacheSqlLimit: 2048
      read-only: false
      connection-timeout: 60000
      idle-timeout: 60000
      validation-timeout: 3000
      max-lifetime: 60000
      login-timeout: 5
      maximum-pool-size: 60
      minimum-idle: 10
  redis:
    database: 0
    host: ************
    port: 6379
    password:
    timeout: 10000
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${caict.myFilePath}
  servlet:
    multipart:
      enabled: true
      max-file-size: 10240000000
      max-request-size: 102400000000
  main:
    allow-circular-references: true

fdfs:
  so-timeout: 1500
  connect-timeout: 600
  tracker-list: *************:22122

mybatis:
  configuration:
    map-underscore-to-camel-case: true