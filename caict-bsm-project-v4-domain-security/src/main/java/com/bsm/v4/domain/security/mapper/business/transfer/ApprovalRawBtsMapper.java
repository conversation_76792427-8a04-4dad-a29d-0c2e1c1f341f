package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.ApprovalRawBts;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface ApprovalRawBtsMapper extends BasicMapper<ApprovalRawBts> {

    /**
     * 根据fileId删除
     */
    @Delete("delete from APPROVAL_RAW_BTS where FILE_GUID = #{fileId}")
    int deleteAllByFileId(@Param("fileId") String fileId);

    /**
     * 根据appGuid查询总数
     */
    @Select("select count(*) from APPROVAL_RAW_BTS where APP_GUID = #{appGuid}")
    int selectCountAllByAppGuid(@Param("appGuid") String appGuid);

    /**
     * 根据fileId查询总数
     */
    @Select("select count(1) from APPROVAL_RAW_BTS where FILE_GUID = #{fileId}")
    int selectCountByFileId(@Param("fileId") String fileId);

    /**
     * 根据appGuid查询
     */
    @Select("select * from APPROVAL_RAW_BTS where APP_GUID = #{appGuid} ")
    List<ApprovalRawBts> findAllByAppGuid(@Param("appGuid") String appGuid);

//    /**
//     * 3.0自动生成待审核数据
//     */
//    @Insert("insert into APPROVAL_RAW_BTS(GUID,APP_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ," +
//            "ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER,HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,UPLOAD_DATE,IS_DOWNLOAD,USER_GUID,DEAL_COUNT,UPLOAD_FLAG,VENDOR_NAME," +
//            "DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,FILE_GUID,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,GEN_NUM," +
//            "ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,EXPIRE_DATE, BTS_DATA_TYPE,ST_SCENE,TRF_DATE,TRF_USER,TRF_DATA) " +
//            "(select t.GUID,#{appGuid} as APP_GUID,t.IS_VALID,t.CELL_NAME,t.CELL_ID,t.BTS_NAME,t.BTS_ID,t.TECH_TYPE,t.LOCATION,t.LONGITUDE,t.LATITUDE,t.SEND_START_FREQ," +
//            "t.SEND_END_FREQ,t.ACC_START_FREQ,t.ACC_END_FREQ,t.MAX_EMISSIVE_POWER,t.HEIGHT,t.DATA_TYPE,t.COUNTY,0,sysdate as UPLOAD_DATE,0 as IS_DOWNLOAD,t.USER_GUID,0,2," +
//            "t.VENDOR_NAME,t.DEVICE_MODEL,t.MODEL_CODE,t.ANTENNA_GAIN,TRANSPORT_FILE.GUID as FILE_GUID,t.ANTENNA_MODEL,t.ANTENNA_FACTORY,t.POLARIZATION_MODE,t.ANTENNA_AZIMUTH," +
//            "t.FEEDER_LOSS,t.ALTITUDE,t.SET_YEAR,t.SET_MONTH,t.GEN_NUM, t.ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,EXPIRE_DATE, " +
//            "t.BTS_DATA_TYPE,t.ST_SCENE,t.TRF_DATE,t.TRF_USER,t.TRF_DATA  " +
//            "from TRANSPORT_SCHEDULE t,TRANSPORT_FILE,dual,FSA_REGION region where t.JOB_GUID = TRANSPORT_FILE.JOB_GUID and t.COUNTY = region.NAME " +
//            "and t.JOB_GUID = #{jobGuid} and t.USER_GUID = #{userGuid} and t.BTS_DATA_TYPE = #{dataType} and t.GEN_NUM = #{genNum} and region.PARENT_ID = " +
//            "(select ID from FSA_REGION where CODE = #{regionCode}))")
//    int insertBySchedule(@Param("jobGuid") String jobGuid, @Param("userGuid") String userGuid, @Param("dataType") String dataType, @Param("genNum") String genNum,
//                         @Param("appGuid") String appGuid, @Param("regionCode") String regionCode);

    /**
     * 生成待审核数据
     */
    @Insert("insert into APPROVAL_RAW_BTS(GUID,APP_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ," +
            "ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER,HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,UPLOAD_DATE,IS_DOWNLOAD,USER_GUID,DEAL_COUNT,UPLOAD_FLAG,VENDOR_NAME," +
            "DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,FILE_GUID,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,GEN_NUM," +
            "ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,EXPIRE_DATE, BTS_DATA_TYPE,ST_SCENE,TRF_DATE,TRF_USER,TRF_DATA) " +
            "(select t.GUID,#{appGuid} as APP_GUID,t.IS_VALID,t.CELL_NAME,t.CELL_ID,t.BTS_NAME,t.BTS_ID,t.TECH_TYPE,t.LOCATION,t.LONGITUDE,t.LATITUDE,t.SEND_START_FREQ," +
            "t.SEND_END_FREQ,t.ACC_START_FREQ,t.ACC_END_FREQ,t.MAX_EMISSIVE_POWER,t.HEIGHT,t.DATA_TYPE,t.COUNTY,0,sysdate as UPLOAD_DATE,0 as IS_DOWNLOAD,t.USER_GUID,0,2," +
            "t.VENDOR_NAME,t.DEVICE_MODEL,t.MODEL_CODE,t.ANTENNA_GAIN,TRANSPORT_FILE.GUID as FILE_GUID,t.ANTENNA_MODEL,t.ANTENNA_FACTORY,t.POLARIZATION_MODE,t.ANTENNA_AZIMUTH," +
            "t.FEEDER_LOSS,t.ALTITUDE,t.SET_YEAR,t.SET_MONTH,t.GEN_NUM, t.ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,EXPIRE_DATE, " +
            "t.BTS_DATA_TYPE,t.ST_SCENE,t.TRF_DATE,t.TRF_USER,t.TRF_DATA  " +
            "from TRANSPORT_SCHEDULE t,TRANSPORT_FILE,dual where t.JOB_GUID = TRANSPORT_FILE.JOB_GUID " +
            "and t.JOB_GUID = #{jobGuid} and t.USER_GUID = #{userGuid} and t.BTS_DATA_TYPE = #{dataType} and t.GEN_NUM = #{genNum} )")
    int insertBySchedule(@Param("jobGuid") String jobGuid, @Param("userGuid") String userGuid, @Param("dataType") String dataType, @Param("genNum") String genNum,
                         @Param("appGuid") String appGuid, @Param("regionCode") String regionCode);

    /**
     * 根据appGuid删除
     */
    @Delete("delete from APPROVAL_RAW_BTS where APP_GUID = #{appGuid} AND BTS_DATA_TYPE = #{dataType} AND GEN_NUM = #{genNum}")
    int deleteByAppGuidDataTypeGenNum(@Param("appGuid") String appGuid, @Param("dataType") String dataType, @Param("genNum") String genNum);
}
