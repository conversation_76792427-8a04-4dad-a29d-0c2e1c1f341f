package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.LogTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.ConfirmDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.ConfirmDataDTO;
import com.bsm.v4.system.model.entity.business.transfer.LogTransportJob;
import com.bsm.v4.system.model.vo.business.transfer.LogTransportJobVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface LogTransportJobMapper extends BasicMapper<LogTransportJob> {

    /**
     * 根据日志类型和文件id查询
     */
    @Select("select LOG_TRANSPORT_JOB.*,TRANSPORT_FILE.FILE_LOCAL_NAME as fileName from LOG_TRANSPORT_JOB,TRANSPORT_FILE where LOG_TRANSPORT_JOB.FILE_GUID = TRANSPORT_FILE.GUID and LOG_TYPE = #{logType} and FILE_GUID = #{fileGuid}")
    List<LogTransportJobDTO> findAllByLogTypeFileId(@Param("logType") Long logType, @Param("fileGuid") String fileGuid);

    /**
     * 下载校验后错误的日志
     *
     * @param fileGuid
     * @return
     */
    @Select("select LOG_DETAIL, BTS_ID, CELL_ID from LOG_TRANSPORT_JOB where GUID in (select distinct max(GUID) from LOG_TRANSPORT_JOB where FILE_GUID = #{fileGuid} group by LOG_DETAIL)")
    List<LogTransportJob> findErrorDataByFileId(@Param("fileGuid") String fileGuid);

    /**
     * 根据fileId删除日志信息
     */
    @Delete("delete from LOG_TRANSPORT_JOB where FILE_GUID = #{fileId}")
    void deleteByFileId(@Param("fileId") String fileId);

    /**
     * 根据日志类型和FileId查询总条数
     */
    @Select("select count(1) from LOG_TRANSPORT_JOB where LOG_TYPE = #{logType} and FILE_GUID = #{fileId}")
    int selectCountByTypeFileId(@Param("logType") Long logType, @Param("fileId") String fileId);

    /**
     * 初始加载登录用户待办任务-运营商
     */
    /*@Select("SELECT U.JOBID jobId,U.JOBNAME jobName, U.DATANUM fileDataNum, U.CHECKFLAG downType, U.FILESTATE fileState, U.IS_COMPARE isCompare, U.TYPE userType, " +
            "  U.genNum, U.dataType,U.LOG_DETAIL message " +
            "  FROM (   SELECT E.JOBID,MAX(E.JOBNAME) JOBNAME, COUNT(1) DATANUM,0 CHECKFLAG, '数据校验失败' FILESTATE,MAX(E.TYPE) TYPE,'' GENNUM,'' DATATYPE, " +
            "  '待提交' IS_COMPARE,( SELECT LISTAGG(T.LOG_DETAIL, ',') WITHIN GROUP(ORDER BY T.JOB_GUID) AS LOG_DETAIL FROM( " +
            "       SELECT Distinct t.log_detail,T.JOB_GUID FROM LOG_TRANSPORT_JOB t JOIN TRANSPORT_JOB j ON t.job_guid = j.guid  AND J.USER_GUID IN " +
            "    (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T ON H.REGION_ID = T.REGION_ID WHERE T.ID = #{userId})AND J.IS_COMPARE NOT IN ('10', '14','16'))t WHERE  T.JOB_GUID = E.JOBID AND ROWNUM <= 5  " +
            "        )|| '(......)' LOG_DETAIL FROM (SELECT DISTINCT E.JOB_GUID JOBID, J.JOB_NAME JOBNAME, " +
            "  E.BTS_ID BTSID, E.CELL_ID CELLID,A.TYPE " +
            "  FROM LOG_TRANSPORT_JOB E INNER JOIN TRANSPORT_JOB J ON J.GUID = E.JOB_GUID AND J.USER_GUID = #{userId} AND J.IS_COMPARE NOT IN('10','14','16') " +
            "  LEFT JOIN SYS_USERS A ON A.ID = J.USER_GUID " +
            "  ) E GROUP BY E.JOBID " +
            "  UNION ALL " +
            "   SELECT R.JOBID,MAX(R.JOBNAME) JOBNAME,COUNT(1) DATANUM, 1 CHECKFLAG, '数据校验成功' FILESTATE,MAX(R.TYPE) TYPE,R.GENNUM,R.DATATYPE, " +
            "  MAX(R.IS_COMPARE) IS_COMPARE,''LOG_DETAIL " +
            "  FROM (  SELECT T.JOB_GUID  JOBID,J.JOB_NAME JOBNAME, T.BTS_ID  BTSID, T.CELL_ID  CELLID,T.DATA_TYPE DATATYPE, T.GEN_NUM GENNUM, " +
            "  J.IS_COMPARE,A.TYPE FROM TRANSPORT_RAW_BTS_DEAL_LOG T LEFT JOIN TRANSPORT_JOB J " +
            "  ON J.GUID = T.JOB_GUID AND J.IS_COMPARE NOT IN('10','14','16') LEFT JOIN SYS_USERS A ON A.ID = J.USER_GUID " +
            "  WHERE T.USER_GUID = #{userId}  ) R GROUP BY R.GENNUM,R.DATATYPE,R.JOBID) U")
    List<LogTransportJobDTO> getTaskDataForOperator(@Param("userId")String userId);*/


    @Select("<script>" +
            "select * from ( " +
            "select a.guid,b.type as userType,row_number() over(partition by b.type order by a.GMT_CREATE) rn " +
            "from TRANSPORT_JOB a,sys_users b where a.user_guid = b.id " +
            " <if test='userId != null and userId != \"\"'>" +
            " AND a.user_guid = #{userId} </if> " +
            "and a.is_compare = '5') c " +
            "where c.rn = 1" +
            "</script>")
    @Results({
            @Result(property = "guid",column = "GUID"),
            @Result(property = "transportJobBranchList",column = "GUID",javaType = List.class,many = @Many(select = "findListByJobGuid"))
    })
    List<ConfirmDTO> getTaskDataForOperator(@Param("userId")String userId);

    @Select("select job_guid as jobId,guid as jobBranchId,cell_count sumCell,is_compare as isCompare," +
            "data_type dataType,gen_num genNum,tech_type techType,'5' fileState " +
            "from transport_job_branch where job_guid = #{guid} " +
            "union all " +
            "select JOB_GUID as jobId,JOB_GUID as jobBranchId,TO_CHAR(count(*)) sumCell,'4' as isCompare,'' dataType,'' genNum,'' techType,'5' fileState  " +
            "from LOG_TRANSPORT_JOB WHERE JOB_GUID = #{guid} group by JOB_GUID")
    List<ConfirmDataDTO> findListByJobGuid(@Param("guid") String jobGuid);

    /**
     * 待办校验失败数据导出
     */
    @Select("<script>  SELECT T.CELL_NAME cellName, T.CELL_ID cellId, T.BTS_NAME btsName,T.BTS_ID btsId,T.TECH_TYPE techType,T.LOCATION location,T.COUNTY county,T.LONGITUDE longitude," +
            "  T.LATITUDE latitude,T.SEND_START_FREQ sendStartFreq, " +
            "  T.SEND_END_FREQ sendEndFreq,T.ACC_START_FREQ accStartFreq,T.ACC_END_FREQ accEndFreq,T.MAX_EMISSIVE_POWER maxEmissivePower,T.HEIGHT height,T.DEVICE_FACTORY deviceFactory," +
            "  T.DEVICE_MODEL deviceModel,T.MODEL_CODE modelCode, " +
            "  T.ANTENNA_MODEL antennaModel,T.ANTENNA_FACTORY antennaFactory,T.POLARIZATION_MODE polarizationModel,T.ANTENNA_AZIMUTH antennaAzimuth,T.FEEDER_LOSS feederLoss," +
            "  T.ANTENNA_GAIN antennaGain,T.ALTITUDE altitude,T.EXPAND_STATION expandStation, " +
            "  T.ST_SCENE stScene,T.TRF_DATE trfDate,T.TRF_USER trfUser,T.TRF_DATA trfData,T.JOB_GUID jobId,T.LOG_DETAIL logDetail,T.LOG_SHORT logShort,T.LOG_TYPE logType " +
            "   FROM LOG_TRANSPORT_JOB T " +
            "  LEFT JOIN TRANSPORT_JOB J " +
            "  ON J.GUID = T.JOB_GUID  " +
            "   <if test='logTransportJobVO.userType != null and logTransportJobVO.userType != \"\"'>" +
            "       <if test=' logTransportJobVO.userType == \"operator\".toString() '> " +
            "          AND J.USER_GUID = #{logTransportJobVO.userId}</if>" +
            "       <if test=' logTransportJobVO.userType == \"wuweiCity\".toString() '> " +
            "          AND J.USER_GUID IN (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T ON H.REGION_ID = T.REGION_ID WHERE T.ID =  #{logTransportJobVO.userId}) </if>" +
            "       <if test=' logTransportJobVO.userType == \"wuweiProvincial\".toString() || logTransportJobVO.userType == \"admin\".toString()'> " +
            "          AND 1 = 1</if>" +
            "   </if>" +
            "  AND J.IS_COMPARE NOT IN('10','14','16')" +
            "  AND T.JOB_GUID = #{logTransportJobVO.jobId} </script>")
    List<LogTransportJobDTO> findExportFailedTaskDetail(@Param("logTransportJobVO") LogTransportJobVO logTransportJobVO);

    /**
     * 初始加载登录用户待办任务-地市无委
     */
    @Select("  SELECT U.JOBID jobId,U.JOBNAME jobName, U.DATANUM fileDataNum, U.CHECKFLAG downType, U.FILESTATE fileState, U.IS_COMPARE isCompare, U.LOG_DETAIL message, " +
            "   U.TYPE userType, U.genNum, U.dataType FROM (   SELECT E.JOBID, MAX(E.JOBNAME) JOBNAME, COUNT(1) DATANUM, 0 CHECKFLAG, '数据校验失败' FILESTATE, " +
            "  MAX(E.TYPE) TYPE, '' GENNUM, '' DATATYPE, '待提交' IS_COMPARE,  ( SELECT LISTAGG(T.LOG_DETAIL, ',') WITHIN GROUP(ORDER BY T.JOB_GUID) AS LOG_DETAIL FROM( " +
            "       SELECT Distinct t.log_detail,T.JOB_GUID FROM LOG_TRANSPORT_JOB t JOIN TRANSPORT_JOB j ON t.job_guid = j.guid  AND J.USER_GUID IN " +
            "    (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T ON H.REGION_ID = T.REGION_ID WHERE T.ID = #{userId})AND J.IS_COMPARE NOT IN ('10', '14','16'))t WHERE  T.JOB_GUID = E.JOBID AND ROWNUM <= 5  " +
            "        )|| '(......)' LOG_DETAIL FROM (SELECT DISTINCT E.JOB_GUID JOBID, E.BTS_ID BTSID,  E.CELL_ID CELLID, J.JOB_NAME JOBNAME, A.TYPE " +
            "   FROM LOG_TRANSPORT_JOB E JOIN TRANSPORT_JOB J ON J.GUID = E.JOB_GUID AND J.USER_GUID = #{userId} AND J.IS_COMPARE NOT IN ('10', '14','16')  LEFT JOIN SYS_USERS A " +
            "    ON A.ID = J.USER_GUID) E GROUP BY E.JOBID " +
            "UNION ALL " +
            "SELECT R.JOBID,MAX(R.JOBNAME) JOBNAME,  COUNT(1) DATANUM, 1 CHECKFLAG, '数据校验成功' FILESTATE, MAX(R.TYPE) TYPE,R.GENNUM, R.DATATYPE, MAX(R.IS_COMPARE) IS_COMPARE, '' LOG_DETAIL " +
            "  FROM (SELECT T.JOB_GUID   JOBID,J.JOB_NAME JOBNAME, T.BTS_ID     BTSID, T.CELL_ID    CELLID,T.GEN_NUM GENNUM,T.DATA_TYPE DATATYPE, J.IS_COMPARE, A.TYPE " +
            "          FROM TRANSPORT_RAW_BTS_DEAL_LOG T LEFT JOIN TRANSPORT_JOB J ON J.GUID = T.JOB_GUID AND J.IS_COMPARE NOT IN ('10', '14','16') " +
            "          LEFT JOIN SYS_USERS A ON A.ID = J.USER_GUID WHERE T.USER_GUID IN (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T " +
            "          ON H.REGION_ID = T.REGION_ID WHERE T.ID = #{userId})) R " +
            " GROUP BY R.GENNUM,R.DATATYPE,R.JOBID) U " +
            " ORDER BY JOBID")
    List<LogTransportJobDTO> getTaskDataForCity(@Param("jobId")String userId);

    /**
     * 初始加载登录用户待办任务-省级无委or管理员
     */
    @Select("SELECT U.JOBID jobId,U.JOBNAME jobName, U.DATANUM fileDataNum, U.CHECKFLAG downType, U.FILESTATE fileState, U.IS_COMPARE isCompare, U.LOG_DETAIL message, U.TYPE userType, " +
            "            U.genNum, U.dataType " +
            "              FROM (      SELECT E.JOBID,MAX(E.JOBNAME) JOBNAME, COUNT(1) DATANUM,0 CHECKFLAG, '数据校验失败' FILESTATE,MAX(E.TYPE) TYPE,'' GENNUM,'' DATATYPE, " +
            "              '待提交' IS_COMPARE, ( SELECT LISTAGG(T.LOG_DETAIL, ',') WITHIN GROUP(ORDER BY T.JOB_GUID) AS LOG_DETAIL FROM( " +
            "       SELECT Distinct t.log_detail,T.JOB_GUID FROM LOG_TRANSPORT_JOB t JOIN TRANSPORT_JOB j ON t.job_guid = j.guid  AND J.USER_GUID IN " +
            "    (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T ON H.REGION_ID = T.REGION_ID)AND J.IS_COMPARE NOT IN ('10', '14','16'))t WHERE  T.JOB_GUID = E.JOBID AND ROWNUM <= 5  " +
            "        )|| '(......)' LOG_DETAIL FROM (SELECT DISTINCT E.JOB_GUID JOBID,J.JOB_NAME JOBNAME, " +
            "              E.BTS_ID BTSID, E.CELL_ID CELLID,A.TYPE " +
            "              FROM LOG_TRANSPORT_JOB E INNER JOIN TRANSPORT_JOB J ON J.GUID = E.JOB_GUID AND J.IS_COMPARE NOT IN('10','14','16')  " +
            "              LEFT JOIN SYS_USERS A ON A.ID = J.USER_GUID " +
            "              ) E GROUP BY E.JOBID " +
            "              UNION ALL " +
            "                SELECT R.JOBID,MAX(R.JOBNAME) JOBNAME, COUNT(1) DATANUM, 1 CHECKFLAG, '数据校验成功' FILESTATE,MAX(R.TYPE) TYPE,R.GENNUM, R.DATATYPE, " +
            "              MAX(R.IS_COMPARE) IS_COMPARE, '' LOG_DETAIL " +
            "              FROM (SELECT T.JOB_GUID   JOBID,J.JOB_NAME JOBNAME, T.BTS_ID   BTSID, T.CELL_ID CELLID,T.GEN_NUM GENNUM,T.DATA_TYPE DATATYPE, " +
            "              J.IS_COMPARE,A.TYPE FROM TRANSPORT_RAW_BTS_DEAL_LOG T LEFT JOIN TRANSPORT_JOB J " +
            "              ON J.GUID = T.JOB_GUID AND J.IS_COMPARE NOT IN('10','14','16') LEFT JOIN SYS_USERS A ON A.ID = J.USER_GUID) R " +
            "               GROUP BY R.GENNUM,R.DATATYPE,R.JOBID) U  ORDER BY JOBID")
    List<LogTransportJobDTO> getTaskDataForProvincialAdmin();

    /**
     * 待办校验成功数据导出
     */
    @Select("<script>  SELECT T.CELL_NAME cellName, T.CELL_ID cellId, T.BTS_NAME btsName,T.BTS_ID btsId,T.TECH_TYPE techType,T.LOCATION location,T.COUNTY county,T.LONGITUDE longitude, " +
            "  T.LATITUDE latitude,T.SEND_START_FREQ sendStartFreq, " +
            "  T.SEND_END_FREQ sendEndFreq,T.ACC_START_FREQ accStartFreq,T.ACC_END_FREQ accEndFreq,T.MAX_EMISSIVE_POWER maxEmissivePower,T.HEIGHT height,T.DEVICE_FACTORY deviceFactory, " +
            "  T.DEVICE_MODEL deviceModel,T.MODEL_CODE modelCode, " +
            "  T.ANTENNA_MODEL antennaModel,T.ANTENNA_FACTORY antennaFactory,T.POLARIZATION_MODE polarizationModel,T.ANTENNA_AZIMUTH antennaAzimuth,T.FEEDER_LOSS feederLoss, " +
            "  T.ANTENNA_GAIN antennaGain,T.ALTITUDE altitude,T.EXPAND_STATION expandStation, " +
            "  T.ST_SCENE stScene,T.TRF_DATE trfDate,T.TRF_USER trfUser,T.TRF_DATA trfData,T.JOB_GUID jobId " +
            "    FROM TRANSPORT_RAW_BTS_DEAL_LOG T " +
            "    LEFT JOIN TRANSPORT_JOB J " +
            "      ON J.GUID = T.JOB_GUID " +
            "     AND J.IS_COMPARE NOT IN ('10', '14', '16') " +
            "    LEFT JOIN SYS_USERS A " +
            "      ON A.ID = J.USER_GUID " +
            "   WHERE 1 = 1 " +
            "   <if test='logTransportJobVO.userType != null and logTransportJobVO.userType != \"\"'>" +
            "       <if test=' logTransportJobVO.userType == \"operator\".toString() '> " +
            "          AND J.USER_GUID = #{logTransportJobVO.userId}</if>" +
            "       <if test=' logTransportJobVO.userType == \"wuweiCity\".toString() '> " +
            "          AND J.USER_GUID IN (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T ON H.REGION_ID = T.REGION_ID WHERE T.ID =  #{logTransportJobVO.userId}) </if>" +
            "       <if test=' logTransportJobVO.userType == \"wuweiProvincial\".toString() || logTransportJobVO.userType == \"admin\".toString()'> " +
            "          AND 1 = 1</if>" +
            "   </if>" +
            "   AND T.JOB_GUID = #{logTransportJobVO.jobId} " +
            "   AND T.GEN_NUM = #{logTransportJobVO.genNum} " +
            "   AND T.DATA_TYPE = #{logTransportJobVO.dataType} </script>")
    List<LogTransportJobDTO> findExportPassTaskDetail(@Param("logTransportJobVO") LogTransportJobVO logTransportJobVO);


    @Select("select * from LOG_TRANSPORT_JOB where job_guid = #{jobId}")
    List<TransportRawBtsDealDTO> findByJobId(@Param("jobId") String jobId);
}
