package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.UsersLogMapper;
import com.bsm.v4.system.model.dto.security.UsersLogDTO;
import com.bsm.v4.system.model.entity.security.UsersLog;
import com.bsm.v4.system.model.vo.security.UsersLogSearchVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: UsersLogService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service.security
 * @Date 2023/8/17 11:48
 * @description:
 */
@Service
public class UsersLogService extends BasicService<UsersLog> {


    @Autowired
    private UsersLogMapper usersLogMapper;


    /**
     * 条件查询
     */
    public List<UsersLogDTO> findAllByWhere(UsersLogSearchVO usersLogSearchVO) {
        return usersLogMapper.findAllByWhere(usersLogSearchVO);
    }
}
