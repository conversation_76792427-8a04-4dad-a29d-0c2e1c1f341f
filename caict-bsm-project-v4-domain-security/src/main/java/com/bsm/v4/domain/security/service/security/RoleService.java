package com.bsm.v4.domain.security.service.security;

import com.caictframework.data.service.BasicService;
import com.bsm.v4.domain.security.mapper.security.RoleMapper;
import com.bsm.v4.system.model.dto.security.RoleDTO;
import com.bsm.v4.system.model.vo.security.RoleSearchVO;
import com.bsm.v4.system.model.entity.security.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 */
@Service
public class RoleService extends BasicService<Role> {

    @Autowired
    private RoleMapper roleMapper;

    /**
     * 条件查询
     */
    public List<RoleDTO> findAllByWhere(RoleSearchVO roleSearchVO) {
        return roleMapper.findAllByWhere(roleSearchVO);
    }

    /**
     * 查询单个角色（根据id）并附带出所属用户信息
     */
    public RoleDTO findOneRoleDTO(String roleId) {
        return roleMapper.findOneRoleDTO(roleId);
    }
}
