package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.LoginMapper;
import com.bsm.v4.system.model.entity.security.Login;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Title: LoginService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service
 * @Date 2023/8/15 15:22
 * @description:
 */
@Service
public class LoginService extends BasicService<Login> {
    @Autowired
    private LoginMapper loginMapper;

    /**
     * 根据用户删除
     */
    public int deleteByUser(String userId) {
        return loginMapper.deleteByUser(userId);
    }
}
