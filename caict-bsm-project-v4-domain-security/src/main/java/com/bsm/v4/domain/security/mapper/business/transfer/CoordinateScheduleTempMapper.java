package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleShowDTO;
import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleTempDTO;
import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTemp;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface CoordinateScheduleTempMapper extends BasicMapper<CoordinateScheduleTemp> {

    /**
     * 根据流程类型查询代办数量
     */
    @Select({"<script>",
            "select APPROVAL_TRANSPORT_JOB.GUID as appGuid,APPROVAL_TRANSPORT_JOB.JOB_GUID as jobGuid,FSA_USERS.TYPE,APPROVAL_TRANSPORT_JOB.IS_COMPARE as compare, ",
            "(select count(*) from COORDINATE_SCHEDULE_TEMP where COORDINATE_SCHEDULE_TEMP.data_type = '1' and COORDINATE_SCHEDULE_TEMP.JOB_GUID = APPROVAL_TRANSPORT_JOB.JOB_GUID) as scheduleNew, ",
            "(select count(*) from COORDINATE_SCHEDULE_TEMP where COORDINATE_SCHEDULE_TEMP.data_type = '2' and COORDINATE_SCHEDULE_TEMP.JOB_GUID = APPROVAL_TRANSPORT_JOB.JOB_GUID) as scheduleUpdate, ",
            "(select count(*) from COORDINATE_SCHEDULE_TEMP where COORDINATE_SCHEDULE_TEMP.data_type = '3' and COORDINATE_SCHEDULE_TEMP.JOB_GUID = APPROVAL_TRANSPORT_JOB.JOB_GUID) as scheduleDelete ",
            "from APPROVAL_TRANSPORT_JOB,FSA_USERS  ",
            "where APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID ",
            "and APPROVAL_TRANSPORT_JOB.IS_COMPARE in ",
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>",
            "#{compare}",
            "</foreach>",
            "</script>"})
    List<CoordinateScheduleShowDTO> findAllCoordinateSchedule(@Param("compares") List<String> compares);

    /**
     * 根据jobId、类型查询总数
     */
    @Select("select count(*) from COORDINATE_SCHEDULE_TEMP " +
            "where APP_GUID = #{appGuid} " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null)")
    int selectCountByJobIdDataType(@Param("appGuid") String appGuid, @Param("dataType") String dataType);

    /**
     * 根据jobId、类型分页查询
     */
    @Select("select COORDINATE_SCHEDULE_TEMP.*,(case when COORDINATE_SCHEDULE_TEMP_LOG.STATUS is null THEN '未干扰协调' else COORDINATE_SCHEDULE_TEMP_LOG.STATUS end) as status " +
            "from COORDINATE_SCHEDULE_TEMP " +
            "left join COORDINATE_SCHEDULE_TEMP_LOG on COORDINATE_SCHEDULE_TEMP.GUID = COORDINATE_SCHEDULE_TEMP_LOG.GUID " +
            "where COORDINATE_SCHEDULE_TEMP.JOB_GUID = #{jobGuid}  " +
            "and (COORDINATE_SCHEDULE_TEMP.DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (COORDINATE_SCHEDULE_TEMP.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "order by COORDINATE_SCHEDULE_TEMP.JOB_GUID,COORDINATE_SCHEDULE_TEMP.CELL_ID")
    List<CoordinateScheduleTempDTO> findAllPageByJobIdDataType(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("genNum") String genNum);

    /**
     * 根据appGuid删除
     */
    @Delete("delete COORDINATE_SCHEDULE_TEMP where APP_GUID = #{appGuid}")
    int deleteAllByAppGuid(@Param("appGuid") String appGuid);

    /**
     * 根据appGuid查询数量
     */
    @Select("select count(*) from COORDINATE_SCHEDULE_TEMP where APP_GUID = #{appGuid}")
    int selectCountByAppGuid(@Param("appGuid") String appGuid);
}
