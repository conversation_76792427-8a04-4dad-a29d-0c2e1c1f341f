package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.RsbtOrgMapper;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.dto.security.RsbtOrgDTO;
import com.bsm.v4.system.model.entity.security.RsbtOrg;
import com.bsm.v4.system.model.vo.security.OrgSearchVO;
import com.bsm.v4.system.model.vo.security.OrgVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: OrgService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service
 * @Date 2023/8/14 16:52
 * @description:
 */
@Service
public class OrgService extends BasicService<RsbtOrg> {

    @Autowired
    private RsbtOrgMapper orgMapper;

    /**
     * 条件查询
     */
    public List<OrgDTO> findAllByWhere1(OrgVO orgSearchVO) {
        return orgMapper.findAllByWhere(orgSearchVO);
    }

    /**
     * 查询组织机构详情
     */
    public OrgDTO findOneDetails(String id) {
        return orgMapper.findOneDetails(id);
    }

    /**
     * 查询组织机构详情
     */
    public OrgDTO findOneByOrgName(String name) {
        return orgMapper.findOneByOrgName(name);
    }

    /**
     * 根据父级查询全部
     */
    public List<OrgDTO> findAllBySupCode(String orgSupCode) {
        return orgMapper.findAllBySupCode(orgSupCode);
    }

    /**
     * 根据状态查询制式
     */
    public List<FsaCheckRuleDTO> findDistinctCheckRuleByIsDeleted() {
        return orgMapper.findDistinctCheckRuleByIsDeleted();
    }

    /**
     * 根据状态查询全部规则
     */
    public List<FsaCheckRuleDTO> findAllCheckRuleByIsDeleted() {
        return orgMapper.findAllCheckRuleByIsDeleted();
    }

    /**
     * 根据地区code查询
     */
    public String findUserIDByRegionCodeAndOrgType(String regionCode, String orgType) {
        return orgMapper.findUserIDByRegionCodeAndOrgType(regionCode, orgType);
    }


}
