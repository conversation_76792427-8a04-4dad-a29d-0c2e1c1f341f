package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.EchartsResDTO;
import com.bsm.v4.domain.security.mapper.business.station.RsbtStationMapper;
import com.bsm.v4.system.model.dto.business.station.*;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationService extends BasicService<RsbtStation> {

    @Autowired
    private RsbtStationMapper rsbtStationMapper;

    /**
     * 获取没有入申请表的所有站点
     */
    public List<StationDTO> selectAllNotApplyTable() {
        return rsbtStationMapper.selectAllNotApplyTable();
    }

    /**
     * 查询未生成执照的基站
     */
    public List<StationLicenseDTO> selectAllNotLicense() {
        return rsbtStationMapper.selectAllNotLicense();
    }

    /**
     * 更新执照状态为发布执照
     */
    public void updateStationLicenseStatus(List<StationLicenseDTO> list) {
        List<String> guidList = list.stream().map(RsbtStation::getGuid).collect(Collectors.toList());
        int len = 1000;

        int size = guidList.size();
        int count = (size + len - 1) / len;
        for (int i = 0; i < count; i++) {
            List<String> subGuidList = guidList.subList(i * len, Math.min((i + 1) * len, size));
            rsbtStationMapper.updateStationLicenseStatus(subGuidList);
        }
    }

    /**
     * 根据基站识别号下载
     */
    public RsbtStation querySattionByBts(String bts, String techType, String orgCode) {
        return rsbtStationMapper.querySattionByBts(bts, techType, orgCode);
    }

    /**
     * 条件查询总数
     */
    public int selectCountByWhere(StationDTO stationDTO) {
        return rsbtStationMapper.selectCountByWhere(stationDTO);
    }

    /**
     * 分页条件查询 yunyingshang
     *
     * @param stationDTO stationDTO
     * @param usersDTO   usersDTO
     * @return list
     */
    public List<StationDTO> findAllByWhere(StationDTO stationDTO, UsersDTO usersDTO) {
        return rsbtStationMapper.findAllByWhere(stationDTO, usersDTO);
    }

    /**
     * 分页条件查询 wuwei
     *
     * @param stationDTO stationDTO
     * @param usersDTO   usersDTO
     * @return list
     */
    public List<StationDTO> findAllByWuWei(StationDTO stationDTO, UsersDTO usersDTO) {
        return rsbtStationMapper.findAllByWuWei(stationDTO, usersDTO);
    }

    /**
     * 查询详情DTO
     */
    public StationDTO findOneByGuid(String guid) {
        StationDTO stationDTO = rsbtStationMapper.findOneByGuid1(guid);
        stationDTO.setSectionDTOList(rsbtStationMapper.getListDTO(stationDTO.getStCCode(), stationDTO.getAppGuid()));
        return stationDTO;
    }


    public List<RsbtStation> selectExistStation(Set<String> stationSet) {
        return rsbtStationMapper.selectExistStation(stationSet);
    }

    /**
     * 统计
     */
    public int queryStationCount() {
        return rsbtStationMapper.queryStationCount();
    }

    public List<EchartsResDTO> queryStationCountyNumber() {
        return rsbtStationMapper.queryStationCountyNumber();
    }

    public int queryNewStaByThisMonth() {
        return rsbtStationMapper.queryNewStaByThisMonth();
    }

    public List<StaInfoDTO> queryNewInsertStation() {
        return rsbtStationMapper.queryNewInsertStation();
    }

    /**
     * 查询区域排名（根据基站数排名)
     */
    public List<RegionSortDTO> selectRegionSortDTO() {
        List<RegionSortDTO> list = rsbtStationMapper.selectRegionSortDTO();
        for (int i = 0; i < list.size(); i++) {
            String regionName = list.get(i).getRegionName();
            List<OrgStationNumSortDTO> orgStationNumSortDTOS = rsbtStationMapper.selectOrgSortDTO(regionName);
            for (OrgStationNumSortDTO org : orgStationNumSortDTOS) {
                checkoutOrg(org.getOrgName(), org);
            }
            list.get(i).setOrgStationNumSortDTOList(orgStationNumSortDTOS);
        }
        return list;
    }

    /**
     * 校验组织名称
     */
    public static void checkoutOrg(String orgName, OrgStationNumSortDTO orgStationNumSortDTO) {
        if (orgName.contains("中国移动")) orgStationNumSortDTO.setOrgName("中国移动");
        else if (orgName.contains("中国电信")) orgStationNumSortDTO.setOrgName("中国电信");
        else if (orgName.contains("中国联合")) orgStationNumSortDTO.setOrgName("中国联通");
        else if (orgName.contains("中国广电")) orgStationNumSortDTO.setOrgName("中国广电");
    }

    public List<RegionSortDTO> searchTwo(List<RegionSortDTO> list) {
        list.sort((o1, o2) -> -o1.getRegionStationNum().compareTo(o2.getRegionStationNum()));
        return list;
    }

    /**
     * 查询台站分布
     *
     * @return
     */
    public List<RegionSortDTO> getDistributionStatistics() {
        return rsbtStationMapper.getDistributionStatistics();
    }

    /**
     * 查询运营商对应的基站数
     */
    public List<OrgStationNumSortDTO> selectOrgStationNum() {
        List<OrgStationNumSortDTO> list = rsbtStationMapper.selectOrgStationNum();
        for (OrgStationNumSortDTO org : list) {
            checkoutOrg(org.getOrgName(), org);
        }
        return list;
    }

    /**
     * 降序排列
     *
     * @param list
     * @return
     */
    public List<OrgStationNumSortDTO> search(List<OrgStationNumSortDTO> list) {
        Collections.sort(list, new Comparator<OrgStationNumSortDTO>() {
            @Override
            public int compare(OrgStationNumSortDTO o1, OrgStationNumSortDTO o2) {
                return -o1.getStationNumber().compareTo(o2.getStationNumber());
            }
        });
        return list;
    }

    public List<StationDTO> queryStationByRange(Double lowLng, Double lowLat, Double highLng, Double highLat) {
        return rsbtStationMapper.queryStationByRange(lowLng, lowLat, highLng, highLat);
    }

    public List<RsbtStation> findListByAppCodePage(String appCode) {
        return rsbtStationMapper.findListByAppCodePage(appCode);
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return rsbtStationMapper.updateAppCode(oldAppCode, newAppCode);
    }

    public RsbtStation selectByStationGuid(String guid) {
        return rsbtStationMapper.selectByStationGuid(guid);
    }

    /**
     * 是否区域内查询
     */
    public List<StationDTO> queryAreaRange(StationDTO stationDTO, UsersDTO usersDTO) {
        return rsbtStationMapper.queryAreaRange(stationDTO, usersDTO);
    }

    /**
     * 根据btsIds 刪除
     */
    public void deleteByStationIds(List<String> stationIds) {
        if (stationIds.size() > 50) {
            int pageNum = stationIds.size() % 50 == 0 ? stationIds.size() / 50 : stationIds.size() / 50 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<String> guids;
                if (i != pageNum) {
                    guids = stationIds.subList((i - 1) * 50, i * 50);
                } else {
                    guids = stationIds.subList((i - 1) * 50, stationIds.size());
                }
                rsbtStationMapper.deleteByStationIds(guids);
            }
        } else {
            rsbtStationMapper.deleteByStationIds(stationIds);
        }
    }
}
