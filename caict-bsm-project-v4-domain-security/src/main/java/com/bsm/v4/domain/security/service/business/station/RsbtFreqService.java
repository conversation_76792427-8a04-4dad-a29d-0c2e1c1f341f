package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtFreqMapper;
import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtFreqService extends BasicService<RsbtFreq> {

    @Autowired
    private RsbtFreqMapper rsbtFreqMapper;

    /**
     * 根据基站guid和扇区id查询
     */
    public RsbtFreq findOneByStationCell(String stationGuid, String cellId) {
        return rsbtFreqMapper.findOneByStationCell(stationGuid, cellId);
    }

    /**
     * 根据基站guid查询
     */
    public List<RsbtFreq> findOneByStationGuid(String stationGuid) {
        return rsbtFreqMapper.findOneByStationGuid(stationGuid);
    }
}
