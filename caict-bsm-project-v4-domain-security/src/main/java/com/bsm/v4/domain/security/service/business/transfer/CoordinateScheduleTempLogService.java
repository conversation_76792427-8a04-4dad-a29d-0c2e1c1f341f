package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.CoordinateScheduleTempLogMapper;
import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleTempLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTempLog;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class CoordinateScheduleTempLogService extends BasicService<CoordinateScheduleTempLog> {

    @Autowired
    private CoordinateScheduleTempLogMapper coordinateScheduleTempLogMapper;

    /**
     * 根据jobId、类型查询总数
     */
    public int selectCountByJobIdDataType(String appGuid, String dataType) {
        return coordinateScheduleTempLogMapper.selectCountByJobIdDataType(appGuid, dataType);
    }

    /**
     * 根据jobId、类型分页查询
     */
    public List<CoordinateScheduleTempLogDTO> findAllPageByJobIdDataType(CoordinateScheduleTempLogDTO dto) {
        return coordinateScheduleTempLogMapper.findAllPageByJobIdDataType(dto);
    }
}
