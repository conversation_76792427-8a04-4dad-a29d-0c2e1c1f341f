package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtNetMapper;
import com.bsm.v4.system.model.entity.business.station.RsbtNet;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RsbtNetService extends BasicService<RsbtNet> {

    @Autowired
    private RsbtNetMapper rsbtNetMapper;

    public RsbtNet getRsbtNetByStationGuid(String stationGuid) {
        return rsbtNetMapper.getRsbtNetByStationGuid(stationGuid);
    }

}
