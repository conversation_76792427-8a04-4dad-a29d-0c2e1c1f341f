package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtAntfeedMapper;
import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtAntfeedService extends BasicService<RsbtAntfeed> {

    @Autowired
    private RsbtAntfeedMapper rsbtAntfeedMapper;

    /**
     * 根据基站id和扇区id查询
     */
    public RsbtAntfeed findOneByStationCell(String stationGuid, String cellId) {
        return rsbtAntfeedMapper.findOneByStationCell(stationGuid, cellId);
    }
}
