package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportFileMapper extends BasicMapper<TransportFile> {

    //根据Job和文件名查询数量
    @Select("select count(*) from TRANSPORT_FILE where (JOB_GUID = #{jobId,jdbcType=VARCHAR} or #{jobId,jdbcType=VARCHAR} is null) " +
            "and (FILE_LOCAL_NAME = #{fileName,jdbcType=VARCHAR} or #{fileName,jdbcType=VARCHAR} is null) ")
    int selectCountByJobFileName(@Param("jobId") String jobId, @Param("fileName") String fileName);

    /**
     * 根据jobGuid查询运营商上传的文件
     */
    @Select("select * from TRANSPORT_FILE where JOB_GUID = #{jobGuid} and FILE_TYPE = '1'")
    List<TransportFileDTO> findOperatorsAllByJobGuid(@Param("jobGuid") String jobGuid);

    /**
     * 根据jobGuid查询附件
     */
    @Select("select * from TRANSPORT_FILE where JOB_GUID = #{jobGuid} and FILE_TYPE = '0'")
    List<TransportFileDTO> findEnclosureAllByJobGuid(@Param("jobGuid") String jobGuid);

    @Select("select b.* from APPROVAL_TRANSPORT_JOB a ,TRANSPORT_FILE b where a.guid = b.job_guid \n" +
            "and a.job_guid = #{jobGuid} and b.gen_num = #{genNum} and b.data_type = #{dataType} and b.file_type = #{fileType}")
    List<TransportFileDTO> findByJobGuid(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("fileType") String fileType);

    @Select("select * from TRANSPORT_FILE " +
            "where TRANSPORT_FILE.job_guid = #{jobGuid} and TRANSPORT_FILE.file_type = #{fileType}")
    List<TransportFileDTO> findByGuidType(@Param("jobGuid") String jobGuid, @Param("fileType") String fileType);

    @Select("select F.FILE_LOCAL_NAME fileLocalName, F.GMT_CREATE gmtCreate, F.FILE_STATE fileState, " +
            "F.GUID fileId, F.FILE_PATH filePath,F.FILE_TYPE " +
            "from TRANSPORT_FILE F " +
            "where file_type = #{fileType} " +
            "and JOB_GUID = #{jobId}")
    List<TransportFileDTO> findFileDetailByJobId(@Param("jobId") String jobId, @Param("fileType") String fileType);
}
