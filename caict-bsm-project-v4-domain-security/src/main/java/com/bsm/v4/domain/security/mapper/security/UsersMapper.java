package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Users;
import com.bsm.v4.system.model.vo.security.UsersSearchVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Title: UsersMapper
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.mapper
 * @Date 2023/8/15 14:47
 * @description:
 */
@Repository
public interface UsersMapper extends BasicMapper<Users> {


    /**
     * 条件查询
     */
    @Select("select * from sys_users")
    List<UsersDTO> findAllByWhere(@Param("usersSearchDTO") UsersSearchVO usersSearchVO);

    @Select("select sys_users.* from sys_users,sys_role_users where sys_users.ID = sys_role_users.users_id and sys_role_users.role_id = #{roleId}")
    List<UsersDTO> findAllUsersByRoleId(@Param("roleId") String roleId);
}

