package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.applytable.BsmApplyTableTemplateDTO;
import com.bsm.v4.system.model.dto.business.applytable.BsmApplyTemplateDTO;
import com.bsm.v4.system.model.dto.business.station.StationBakDTO;
import com.bsm.v4.system.model.dto.business.station.StationDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.PackageDataDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.SchedualDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.SchedualDataDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.TransportJobInBranchDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranch;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDealLog;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Repository
public interface TransportJobBranchMapper extends BasicMapper<TransportJobBranch> {

    @Select("select count(*) from TRANSPORT_JOB_BRANCH where job_guid = #{jobGuid}")
    int selectAllCount(@Param("jobGuid") String jobGuid);

    @Select("select TRANSPORT_JOB_BRANCH.*,FSA_REGION.NAME as regionName from TRANSPORT_JOB_BRANCH , FSA_REGION WHERE TRANSPORT_JOB_BRANCH.REGION_CODE = FSA_REGION.CODE " +
            " and (TRANSPORT_JOB_BRANCH.JOB_GUID = #{transportJobBranchDTO.jobGuid,jdbcType=VARCHAR} or #{transportJobBranchDTO.jobGuid,jdbcType=VARCHAR} is null) order by FSA_REGION.NAME ")
    List<TransportJobBranchDTO> findAllBranch(@Param("transportJobBranchDTO") TransportJobBranchDTO transportJobBranchDTO);

    @Select("select * from (" +
            "select a.guid,b.type as userType,#{regionId} as regionId,row_number() over(partition by b.type order by a.GMT_CREATE) rn " +
            "from TRANSPORT_JOB a,fsa_users b where a.user_guid = b.id " +
            "and a.is_compare in ('10', '11')) c ")
    @Results({
            @Result(property = "guid", column = "GUID"),
            @Result(property = "transportJobBranchList", column = "{guid=GUID,regionId=regionId}", javaType = List.class, many = @Many(select = "findListByJobGuid"))
    })
    List<SchedualDTO> findSchedualP(@Param("regionId") String regionId);

    /**
     * 确认时修改数据确认状态
     */
    @Update("update TRANSPORT_JOB_BRANCH set IS_COMPARE = #{isCompare} where TRANSPORT_JOB_BRANCH.JOB_GUID = #{jobGuid} AND TRANSPORT_JOB_BRANCH.DATA_TYPE = #{dataType} " +
            "AND TRANSPORT_JOB_BRANCH.GEN_NUM = #{genNum} AND TRANSPORT_JOB_BRANCH.TECH_TYPE = #{techType}")
    int updateByConfirm(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("techType") String techType, @Param("isCompare") String isCompare);

    /**
     * 自动确认时修改数据确认状态
     */
    @Update("update TRANSPORT_JOB_BRANCH set IS_COMPARE = #{isCompare} where TRANSPORT_JOB_BRANCH.JOB_GUID = #{jobGuid}")
    int updateByConfirmAuto(@Param("jobGuid") String jobGuid, @Param("isCompare") String isCompare);

    @Select("select count(*) from TRANSPORT_JOB a,TRANSPORT_JOB_BRANCH b where a.guid = b.job_guid and a.guid = #{jobGuid} " +
            "and b.is_compare = '8' ")
    int judgeAllConfirm(@Param("jobGuid") String jobGuid);

    @Delete("delete from TRANSPORT_JOB_BRANCH where JOB_GUID = #{jobId}")
    int deleteByJobId(@Param("jobId") String jobId);

    @Update("UPDATE TRANSPORT_JOB_BRANCH SET IS_COMPARE = #{isCompare} where JOB_GUID = #{jobId} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum}")
    int updateByJobId(@Param("jobId") String jobId, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("isCompare") String isCompare);

    /**
     * 查询用户下所有申请表
     */
    @Select("select * from TRANSPORT_JOB_BRANCH where USER_GUID = #{userId}")
    List<TransportJobBranchDTO> findAllByUser(@Param("userId") String userId);

    /**
     * 根据任务、运营商、类型、制式查询
     */
    @Select("select * from TRANSPORT_JOB_BRANCH where 1=1 " +
            "and (JOB_GUID = #{jobGuid,jdbcType=VARCHAR} or #{jobGuid,jdbcType=VARCHAR} is null) " +
            "and (USER_GUID = #{userGuid,jdbcType=VARCHAR} or #{userGuid,jdbcType=VARCHAR} is null) " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (TECH_TYPE = #{techType,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (REGION_CODE = #{regionCode,jdbcType=VARCHAR} or #{regionCode,jdbcType=VARCHAR} is null) ")
    TransportJobBranchDTO findOneByJobUserTypeGen(@Param("jobGuid") String jobGuid, @Param("userGuid") String userGuid, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("regionCode") String regionCode, @Param("techType") String techType);

    /**
     * 根据任务、运营商、类型、制式查询
     */
    @Select("select b.*, r.name as regionName from TRANSPORT_JOB_BRANCH b, FSA_REGION r where b.REGION_CODE = r.CODE " +
            "and (b.JOB_GUID = #{jobGuid,jdbcType=VARCHAR} or #{jobGuid,jdbcType=VARCHAR} is null) " +
            "and (b.USER_GUID = #{userGuid,jdbcType=VARCHAR} or #{userGuid,jdbcType=VARCHAR} is null) " +
            "and (b.DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (b.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (b.TECH_TYPE = #{techType,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (b.REGION_CODE = #{regionCode,jdbcType=VARCHAR} or #{regionCode,jdbcType=VARCHAR} is null) ")
    List<TransportJobBranchDTO> findOneByJobUserTypeGen1(@Param("jobGuid") String jobGuid, @Param("techType") String techType, @Param("userGuid") String userGuid, @Param("dataType") String dataType, @Param("genNum") String genNum, @Param("regionCode") String regionCode);

    /**
     * 根据appCode查找任务
     */
    @Select("select * from TRANSPORT_JOB_BRANCH where APP_CODE = #{appCode}")
    TransportJobBranch findByAppCode(@Param("appCode") String appCode);

    @Select("select TRANSPORT_JOB_BRANCH.*,FSA_USERS.TYPE AS userType,FSA_REGION.NAME AS regionName " +
            "from TRANSPORT_JOB_BRANCH,FSA_USERS,FSA_REGION " +
            "where TRANSPORT_JOB_BRANCH.USER_GUID = FSA_USERS.ID and TRANSPORT_JOB_BRANCH.REGION_CODE = FSA_REGION.CODE " +
            "AND (TRANSPORT_JOB_BRANCH.JOB_GUID = #{transportJobInBranchDTO.jobGuid,jdbcType=VARCHAR} or #{transportJobInBranchDTO.jobGuid,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} = 'wuwei') " +
            "and (TRANSPORT_JOB_BRANCH.GMT_CREATE >= #{transportJobInBranchDTO.appDateStart,jdbcType=VARCHAR} or #{transportJobInBranchDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB_BRANCH.GMT_CREATE <= #{transportJobInBranchDTO.appDateEnd,jdbcType=VARCHAR} or #{transportJobInBranchDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB_BRANCH.IS_COMPARE = #{transportJobInBranchDTO.isCompare,jdbcType=VARCHAR} or #{transportJobInBranchDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB_BRANCH.DATA_TYPE = #{transportJobInBranchDTO.dataType,jdbcType=VARCHAR} or #{transportJobInBranchDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB_BRANCH.GEN_NUM = #{transportJobInBranchDTO.genNum,jdbcType=VARCHAR} or #{transportJobInBranchDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_JOB_BRANCH.REGION_CODE in (select code from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "order by TRANSPORT_JOB_BRANCH.GMT_CREATE,FSA_REGION.NAME,TRANSPORT_JOB_BRANCH.GEN_NUM desc ")
    List<TransportJobInBranchDTO> findListByPage(@Param("transportJobInBranchDTO") TransportJobInBranchDTO transportJobInBranchDTO, @Param("usersDTO") UsersDTO usersDTO);

    @Select("select * from TRANSPORT_JOB_BRANCH where GUID = #{guid}")
    @Results({
            @Result(property = "guid", column = "GUID"),
            //@Result(property = "transportFileDTOList",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper.findOperatorsAllByJobGuid")),
            @Result(property = "transportFileAttachedDTOList", column = "GUID", many = @Many(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper.findEnclosureAllByJobGuid"))
    })
    TransportJobInBranchDTO findDetail(@Param("guid") String guid);

    @Select("select * from (" +
            "select a.guid,b.type as userType,#{regionId} as regionId,row_number() over(partition by b.type order by a.GMT_CREATE) rn " +
            "from TRANSPORT_JOB a,fsa_users b where a.user_guid = b.id " +
            "and a.is_compare = '10') c " +
            "where c.rn = 1 ")
    @Results({
            @Result(property = "guid", column = "GUID"),
            @Result(property = "transportJobBranchList", column = "{guid=GUID,regionId=regionId}", javaType = List.class, many = @Many(select = "findListByJobGuid"))
    })
    List<SchedualDTO> findSchedual(@Param("regionId") String regionId);

    @Select("select count(approval_transport_job.guid) as sumStation,approval_transport_job.guid as appGuid,approval_transport_job.data_type as dataType,approval_transport_job.gen_num as genNum,approval_transport_job.tech_type as techType, " +
            "(select name from fsa_region where code = approval_transport_job.region_code) as area " +
            "from approval_transport_job,RSBT_STATION_BAK " +
            "where approval_transport_job.guid = RSBT_STATION_BAK.App_guid " +
            "and approval_transport_job.job_guid=#{guid,jdbcType=VARCHAR} " +
            "and approval_transport_job.region_code in (select CODE from FSA_REGION start with ID = #{regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "and RSBT_STATION_BAK.is_sync != 1 " +
            "and RSBT_STATION_BAK.IS_GIVEUP is null " +
            "and approval_transport_job.is_compare not in ('18','19') " +
            "group by approval_transport_job.guid,approval_transport_job.data_type,approval_transport_job.gen_num,approval_transport_job.region_code,approval_transport_job.tech_type " +
            "order by approval_transport_job.region_code")
    List<SchedualDataDTO> findListByJobGuid(@Param("guid") String jobGuid, @Param("regionId") String regionId);

    @Select("select RSBT_STATION_BAK.* from approval_transport_job,RSBT_STATION_BAK where approval_transport_job.GUID = RSBT_STATION_BAK.APP_GUID " +
            "and approval_transport_job.guid = #{transportJobInBranchDTO.appGuid} " +
            "and (RSBT_STATION_BAK.DATA_STATUS = #{transportJobInBranchDTO.dataStatus,jdbcType=VARCHAR} or #{transportJobInBranchDTO.dataStatus,jdbcType=VARCHAR} is null) " +
            "AND (RSBT_STATION_BAK.STAT_NAME LIKE concat(concat('%',#{transportJobInBranchDTO.stationName,jdbcType=VARCHAR}),'%') OR #{transportJobInBranchDTO.stationName,jdbcType=VARCHAR} is null) " +
            "AND (RSBT_STATION_BAK.STAT_ADDR LIKE concat(concat('%',#{transportJobInBranchDTO.stationAddr,jdbcType=VARCHAR}),'%') OR #{transportJobInBranchDTO.stationAddr,jdbcType=VARCHAR} is null) " +
            "and RSBT_STATION_BAK.IS_SYNC != '1' ")
    List<StationBakDTO> findStationListByGuid(@Param("transportJobInBranchDTO") TransportJobInBranchDTO transportJobInBranchDTO);

    @Select("select Rsbt_station.* from TRANSPORT_JOB_BRANCH,APPLY_LINK,Rsbt_station where TRANSPORT_JOB_BRANCH.app_code = APPLY_LINK.app_code_out " +
            "and APPLY_LINK.app_code_in = Rsbt_station.app_code " +
            "and TRANSPORT_JOB_BRANCH.guid = #{guid} ")
    List<StationDTO> findHistoryByGuid(@Param("guid") String guid);

    /**
     * 外网端查询申请表
     */
    @Select("select TRANSPORT_JOB_BRANCH.APP_CODE,RSBT_ORG.ORG_NAME,RSBT_ORG.ORG_ADDR,RSBT_ORG.ORG_CODE,RSBT_ORG.ORG_POST,RSBT_ORG.ORG_LINK_PERSON,RSBT_ORG.ORG_PHONE,RSBT_ORG.ORG_MAIL," +
            "TRANSPORT_JOB_BRANCH.DATA_TYPE,TRANSPORT_JOB_BRANCH.STATION_COUNT,TRANSPORT_JOB_BRANCH.JOB_GUID,TRANSPORT_JOB_BRANCH.USER_GUID,TRANSPORT_JOB_BRANCH.REGION_CODE,TRANSPORT_JOB_BRANCH.GEN_NUM " +
            "from TRANSPORT_JOB_BRANCH,RSBT_ORG,RSBT_ORG_USERS " +
            "where TRANSPORT_JOB_BRANCH.USER_GUID = RSBT_ORG_USERS.USERS_ID and RSBT_ORG_USERS.ORG_GUID = RSBT_ORG.GUID " +
            "and TRANSPORT_JOB_BRANCH.GUID = #{guid} " +
            "and rownum = 1")
    BsmApplyTemplateDTO findBsmApplyTemplateByGuid(@Param("guid") String guid);

    @Select("select TRANSPORT_JOB_BRANCH.* " +
            "from TRANSPORT_JOB_BRANCH,APPLY_LINK,RSBT_APPLY " +
            "where TRANSPORT_JOB_BRANCH.APP_CODE = APPLY_LINK.APP_CODE_OUT and APPLY_LINK.APP_CODE_IN = RSBT_APPLY.APP_CODE " +
            "and RSBT_APPLY.GUID = #{guid} ")
    TransportJobInBranchDTO findBsmApplyTemplateByAppGuid(@Param("guid") String guid);

    /**
     * 外网端查询申请表技术资料信息
     */
    @Select("<script>" +
            "select rownum as id,a.* from" +
            "(select BTS_NAME,BTS_ID,TECH_TYPE,COUNTY,LONGITUDE,LATITUDE,(SEND_START_FREQ || '-' || SEND_END_FREQ) as sendFreq,(ACC_START_FREQ || '-' || ACC_END_FREQ) as accFreq,MAX_EMISSIVE_POWER,HEIGHT " +
            "from TRANSPORT_RAW_BTS_DEAL " +
            "where JOB_GUID = #{jobGuid} and  BTS_DATA_TYPE = #{dataType} and GEN_NUM = #{genNum} " +
            "<if test='code != null and code != \"\"'>" +
            "and COUNTY in(select NAME from SYS_REGION start with CODE = #{code} connect by prior ID = PARENT_ID) </if>" +
            ") a " +
            "order by rownum,a.BTS_ID " +
            "</script>")
    List<BsmApplyTableTemplateDTO> findBsmApplyTableTemplateByJobUserDataType(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("code") String code, @Param("genNum") String genNum);

    /**
     * 内网查询本系统申请表
     */
    @Select("select * from TRANSPORT_JOB_BRANCH where STATE = '2' ")
    List<TransportJobBranchDTO> findAllApply(@Param("transportJobInBranchDTO") TransportJobInBranchDTO transportJobInBranchDTO);

    /**
     * 内网查询本系统申请表
     */
    @Select("select TRANSPORT_RAW_BTS_DEAL_LOG.* from TRANSPORT_JOB_BRANCH,TRANSPORT_RAW_BTS_DEAL_LOG where TRANSPORT_JOB_BRANCH.JOB_GUID = TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID " +
            "AND TRANSPORT_JOB_BRANCH.GUID = #{guid} and TRANSPORT_RAW_BTS_DEAL_LOG.USER_GUID = #{userGuid}")
    List<TransportRawBtsDealLog> findAllDetailByUser(@Param("guid") String guid, @Param("userGuid") String userGuid);


    /**
     * 内网查询本系统申请表
     */
    @Select("select transport_job_branch.*,rsbt_apply.guid appGuid from transport_job_branch,apply_link,Rsbt_Apply " +
            "where transport_job_branch.app_code = apply_link.app_code_out " +
            "and apply_link.app_code_in = rsbt_apply.app_code " +
            "and Rsbt_Apply.app_code = #{appCode} ")
    TransportJobInBranchDTO selectByNewAppCode(@Param("appCode") String appCode);

    /**
     * 根据job和状态查询数量
     */
    @Select("select count(1) from TRANSPORT_JOB_BRANCH where JOB_GUID = #{jobGuid} and STATE = #{state}")
    int selectCountByJobState(@Param("jobGuid") String jobGuid, @Param("state") String state);

    @Select("select * from (" +
            "select a.*,b.type as userType,row_number() over(partition by b.type order by a.GMT_CREATE) rn " +
            "from TRANSPORT_JOB a,fsa_users b where a.user_guid = b.id " +
            "and a.is_compare ='10' ) c " +
            "where c.rn = 1 ")
    List<PackageDataDTO> findSchedualPackage();

    @Select("select * from (" +
            "select a.*,b.type as userType,row_number() over(partition by b.type order by a.GMT_CREATE desc) rn " +
            "from TRANSPORT_JOB a,fsa_users b where a.user_guid = b.id " +
            "and a.is_compare ='13' " +
            "and a.is_deleted is null ) c " +
            "where c.rn = 1 ")
    List<PackageDataDTO> findSchedualPackage2();

    /**
     * 查询branch是否都不为已完成
     *
     * @param jobGuid job id
     * @return int
     */
    @Select("select count(*) from TRANSPORT_JOB a,TRANSPORT_JOB_BRANCH b where a.guid = b.job_guid and a.guid = #{jobGuid} " +
            "and b.is_compare != '11' ")
    int judgeAllConfirmFail(@Param("jobGuid") String jobGuid);

    /**
     * 变更数据取前两个数据进行对照查看
     */
    @Select("select * from ( " +
            "Select rsbt_station_bak.*,rsbt_station_bak.st_c_code stationCode,rsbt_station_bak.net_ts netType from rsbt_station_bak where st_c_code = #{stationBakDTO.stCCode} " +
            "            and org_type = #{stationBakDTO.orgType}  " +
            "            order by bak_date desc) where rownum < 3 ")
    List<StationBakDTO> findUpdateList(@Param("stationBakDTO") StationBakDTO stationBakDTO);


}
