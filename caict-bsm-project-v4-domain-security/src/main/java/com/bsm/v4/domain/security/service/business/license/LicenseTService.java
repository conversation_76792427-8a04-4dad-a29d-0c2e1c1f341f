package com.bsm.v4.domain.security.service.business.license;

import com.bsm.v4.domain.security.mapper.business.license.LicenseTMapper;
import com.bsm.v4.system.model.entity.business.license.RsbtLicenseT;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/11
 */
@Service
public class LicenseTService extends BasicService<RsbtLicenseT> {

    @Autowired
    private LicenseTMapper licenseTMapper;

}
