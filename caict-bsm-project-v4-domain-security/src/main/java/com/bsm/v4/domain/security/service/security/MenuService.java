package com.bsm.v4.domain.security.service.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.system.model.dto.security.MenuDTO;
import com.caictframework.data.service.BasicService;
import com.bsm.v4.domain.security.mapper.security.MenuMapper;
import com.bsm.v4.system.model.entity.security.Menu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by dengsy on 2023-02-10.
 */
@Service
public class MenuService extends BasicService<Menu> {

    @Autowired
    private MenuMapper menuMapper;

    /**
     * 查询所有菜单（返回dto）
     */
    public List<MenuDTO> findAllDto() {
        return menuMapper.findAllDto();
    }

    /**
     * 查询所有菜单（根据菜单类型）
     */
    public List<MenuDTO> findAllByType(String type) {
        return menuMapper.findAllByType(type);
    }


    public List<MenuDTO> findAllByParentId(String parentId) {
        return menuMapper.findAllByParentId(parentId);
    }

    /**
     * 更新父级
     */
    public int updateParent(String parentOld, String parentNew) {
        return menuMapper.updateParent(parentOld, parentNew);
    }

    /**
     * 根据用户查询菜单
     */
    public List<MenuDTO> findAllByUser(String userId) {
        return menuMapper.findAllByUser(userId);
    }
}
