package com.bsm.v4.domain.security.mapper.business.station;


import com.bsm.v4.system.model.entity.business.station.RsbtStationAppendix;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtStationAppendixMapper extends BasicMapper<RsbtStationAppendix> {

    /**
     * 获取需要注销的基站数据
     */
    @Select("SELECT * FROM RSBT_STATION_APPENDIX a WHERE NOT EXISTS(SELECT b.STATION_GUID FROM RSBT_EQU b LEFT JOIN RSBT_EQU_APPENDIX c  ON b.GUID = c.GUID WHERE c.IS_DELETED = 0 AND a.GUID = b.STATION_GUID AND a.IS_DELETED = 0)")
    List<RsbtStationAppendix> findAllByNotSection();

}
