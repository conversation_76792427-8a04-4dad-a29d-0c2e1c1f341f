package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtStationAppendixMapper;
import com.bsm.v4.system.model.entity.business.station.RsbtStationAppendix;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/23
 */
@Service
public class RsbtStationAppendixService extends BasicService<RsbtStationAppendix> {

    @Autowired
    private RsbtStationAppendixMapper rsbtStationAppendixMapper;

    /**
     * 获取需要注销的基站数据
     */
    public List<RsbtStationAppendix> findAllByNotSection() {
        return rsbtStationAppendixMapper.findAllByNotSection();
    }
}
