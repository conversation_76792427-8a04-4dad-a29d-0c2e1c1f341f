package com.bsm.v4.domain.security.service.business.applytable;

import com.bsm.v4.domain.security.mapper.business.applytable.BsmApplyTableStationMapper;
import com.bsm.v4.system.model.entity.business.applytable.BsmApplytableStation;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-28.
 */
@Service
public class BsmApplyTableStationService extends BasicService<BsmApplytableStation> {

    @Autowired
    private BsmApplyTableStationMapper bsmApplyTableStationMapper;

    public List<BsmApplytableStation> selectNotStation() {
        return bsmApplyTableStationMapper.selectNotStation();
    }

    public int selectCountByApplyTable(String applyTableGuid) {
        return bsmApplyTableStationMapper.selectCountByApplyTable(applyTableGuid);
    }

    public int deleteAllByGuid(String guid) {
        return bsmApplyTableStationMapper.deleteAllByGuid(guid);
    }
}
