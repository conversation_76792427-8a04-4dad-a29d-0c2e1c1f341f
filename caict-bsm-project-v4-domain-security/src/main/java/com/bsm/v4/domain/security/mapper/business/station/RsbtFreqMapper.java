package com.bsm.v4.domain.security.mapper.business.station;


import com.bsm.v4.system.model.entity.business.station.RsbtFreq;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtFreqMapper extends BasicMapper<RsbtFreq> {

    /**
     * 根据基站guid和扇区id查询
     */
    @Select("select * from RSBT_FREQ LEFT JOIN RSBT_FREQ_T ON RSBT_FREQ.GUID = RSBT_FREQ_T.GUID" +
            "  where RSBT_FREQ.STATION_GUID = #{stationGuid} and RSBT_FREQ_T.FT_FREQ_CCODE = #{cellId}")
    RsbtFreq findOneByStationCell(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);

    /**
     * 根据基站guid查询
     */
    @Select("select distinct FREQ_EFB, FREQ_EFE, FREQ_RFB, FREQ_RFE from RSBT_FREQ LEFT JOIN RSBT_FREQ_T ON RSBT_FREQ.GUID = RSBT_FREQ_T.GUID" +
            "  where RSBT_FREQ.STATION_GUID = #{stationGuid}")
    List<RsbtFreq> findOneByStationGuid(@Param("stationGuid") String stationGuid);
}
