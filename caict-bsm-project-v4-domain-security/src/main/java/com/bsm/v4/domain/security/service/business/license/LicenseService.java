package com.bsm.v4.domain.security.service.business.license;

import com.bsm.v4.domain.security.mapper.business.license.LicenseMapper;
import com.bsm.v4.system.model.dto.business.license.LicenseDTO;
import com.bsm.v4.system.model.dto.business.license.LicensePdfDTO;
import com.bsm.v4.system.model.dto.business.license.LicenseStatisticDTO;
import com.bsm.v4.system.model.dto.business.station.ISectionDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.license.RsbtLicense;
import com.caictframework.data.service.BasicService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Service
public class LicenseService extends BasicService<RsbtLicense> {

    @Autowired
    private LicenseMapper licenseMapper;

//    public List<LicenseDTO> getLicenseVOByPager(int startRows,int endRows,String orgName,String licenseCode,
//                                                String companyName,String stationName,String applytableCode,
//                                                String StationCode,Long licenseState,String netType,
//                                                Date expirationDate){
//        return licenseMapper.getLicenseVOByPager(startRows,endRows,orgName,licenseCode,companyName,stationName,applytableCode,StationCode,licenseState,netType,expirationDate);
//    }
//
//    public int getLicenseNum(String orgName,String licenseCode,
//                             String companyName,String stationName,String applytableCode,
//                             String StationCode,Long licenseState,String netType,
//                             Date expirationDate){
//        return licenseMapper.getLicenseNum(orgName,licenseCode,companyName,stationName,applytableCode,StationCode,licenseState,netType,expirationDate);
//    }
//
//    public List<LicenseDTO> getLicenseVO(String stationId){
//        return licenseMapper.getLicenseVO(stationId);
//    }
//
//    public List<LicensePdfDTO> getLicense(String stationId){
//        return licenseMapper.getLicense(stationId);
//    }
//
//    public List<String> getAntPole(String stationId){
//        return licenseMapper.getAntPole(stationId);
//    }
//
//    public List<String> getStationId(String linceseCode,String statName,Date linceseEndDate,
//                                     String applytableCode,String stCode,Long linceseState,
//                                     String orgName,String orgUser){
//        return licenseMapper.getStationId(linceseCode,statName,linceseEndDate,applytableCode,stCode,linceseState,orgName,orgUser);
//    }

    /**
     * 条件查询总数
     */
    public int selectCountWhere(LicenseDTO licenseDTO) {
        return licenseMapper.selectCountWhere(licenseDTO);
    }

    /**
     * 分页条件查询 省级运营商
     */
    public List<LicenseDTO> findAllPageByWhere1(LicenseDTO licenseDTO, UsersDTO usersDTO) {
        return licenseMapper.findAllPageByWhere1(licenseDTO, usersDTO);
    }

    /**
     * 分页条件查询 地市运营商
     */
    public List<LicenseDTO> findAllPageByWhere2(LicenseDTO licenseDTO, UsersDTO usersDTO) {
        return licenseMapper.findAllPageByWhere2(licenseDTO, usersDTO);
    }

    /**
     * 分页条件查询 wuwei
     */
    public List<LicenseDTO> findAllPageByWuWei(LicenseDTO licenseDTO, UsersDTO usersDTO) {
        if (StringUtils.isNotEmpty(licenseDTO.getLicenseCounty())) {
            if (usersDTO.getRoleDTO().getType().equals("1")) {
                // 省
                licenseDTO.setLicenseCountyStr(licenseDTO.getLicenseCounty().substring(0, 4));
                return licenseMapper.findAllPageByWuWei3(licenseDTO, usersDTO);
            } else {
                // 地市
                return licenseMapper.findAllPageByWuWei1(licenseDTO, usersDTO);
            }
        } else {
            return licenseMapper.findAllPageByWuWei2(licenseDTO, usersDTO);
        }
    }

    /**
     * 根据基站查询详情
     */
    public LicensePdfDTO findOneFDFByStationGuid(String stationGuid) {
        return licenseMapper.findOneFDFByStationGuid(stationGuid);
    }

    /**
     * 根据基站数组查询详情
     */
    public List<LicensePdfDTO> findOneFDFByStationGuids(String[] stationGuids) {
        return licenseMapper.findOneFDFByStationGuids(stationGuids);
    }

    /**
     * 根据基站数组查询详情
     */
    public List<LicensePdfDTO> downloadLicensesByCounty(String licenseCountyCode, String orgName) {
        return licenseMapper.downloadLicensesByCounty(licenseCountyCode, orgName);
    }

    /**
     * 查询执照统计数据
     *
     * @return dto
     */
    public LicenseStatisticDTO getStatisticCount() {
        return licenseMapper.getStatisticCount();
    }

    /**
     * 根据基站id查询执照
     */
    public RsbtLicense selectByStationGuid(String stationGuid) {
        return licenseMapper.selectByStationGuid(stationGuid);
    }

    public int updateBatchByStationGuid(List<String> stationGuids, String appCode) {
        if (stationGuids.size() > 50) {
            int pageNum = stationGuids.size() % 50 == 0 ? stationGuids.size() / 50 : stationGuids.size() / 50 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<String> guids;
                if (i != pageNum) {
                    guids = stationGuids.subList((i - 1) * 50, i * 50);
                } else {
                    guids = stationGuids.subList((i - 1) * 50, stationGuids.size());
                }
                licenseMapper.updateBatchByStationGuid(guids, appCode);
            }
        } else {
            licenseMapper.updateBatchByStationGuid(stationGuids, appCode);
        }
        return 1;
    }

    /**
     * 查看执照数据
     */
    public List<ISectionDTO> getISectionDTOList(String stationGuid) {
        return licenseMapper.getISectionDTOList(stationGuid);
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return licenseMapper.updateAppCode(oldAppCode, newAppCode);
    }

    /**
     * 根据stationId 删除执照
     *
     * @param stationGuids stationGuids
     */
    public void deleteByStationIds(List<String> stationGuids) {
        if (stationGuids.size() > 50) {
            int pageNum = stationGuids.size() % 50 == 0 ? stationGuids.size() / 50 : stationGuids.size() / 50 + 1;
            for (int i = 1; i <= pageNum; i++) {
                List<String> guids;
                if (i != pageNum) {
                    guids = stationGuids.subList((i - 1) * 50, i * 50);
                } else {
                    guids = stationGuids.subList((i - 1) * 50, stationGuids.size());
                }
                licenseMapper.deleteByStationIds(guids);
            }
        } else {
            licenseMapper.deleteByStationIds(stationGuids);
        }
    }
}
