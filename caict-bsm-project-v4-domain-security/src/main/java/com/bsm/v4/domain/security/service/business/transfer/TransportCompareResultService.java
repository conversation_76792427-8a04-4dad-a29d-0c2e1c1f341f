package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.TransportCompareResultMapper;
import com.bsm.v4.system.model.dto.business.transfer.TransportCompareResultDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportCompareResult;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TransportCompareResultService extends BasicService<TransportCompareResult> {

    @Autowired
    private TransportCompareResultMapper mapper;

    /**
     * 根据类型查询数量
     */
    public int selectCountByResultType(String appGuid, String resultType) {
        return mapper.selectCountByResultType(appGuid, resultType);
    }

    /**
     * 根据类型查询不属于的数量
     */
    public int selectCountByNotResultType(String appGuid, String resultType) {
        return mapper.selectCountByNotResultType(appGuid, resultType);
    }

    /**
     * 返回各比对结果数据的条数
     */
    public List<Integer> getCount(String jobId) {
        return mapper.getCount(jobId);
    }

    /**
     * 查询详情附带发现审核后的数据
     */
    public TransportCompareResultDTO findOneDetail(String guid) {
        return mapper.findOneDetail(guid);
    }

    /**
     * 查询详情附带发现审核前的数据
     */
    public TransportCompareResultDTO findDetail(String guid) {
        return mapper.findDetail(guid);
    }

    /**
     * 根据appGuid查询总数
     */
    public int selectCountByAppGuid(String appGuid, String resultType) {
        return mapper.selectCountByAppGuid(appGuid, resultType);
    }

    /**
     * 根据appGuid分页查询查询
     */
    public List<TransportCompareResultDTO> findAllPageByAppGuid(String appGuid, String resultType) {
        return mapper.findAllPageByAppGuid(appGuid, resultType);
    }

    /**
     * 根据appGuid分页查询查询（审核前)
     */
    public List<TransportCompareResultDTO> findAllPageScheduleByAppGuid(String appGuid, String resultType) {
        return mapper.findAllPageScheduleByAppGuid(appGuid, resultType);
    }

    /**
     * 添加对比数据
     */
    public int insertByAppGuid(String appGuid) {
        return mapper.insertByAppGuid(appGuid);
    }

    /**
     * 根据jobId删除数据
     */
    public int deleteByJobGuid(String jobGuid) {
        return mapper.deleteByJobGuid(jobGuid);
    }
}
