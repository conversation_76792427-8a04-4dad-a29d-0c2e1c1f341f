package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.entity.security.Region;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Title: RegionMapper
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.mapper.security
 * @Date 2023/8/16 17:25
 * @description:
 */
@Repository
public interface RegionMapper extends BasicMapper<Region> {

    @Select("select * from sys_region")
    List<RegionDTO> findAllDto();

    @Select("select * from sys_region where code = #{code}")
    RegionDTO findOneByCode(@Param("code") String code);

    @Select("select * from sys_region where ID =(select PARENT_ID from sys_region where name = #{name})")
    RegionDTO findParentOneByName(@Param("name") String name);

    @Select("select * from sys_region where name = #{name}")
    RegionDTO findOneByName(@Param("name") String name);

    @Select("select * from sys_region where parent_id = #{parentId}")
    List<RegionDTO> findAllByParentId(@Param("parentId") String parentId);

    /**
     * 根据id查询自身和下级的所有数据
     */
    @Select("select * from sys_region where id = #{parentId} or parent_id = #{parentId}")
    List<RegionDTO> findAllByIdOrParentId(@Param("parentId") String parentId);

    @Select("select * from sys_region " +
            "where parent_id = (select id from sys_region where name = #{name}) ")
    List<RegionDTO> findAreaByProvince(@Param("name") String name);

    /**
     * 根据父级和数据级别查询
     */
    @Select("WITH tree_region (id, parent_id, data_level) AS (" +
            "SELECT id, parent_id, data_level FROM sys_region WHERE id = #{parentId} " +
            "UNION ALL " +
            "SELECT pr.id, pr.parent_id, pr.data_level FROM sys_region pr " +
            "INNER JOIN tree_region ON pr.parent_id = tree_region.id" +
            ")" +
            "SELECT * FROM tree_region WHERE data_level = #{dataLevel}")
    List<RegionDTO> findAllByParentLevel(@Param("parentId") String parentId, @Param("dataLevel") int dataLevel);


    /**
     * 根据父级和数据级别查询
     */
//    @Select("with recursive tree_region as " +
//            "(" +
//            "select * from pm_region where id = #{parentId} " +
//            "union " +
//            "select pr.* from pm_region pr " +
//            "inner join tree_region on pr.parent_id = tree_region.id" +
//            ")" +
//            "select * from tree_region where data_level = #{dataLevel}")
//    List<RegionDTO> findAllByParentLevel(@Param("parentId")String parentId,@Param("dataLevel")int dataLevel);
    @Select("select * from sys_region where id = #{id}")
    RegionDTO findOneById(@Param("id") String id);

    /**
     * 根据组织查询所有区
     * */
//    @Select("select SYS_REGION.* " +
//            "from SYS_REGION " +
//            "where parent_id in " +
//            "(" +
//            "select id from SYS_REGION where parent_id = (select id from SYS_REGION,RSBT_ORG where SYS_REGION.CODE = RSBT_ORG.ORG_AREA_CODE and RSBT_ORG.GUID = #{orgGuid}) " +
//            ")")
    @Select("select SYS_REGION.*  " +
            "            from SYS_REGION  " +
            "            where parent_id in  " +
            "            ( " +
            "            select id from SYS_REGION where PARENT_ID = (select id from SYS_REGION where CODE =#{orgCode}))")
    List<RegionDTO> findAllByOrg(@Param("orgCode")String orgCode);
}
