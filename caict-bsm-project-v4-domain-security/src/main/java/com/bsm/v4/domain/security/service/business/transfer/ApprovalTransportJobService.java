package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.ApprovalTransportJobMapper;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalTransportJob;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class ApprovalTransportJobService extends BasicService<ApprovalTransportJob> {

    @Autowired
    private ApprovalTransportJobMapper approvalTransportJobMapper;

    /**
     * 根据jobGuid查询未提交的区域数据
     */
    public ApprovalTransportJobDTO findOneByJobGuidUploadRegion(String jobGuid, String dataType, String genNum, String regionCode, String techType) {
        return approvalTransportJobMapper.findOneByJobGuidUploadRegion(jobGuid, dataType, genNum, regionCode, techType);
    }

//    /**
//     * 分页查询对比任务
//     * */
//    public List<ApprovalTransportJobDTO> findPageCompareJobVOListByPage(String isCompare, Date appDateStart, Date appDateEnd, String genNum, String userType){
//        return approvalTransportJobMapper.findPageCompareJobVOListByPage(isCompare,appDateStart,appDateEnd,genNum,userType);
//    }

    public int findNotComplete(String jobId, String isCompare) {
        return approvalTransportJobMapper.findNotComplete(jobId, isCompare);
    }

//    /**
//     * 根据guid修改流程状态
//     * */
//    public int updateIsCompareByGuid(String isCompare,String guid){
//        ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
//        approvalTransportJob.setGuid(guid);
//        approvalTransportJob.setIsCompare(isCompare);
//        return approvalTransportJobMapper.update(approvalTransportJob);
//    }

    public List<ApprovalTransportJobDTO> findListByPage(ApprovalTransportJobDTO approvalTransportJobDTO, UsersDTO usersDTO) {
        return approvalTransportJobMapper.findListByPage(approvalTransportJobDTO, usersDTO);
    }

    public int judgeComplete(String jobGuid) {
        return approvalTransportJobMapper.judgeComplete(jobGuid);
    }

    public int judgeCompleteByRegion(String regionCode, String userGuid) {
        return approvalTransportJobMapper.judgeCompleteByRegion(regionCode, userGuid);
    }

    /**
     * 待办事项job查询
     *
     * @param areaCode code
     * @param userType userType
     * @return list
     */
    public List<TransportJobDTO> findJobListByPage(String areaCode, String userType) {
        return approvalTransportJobMapper.findJobListByPage(areaCode, userType);
    }

    /**
     * 待办事项job branch查询
     *
     * @param usersDTO                usersDTO
     * @param approvalTransportJobDTO approvalTransportJobDTO
     * @return list
     */
    public List<ApprovalTransportJobDTO> findDetailListByPage(ApprovalTransportJobDTO approvalTransportJobDTO, UsersDTO usersDTO) {
        return approvalTransportJobMapper.findDetailListByPage(approvalTransportJobDTO, usersDTO);
    }

    public List<ApprovalTransportJob> findNotPassList(String jobId, String opStatus) {
        return approvalTransportJobMapper.findNotPassList(jobId, opStatus);
    }

    /**
     * 查询当前任务中有跟上次审核不通过的任务相同的增量、代数、地区的任务
     */
    public ApprovalTransportJob findByDetail(String jobId, String dataType, String genNum, String regionCode, String techType) {
        return approvalTransportJobMapper.findByDetail(jobId, dataType, genNum, regionCode, techType);
    }

    /**
     * 根据appGuid插入log数据 解决审核变更对比扇区信息不全
     *
     * @param newAppGuid 新生成的appGuid
     * @param oldAppGuid 以前存在的appGuid
     */
    public void insertApprovalScheduleLog(String newAppGuid, String oldAppGuid) {
        approvalTransportJobMapper.insertApprovalScheduleLog(newAppGuid, oldAppGuid);
    }

    /**
     * 根据jobGuid和isCompare查询approveTransportJob集合
     */
    public List<ApprovalTransportJob> findByJobGuidAndIsCompare(String jobGuid, String isCompare) {
        return approvalTransportJobMapper.findByJobGuidAndIsCompare(jobGuid, isCompare);
    }

    /**
     * 根据jobGuid查询未提交的数据
     */
    public ApprovalTransportJobDTO findOneByJobGuidUpload(String jobGuid, String dataType, String genNum) {
        return approvalTransportJobMapper.findOneByJobGuidUpload(jobGuid, dataType, genNum);
    }

    /**
     * 查询对比任务总数
     */
    public int selectCountCompareJobVOListByPage(String isCompare, Date appDateStart, Date appDateEnd, String genNum, String userType) {
        return approvalTransportJobMapper.selectCountCompareJobVOListByPage(isCompare, appDateStart, appDateEnd, genNum, userType);
    }

    /**
     * 分页查询对比任务
     */
    public List<ApprovalTransportJobDTO> findPageCompareJobVOListByPage(ApprovalTransportJobDTO dto) {
        return approvalTransportJobMapper.findPageCompareJobVOListByPage(dto);
    }

    /**
     * 查询异常数据总数
     */
    public int selectCountApprovalTransportJoVOLog(String userType, String userId, Long fileState) {
        return approvalTransportJobMapper.selectCountApprovalTransportJoVOLog(userType, userId, fileState);
    }

    /**
     * 分页查询异常数据总数
     */
    public List<ApprovalTransportJobDTO> findApprovalTransportJobVOLogPage(String userType, String userId, Long fileState) {
        return approvalTransportJobMapper.findApprovalTransportJobVOLogPage(userType, userId, fileState);
    }

    /**
     * 根据guid修改流程状态
     */
    public int updateIsCompareByGuid(String isCompare, String guid) {
        ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
        approvalTransportJob.setGuid(guid);
        approvalTransportJob.setIsCompare(isCompare);
        return this.update(approvalTransportJob);
    }

    /**
     * 查询待办任务总数
     */
    public int selectCountByWhere(ApprovalTransportJobDTO dto) {
        return approvalTransportJobMapper.selectCountByWhere(dto);
    }

    /**
     * 分页查询待办任务
     */
    public List<ApprovalTransportJobDTO> findAllPageByWhere(ApprovalTransportJobDTO dto) {
        return approvalTransportJobMapper.findAllPageByWhere(dto);
    }

    /**
     * 查看审核列表历史记录总数
     */
    public int selectCountAppTransportJob(ApprovalTransportJobDTO dto) {
        return approvalTransportJobMapper.selectCountAppTransportJob(dto);
    }

    /**
     * 分页查看审核列表历史记录
     */
    public List<ApprovalTransportJobDTO> findAllPageAppTransportJob(ApprovalTransportJobDTO dto) {
        return approvalTransportJobMapper.findAllPageAppTransportJob(dto);
    }

    /**
     * 根据guid修改对比差异
     */
    public int updateCountByGuid(String guid, int identCount, int diffCount) {
        ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
        approvalTransportJob.setGuid(guid);
        approvalTransportJob.setGmtModified(new Date());
        approvalTransportJob.setIdenCount((long) identCount);
        approvalTransportJob.setDiffCount((long) diffCount);
        return this.update(approvalTransportJob);
    }


    /**
     * 根据guid修改任务状态
     */
    public int updateIsCompareAndStatusByGuid(String isCompare, String guid) {
        ApprovalTransportJob approvalTransportJob = new ApprovalTransportJob();
        approvalTransportJob.setGuid(guid);
        approvalTransportJob.setGmtModified(new Date());
        approvalTransportJob.setIsCompare(isCompare);
        return this.update(approvalTransportJob);
    }
}
