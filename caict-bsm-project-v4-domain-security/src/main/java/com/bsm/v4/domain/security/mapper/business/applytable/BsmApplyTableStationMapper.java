package com.bsm.v4.domain.security.mapper.business.applytable;

import com.bsm.v4.system.model.entity.business.applytable.BsmApplytableStation;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-28.
 */
@Repository
public interface BsmApplyTableStationMapper extends BasicMapper<BsmApplytableStation> {

    @Select("select GUID as guid,APPLYTABLE_GUID as applytableGuid,STATION_GUID as stationGuid,GMT_CREATE as gmtCreate,GMT_MODIFIED as gmtModified,IS_DELETED as isDeleted from BSM_APPLYTABLE_STATION where STATION_GUID not in(select STATION_GUID from RSBT_STATION)")
    List<BsmApplytableStation> selectNotStation();

    @Select("select count(*) from BSM_APPLYTABLE_STATION where station_guid not in(select station_guid from RSBT_STATION) and APPLYTABLE_GUID = #{applyTableGuid}")
    int selectCountByApplyTable(@Param("applyTableGuid") String applyTableGuid);

    @Delete("delete from BSM_APPLYTABLE_STATION where guid = #{guid}")
    int deleteAllByGuid(@Param("guid") String guid);
}
