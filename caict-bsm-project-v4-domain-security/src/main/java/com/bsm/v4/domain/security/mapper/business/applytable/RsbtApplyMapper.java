package com.bsm.v4.domain.security.mapper.business.applytable;

import com.bsm.v4.system.model.dto.business.applytable.ApplyTableDTO;
import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11AntFeedDataDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11EquDataDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11SectorDataDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11StationDataDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import com.bsm.v4.system.model.vo.business.applytable.ApplyTableVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Created by dengsy on 2020-04-28.
 */
@Repository
public interface RsbtApplyMapper extends BasicMapper<RsbtApply> {

    @Select("select * from BSM_APPLYTABLE where ORG_GUID = #{orgId} and NET_TYPE = #{netType} and STATION_COUNT < #{stationCount}")
    RsbtApply selectOneByOrgIdAndStationCount(@Param("orgId") String orgId, @Param("netType") String netType, @Param("stationCount") String stationCount);

    @Select("select * from (select * from BSM_APPLYTABLE order by applytable_code desc) a where rownum <= 1")
    RsbtApply selectOneOrderByCode();

    @Select("select * from RSBT_APPLY where app_code = #{appCode}")
    RsbtApply findOneByCode(@Param("appCode") String appCode);

    /**
     * 条件查询总数
     */
    @Select("select count(*) from BSM_APPLYTABLE " +
            "LEFT JOIN RSBT_ORG ON BSM_APPLYTABLE.ORG_GUID = RSBT_ORG.ORG_GUID " +
            "where BSM_APPLYTABLE.IS_DELETED = 0 " +
            "and (RSBT_ORG.ORG_NAME like concat(concat('%',#{bsmApplytableDTO.orgName,jdbcType=VARCHAR}),'%') or #{bsmApplytableDTO.orgName,jdbcType=VARCHAR} is null) " +
            "and (BSM_APPLYTABLE.APPLYTABLE_CODE like concat(concat('%',#{bsmApplytableDTO.applytableCode,jdbcType=VARCHAR}),'%') or #{bsmApplytableDTO.applytableCode,jdbcType=VARCHAR} is null) " +
            "and (BSM_APPLYTABLE.GMT_MODIFIED >= #{bsmApplytableDTO.appDateStart,jdbcType=VARCHAR} or #{bsmApplytableDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (BSM_APPLYTABLE.GMT_MODIFIED <= #{bsmApplytableDTO.appDateEnd,jdbcType=VARCHAR} or #{bsmApplytableDTO.appDateEnd,jdbcType=VARCHAR} is null) ")
    int selectCountByWhere(@Param("bsmApplytableDTO") RsbtApplyDTO bsmApplytableDTO);

    /**
     * 分页条件查询
     */
    @Select("SELECT RSBT_APPLY.*, RSBT_ORG.ORG_CODE AS orgCode,RSBT_ORG.ORG_NAME AS orgName, RSBT_ORG.ORG_ADDR AS orgAddr  " +
            "FROM RSBT_APPLY LEFT JOIN RSBT_APPLY_APPENDIX ON RSBT_APPLY_APPENDIX.GUID = RSBT_APPLY.GUID " +
            "\tLEFT JOIN RSBT_ORG ON RSBT_APPLY.ORG_GUID = RSBT_ORG.GUID " +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "WHERE RSBT_APPLY_APPENDIX.STATUS = '1' " +
            "\tAND ( RSBT_ORG.ORG_NAME LIKE concat( concat( '%',#{ bsmApplytableDTO.orgName, jdbcType = VARCHAR } ), '%' ) OR #{ bsmApplytableDTO.orgName, jdbcType = VARCHAR } IS NULL )  " +
            "\tAND ( RSBT_APPLY.APP_CODE LIKE concat( concat( '%',#{ bsmApplytableDTO.appCode, jdbcType = VARCHAR } ), '%' ) OR #{ bsmApplytableDTO.appCode, jdbcType = VARCHAR } IS NULL )  " +
            "\tAND ( RSBT_APPLY.APP_DATE >= #{ bsmApplytableDTO.appDateStart, jdbcType = VARCHAR } OR #{ bsmApplytableDTO.appDateStart, jdbcType = VARCHAR } IS NULL )  " +
            "\tAND ( RSBT_APPLY.APP_DATE <= #{ bsmApplytableDTO.appDateEnd, jdbcType = VARCHAR } OR #{ bsmApplytableDTO.appDateEnd, jdbcType = VARCHAR } IS NULL )   " +
            "ORDER BY RSBT_APPLY.APP_CODE")
    List<RsbtApplyDTO> findAllByWhere(@Param("bsmApplytableDTO") RsbtApplyDTO bsmApplytableDTO);


    @Select("<script>" +
            "select TRANSPORT_JOB.guid jobId,TRANSPORT_JOB.JOB_NAME,TRANSPORT_JOB.GMT_CREATE,SYS_USERS.TYPE orgType,TRANSPORT_JOB_BRANCH.IS_COMPARE applyStatus " +
            "from TRANSPORT_JOB left join SYS_USERS on TRANSPORT_JOB.USER_GUID = SYS_USERS.ID " +
            "left join TRANSPORT_JOB_BRANCH on TRANSPORT_JOB.GUID = TRANSPORT_JOB_BRANCH.JOB_GUID " +
            "where TRANSPORT_JOB_BRANCH.IS_COMPARE >= 8 " +
            "<if test='vo.userId != null and vo.userId != \"\"'>" +
            "     AND TRANSPORT_JOB.USER_GUID = #{vo.userId,jdbcType=VARCHAR}</if>" +
            "<if test='vo.jobName != null and vo.jobName != \"\"'>" +
            "     AND TRANSPORT_JOB.JOB_NAME LIKE concat(concat('%',#{vo.jobName}),'%')</if>" +
            "<if test='vo.gmtCreateStart != null'>" +
            "    AND TRANSPORT_JOB.GMT_CREATE  &gt;= #{vo.gmtCreateStart,jdbcType=DATE}</if> " +
            "<if test='vo.gmtCreateEnd != null'>" +
            "    AND TRANSPORT_JOB.GMT_CREATE  &lt;= #{vo.gmtCreateEnd,jdbcType=DATE}</if> " +
            "<if test='vo.isCompare != null and vo.isCompare != \"\"'>" +
            "    AND TRANSPORT_JOB.IS_COMPARE = #{vo.isCompare,jdbcType=VARCHAR} </if> " +
            "order by TRANSPORT_JOB.GMT_CREATE DESC" +
            "</script>")
    List<ApplyTableDTO> findDealDataList(@Param("vo") ApplyTableVO vo);

    @Select("select * from TRANSPORT_JOB_BRANCH where job_guid = #{jobId}")
    List<TransportJobBranchDTO> findApplyDetailList(@Param("jobId")String jobId);

    /**
     * 内网端分页条件查询
     */
    @Select("SELECT RSBT_APPLY.* " +
            "FROM RSBT_APPLY LEFT JOIN APPLY_LINK ON RSBT_APPLY.APP_CODE = APPLY_LINK.APP_CODE_IN " +
            "left join APPLY_JOB_IN ON RSBT_APPLY.APP_CODE = APPLY_JOB_IN.APP_CODE " +
            "WHERE APPLY_LINK.SYNC_STATUS = #{ bsmApplytableDTO.isSync, jdbcType = VARCHAR } " +
            "AND (APPLY_JOB_IN.REGION_CODE = (select code from fsa_region where id = #{bsmApplytableDTO.regionId, jdbcType = VARCHAR}) or #{bsmApplytableDTO.regionId, jdbcType = VARCHAR} is null) " +
            "AND ( RSBT_APPLY.APP_CODE LIKE concat( concat( '%',#{ bsmApplytableDTO.appCode, jdbcType = VARCHAR } ), '%' ) OR #{ bsmApplytableDTO.appCode, jdbcType = VARCHAR } IS NULL )  " +
            "AND ( RSBT_APPLY.APP_DATE >= #{ bsmApplytableDTO.appDateStart, jdbcType = VARCHAR } OR #{ bsmApplytableDTO.appDateStart, jdbcType = VARCHAR } IS NULL )  " +
            "AND ( RSBT_APPLY.APP_DATE <= #{ bsmApplytableDTO.appDateEnd, jdbcType = VARCHAR } OR #{ bsmApplytableDTO.appDateEnd, jdbcType = VARCHAR } IS NULL )  " +
            "ORDER BY RSBT_APPLY.APP_CODE")
    @Results({
            @Result(property = "appCode", column = "APP_CODE"),
            @Result(property = "techTableList", column = "APP_CODE", javaType = List.class, many = @Many(select = "selectTechList"))
    })
    List<RsbtApplyDTO> findAllInByWhere(@Param("bsmApplytableDTO") RsbtApplyDTO bsmApplytableDTO);

    @Select("select STAT_TDI from RSBT_STATION_BAK where app_code = #{appCode} order by STAT_TDI")
    List<String> selectTechList(@Param("appCode") String appCode);

    /**
     * 根据guid查询详情DTO
     */
    @Select("select * from RSBT_APPLY where GUID = #{guid}")
    @Results({
            @Result(property = "orgGuid", column = "ORG_GUID"),
            @Result(property = "orgDTO", column = "ORG_GUID", one = @One(select = "com.caict.bsm.project.domain.security.mapper.RsbtOrgMapper.findOneByGuid"))
    })
    RsbtApplyDTO findOneByGuid(@Param("guid") String guid);


    @Select({"<script>" +
            "SELECT * FROM BSM_APPLYTABLE WHERE APPLYTABLE_CODE in " +
            "<foreach item = 'item' index = 'index' collection = 'appSet'  open='(' separator=',' close=')'>" +
            " #{item}" +
            "</foreach>" +
            "</script>"})
    List<RsbtApply> selectExistApplytable(@Param("appSet") Set<String> appSet);


    @Update("UPDATE BSM_APPLYTABLE a " +
            "SET a.STATION_COUNT = ( SELECT COUNT( * ) num FROM BSM_APPLYTABLE_STATION b WHERE a.GUID = b.APPLYTABLE_GUID GROUP BY APPLYTABLE_GUID ) " +
            "WHERE" +
            " EXISTS ( SELECT 1 FROM BSM_APPLYTABLE_STATION b WHERE a.GUID = b.APPLYTABLE_GUID )")
    int updateApplyStationCount();

    @Select("SELECT COUNT(*) FROM RSBT_APPLY,RSBT_APPLY_APPENDIX " +
            "WHERE RSBT_APPLY.GUID = RSBT_APPLY_APPENDIX.GUID " +
            "AND RSBT_APPLY_APPENDIX.IS_DELETED = '0'")
    int queryApplyTableCount();

    @Select("select * from rsbt_station_bak s,rsbt_station_t t " +
            "where s.STATION_GUID = t.guid " +
            "and s.app_code = #{appCode} " +
            "and s.STAT_TDI = #{techNum}")
    Table11StationDataDTO findStation(@Param("appCode") String appCode, @Param("techNum") String techNum);

    @Select("select AT_CCODE,ant_Type, ant_Model, ant_Pole, at_3DBE, at_3DBR,ant_EGain, ant_RGain, ant_Menu, ant_Hight as antHeight, feed_Lose" +
            " from rsbt_antfeed s,rsbt_antfeed_t t where s.guid = t.guid and s.station_guid = #{stationGuid} order by t.AT_CCode")
    List<Table11AntFeedDataDTO> findAntFeed(@Param("stationGuid") String stationGuid);

    @Select("select s.EQU_MODEL as equModel, s.EQU_AUTH as equAuth,s.EQU_MENU as equMenu, e.ET_EQU_SUM as etEquSum,e.ET_EQU_UPOW as equPow,e.ET_POW_U as equPowU" +
            " from rsbt_equ s,rsbt_equ_t e,rsbt_eaf t where s.guid = e.guid and s.guid = t.equ_guid and t.guid = #{eafGuid}")
    List<Table11EquDataDTO> findEqu(@Param("eafGuid") String eafGuid);

    @Select("select rsbt_antfeed_t.AT_CCode,rsbt_antfeed.ANT_Angle,rsbt_antfeed_t.AT_CSGN,rsbt_antfeed_t.AT_RANG,rsbt_antfeed_t.AT_EANG," +
            "rsbt_freq.FREQ_EFB,rsbt_freq.FREQ_EFE,rsbt_freq.FREQ_RFB,rsbt_freq.FREQ_RFE,rsbt_eaf.GUID AS EAFGUID " +
            "from rsbt_freq,rsbt_antfeed,rsbt_antfeed_t,rsbt_eaf " +
            "where rsbt_freq.guid = rsbt_eaf.freq_guid " +
            "and rsbt_antfeed.guid = rsbt_eaf.ant_guid " +
            "and rsbt_antfeed.guid = rsbt_antfeed_t.guid " +
            "and rsbt_eaf.station_guid = #{stationGuid} " +
            "order by rsbt_antfeed_t.AT_CCode")
    @Results({
            @Result(property = "eafGuid", column = "EAFGUID"),
            @Result(property = "equData", column = "EAFGUID", one = @One(select = "findEqu"))
    })
    List<Table11SectorDataDTO> findSectors(@Param("stationGuid") String stationGuid);

    /**
     * 查询天线信息
     *
     * @param btsId   btsId
     * @param appGuid appGuid
     * @return list
     */
    @Select("select CELL_ID as atCCode, ANTENNA_MODEL as ant_Type, ANTENNA_MODEL as ant_Model, POLARIZATION_MODE as ant_Pole, AT_EANG as at3DBE, AT_RANG as at3DBR," +
            "ANTENNA_GAIN as antEGain, ANTENNA_GAIN as antRGain, ANTENNA_FACTORY as antMenu, HEIGHT as antHeight, FEEDER_LOSS as feedLose " +
            "from APPROVAL_SCHEDULE_LOG where BTS_ID = #{btsId} and APP_GUID = #{appGuid}")
    List<Table11AntFeedDataDTO> findAntFeed1(@Param("btsId") String btsId, @Param("appGuid") String appGuid);

    /**
     * 查询设备信息
     *
     * @param cellId  cellId
     * @param appGuid appGuid
     * @return list
     */
    @Select("select DEVICE_MODEL as equModel, MODEL_CODE as equAuth, ANTENNA_FACTORY as equMenu, '1' as etEquSum, MAX_EMISSIVE_POWER as equPow, 'W' as equPowU " +
            " from APPROVAL_SCHEDULE_LOG where CELL_ID = #{cellId} and APP_GUID = #{appGuid}")
    Table11EquDataDTO findEqu1(@Param("cellId") String cellId, @Param("appGuid") String appGuid);

    /**
     * 查询扇区和设备信息
     *
     * @param btsId   btsId
     * @param appGuid appGuid
     * @return list
     */
    @Select("select BTS_ID, APP_GUID, CELL_ID as atCCode, ANTENNA_GAIN as antAngle, CELL_ID as atCsgn, AT_RANG as atRang, AT_EANG as atEang, ACC_START_FREQ as freqRfb," +
            "ACC_END_FREQ as freqRfe, SEND_START_FREQ as freqEfb, SEND_END_FREQ as freqEfe from APPROVAL_SCHEDULE_LOG where BTS_ID = #{btsId} and APP_GUID = #{appGuid}")
    List<Table11SectorDataDTO> findSectors1(@Param("btsId") String btsId, @Param("appGuid") String appGuid);
}
