package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.EarthTableMapper;
import com.bsm.v4.system.model.dto.business.station.EarthTableDTO;
import com.bsm.v4.system.model.entity.business.station.EarthTable;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Service
public class EarthTableService extends BasicService<EarthTable> {

    @Autowired
    private EarthTableMapper earthTableMapper;

    public List<EarthTableDTO> findEarthByPage(EarthTableDTO earthTableDTO) {
        return earthTableMapper.findEarthByPage(earthTableDTO);
    }
}
