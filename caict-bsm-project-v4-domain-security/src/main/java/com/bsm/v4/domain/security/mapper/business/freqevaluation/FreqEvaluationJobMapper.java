package com.bsm.v4.domain.security.mapper.business.freqevaluation;

import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationJobVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@Repository
public interface FreqEvaluationJobMapper extends BasicMapper<FreqEvaluationJob> {

    /**
     * 根据条件查询
     *
     * @param vo vo
     * @return list
     */
    @Select("<script>" +
            "select * from BSM_FREQ_EVALUATION_JOB j where 1 = 1 " +
            "   <if test='vo.type != null and vo.type != \"\"'>" +
            "       AND j.TYPE = #{vo.type,jdbcType=VARCHAR} </if> " +
            "   <if test='vo.jobName != null and vo.jobName != \"\"'>" +
            "       AND j.JOB_NAME = #{vo.jobName,jdbcType=VARCHAR} </if> " +
            "<if test='vo.startTime != null'>" +
            "    AND j.CREATE_DATE &gt;= #{vo.startTime,jdbcType=DATE}</if> " +
            "<if test='vo.endTime != null'>" +
            "    AND j.CREATE_DATE &lt;= #{vo.endTime,jdbcType=DATE}</if> " +
            " order by j.CREATE_DATE desc " +
            "</script>")
    List<FreqEvaluationJob> findAllByWhere(@Param("vo") FreqEvaluationJobVO vo);
}
