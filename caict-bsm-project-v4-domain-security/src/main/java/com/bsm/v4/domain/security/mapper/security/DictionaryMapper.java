package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.DictionaryDTO;
import com.bsm.v4.system.model.entity.security.Dictionary;
import com.bsm.v4.system.model.vo.security.DictionarySearchVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Title: DictionaryMapper
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.mapper.security
 * @Date 2023/8/16 16:04
 * @description:
 */
@Repository
public interface DictionaryMapper extends BasicMapper<Dictionary> {
    /**
     * 根据code查询数量
     */
    @Select("select count(1) from sys_dictionary where code = #{code}")
    int selectCountByCode(@Param("code") String code);


    @Select("select * from sys_dictionary")
    List<DictionaryDTO> findAllByWhere(@Param("DictionaryDTO") DictionarySearchVO dictionarySearchVO);

    /**
     * 分页查询全部
     */
    @Select("select * from sys_dictionary " +
            "where (parent_id = #{dictionarySearchDTO.parentId,jdbcType=VARCHAR} or #{dictionarySearchDTO.parentId,jdbcType=VARCHAR} is null) " +
            "and (code = #{dictionarySearchDTO.code,jdbcType=VARCHAR} or #{dictionarySearchDTO.code,jdbcType=VARCHAR} is null) " +
            "and (name = #{dictionarySearchDTO.name,jdbcType=VARCHAR} or #{dictionarySearchDTO.name,jdbcType=VARCHAR} is null) " +
            "and (status = #{dictionarySearchDTO.status,jdbcType=VARCHAR} or #{dictionarySearchDTO.status,jdbcType=VARCHAR} is null) " +
            "and (data_type = #{dictionarySearchDTO.dataType,jdbcType=VARCHAR} or #{dictionarySearchDTO.dataType,jdbcType=VARCHAR} is null) " +
            "order by sort asc,code asc")
    List<DictionaryDTO> findAllDtoByWhere(@Param("dictionarySearchDTO") DictionarySearchVO dictionarySearchVO);

    /**
     * 查询所有数据字典（根据parentID)
     */
    @Select("select * from sys_dictionary where parent_id = #{parentId} or #{parentId} = '' or #{parentId} is null ")
    List<DictionaryDTO> findAllByParentId(@Param("parentId") String parentId);

    /**
     * 变更父级
     */
    @Update("update sys_dictionary set parent_id = #{parentNew} where parent_id = #{parentOld}")
    int updateParent(@Param("parentOld") String parentOld, @Param("parentNew") String parentNew);

    /**
     * 根据父级编号查询
     */
    @Select("select * from sys_dictionary where " +
            "parent_id = (select id from sys_dictionary where code = #{code}) order by code")
    List<DictionaryDTO> findAllByParentCode(@Param("code") String code);
}
