package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.UsersMapper;
import com.bsm.v4.system.model.dto.security.RoleDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Users;
import com.bsm.v4.system.model.vo.security.RoleSearchVO;
import com.bsm.v4.system.model.vo.security.UsersSearchVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: UsersService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service
 * @Date 2023/8/15 14:46
 * @description:
 */
@Service
public class UsersService extends BasicService<Users> {

    @Autowired
    private UsersMapper usersMapper;

    /**
     * 条件查询
     */
    public List<UsersDTO> findAllByWhere(UsersSearchVO usersSearchVO) {
        return usersMapper.findAllByWhere(usersSearchVO);
    }


}
