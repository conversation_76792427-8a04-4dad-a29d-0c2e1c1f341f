package com.bsm.v4.domain.security.mapper.business.station;

import com.bsm.v4.system.model.dto.business.station.StationCountyNumDTO;
import com.bsm.v4.system.model.dto.business.station.StationDTO;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtStationBakMapper extends BasicMapper<RsbtStationBak> {

    @Select({"<script>",
            "select * from RSBT_STATION_BAK " ,
            "WHERE APP_GUID = #{appGuid} " +
                    "AND RSBT_STATION_BAK.GUID IN ",
            "<foreach collection='guids' item='guid' open='(' separator=',' close=')'>",
            "#{guid} ",
            "</foreach>",
            "</script>"})
    List<RsbtStationBak> findByGuids(@Param("guids") List<String> guids, @Param("appGuid") String appGuid);

    /**
     * 根据app_guid和IS_SHOW 查询数据
     *
     * @param appGuid  appGuid
     * @param dataType dataType
     * @param genNum   genNum
     * @return list
     */
    @Select("select * from RSBT_STATION_BAK WHERE APP_GUID = #{appGuid} AND IS_SHOW = '0' AND GEN_NUM = #{genNum} AND DATA_TYPE = #{dataType} ")
    List<RsbtStationBak> findByAppGuidAndIsShow(@Param("genNum") String genNum, @Param("dataType") String dataType, @Param("appGuid") String appGuid);

    /**
     * 根据app_guid和IS_SHOW 查询数据
     *
     * @param appGuid  appGuid
     * @return int
     */
    @Select("select count(*) from RSBT_STATION_BAK WHERE APP_GUID = #{appGuid} AND IS_SHOW = '0' ")
    int findUnCheckNumByAppGuidAndIsShow(@Param("appGuid") String appGuid);

    /**
     * 根据app_guid查询数据
     *
     * @param appGuid  appGuid
     * @return int
     */
    @Select("select count(*) from RSBT_STATION_BAK WHERE APP_GUID = #{appGuid} ")
    int findAllNumByAppGuidAndIsShow(@Param("appGuid") String appGuid);

    /**
     * 根据app_guid查询数据
     *
     * @param appGuid  appGuid
     * @return int
     */
    @Select("select * from RSBT_STATION_BAK WHERE APP_GUID = #{appGuid} and IS_SHOW = '3' ")
    List<RsbtStationBak> findAllByAppGuidAndIsShow(@Param("appGuid") String appGuid);

    /**
     * 根据app_guid查询数据
     *
     * @param appGuid  appGuid
     * @return int
     */
    @Select("select count(*) from RSBT_STATION_BAK WHERE APP_GUID = #{appGuid} and IS_SHOW != '0' ")
    int findCheckByAppGuidAndIsShow(@Param("appGuid") String appGuid);

    @Update({"<script>",
            "update RSBT_STATION_BAK set is_sync = '1' " ,
            "WHERE RSBT_STATION_BAK.APP_GUID = #{appGuid} " +
                    "AND RSBT_STATION_BAK.GUID IN ",
            "<foreach collection='guids' item='guid' open='(' separator=',' close=')'>",
            "#{guid} ",
            "</foreach>",
            "</script>"})
    int updateIsSync(@Param("guids") List<String> guids, @Param("appGuid") String appGuid);

    @Select("select * from (" +
            "select RSBT_STATION_BAK.*,rownum as rn from RSBT_STATION_BAK " +
            "where APP_GUID = #{appGuid} " +
            "and (IS_VALID = #{isValid,jdbcType=VARCHAR} or #{isValid,jdbcType=VARCHAR} is null) " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "AND (STAT_NAME LIKE concat(concat('%',#{statName,jdbcType=VARCHAR}),'%') OR #{statName,jdbcType=VARCHAR} is null) "+
            "AND (STAT_ADDR LIKE concat(concat('%',#{statAddr,jdbcType=VARCHAR}),'%') OR #{statAddr,jdbcType=VARCHAR} is null) "+
            "and IS_SYNC not in ('1','4') and IS_VALID != '4' " +
            "and is_show not in ('1','2','3') " +
            "and rownum <= #{endRows}) r where r.rn > #{startRows}")
    List<RsbtStationBak> findByPage(@Param("appGuid") String appGuid, @Param("isValid") String isValid, @Param("dataType") String dataType,
                                    @Param("genNum") String genNum, @Param("startRows") int startRows, @Param("endRows") int endRows,
                                    @Param("statName") String statName, @Param("statAddr") String statAddr);

    @Select("select count(*) from RSBT_STATION_BAK " +
            "where APP_GUID = #{appGuid} " +
            "and (IS_VALID = #{isValid,jdbcType=VARCHAR} or #{isValid,jdbcType=VARCHAR} is null) " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "AND (STAT_NAME LIKE concat(concat('%',#{statName,jdbcType=VARCHAR}),'%') OR #{statName,jdbcType=VARCHAR} is null) "+
            "AND (STAT_ADDR LIKE concat(concat('%',#{statAddr,jdbcType=VARCHAR}),'%') OR #{statAddr,jdbcType=VARCHAR} is null) "+
            "and IS_SYNC not in ('1','4') " +
            "and is_show not in ('1','2') ")
    int findCount(@Param("appGuid") String appGuid, @Param("isValid") String isValid, @Param("dataType") String dataType, @Param("genNum") String genNum,
                  @Param("statName") String statName, @Param("statAddr") String statAddr);

    @Select("select count(*) from RSBT_STATION_BAK where APP_GUID = #{appGuid} and IS_SHOW = '0' ")
    int findCountByAppGuid(@Param("appGuid") String appGuid);

    @Select("select * from RSBT_STATION_BAK WHERE app_guid = #{appGuid}")
    List<RsbtStationBak> findByAppGuid(@Param("appGuid") String appGuid);

    @Select("select * from RSBT_STATION_BAK WHERE app_guid = #{appGuid} and is_show in ('2','3') ")
    List<RsbtStationBak> findNotPassData(@Param("appGuid") String appGuid);

    @Select("select * from RSBT_STATION_BAK WHERE app_guid = #{appGuid} and st_c_code = #{stCCode} ")
    RsbtStationBak findByCondition(@Param("appGuid") String appGuid, @Param("stCCode") String stCCode);

    /**
     * 查询经纬度范围内的台站
     *
     * @param appGuid appGuid
     * @param highLat highLat
     * @param highLng highLng
     * @param lowLat  lowLat
     * @param lowLng  lowLng
     * @return json
     */
    @Select("select BAK.STATION_GUID guid, BAK.STAT_NAME stationName, BAK.ORG_TYPE orgType, BAK.STAT_LG longitude, BAK.STAT_LA latitude, BAK.GEN_NUM genNum, " +
            " BAK.ST_C_CODE stCCode, BAK.STAT_ADDR location from RSBT_STATION_BAK BAK left join RSBT_STATION on BAK.STATION_GUID = RSBT_STATION.GUID " +
            " where BAK.APP_GUID = #{appGuid} and BAK.STAT_LG > #{lowLng} AND BAK.STAT_LG < #{highLng} AND BAK.STAT_LA > #{lowLat} AND BAK.STAT_LA < #{highLat} ")
    List<StationDTO> findAllBtsInMap(@Param("lowLng") Double lowLng, @Param("lowLat") Double lowLat, @Param("highLng") Double highLng,
                                     @Param("highLat") Double highLat, @Param("appGuid") String appGuid);

    /**
     * 查询经纬度范围内的台站
     *
     * @param appGuid appGuid
     * @return json
     */
    @Select("select BAK.STATION_GUID guid, BAK.STAT_NAME stationName, BAK.ORG_TYPE orgType, BAK.STAT_LG longitude, BAK.STAT_LA latitude, BAK.GEN_NUM genNum, " +
            " BAK.ST_C_CODE stCCode, BAK.STAT_ADDR location from RSBT_STATION_BAK BAK left join RSBT_STATION on BAK.STATION_GUID = RSBT_STATION.GUID " +
            " where BAK.APP_GUID = #{appGuid} and BAK.STATION_GUID = #{guid} ")
    @Results({
            @Result(property = "guid",column = "GUID"),
            @Result(property = "sectionDTOList",column = "GUID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.station.RsbtStationMapper.getListVO"))
    })
    StationDTO getStationInfo(@Param("guid") String guid, @Param("appGuid") String appGuid);

    /**
     * 查询经纬度范围内的台站数量
     *
     * @param appGuid appGuid
     * @return json
     */
    @Select("select region.NAME county, count(bak.GUID) count, region.CODE countyCode from RSBT_STATION_BAK bak left join APPROVAL_TRANSPORT_JOB job " +
            "left join FSA_REGION region on job.REGION_CODE = region.CODE " +
            "on job.GUID = bak.APP_GUID where job.GUID = #{appGuid} and bak.IS_SHOW != '3' group by region.NAME, region.CODE")
    List<StationCountyNumDTO> countByBtsInMap(@Param("appGuid") String appGuid);
}
