package com.bsm.v4.domain.security.service.business.applytable;

import com.bsm.v4.domain.security.mapper.business.applytable.RsbtApplyMapper;
import com.bsm.v4.system.model.dto.business.applytable.ApplyTableDTO;
import com.bsm.v4.system.model.dto.business.applytable.RsbtApplyDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11AntFeedDataDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11CommonDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11SectorDataDTO;
import com.bsm.v4.system.model.dto.business.techtable.Table11StationDataDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApply;
import com.bsm.v4.system.model.vo.business.applytable.ApplyTableVO;
import com.caictframework.data.service.BasicService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * Created by dengsy on 2020-04-28.
 */
@Service
public class RsbtApplyService extends BasicService<RsbtApply> {

    @Autowired
    private RsbtApplyMapper bsmApplyTableMapper;

    public RsbtApply selectOneByOrgIdAndStationCount(String orgId, String netType, String stationCount) {
        return bsmApplyTableMapper.selectOneByOrgIdAndStationCount(orgId, netType, stationCount);
    }

    public RsbtApply findOneByCode(String appCode) {
        return bsmApplyTableMapper.findOneByCode(appCode);
    }

    public RsbtApply selectOneOrderByCode() {
        return bsmApplyTableMapper.selectOneOrderByCode();
    }

    /**
     * 条件查询总数
     */
    public int selectCountByWhere(RsbtApplyDTO bsmApplytableDTO) {
        return bsmApplyTableMapper.selectCountByWhere(bsmApplytableDTO);
    }

    /**
     * 分页条件查询
     */
    public List<RsbtApplyDTO> findAllByWhere(RsbtApplyDTO bsmApplytableDTO) {
        return bsmApplyTableMapper.findAllByWhere(bsmApplytableDTO);
    }

    public List<ApplyTableDTO> findDealDataList(ApplyTableVO vo) {
        return bsmApplyTableMapper.findDealDataList(vo);
    }

    public List<TransportJobBranchDTO> findApplyDetailList(String jobId) {
        return bsmApplyTableMapper.findApplyDetailList(jobId);
    }

    /**
     * 内网分页条件查询
     */
    public List<RsbtApplyDTO> findAllInByWhere(RsbtApplyDTO bsmApplytableDTO) {
        return bsmApplyTableMapper.findAllInByWhere(bsmApplytableDTO);
    }

    /**
     * 根据guid查询详情DTO
     */
    public RsbtApplyDTO findOneByGuid(String guid) {
        return bsmApplyTableMapper.findOneByGuid(guid);
    }

    public Table11CommonDTO findTechTable(RsbtApplyDTO applyDTO) {
        Table11CommonDTO table11CommonDTO = new Table11CommonDTO();
        RsbtApply apply = findOneByCode(applyDTO.getAppCode());

        Table11StationDataDTO station = bsmApplyTableMapper.findStation(applyDTO.getAppCode(), applyDTO.getTechNum());
        if (station != null) {
            if (apply != null) {
                station.setTechType(apply.getAppSubType());
            }
            table11CommonDTO.setStationData(station);
            table11CommonDTO.setStatAppType(station.getStatAppType());
            table11CommonDTO.setMemo(StringUtils.isNotEmpty(station.getMemo()) ? station.getMemo() : "");
            table11CommonDTO.setStatTdi(station.getStatTdi());

            List<Table11SectorDataDTO> sectorDatas = bsmApplyTableMapper.findSectors1(station.getStCCode(), station.getAppGuid());
            if (sectorDatas != null) {
                table11CommonDTO.setSectorDatas(sectorDatas);
                station.setStCSum(sectorDatas.size());
                for (Table11SectorDataDTO table11SectorDataDTO : sectorDatas) {
                    table11SectorDataDTO.setEquData(bsmApplyTableMapper.findEqu1(table11SectorDataDTO.getAtCCode(), station.getAppGuid()));
                }
            }
            List<Table11AntFeedDataDTO> antFeeds = bsmApplyTableMapper.findAntFeed1(station.getStCCode(), station.getAppGuid());
            if (antFeeds != null) {
                table11CommonDTO.setAntFeedDatas(antFeeds);
            }
        }
        return table11CommonDTO;
    }

    public List<RsbtApply> selectExistApplytable(Set<String> appSet) {
        return bsmApplyTableMapper.selectExistApplytable(appSet);
    }

    public int updateApplyStationCount() {
        return bsmApplyTableMapper.updateApplyStationCount();
    }

    public int queryApplyTableCount() {
        return bsmApplyTableMapper.queryApplyTableCount();
    }
}
