package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.DictionaryMapper;
import com.bsm.v4.system.model.dto.security.DictionaryDTO;
import com.bsm.v4.system.model.entity.security.Dictionary;
import com.bsm.v4.system.model.vo.security.DictionarySearchVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: DictionaryService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service.security
 * @Date 2023/8/16 16:02
 * @description:
 */
@Service
public class DictionaryService extends BasicService<Dictionary> {
    @Autowired
    private DictionaryMapper dictionaryMapper;

    /**
     * 根据code查询出数量
     */
    public int selectCountByCode(String code) {
        return dictionaryMapper.selectCountByCode(code);
    }


    /**
     * 条件查询
     */
    public List<DictionaryDTO> findAllByWhere(DictionarySearchVO dictionarySearchVO) {
        return dictionaryMapper.findAllByWhere(dictionarySearchVO);
    }

    /**
     * 分页查询全部
     */
    public List<DictionaryDTO> findAllDtoByWhere(DictionarySearchVO dictionarySearchVO) {
        return dictionaryMapper.findAllDtoByWhere(dictionarySearchVO);
    }

    /**
     * 查询所有数据字典（根据parentID)
     */
    public List<DictionaryDTO> findAllByParentId(String parentId) {
        return dictionaryMapper.findAllByParentId(parentId);
    }

    /**
     * 变更父级
     */
    public int updateParent(String parentOld, String parentNew) {
        return dictionaryMapper.updateParent(parentOld, parentNew);
    }

    /**
     * 根据父级编号查询
     */
    public List<DictionaryDTO> findAllByParentCode(String code) {
        return dictionaryMapper.findAllByParentCode(code);
    }
}
