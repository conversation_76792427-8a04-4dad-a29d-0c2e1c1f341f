package com.bsm.v4.domain.security.mapper.security;

import com.caictframework.data.mapper.BasicMapper;
import com.bsm.v4.system.model.dto.security.RoleDTO;
import com.bsm.v4.system.model.vo.security.RoleSearchVO;
import com.bsm.v4.system.model.entity.security.Role;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 */
@Repository
public interface RoleMapper extends BasicMapper<Role> {

    /**
     * 条件查询
     */
    @Select("select * from sys_role")
    List<RoleDTO> findAllByWhere(@Param("roleSearchDTO") RoleSearchVO roleSearchVO);

    /**
     * 查询单个角色（根据id）并附带出所属用户信息
     */
    @Select("select * from sys_role where id = #{roleId}")
    @Results({
            @Result(property = "id", column = "ID"),
            @Result(property = "usersDTOs", column = "ID", many = @Many(select = "com.bsm.v4.domain.security.mapper.security.UsersMapper.findAllUsersByRoleId")),
            @Result(property = "menuDTOs", column = "ID", many = @Many(select = "com.bsm.v4.domain.security.mapper.security.MenuMapper.findAllMenuByRoleId"))
    })
    RoleDTO findOneRoleDTO(@Param("roleId") String roleId);
}
