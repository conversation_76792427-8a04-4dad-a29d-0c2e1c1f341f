package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranchTemp;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBts;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportRawBtsMapper extends BasicMapper<TransportRawBts> {

    /**
     * 根据fileId删除
     */
    @Delete("delete from TRANSPORT_RAW_BTS where FILE_GUID = #{fileId}")
    int deleteAllByFileId(@Param("fileId") String fileId);

    /**
     * 根据jobId查询总数
     */
    @Select("select count(1) from TRANSPORT_RAW_BTS where JOB_GUID = #{jobId}")
    int selectCountByJobId(@Param("jobId") String jobId);

    /**
     * 根据jobId分页查询
     */
    @Select("select * from TRANSPORT_RAW_BTS " +
            "where JOB_GUID = #{jobId} and IS_VALID != 4 " +
            "order by GUID ")
    List<TransportRawBtsDTO> findAllPageByJobId(@Param("jobId") String jobId);

    /**
     * 根据Guid查询DTO
     */
    @Select("select * from TRANSPORT_RAW_BTS_DEAL_LOG where GUID = #{guid}")
    TransportRawBtsDTO findOneByGuid(@Param("guid") String guid);

    /**
     * 数据库基础数据反向生成
     */
    @Select("select (RSBT_STATION.STAT_NAME || '-' || RSBT_ANTFEED_T.AT_CSGN) as CELL_NAME," +
            "RSBT_ANTFEED_T.AT_CSGN as CELL_ID," +
            "RSBT_STATION.STAT_NAME as BTS_NAME," +
            "RSBT_STATION_T.ST_C_Code as BTS_ID," +
            "FSA_DICTIONARY.REMARK as TECH_TYPE," +
            "RSBT_STATION.STAT_ADDR as LOCATION," +
            "RSBT_STATION.STAT_AREA_CODE as COUNTY," +
            "RSBT_STATION.STAT_LG as LONGITUDE," +
            "RSBT_STATION.STAT_LA as LATITUDE," +
            "RSBT_FREQ.FREQ_EFB as SEND_START_FREQ," +
            "RSBT_FREQ.FREQ_EFE as SEND_END_FREQ," +
            "RSBT_FREQ.FREQ_RFE as ACC_START_FREQ," +
            "RSBT_FREQ.FREQ_RFB as ACC_END_FREQ," +
            "RSBT_EQU_T.ET_EQU_UPOW as MAX_EMISSIVE_POWER," +
            "RSBT_ANTFEED.ANT_HIGHT as HEIGHT," +
            "RSBT_EQU.EQU_MENU as DEVICE_FACTORY," +
            "RSBT_EQU.EQU_MODEL as DEVICE_MODEL," +
            "RSBT_EQU.EQU_CODE as MODEL_CODE," +
            "RSBT_ANTFEED.ANT_TYPE as ANTENNA_MODEL," +
            "RSBT_ANTFEED.ANT_MENU as ANTENNA_FACTORY," +
            "RSBT_ANTFEED.ANT_POLE as POLARIZATION_MODE," +
            "nvl(RSBT_ANTFEED.ANT_ANGLE,0) as ANTENNA_AZIMUTH," +
            "nvl(RSBT_ANTFEED.FEED_LOSE,0) as FEEDER_LOSS," +
            "nvl(RSBT_ANTFEED.ANT_GAIN,0) as ANTENNA_GAIN," +
            "nvl(RSBT_STATION.STAT_AT,0) as ALTITUDE " +
            "from RSBT_NET,RSBT_STATION,RSBT_STATION_T,RSBT_ANTFEED,RSBT_ANTFEED_T,RSBT_EQU,RSBT_EQU_T,RSBT_FREQ,RSBT_FREQ_T,FSA_DICTIONARY " +
            "where RSBT_STATION.NET_GUID = RSBT_NET.GUID and RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
            "and RSBT_STATION.GUID = RSBT_ANTFEED.STATION_GUID and RSBT_ANTFEED.GUID = RSBT_ANTFEED_T.GUID " +
            "and RSBT_EQU.GUID = RSBT_EQU_T.GUID and RSBT_EQU_T.ET_EQU_CCode = RSBT_ANTFEED_T.AT_CSGN and RSBT_EQU.STATION_GUID = RSBT_STATION.GUID " +
            "and RSBT_FREQ.GUID = RSBT_FREQ_T.GUID and RSBT_FREQ_T.FT_FREQ_CSGN = RSBT_ANTFEED_T.AT_CSGN and RSBT_FREQ.STATION_GUID = RSBT_STATION.GUID " +
            "and RSBT_NET.Net_Ts = FSA_DICTIONARY.Code " +
            "and RSBT_STATION.ORG_Code in (select ORG_Code from RSBT_ORG where ORG_NAME like concat(concat('%',#{orgName}),'%') ) " +
            "and FSA_DICTIONARY.REMARK = #{netTs}" +
            "and rownum <= #{rownum} ")
    List<TransportRawBtsDTO> setTransportRawBtsDTOs(@Param("orgName") String orgName, @Param("netTs") String netTs, @Param("rownum") int rownum);

    /**
     * 根据fileId查询总数
     */
    @Select("select count(1) from TRANSPORT_RAW_BTS where FILE_GUID = #{fileId}")
    int selectCountByFileId(@Param("fileId") String fileId);

    /**
     * 查询基站基础数据不一致的基站识别号
     */
    @Select("select BTS_ID from " +
            "(select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,ST_SERV_R from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid} and FILE_GUID = #{fileGuid} " +
            "and EXPAND_STATION = '1') " +
            "group by BTS_ID " +
            "having count(*) > 1")
    List<String> findAllBtsId(@Param("jobGuid") String jobGuid, @Param("fileGuid") String fileGuid);

    /**
     * 查询宏站中室内室外站基站识别号
     */
    @Select("select BTS_ID from " +
            "(select distinct BTS_ID,ATTRIBUTE_STATION from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid} and FILE_GUID = #{fileGuid} and IS_VALID != '4' and EXPAND_STATION = '1' ) " +
            "group by BTS_ID " +
            "having count(*) > 1")
    List<TransportRawBtsDTO> findAllBtsIdExpend(@Param("jobGuid") String jobGuid, @Param("fileGuid") String fileGuid);

    /**
     * 根据基站批量修改状态
     */
    @Update("update TRANSPORT_RAW_BTS set IS_VALID = #{isValid} " +
            "where BTS_ID in (" +
            "select BTS_ID from " +
            "(select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,ST_SERV_R from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid} and FILE_GUID = #{fileGuid} and IS_VALID != '4' and EXPAND_STATION in ('1','是')) " +
            "group by BTS_ID having count(*) > 1 )")
    int updateIsValid(@Param("jobGuid") String jobGuid, @Param("fileGuid") String fileGuid, @Param("isValid") long isValid);

    /**
     * 批量处理直放站，修改bts_id为cell_id
     */
//    @Update("update TRANSPORT_RAW_BTS t set t.BTS_ID = t.BTS_ID ||  '-(z' || rownum || ')' where exists( " +
//            "select * from (select BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE from TRANSPORT_RAW_BTS " +
//            "where JOB_GUID = #{jobGuid} and BTS_ID in(select BTS_ID from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid} group by BTS_ID having count(BTS_ID) > 1) " +
//            "group by BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE " +
//            "having count(BTS_NAME) > 1 or count(TECH_TYPE) > 1 or count(LOCATION) > 1 or count(COUNTY) > 1 or count(ALTITUDE) > 1 or count(LATITUDE) > 1 or count(LONGITUDE) > 1 " +
//            "order by BTS_ID ) a " +
//            "where t.BTS_ID = a.BTS_ID and t.BTS_NAME = a.BTS_NAME and t.TECH_TYPE = a.TECH_TYPE and t.LOCATION = a.LOCATION and t.COUNTY = a.COUNTY and t.ALTITUDE = a.ALTITUDE and t.LATITUDE = a.LATITUDE and t.LONGITUDE = a.LONGITUDE " +
//            ")")
    @Update("update TRANSPORT_RAW_BTS set BTS_ID = BTS_ID ||  '-(z' || rownum || ')' " +
            "where BTS_ID in (" +
            "select BTS_ID from " +
            "(select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid} and FILE_GUID = #{fileGuid} and IS_VALID != '4') " +
            "group by BTS_ID " +
            "having count(*) > 1" +
            ")")
    int updateBtsFromCell(@Param("jobGuid") String jobGuid, @Param("fileGuid") String fileGuid);

    /**
     * 识别新增（方法待优化）
     */
    @Update("update TRANSPORT_RAW_BTS t set t.DATA_TYPE = " +
            "(case when (select count(1) from ASYNC_RAW_BTS where t.BTS_ID = ASYNC_RAW_BTS.BTS_ID and t.CELL_ID = ASYNC_RAW_BTS.CELL_ID and t.TECH_TYPE = ASYNC_RAW_BTS.TECH_TYPE) = 0 then '1' " +
            "else '5' end) " +
            "where t.JOB_GUID = #{jobGuid} and t.REGION_CODE = #{regionCode} and t.IS_VALID != 4")
    int updateBtsDataTypeAdd(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode);

    /**
     * 识别变更和不变（方法待优化）
     */
    @Update("update TRANSPORT_RAW_BTS t set t.DATA_TYPE = " +
            "(case when (" +
            "select count(1) from ASYNC_RAW_BTS where t.CELL_NAME = ASYNC_RAW_BTS.CELL_NAME and t.CELL_ID = ASYNC_RAW_BTS.CELL_ID and t.BTS_NAME = ASYNC_RAW_BTS.BTS_NAME " +
            "and t.BTS_ID = ASYNC_RAW_BTS.BTS_ID and t.TECH_TYPE = ASYNC_RAW_BTS.TECH_TYPE and t.LOCATION = ASYNC_RAW_BTS.LOCATION and t.LONGITUDE = ASYNC_RAW_BTS.LONGITUDE " +
            "and t.LATITUDE = ASYNC_RAW_BTS.LATITUDE and t.SEND_START_FREQ = ASYNC_RAW_BTS.SEND_START_FREQ and t.SEND_END_FREQ = ASYNC_RAW_BTS.SEND_END_FREQ " +
            "and t.ACC_START_FREQ = ASYNC_RAW_BTS.ACC_START_FREQ and t.ACC_END_FREQ = ASYNC_RAW_BTS.ACC_END_FREQ and t.MAX_EMISSIVE_POWER = ASYNC_RAW_BTS.MAX_EMISSIVE_POWER " +
            "and t.HEIGHT = ASYNC_RAW_BTS.HEIGHT and t.COUNTY = ASYNC_RAW_BTS.COUNTY and nvl(t.VENDOR_NAME,'1') = nvl(ASYNC_RAW_BTS.VENDOR_NAME ,'1') and nvl(t.DEVICE_MODEL,'1') = nvl(ASYNC_RAW_BTS.DEVICE_MODEL ,'1') " +
            "and nvl(t.MODEL_CODE,'1') = nvl(ASYNC_RAW_BTS.MODEL_CODE ,'1') and t.ANTENNA_GAIN = ASYNC_RAW_BTS.ANTENNA_GAIN  and t.ANTENNA_MODEL = ASYNC_RAW_BTS.ANTENNA_MODEL " +
            "and nvl(t.ANTENNA_FACTORY,'1') = nvl(ASYNC_RAW_BTS.ANTENNA_FACTORY ,'1') and t.POLARIZATION_MODE = ASYNC_RAW_BTS.POLARIZATION_MODE and nvl(t.ANTENNA_AZIMUTH,'1') = nvl(ASYNC_RAW_BTS.ANTENNA_AZIMUTH ,'1') " +
            "and nvl(t.FEEDER_LOSS,'1') = nvl(ASYNC_RAW_BTS.FEEDER_LOSS ,'1') and t.ALTITUDE = ASYNC_RAW_BTS.ALTITUDE" +
            ") = 0 then '2' " +
            "else '4' end) " +
            "where t.JOB_GUID = #{jobGuid} and t.REGION_CODE = #{regionCode} and t.DATA_TYPE = '5' and t.IS_VALID != 4")
    int updateBtsDataTypeUpdate(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode);

    /**
     * 识别延续和不变（方法待优化）
     */
    @Update("update TRANSPORT_RAW_BTS t set t.DATA_TYPE = " +
            "(case when (select count(1) from ASYNC_RAW_BTS where (select sysdate from dual) >= (up_date - interval '3' month) " +
            "and t.CELL_ID = ASYNC_RAW_BTS.CELL_ID and t.BTS_ID = ASYNC_RAW_BTS.BTS_ID) = 0 then '4' " +
            "else '6' end) " +
            "where t.JOB_GUID = #{jobGuid} and t.REGION_CODE = #{regionCode} and t.DATA_TYPE = '4' and t.IS_VALID != 4")
    int updateBtsDataTypeContinue(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode);

    /**
     * 运营商增量提交新增数据时进行数据类型更改
     */
    @Update("update TRANSPORT_RAW_BTS t set t.DATA_TYPE = " +
            "(case when (" +
            "select count(1) from ASYNC_RAW_BTS where t.CELL_ID = ASYNC_RAW_BTS.CELL_ID and t.BTS_ID = ASYNC_RAW_BTS.BTS_ID) = 0 then '1' " +
            "else '4' end) " +
            "where t.JOB_GUID = #{jobGuid} and t.REGION_CODE = #{regionCode} and t.IS_VALID != 4")
    int updateBtsDataTypeSingleAdd(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode);

    /**
     * 运营商增量提交变更数据时进行数据类型更改
     */
    @Update("update TRANSPORT_RAW_BTS t set t.DATA_TYPE = " +
            "(case when (" +
            "select count(1) from ASYNC_RAW_BTS where t.CELL_ID = ASYNC_RAW_BTS.CELL_ID and t.BTS_ID = ASYNC_RAW_BTS.BTS_ID) = 1 then '2' " +
            "else '4' end) " +
            "where t.JOB_GUID = #{jobGuid} and t.REGION_CODE = #{regionCode} and t.IS_VALID != 4")
    int updateBtsDataTypeSingleUpdate(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode);

    /**
     * 运营商增量提交变更数据时进行数据类型注销
     */
    @Update("update TRANSPORT_RAW_BTS t set t.DATA_TYPE = " +
            "(case when (" +
            "select count(1) from ASYNC_RAW_BTS where t.CELL_ID = ASYNC_RAW_BTS.CELL_ID and t.BTS_ID = ASYNC_RAW_BTS.BTS_ID) = 1 then '3' " +
            "else '4' end) " +
            "where t.JOB_GUID = #{jobGuid} and t.REGION_CODE = #{regionCode} and t.IS_VALID != 4")
    int updateBtsDataTypeSingleDel(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode);


    /**
     * 根据Job删除
     */
    @Delete("delete from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid}")
    int deleteAllByJob(@Param("jobGuid") String jobGuid);

    /**
     * 根据cellid查询数据
     *
     * @param orgType orgType
     * @return list
     */
    @Select("select CELL_ID, LONGITUDE, LATITUDE, TECH_TYPE, LOCATION, COUNTY, ALTITUDE, BTS_ID,BTS_NAME from ASYNC_RAW_BTS where " +
            " BTS_ID LIKE '%_GZL%' and BTS_NAME like '%(直放站)' AND ORG_TYPE = #{orgType} ")
    List<AsyncRawBts> findLayuanByOrgType(@Param("orgType") String orgType);

    /**
     * 查询基站基础数据不一致的基站识别号
     *
     * @param jobGuid jobGuid
     * @return list
     */
    @Select("select BTS_ID from " +
            "(select distinct BTS_ID,TECH_TYPE from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid} and IS_VALID != 4 and EXPAND_STATION in ('1','直放站') ) " +
            "group by BTS_ID,TECH_TYPE ")
    List<TransportRawBtsDTO> findAllBtsId1(@Param("jobGuid") String jobGuid);

    /**
     * 根据btsids查询所有
     *
     * @param jobId  jobId
     * @param btsIds btsId
     * @return list
     */
    @Select("<script>" +
            " select * from TRANSPORT_RAW_BTS where 1 = 1 and JOB_GUID = #{jobId} and IS_VALID != 4 " +
            " <foreach item='item' collection='btsIds' separator='or' open='and (' close=')' index=''>" +
            " BTS_ID = #{item} " +
            "</foreach>" +
            " ORDER BY BTS_ID " +
            "</script>")
    List<TransportRawBts> findAllByBtsIds(@Param("btsIds") String[] btsIds, @Param("jobId") String jobId);


    @Select("select count(distinct BTS_ID) as STATION_COUNT,count(CELL_ID) as CELL_COUNT,REGION_CODE from " +
            "(select BTS_ID,CELL_ID,REGION_CODE " +
            "from TRANSPORT_RAW_BTS,sys_region REGION_HANDLE " +
            "where TRANSPORT_RAW_BTS.COUNTY = REGION_HANDLE.NAME and JOB_GUID = #{jobId}) " +
            "group by REGION_CODE")
    List<TransportJobBranchTemp> selectDistinctRegionByJobid(@Param("jobId") String jobId);
}
