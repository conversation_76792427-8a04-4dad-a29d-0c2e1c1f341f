package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtEquMapper;
import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtEquService extends BasicService<RsbtEqu> {

    @Autowired
    private RsbtEquMapper rsbtEquMapper;

    /**
     * 根据基站id和扇区id查询
     */
    public RsbtEqu findOneByStationSection(String stationGuid, String cellId) {
        return rsbtEquMapper.findOneByStationSection(stationGuid, cellId);
    }
}
