package com.bsm.v4.domain.security.mapper.business.station;

import com.bsm.v4.system.model.dto.business.station.EarthTableDTO;
import com.bsm.v4.system.model.entity.business.station.EarthTable;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Repository
public interface EarthTableMapper extends BasicMapper<EarthTable> {

    @Select("select * from FSA_EARTH_STATION " +
            "where TABLE_TYPE = #{earthTableDTO.tableType,jdbcType=VARCHAR} " +
            "and (FSA_EARTH_STATION.ID_CODE = #{earthTableDTO.idCode,jdbcType=VARCHAR} or #{earthTableDTO.idCode,jdbcType=VARCHAR} is null) ")
    List<EarthTableDTO> findEarthByPage(@Param("earthTableDTO") EarthTableDTO earthTableDTO);
}
