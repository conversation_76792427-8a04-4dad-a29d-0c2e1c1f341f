package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.RoleUsersMapper;
import com.bsm.v4.system.model.entity.security.RoleUsers;
import com.caictframework.data.service.BasicService;
import com.caictframework.data.util.SnowflakeManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Title: RoleUsersService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service.security
 * @Date 2023/8/16 11:29
 * @description:
 */
@Service
public class RoleUsersService extends BasicService<RoleUsers> {
    @Autowired
    private RoleUsersMapper roleUsersMapper;
    @Autowired
    private SnowflakeManager snowflakeManager;

    /**
     * 绑定用户
     */
    @Transactional
    public int setUsersRole(String roleId, String[] usersIds) {//删除角色所有用户
        deleteByRoleId(roleId);
        for (String usersId : usersIds) {
            RoleUsers roleUsers = new RoleUsers();
            roleUsers.setUsersId(usersId);

            List<RoleUsers> roleUsersList = findAllByWhere(roleUsers);
            if (roleUsersList != null && roleUsersList.size() != 0) {
                roleUsers.setId(roleUsersList.get(0).getId());
                roleUsers.setRoleId(roleId);
                update(roleUsers);
            } else {
                Long id = snowflakeManager.nextValue();
                roleUsers.setId(id.toString());
                roleUsers.setRoleId(roleId);
                insert(roleUsers);
            }
        }
        return 1;
    }

    /**
     * 根据role_id删除role_users
     */
    public int deleteByRoleId(String roleId) {
        return roleUsersMapper.deleteByRoleId(roleId);
    }

    /**
     * 根据users_id删除role_users
     */
    public int deleteByUsersId(String usersId) {
        return roleUsersMapper.deleteByUsersId(usersId);
    }
}
