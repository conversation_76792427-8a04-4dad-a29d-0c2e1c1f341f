package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.*;
import com.bsm.v4.system.model.dto.business.transfer_in.TransportJobInDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportJobMapper extends BasicMapper<TransportJob> {

    /**
     * 根据用户类型、状态和流程查询数量
     */
    @Select({"<script>",
            "select count(*) from TRANSPORT_JOB  ",
            "where IS_COMPARE != #{isCompare} ",
            "and USER_GUID = #{userId} ",
            "and IS_COMPARE in ",
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>",
            "#{compare}",
            "</foreach>",
            "</script>"})
    int selectCountByTypeStateCompare(@Param("userId") String userId, @Param("isCompare") String isCompare, @Param("compares") List<String> compares);

    /**
     * 根据用户类型、任务名称查询
     */
    @Select("select GUID jobId,JOB_STATE jobStatus from TRANSPORT_JOB where USER_GUID in(select ID from sys_users where TYPE = #{userType}) and JOB_NAME = #{jobName}")
    TransportJobDTO findOneByJobName(@Param("userType") String userType, @Param("jobName") String jobName);

    /**
     * 根据用户类型、任务ID查询
     */
    @Select("select GUID jobId,JOB_STATE jobStatus from TRANSPORT_JOB where USER_GUID in(select ID from sys_users where TYPE = #{userType,jdbcType=VARCHAR}) and GUID = #{jobId,jdbcType=VARCHAR}")
    TransportJobDTO findOneByJobId(@Param("userType") String userType, @Param("jobId") String jobId);

    /**
     * 根据userType和任务流程状态查询不是同一个Job的任务
     */
    @Select("select * from TRANSPORT_JOB where USER_GUID in (select ID from sys_users where TYPE = #{userType}) and IS_COMPARE = #{isCompare} and GUID != #{jobId}")
    TransportJobDTO findOneByUserTypeAndCompareNotJobId(@Param("userType") String userType, @Param("isCompare") String isCompare, @Param("jobId") String jobId);

    /**
     * 条件查询总数
     */
    @Select("select count(*) from TRANSPORT_JOB " +
            "where USER_GUID = #{userId,jdbcType=VARCHAR} " +
            "and (IS_COMPARE = #{transportJobDTO.isCompare,jdbcType=VARCHAR} or #{transportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (JOB_NAME like concat(concat('%',#{transportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (GMT_CREATE >= #{transportJobDTO.jobDateStart,jdbcType=VARCHAR} or #{transportJobDTO.jobDateStart,jdbcType=VARCHAR} is null) " +
            "and (GMT_CREATE <= #{transportJobDTO.jobDateEnd,jdbcType=VARCHAR} or #{transportJobDTO.jobDateEnd,jdbcType=VARCHAR} is null) ")
    int selectAllCount(@Param("transportJobDTO") TransportJobDTO transportJobDTO, @Param("userId") String userId);

    /**
     * 条件分页查询
     */
    @Select("select TRANSPORT_JOB.*,TRANSPORT_JOB.IS_COMPARE AS jobStatus,sys_users.TYPE AS USERTYPE from TRANSPORT_JOB,sys_users " +
            "where TRANSPORT_JOB.USER_GUID = sys_users.ID " +
            "and (TRANSPORT_JOB.USER_GUID = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null)" +
            "and (sys_users.TYPE = #{transportJobDTO.userType,jdbcType=VARCHAR} or #{transportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_STATE = #{transportJobDTO.jobState,jdbcType=VARCHAR} or #{transportJobDTO.jobState,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.IS_COMPARE = #{transportJobDTO.isCompare,jdbcType=VARCHAR} or #{transportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{transportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE >= #{transportJobDTO.jobDateStart,jdbcType=DATE} or #{transportJobDTO.jobDateStart,jdbcType=DATE} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE <= #{transportJobDTO.jobDateEnd,jdbcType=DATE} or #{transportJobDTO.jobDateEnd,jdbcType=DATE} is null) " +
            "order by TRANSPORT_JOB.GMT_CREATE DESC")
    List<TransportJobDTO> findAllPage(@Param("transportJobDTO") TransportJobDTO transportJobDTO, @Param("userId") String userId);

    /**
     * 根据事件名称查询
     */
    @Select("select * from TRANSPORT_JOB where (JOB_NAME like concat(concat('%',#{jobName,jdbcType=VARCHAR}),'%') or #{jobName,jdbcType=VARCHAR} is null) " +
            "and JOB_STATE = #{jobState} and USER_GUID = #{userId}")
    List<TransportJobDTO> findAllLikeByJobNameAndJobState(@Param("jobName") String jobName, @Param("jobState") Long jobState, @Param("userId") String userId);

    /**
     * 查询任务文件异常总数
     */
    @Select("select count(*) from TRANSPORT_JOB,TRANSPORT_FILE " +
            "where TRANSPORT_JOB.GUID = TRANSPORT_FILE.JOB_GUID " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{jobName,jdbcType=VARCHAR}),'%') or #{jobName,jdbcType=VARCHAR} is null)" +
            "and TRANSPORT_JOB.USER_GUID = #{userId} and TRANSPORT_FILE.FILE_STATE = #{fileState} " +
            "order by TRANSPORT_JOB.GMT_CREATE desc ")
    int selectCountTransportJobVOLogCsv(@Param("jobName") String jobName, @Param("userId") String userId, @Param("fileState") Long fileState);

    /**
     * 分页查询任务文件异常列表
     */
    @Select("select TRANSPORT_JOB.*,TRANSPORT_FILE.GUID as fileGuid,TRANSPORT_FILE.FILE_LOCAL_NAME as fileName,TRANSPORT_FILE.FILE_STATE as fileState " +
            "from TRANSPORT_JOB,TRANSPORT_FILE " +
            "where TRANSPORT_JOB.GUID = TRANSPORT_FILE.JOB_GUID " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{jobName,jdbcType=VARCHAR}),'%') or #{jobName,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_JOB.USER_GUID = #{userId} and TRANSPORT_FILE.FILE_STATE = #{fileState} " +
            "order by TRANSPORT_JOB.GMT_CREATE desc ")
    List<TransportJobDTO> selectTransportJobVOLogCsv(@Param("jobName") String jobName, @Param("userId") String userId, @Param("fileState") Long fileState);

    /**
     * 根据业务流程状态查询总数
     */
    @Select("select count(*) from TRANSPORT_JOB where IS_COMPARE = #{isCompare}")
    int selectCountByIsCompareOrderGmtModified(@Param("isCompare") String isCompare);

    /**
     * 根据业务流程状态分页查询
     */
    @Select("select * from TRANSPORT_JOB " +
            "where IS_COMPARE = #{isCompare} " +
            "order by TRANSPORT_JOB.GMT_MODIFIED ")
    List<TransportJobDTO> findAllPageByIsCompareOrderGmtModified(@Param("isCompare") String isCompare);

    /**
     * 查询详情
     */
    @Select("select * from TRANSPORT_JOB where GUID = #{jobGuid}")
    @Results({
            @Result(property = "guid", column = "GUID"),
            @Result(property = "transportFileDTOList", column = "GUID", many = @Many(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper.findOperatorsAllByJobGuid")),
            @Result(property = "transportFileAttachedDTOList", column = "GUID", many = @Many(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper.findEnclosureAllByJobGuid"))
    })
    TransportJobDTO findOneByGuid(@Param("jobGuid") String jobGuid);

    @Select("select * from TRANSPORT_JOB WHERE IS_COMPARE != '10'")
    List<TransportJob> findJob();


    /**
     * 条件分页查询
     */
    @Select("select TRANSPORT_JOB.*,sys_users.TYPE AS USERTYPE from TRANSPORT_JOB,sys_users " +
            "where TRANSPORT_JOB.USER_GUID = sys_users.ID " +
            "and (TRANSPORT_JOB.IS_COMPARE = #{transportJobInDTO.isCompare,jdbcType=VARCHAR} or #{transportJobInDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{transportJobInDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobInDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE >= #{transportJobInDTO.appDateStart,jdbcType=VARCHAR} or #{transportJobInDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE <= #{transportJobInDTO.appDateEnd,jdbcType=VARCHAR} or #{transportJobInDTO.appDateEnd,jdbcType=VARCHAR} is null) " +
            "and (sys_users.TYPE = #{transportJobInDTO.userType,jdbcType=VARCHAR} or #{transportJobInDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (sys_users.REGION_ID = #{usersDTO.regionId,jdbcType=VARCHAR} or #{usersDTO.regionId,jdbcType=VARCHAR} is null) " +
            "order by TRANSPORT_JOB.GMT_CREATE DESC")
    List<TransportJobInDTO> findInAllPage(@Param("transportJobInDTO") TransportJobInDTO transportJobInDTO, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 根据用户查询全部
     */
    @Select("select * from TRANSPORT_JOB where USER_GUID = #{userId} order by JOB_DATE desc")
    List<TransportJobDTO> findAllByUser(@Param("userId") String userId);

    /**
     * 根据用户查询全部
     */
    @Select("select TRANSPORT_RAW_BTS.* from TRANSPORT_JOB,TRANSPORT_RAW_BTS where TRANSPORT_JOB.GUID = TRANSPORT_RAW_BTS.JOB_GUID " +
            "and TRANSPORT_JOB.guid = #{guid} and TRANSPORT_RAW_BTS.REGION_CODE = #{regionCode} and TRANSPORT_RAW_BTS.DATA_TYPE != #{dataType}")
    List<TransportRawBtsDTO> findIncrease(@Param("guid") String guid, @Param("regionCode") String regionCode, @Param("dataType") String dataType);

    /**
     * 根据branch地区code查询
     *
     * @param areaCode areaCode
     * @return list
     */
    @Select("select distinct j.guid, j.JOB_NAME, j.GMT_CREATE, u.TYPE as userType, b.REGION_CODE, j.IS_COMPARE as jobStatus from TRANSPORT_JOB j left join TRANSPORT_JOB_BRANCH b " +
            "on b.JOB_GUID = j.GUID left join sys_users u on u.ID = j.USER_GUID where b.REGION_CODE = #{areaCode} order by j.GMT_CREATE desc ")
    List<TransportJobDTO> findJobPageByBranchCode(@Param("areaCode") String areaCode);

    @Update("update TRANSPORT_JOB set is_deleted = #{isDeal} where guid = #{jobId}")
    int updateIsDeleted(@Param("jobId") String jobId, @Param("isDeal") String isDeal);

    @Select("select * from ( " +
            "select * from TRANSPORT_JOB a,fsa_users b where a.user_guid = b.id  " +
            "            and a.is_compare = '10' order by a.GMT_CREATE ) where rownum = 1")
    TransportJob findByStatusOrderByDate();

    @Select("select apply_job_in.app_code from transport_job " +
            "left join approval_transport_job " +
            "on transport_job.guid = approval_transport_job.job_guid " +
            "left join apply_job_in on approval_transport_job.guid = apply_job_in.app_guid " +
            "where apply_job_in.is_compare = '2'")
    List<String> findAppCodeList(@Param("jobGuid") String jobGuid);

    /**
     * 当前用户所创建任务查询
     */
    @Select("<script>SELECT T.JOB_NAME jobName, " +
            "        T.GMT_CREATE gmtCreateStart, " +
            "        T.GUID jobId, " +
            "        F.FILE_NUM fileNum, " +
            "        C.FILE_NUM materialNum, " +
            "        T.IS_COMPARE isCompare " +
            "   FROM TRANSPORT_JOB T " +
            "   LEFT JOIN SYS_USERS U " +
            "     ON T.USER_GUID = U.ID " +
            "   LEFT JOIN (SELECT COUNT(1) FILE_NUM , MAX(F.JOB_GUID) JOB_GUID FROM TRANSPORT_FILE F WHERE F.FILE_TYPE != '0' GROUP BY F.JOB_GUID) F " +
            "     ON T.GUID = F.JOB_GUID " +
            "   LEFT JOIN (SELECT COUNT(1) FILE_NUM , MAX(F.JOB_GUID) JOB_GUID FROM TRANSPORT_FILE F WHERE F.FILE_TYPE = '0' GROUP BY F.JOB_GUID) C " +
            "     ON T.GUID = C.JOB_GUID " +
            "  WHERE 1 = 1 " +
            "   <if test='dto.userType != null and dto.userType != \"\"'>" +
            "       <if test=' dto.userType == \"operator\".toString() '> " +
            "          AND U.ID = #{dto.userId,jdbcType=VARCHAR}</if>" +
            "       <if test=' dto.userType == \"wuweiCity\".toString() '> " +
            "          AND U.ID IN (SELECT H.ID FROM SYS_USERS H JOIN SYS_USERS T ON H.REGION_ID = T.REGION_ID WHERE T.ID =  #{dto.userId,jdbcType=VARCHAR}) </if>" +
            "       <if test=' dto.userType == \"wuweiProvincial\".toString() || dto.userType == \"admin\".toString()'> " +
            "          AND 1 = 1</if>" +
            "   </if>"+
            "<if test='dto.jobName != null and dto.jobName != \"\"'>" +
            "     AND T.JOB_NAME LIKE #{dto.jobName,jdbcType=VARCHAR}||'%'</if>" +
            "<if test='dto.gmtCreateStart != null'>" +
            "    AND T.GMT_CREATE  &gt;= #{dto.gmtCreateStart,jdbcType=DATE}</if> " +
            "<if test='dto.gmtCreateEnd != null'>" +
            "    AND T.GMT_CREATE  &lt;= #{dto.gmtCreateEnd,jdbcType=DATE}</if> " +
            "<if test='dto.isCompare != null and dto.isCompare != \"\"'>" +
            "    AND T.IS_COMPARE = #{dto.isCompare,jdbcType=VARCHAR} </if> </script>")
    List<TransportJobDTO> getCurrentTaskList(@Param("dto") TransportJobVO dto);

    @Select("<script>" +
            "SELECT TRANSPORT_JOB.JOB_NAME jobName,TRANSPORT_JOB.GMT_CREATE gmtCreateStart,TRANSPORT_JOB.GUID jobId, (SELECT count(*) " +
            "            from TRANSPORT_JOB T left join TRANSPORT_FILE F " +
            "            on t.GUID = f.JOB_GUID  " +
            "where f.FILE_TYPE != '0' and t.GUID = TRANSPORT_JOB.GUID) fileNum, (SELECT count(*) " +
            "            from TRANSPORT_JOB T left join TRANSPORT_FILE F " +
            "            on t.GUID = f.JOB_GUID " +
            "where f.FILE_TYPE = '0' and t.GUID = TRANSPORT_JOB.GUID) materialNum,TRANSPORT_JOB.IS_COMPARE isCompare " +
            "            from TRANSPORT_JOB left join SYS_USERS U " +
            "on TRANSPORT_JOB.USER_GUID = U.ID " +
            "where 1=1 " +
            "AND TRANSPORT_JOB.data_type = #{dto.dataType,jdbcType=VARCHAR} " +
            "<if test='dto.userId != null and dto.userId != \"\"'>" +
            "     AND U.ID = #{dto.userId,jdbcType=VARCHAR}</if>" +
            "<if test='dto.jobName != null and dto.jobName != \"\"'>" +
            "     AND TRANSPORT_JOB.JOB_NAME LIKE concat(concat('%',#{dto.jobName}),'%')</if>" +
            "<if test='dto.gmtCreateStart != null'>" +
            "    AND TRANSPORT_JOB.GMT_CREATE  &gt;= #{dto.gmtCreateStart,jdbcType=DATE}</if> " +
            "<if test='dto.gmtCreateEnd != null'>" +
            "    AND TRANSPORT_JOB.GMT_CREATE  &lt;= #{dto.gmtCreateEnd,jdbcType=DATE}</if> " +
            "<if test='dto.isCompare != null and dto.isCompare != \"\"'>" +
            "    AND TRANSPORT_JOB.IS_COMPARE = #{dto.isCompare,jdbcType=VARCHAR} </if> " +
            "order by TRANSPORT_JOB.GMT_CREATE DESC" +
            "</script>")
    List<TransportJobDTO> getTaskList(@Param("dto") TransportJobVO dto);



    /**
     * 任务相关文件信息查询
     */
    @Select("<script>  SELECT F.FILE_LOCAL_NAME fileLocalName, F.GMT_CREATE gmtCreate, F.FILE_STATE fileState, F.GUID fileId, F.FILE_PATH filePath, F.JOB_GUID jobGuid " +
            "   FROM TRANSPORT_FILE F " +
            "   INNER JOIN TRANSPORT_JOB T " +
            "   ON T.GUID = F.JOB_GUID" +
            "   WHERE 1 = 1" +
            "   <if test='dto.guid != null and dto.guid != \"\"'>" +
            "    AND T.GUID = #{dto.guid,jdbcType=VARCHAR} </if>" +
            "   <if test='dto.queryFileType != null and dto.queryFileType != \"\"'>" +
            "       <if test=' dto.queryFileType == \"file\".toString() '> " +
            "          AND F.FILE_TYPE != '0'</if>" +
            "       <if test=' dto.queryFileType == \"material\".toString() '> " +
            "          AND F.FILE_TYPE = '0'</if>" +
            " </if>" +
            "</script>")
    List<TransportFileDTO> getTaskFileList(@Param("dto") TransportJobVO dto);

    /**
     * 选中任务详细信息查询
     */
    @Select("SELECT T.JOB_NAME jobName, T.JOB_STATE isCompare, T.GUID jobId " +
            "  FROM TRANSPORT_JOB T " +
            "  WHERE 1 = 1 " +
            "  AND  T.GUID =  #{dto.guid,jdbcType=VARCHAR}")
    TransportJobDTO getCurrentTaskOne(@Param("dto") TransportJobVO dto);

    /**
     * 删除任务
     */
    @Delete("DELETE FROM TRANSPORT_JOB T WHERE T.GUID =  #{dto.guid,jdbcType=VARCHAR}")
    void deleteTransportJob(@Param("dto") TransportJobVO dto);

    /**
     * 建立任务与文件之间关联关系
     */
    @Update("<script> UPDATE TRANSPORT_FILE T SET T.JOB_GUID =  " +
            "        #{jobId,jdbcType=VARCHAR} " +
            "    WHERE T.GUID IN " +
            "    <foreach collection=\"list\" item=\"dto\" open=\"(\" separator=\",\" close=\")\"> " +
            "        #{dto,jdbcType=VARCHAR} " +
            "    </foreach> </script> ")
    void associationSomeId(@Param("list") List<String> list, @Param("jobId") String jobId);

    /**
     * 获取任务删除关联关系的fileId
     */
    @Select("SELECT T.GUID  FROM TRANSPORT_FILE T WHERE T.JOB_GUID = #{dto.guid,jdbcType=VARCHAR}")
    List<String> getMovFileId(@Param("dto")TransportJobVO dto);

    /**
     * 获取编辑时需要删除关联关系的fileId
     */
    @Select("<script> SELECT T.GUID " +
            "  FROM TRANSPORT_FILE T " +
            " WHERE T.JOB_GUID =  #{jobId,jdbcType=VARCHAR} " +
            "   AND NOT EXISTS (SELECT 1 " +
            "          FROM TRANSPORT_FILE D " +
            "         WHERE D.GUID IN  " +
            "    <foreach collection=\"list\" item=\"dto\" open=\"(\" separator=\",\" close=\")\"> " +
            "        #{dto,jdbcType=VARCHAR}  </foreach> " +
            "           AND D.GUID = T.GUID)  </script>")
    List<String> getMovFileIdForUp(@Param("list")List<String> list, @Param("jobId") String jobId);

    /**
     * 更新任务名称
     */
    @Update("UPDATE TRANSPORT_JOB T SET T.JOB_NAME = #{dto.jobName,jdbcType=VARCHAR} WHERE T.GUID = #{dto.jobId,jdbcType=VARCHAR}")
    void upTransportJobName(@Param("dto")TransportJobVO dto);

    /**
     * 获取该job下所有fileId
     */
    @Select("SELECT T.GUID " +
            "  FROM TRANSPORT_FILE T " +
            "  LEFT JOIN TRANSPORT_JOB D " +
            "    ON D.GUID = T.JOB_GUID " +
            " WHERE T.JOB_GUID = #{dto.jobId,jdbcType=VARCHAR} " +
            "AND T.FILE_TYPE = '1'")
    List<String> getFileIdOfJob(@Param("dto")TransportJobDTO dto);

    /**
     * 登录账号下待比对任务查询
     */
    @Select("<script>SELECT J.JOB_NAME jobName, J.GUID  jobId, " +
            "       J.GMT_CREATE jobCreationTime, " +
            "       J.JOB_DATE compCreationTime, " +
            "       J.JOB_STATE jobState " +
            "FROM transport_job J  " +
            "where J.user_guid = #{dto.userId} " +
            "   <if test='dto.jobName != null and dto.jobName != \"\"'>" +
            "       AND J.JOB_NAME like concat(concat('%',#{dto.jobName,jdbcType=VARCHAR}),'%') </if> " +
            "   <if test='dto.gmtCreateStart != null and dto.gmtCreateStart != \"\"'>" +
            "       AND J.GMT_CREATE &lt;= #{dto.gmtCreateStart,jdbcType=VARCHAR} </if> " +
            "   <if test='dto.gmtCreateEnd != null and dto.gmtCreateEnd != \"\"'>" +
            "       AND J.GMT_CREATE &gt;= #{dto.gmtCreateEnd,jdbcType=VARCHAR} </if> " +
            "   <if test='dto.compCreateStart != null and dto.compCreateStart != \"\"'>" +
            "       AND J.JOB_DATE &lt;= #{dto.compCreateStart,jdbcType=VARCHAR} </if> " +
            "   <if test='dto.compCreateEnd != null and dto.compCreateEnd != \"\"'>" +
            "       AND J.JOB_DATE &gt;= #{dto.compCreateEnd,jdbcType=VARCHAR} </if> " +
            "   <if test='dto.jobStatus != null and dto.jobStatus != \"\"'>" +
            "       AND J.JOB_STATE = #{dto.jobStatus,jdbcType=VARCHAR} </if> " +
            "ORDER BY J.JOB_DATE DESC " +
            "</script>")
    List<TransportJobDTO> getComparisonJob(@Param("dto")TransportJobVO dto);

    /**
     * 任务下对应操作类型及数量
     */
    @Select("SELECT t.data_type dataType,t.job_guid jobId,COUNT(1) doDataNum FROM transport_raw_bts_deal_LOG t  " +
            "where t.JOB_GUID = #{dto.jobId}" +
            "GROUP BY T.DATA_TYPE,T.JOB_GUID  " +
            "union all  " +
            "SELECT '0' dataType,A.job_guid jobId,COUNT(1) doDataNum  FROM log_transport_job A  " +
            "where A.JOB_GUID = #{dto.jobId} " +
            "GROUP BY A.data_type ,A.job_guid")
    List<transportDataDetailDTO> getComparisonJobType(@Param("dto")TransportJobVO dto);

    /**
     * 待比任务各类结果下数据查询
     */
    @Select("<script> SELECT '0' dataType,E.GEN_NUM genNum, E.CELL_NAME cellName, E.CELL_ID cellId, E.BTS_NAME btsName, E.BTS_ID btsId, E.TECH_TYPE techType, E.LOCATION location, E.COUNTY county, " +
            "       E.LONGITUDE longitude, E.LATITUDE latitude, E.SEND_START_FREQ sendStartFreq, E.SEND_END_FREQ sendEndFreq, E.ACC_START_FREQ accStartFreq, E.ACC_END_FREQ accEndFreq, " +
            "       E.MAX_EMISSIVE_POWER maxEmissivePower, E.HEIGHT height, E.VENDOR_NAME deviceFactory, E.DEVICE_MODEL deviceModel, E.MODEL_CODE modelCode, E.ANTENNA_MODEL antennaModel, " +
            "       E.ANTENNA_FACTORY antennaFactory, E.POLARIZATION_MODE polarizationModel, E.ANTENNA_AZIMUTH antennaAzimuth, E.FEEDER_LOSS feederLoss, E.ANTENNA_GAIN antennaGain, " +
            "       E.ALTITUDE altitude, E.EXPAND_STATION expandStation, E.ST_SCENE stScene, E.TRF_DATE trfDate, E.TRF_USER , E.TRF_DATA  " +
            " FROM log_transport_job E WHERE E.JOB_GUID = #{dto.jobId,jdbcType=VARCHAR} " +
            "   <if test='dto.dataType != null and dto.dataType != \"\"'>" +
            "       <if test=' dto.dataType == \"0\".toString() '> " +
            "           AND 1 = 1 </if>" +
            "       <if test=' dto.dataType != \"0\".toString() '> " +
            "           AND 1 = 999 </if>" +
            "   </if> " +
            " UNION ALL " +
            " SELECT E.DATA_TYPE dataType,E.GEN_NUM genNum, E.CELL_NAME cellName, E.CELL_ID cellId, E.BTS_NAME btsName, E.BTS_ID btsId, E.TECH_TYPE techType, E.LOCATION location, E.COUNTY county, " +
            "       E.LONGITUDE longitude, E.LATITUDE latitude, E.SEND_START_FREQ sendStartFreq, E.SEND_END_FREQ sendEndFreq, E.ACC_START_FREQ accStartFreq, E.ACC_END_FREQ accEndFreq, " +
            "       E.MAX_EMISSIVE_POWER maxEmissivePower, E.HEIGHT height, E.VENDOR_NAME deviceFactory, E.DEVICE_MODEL deviceModel, E.MODEL_CODE modelCode, E.ANTENNA_MODEL antennaModel, " +
            "       E.ANTENNA_FACTORY antennaFactory, E.POLARIZATION_MODE polarizationModel, E.ANTENNA_AZIMUTH antennaAzimuth, E.FEEDER_LOSS feederLoss, E.ANTENNA_GAIN antennaGain, " +
            "       E.ALTITUDE altitude, E.EXPAND_STATION expandStation, E.ST_SCENE stScene, E.TRF_DATE trfDate, E.TRF_USER , E.TRF_DATA  " +
            " FROM transport_raw_bts_deal_LOG E WHERE E.JOB_GUID = #{dto.jobId,jdbcType=VARCHAR}" +
            "   <if test='dto.dataType != null and dto.dataType != \"\"'>" +
            "       <if test=' dto.dataType == \"0\".toString() '> " +
            "           AND 1 = 999 </if>" +
            "       <if test=' dto.dataType != \"0\".toString() '> " +
            "           AND E.DATA_TYPE = #{dto.dataType,jdbcType=VARCHAR} </if>" +
            "   </if> " +
            "       AND E.GEN_NUM = #{dto.genNum,jdbcType=VARCHAR} </script> " )
    List<LogTransportJobDTO> getComparisonData(@Param("dto")TransportJobVO dto);

    /**
     * 记录任务校验情况及数量
     */
    @Update("<script> UPDATE TRANSPORT_JOB T SET " +
            "   <if test='dto.checkSuccessNum != null and dto.checkSuccessNum != \"\"'>" +
            "      T.CHECK_SUCCESS_NUM = #{dto.checkSuccessNum}, </if> " +
            "   <if test='dto.checkFailedNum != null and dto.checkFailedNum != \"\"'>" +
            "      T.CHECK_FAILED_NUM = #{dto.checkFailedNum}, </if> " +
            "T.JOB_STATE = #{statePartSuccess} " +
            "WHERE T.GUID = #{dto.jobId}</script>")
    void upJobCheckStatus(@Param("statePartSuccess")long statePartSuccess,@Param("dto") TransportJobDTO dto);

    /**
     * 获取待比任务结果状态选项
     */
    @Select("SELECT DISTINCT T.JOB_STATE  queryCode, " +
            "                CASE " +
            "                  WHEN T.JOB_STATE = 0 THEN " +
            "                   '未比对' " +
            "                  WHEN T.JOB_STATE = 1 THEN " +
            "                   '全部通过' " +
            "                  WHEN T.JOB_STATE = 2 THEN " +
            "                   '部分通过' " +
            "                  WHEN T.JOB_STATE = 3 THEN " +
            "                   '全部未通过' " +
            "                END queryName " +
            "  FROM TRANSPORT_JOB T")
    List<LogTransportJobDTO> getTransportJobStateList();
}
