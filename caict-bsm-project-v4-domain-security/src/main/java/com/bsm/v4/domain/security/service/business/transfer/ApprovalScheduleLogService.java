package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.ApprovalScheduleLogMapper;
import com.bsm.v4.system.model.dto.business.station.SectionDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApprovalScheduleLogService extends BasicService<ApprovalScheduleLog> {

    @Autowired
    private ApprovalScheduleLogMapper mapper;

    /**
     * 根据appGuid查询条数
     *
     * @param appGuid appGuid
     * @return int
     */
    public int selectCountByAppGuid(String appGuid) {
        return mapper.selectCountByAppGuid(appGuid);
    }


    /**
     * 根据appGuid、数据类型查询总数
     */
    public int selectCountByAppGuidDateType(String appGuid, String dataType) {
        return mapper.selectCountByAppGuidDateType(appGuid, dataType);
    }

    /**
     * 根据appGuid、数据类型分页查询
     */
    public List<ApprovalScheduleLogDTO> findAllPageByAppGuidDateType(String appGuid, String dataType, String genNum) {
        return mapper.findAllPageByAppGuidDateType(appGuid, dataType, genNum);
    }

    public List<ApprovalScheduleLog> findByAppGuid(String appGuid) {
        return mapper.findByAppGuid(appGuid);
    }

    /**
     * 根据jobId删除数据
     */
    public int deleteByJobGuid(String jobGuid) {
        return mapper.deleteByJobGuid(jobGuid);
    }

    public List<SectionDTO> findSectionList(String appGuid, String stCCode) {
        return mapper.findSectionList(appGuid, stCCode);
    }
}
