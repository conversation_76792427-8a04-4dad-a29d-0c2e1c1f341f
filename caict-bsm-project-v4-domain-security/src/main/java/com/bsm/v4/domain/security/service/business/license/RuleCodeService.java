package com.bsm.v4.domain.security.service.business.license;

import com.bsm.v4.domain.security.mapper.business.license.RuleCodeMapper;
import com.bsm.v4.system.model.entity.business.license.RuleCode;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Service
public class RuleCodeService extends BasicService<RuleCode> {

    @Autowired
    private RuleCodeMapper ruleCodeMapper;
}
