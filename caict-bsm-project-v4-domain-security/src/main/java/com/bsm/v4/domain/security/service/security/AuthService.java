package com.bsm.v4.domain.security.service.security;

import com.alibaba.fastjson.JSONObject;
import com.bsm.v4.domain.security.mapper.security.LoginMapper;
import com.bsm.v4.system.model.dto.security.LoginDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Login;
import com.caictframework.data.service.BasicService;
import com.caictframework.utils.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Created by dengsy on 2019-10-24.
 */
@Service
public class AuthService extends BasicService<Login> {

    @Autowired
    private LoginMapper loginMapper;
    @Autowired
    private RedisService redisService;

    /**
     * 登录
     * 返回token和userId
     * */
    public Map<String, String> loginOn(LoginDTO loginDTO) {
        UsersDTO usersDTO = login(loginDTO.getLoginName(), loginDTO.getPassword());
        if (usersDTO != null) {
            usersDTO.setLastIp(loginDTO.getLastIp());
            usersDTO.setSrrcOrgCode(loginDTO.getSrrcOrgCode());
            usersDTO.setSrrcUserCode(loginDTO.getSrrcUserCode());
            usersDTO.setSrrcUserName(loginDTO.getSrrcUserName());
            return loginOn(usersDTO);
        }
        return null;
    }

    //登录处理
    protected Map<String,String> loginOn(UsersDTO usersDTO){
        Map<String,String> map = new HashMap<>();
        String token = getToken();
        String key = "Caict:"+token;
        map.put("token",token);
        map.put("roleType","");
        map.put("userType","");
        map.put("userId",usersDTO.getUserId());
        map.put("userName",usersDTO.getName());
        if (usersDTO.getRoleDTO() != null && usersDTO.getRoleDTO().getType() != null) map.put("roleType",usersDTO.getRoleDTO().getType());
        if (usersDTO.getType() != null) map.put("userType",usersDTO.getType());
        if (usersDTO.getOrgDTO() != null && usersDTO.getOrgDTO().getRegionDTO() != null) {
            map.put("regionId",usersDTO.getOrgDTO().getRegionDTO().getId());
            usersDTO.setRegionId(usersDTO.getOrgDTO().getRegionDTO().getId());
        }
        setLogin(key,JSONObject.toJSONString(usersDTO));
        return map;
    }

    /**
     * 登陆实现
     * */
    public UsersDTO login(String loginName, String password){
        return loginMapper.login(loginName,password);
    }
    /**
     * 生成token
     * 暂时使用uuid作为源token，后期替换auth
     * */
    public String getToken(){
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 使用token存储用户信息到redis，设置过期时间
     * */
    public void setLogin(String key,String jsonString){
        //redisService.setEx(key,60*30,jsonString);
        redisService.set(key,jsonString);
    }


    public void loginOff(String token) {
        String key = "Caict:"+token;
        if(redisService.get(key) != null){
            redisService.delete(key);
        }
    }

    /**
     * 根据token获取用户登陆信息
     * */
    public String getLoginUsersDTO(String token){
        String key = "Caict:"+token;
        return redisService.get(key);
    }


    /**
     * 根据登录账号修改密码
     * */
    public int updatePasswordByLogin(String loginName,String password){
        return loginMapper.updatePasswordByLogin(loginName,password);
    }

    /**
     * 根据userId查询
     * */
    public Login findOneByUserId(String userId){
        return loginMapper.findAllByUserId(userId);
    }
}
