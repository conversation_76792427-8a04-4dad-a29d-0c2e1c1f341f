package com.bsm.v4.domain.security.mapper.business.transferNew;

import com.bsm.v4.system.model.dto.business.transferNew.TransportFileNewDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportFileNew;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 传输文件表Mapper
 */
@Repository
public interface TransportFileNewMapper extends BasicMapper<TransportFileNew> {

    /**
     * 根据任务ID查询文件列表
     * @param jobId 任务ID
     * @return 文件列表
     */
    @Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE job_id = #{jobId} AND is_deleted = 0 ORDER BY created_at DESC")
    List<TransportFileNewDTO> findByJobId(@Param("jobId") Long jobId);

    /**
     * 根据任务ID和文件类型查询文件列表
     * @param jobId 任务ID
     * @param fileType 文件类型
     * @return 文件列表
     */
    @Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE job_id = #{jobId} AND file_type = #{fileType} AND is_deleted = 0 ORDER BY created_at DESC")
    List<TransportFileNewDTO> findByJobIdAndFileType(@Param("jobId") Long jobId, @Param("fileType") String fileType);

    /**
     * 根据文件名和任务ID查询文件数量（用于重复检查）
     * @param jobId 任务ID
     * @param fileName 文件名
     * @return 文件数量
     */
    @Select("SELECT COUNT(*) FROM TRANSPORT_FILE_NEW WHERE job_id = #{jobId} AND file_local_name = #{fileName} AND is_deleted = 0")
    int countByJobIdAndFileName(@Param("jobId") Long jobId, @Param("fileName") String fileName);

    /**
     * 根据文件MD5查询文件（用于重复检查）
     * @param fileMd5 文件MD5
     * @return 文件信息
     */
    @Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE file_md5 = #{fileMd5} AND is_deleted = 0 LIMIT 1")
    TransportFileNew findByMd5(@Param("fileMd5") String fileMd5);

    /**
     * 根据文件状态查询文件列表
     * @param fileState 文件状态
     * @return 文件列表
     */
    @Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE file_state = #{fileState} AND is_deleted = 0 ORDER BY created_at DESC")
    List<TransportFileNewDTO> findByFileState(@Param("fileState") Integer fileState);

    /**
     * 更新文件状态
     * @param id 文件ID
     * @param fileState 新状态
     * @return 更新行数
     */
    @Select("UPDATE TRANSPORT_FILE_NEW SET file_state = #{fileState}, updated_at = NOW() WHERE id = #{id}")
    int updateFileState(@Param("id") Long id, @Param("fileState") Integer fileState);

    /**
     * 软删除文件
     * @param id 文件ID
     * @return 更新行数
     */
    @Select("UPDATE TRANSPORT_FILE_NEW SET is_deleted = 1, updated_at = NOW() WHERE id = #{id}")
    int softDelete(@Param("id") Long id);

    /**
     * 根据任务ID统计各类型文件数量
     * @param jobId 任务ID
     * @return 统计结果
     */
    @Select("SELECT file_type, COUNT(*) as count FROM TRANSPORT_FILE_NEW WHERE job_id = #{jobId} AND is_deleted = 0 GROUP BY file_type")
    List<TransportFileNewDTO> countByJobIdGroupByFileType(@Param("jobId") Long jobId);

    /**
     * 根据扩展名查询文件列表
     * @param fileExtension 文件扩展名
     * @return 文件列表
     */
    @Select("SELECT * FROM TRANSPORT_FILE_NEW WHERE file_extension = #{fileExtension} AND is_deleted = 0 ORDER BY created_at DESC")
    List<TransportFileNewDTO> findByFileExtension(@Param("fileExtension") String fileExtension);

    /**
     * 分页查询文件列表（带条件）
     * @param dto 查询条件
     * @return 文件列表
     */
    @Select("<script>" +
            "SELECT f.*, j.operator_code as jobName " +
            "FROM TRANSPORT_FILE_NEW f " +
            "LEFT JOIN TRANSPORT_JOB_NEW j ON f.job_id = j.id " +
            "WHERE f.is_deleted = 0 " +
            "<if test='jobId != null'> AND f.job_id = #{jobId} </if>" +
            "<if test='fileType != null and fileType != \"\"'> AND f.file_type = #{fileType} </if>" +
            "<if test='fileState != null'> AND f.file_state = #{fileState} </if>" +
            "<if test='fileLocalName != null and fileLocalName != \"\"'> AND f.file_local_name LIKE CONCAT('%', #{fileLocalName}, '%') </if>" +
            "<if test='startDate != null'> AND f.created_at >= #{startDate} </if>" +
            "<if test='endDate != null'> AND f.created_at <= #{endDate} </if>" +
            "ORDER BY f.created_at DESC" +
            "</script>")
    List<TransportFileNewDTO> findPageWithConditions(TransportFileNewDTO dto);
}
