package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.TransportJobBranchTempMapper;
import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranchTemp;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description TransportJobBranchTempService
 * @date 2023/4/27 17:13
 */

@Service
public class TransportJobBranchTempService extends BasicService<TransportJobBranchTemp> {

    @Autowired
    private TransportJobBranchTempMapper transportJobBranchTempMapper;

//    public int insertBatch(List<TransportJobBranchTemp> transportJobBranchTemps){
//        return transportJobBranchTempMapper.insertBatch(transportJobBranchTemps);
//    }

    public List<TransportJobBranchTemp> findAllBranch(String jobGuid) {
        return transportJobBranchTempMapper.findAllBranch(jobGuid);
    }

    public List<TransportJobBranchTemp> findGroupByRegion() {
        return transportJobBranchTempMapper.findGroupByRegion();
    }

    public int updateIsCompareByJobGuidRegion(String jobGuid, String regionCode, String status) {
        return transportJobBranchTempMapper.updateIsCompareByJobGuidRegion(jobGuid, regionCode, status);
    }
}
