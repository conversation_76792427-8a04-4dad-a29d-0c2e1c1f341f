package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.UsersLogDTO;
import com.bsm.v4.system.model.entity.security.UsersLog;
import com.bsm.v4.system.model.vo.security.UsersLogSearchVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Title: UsersLogMapper
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.mapper.security
 * @Date 2023/8/17 11:49
 * @description:
 */
@Repository
public interface UsersLogMapper extends BasicMapper<UsersLog> {


    /**
     * 条件查询
     */
    @Select("select * from sys_users_log")
    List<UsersLogDTO> findAllByWhere(@Param("usersLogSearchDTO") UsersLogSearchVO usersLogSearchVO);
}
