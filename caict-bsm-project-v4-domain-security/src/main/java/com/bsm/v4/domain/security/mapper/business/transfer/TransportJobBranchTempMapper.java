package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.TransportJobBranchTemp;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description TransportJobBranchTempMapper
 * @date 2023/4/27 17:15
 */
@Repository
public interface TransportJobBranchTempMapper extends BasicMapper<TransportJobBranchTemp> {

    @Select("select TRANSPORT_JOB_BRANCH_TEMP.*,FSA_REGION.NAME as regionName from TRANSPORT_JOB_BRANCH_TEMP , FSA_REGION WHERE TRANSPORT_JOB_BRANCH.REGION_CODE = FSA_REGION.CODE " +
            " and TRANSPORT_JOB_BRANCH_TEMP.JOB_GUID = #{jobGuid} order by FSA_REGION.NAME ")
    List<TransportJobBranchTemp> findAllBranch(@Param("jobGuid") String jobGuid);

    @Select("select * " +
            "  from (select a.*, " +
            "               b.type as userType, " +
            "               row_number() over(partition by b.type,a.region_code order by a.GMT_CREATE) rn " +
            "          from TRANSPORT_JOB_BRANCH_TEMP a, fsa_users b " +
            "         where a.user_guid = b.id " +
            "           and a.is_compare = '5') c " +
            " where c.rn = 1")
    List<TransportJobBranchTemp> findGroupByRegion();


    @Update("update TRANSPORT_JOB_BRANCH_TEMP set is_compare = #{status} where job_guid = #{jobGuid} and region_code = #{regionCode}")
    int updateIsCompareByJobGuidRegion(@Param("jobGuid") String jobGuid, @Param("regionCode") String regionCode, @Param("status") String status);
}
