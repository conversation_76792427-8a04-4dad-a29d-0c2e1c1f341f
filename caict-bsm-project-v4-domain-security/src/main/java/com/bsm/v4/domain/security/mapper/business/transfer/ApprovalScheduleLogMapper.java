package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.station.SectionDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApprovalScheduleLogMapper extends BasicMapper<ApprovalScheduleLog> {

    /**
     * 根据appGuid查询条数
     *
     * @param appGuid appGuid
     * @return int
     */
    @Select("select count(1) from APPROVAL_SCHEDULE_LOG where APP_GUID = #{appGuid} ")
    int selectCountByAppGuid(@Param("appGuid") String appGuid);

    /**
     * 根据appGuid、数据类型查询总数
     */
    @Select("select count(*) from APPROVAL_SCHEDULE_LOG where APP_GUID = #{appGuid} " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null)")
    int selectCountByAppGuidDateType(@Param("appGuid") String appGuid, @Param("dataType") String dataType);

    /**
     * 根据appGuid、数据类型分页查询
     */
    @Select("select * from APPROVAL_SCHEDULE_LOG " +
            "where JOB_GUID = #{appGuid,jdbcType=VARCHAR} " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_SCHEDULE_LOG.GUID ")
    List<ApprovalScheduleLogDTO> findAllPageByAppGuidDateType(@Param("appGuid") String appGuid, @Param("dataType") String dataType, @Param("genNum") String genNum);


    /**
     * 根据guid查询详情
     */
    @Select("select * from APPROVAL_SCHEDULE_LOG where GUID = #{guid}")
    ApprovalScheduleLogDTO findOneByGuid(@Param("guid") String guid);


    @Select("select * from APPROVAL_SCHEDULE_LOG where APP_GUID = #{appGuid}")
    List<ApprovalScheduleLog> findByAppGuid(@Param("appGuid") String appGuid);

    /**
     * 根据jobId删除数据
     */
    @Delete("delete from APPROVAL_SCHEDULE_LOG where job_guid = #{jobGuid}")
    int deleteByJobGuid(@Param("jobGuid") String jobGuid);

    @Select("select CELL_ID SECTION_CODE, CELL_NAME SECTION_NAME, HEIGHT ANT_HIGHT, SEND_START_FREQ freqEfb, SEND_END_FREQ freqEfe, ACC_END_FREQ freqRfe, " +
            " ACC_START_FREQ freqRfb, MAX_EMISSIVE_POWER maxPower, MODEL_CODE equMenu, DEVICE_MODEL equAuth, POLARIZATION_MODE antPole, ANTENNA_MODEL antType, " +
            " ANTENNA_FACTORY antMenu, FEEDER_LOSS feedLose, ANTENNA_AZIMUTH antAngle,ANTENNA_GAIN antGain from APPROVAL_SCHEDULE_LOG where app_guid = #{appGuid} " +
            " and bts_id = #{stCCode} order by CELL_ID ")
    List<SectionDTO> findSectionList(@Param("appGuid") String appGuid, @Param("stCCode") String stCCode);
}
