package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.entity.security.RoleMenu;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Title: RoleMenuMapper
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.mapper.security
 * @Date 2023/8/16 11:47
 * @description:
 */
@Repository
public interface RoleMenuMapper extends BasicMapper<RoleMenu> {

    /**
     * 根据角色删除
     */
    @Delete("delete from sys_role_menu where role_id = #{roleId}")
    int deleteAllByRole(@Param("roleId") String roleId);

    /**
     * 根据菜单删除
     */
    @Delete("delete from sys_role_menu where menu_id = #{menuId}")
    int deleteAllByMenu(@Param("menuId") String menuId);
}
