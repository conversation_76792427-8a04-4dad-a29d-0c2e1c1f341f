package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDealLog;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportRawBtsDealLogMapper extends BasicMapper<TransportRawBtsDealLog> {

    /**
     * 查询任务不同状态的数量
     */
    @Select("select count(is_valid) isValidCount,IS_VALID from TRANSPORT_RAW_BTS_DEAL_LOG where job_guid = #{jobGuid} group by IS_VALID")
    List<TransportRawBtsDealLogDTO> selectCountByUserGroupValid(@Param("jobGuid") String jobGuid);

    /**
     * 查询任务日志详细
     */
    @Select("select TRANSPORT_RAW_BTS_DEAL_LOG.*,LOG_TRANSPORT_JOB.LOG_DETAIL,LOG_TRANSPORT_JOB.LOG_SHORT " +
            "from TRANSPORT_RAW_BTS_DEAL_LOG inner join LOG_TRANSPORT_JOB " +
            "on TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID = LOG_TRANSPORT_JOB.JOB_GUID and TRANSPORT_RAW_BTS_DEAL_LOG.BTS_ID = LOG_TRANSPORT_JOB.BTS_ID " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID = LOG_TRANSPORT_JOB.CELL_ID or LOG_TRANSPORT_JOB.CELL_ID is null) " +
            "where TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID = #{jobGuid} " +
            "order by TRANSPORT_RAW_BTS_DEAL_LOG.BTS_ID,TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID")
    List<TransportRawBtsDealLogDTO> findAllDetailByJob(@Param("jobGuid") String jobGuid);

    /**
     * 生成下载数据
     */
    @Insert("insert into TRANSPORT_RAW_BTS_DEAL_LOG(GUID,JOB_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,UPLOAD_DATE,IS_DOWNLOAD,USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,REGION_CODE,BTS_DATA_TYPE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE) " +
            "select GUID,JOB_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,'1',UPLOAD_DATE,'0',USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,REGION_CODE,BTS_DATA_TYPE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE " +
            "from TRANSPORT_RAW_BTS_DEAL where job_guid = #{jobGuid} ")
    int insertByTransportRawBts(@Param("jobGuid") String jobGuid);

    /**
     * 查询数据
     *
     * @param jobId jobId
     * @param btsId btsId
     * @return list
     */
    @Select("select * from TRANSPORT_RAW_BTS_DEAL_LOG where BTS_ID = #{btsId} AND JOB_GUID = #{jobId} ")
    List<TransportRawBtsDealLog> findByBtsIdJobGuid(@Param("btsId") String btsId, @Param("jobId") String jobId);

    /**
     * 根据btsId更新数据
     *
     * @param jobId  jobId
     * @param btsIds list
     */
    @Update("<script>update TRANSPORT_RAW_BTS_DEAL_LOG set BTS_DATA_TYPE = #{dataType} where JOB_GUID = #{jobId} " +
            "and REGION_CODE = #{regionCode} " +
            " and BTS_ID in " +
            "<foreach collection='btsIds' item='btsId' open='(' separator=',' close=')'>" +
            " #{btsId} " +
            "</foreach>" +
            "</script>")
    void updateByBtsIds(@Param("dataType") String dataType, @Param("jobId") String jobId, @Param("btsIds") List<String> btsIds, @Param("regionCode") String regionCode);

    /**
     * 根据job流程状态用户分页查询
     *
     * @param userId
     * @param transportRawBtsDealDTO
     * @return list
     */
    @Select("select a.* " +
            "from TRANSPORT_RAW_BTS_DEAL_LOG a,TRANSPORT_JOB b " +
            "where a.JOB_GUID = b.GUID " +
            "AND b.IS_COMPARE = '10' " +
            " AND (b.USER_GUID = #{userId,jdbcType=VARCHAR} OR #{userId,jdbcType=VARCHAR} is null)" +
            " AND (a.JOB_GUID = #{transportRawBtsDealDTO.guid,jdbcType=VARCHAR} OR #{transportRawBtsDealDTO.guid,jdbcType=VARCHAR} is null)" +
            " AND (a.CELL_ID LIKE concat(concat('%',#{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} is null)" +
            " AND (a.CELL_NAME LIKE concat(concat('%',#{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR} is null)" +
            " AND (a.TECH_TYPE = #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} is null)")
    List<TransportRawBtsDealDTO> findAllPageNew(String userId, TransportRawBtsDealDTO transportRawBtsDealDTO);

    @Select("select * from TRANSPORT_RAW_BTS_DEAL_LOG where JOB_GUID = #{vo.jobId} " +
            "and bts_data_type = #{vo.dataType} " +
            "and gen_num = #{vo.genNum}")
    List<TransportRawBtsDealDTO> findDealDataListR(@Param("vo") TransportJobVO vo);
}
