package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.RoleMenuMapper;
import com.bsm.v4.system.model.entity.security.RoleMenu;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Title: RoleMenuService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service.security
 * @Date 2023/8/16 11:47
 * @description:
 */
@Service
public class RoleMenuService extends BasicService<RoleMenu> {

    @Autowired
    private RoleMenuMapper roleMenuMapper;

    /**
     * 根据角色删除
     */
    public int deleteAllByRole(String roleId) {
        return roleMenuMapper.deleteAllByRole(roleId);
    }

    /**
     * 根据菜单删除
     */
    public int deleteAllByMenu(String menuId) {
        return roleMenuMapper.deleteAllByMenu(menuId);
    }
}
