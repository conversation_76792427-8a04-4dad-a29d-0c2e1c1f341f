package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.AsyncRawBtsMapper;
import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.es.EsAsyncRawBts;
import com.bsm.v4.system.model.vo.es.EsAsyncRawBtsVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class AsyncRawBtsService extends BasicService<AsyncRawBts> {

    @Autowired
    private AsyncRawBtsMapper asyncRawBtsMapper;

    public List<Integer> selectCountByOpType(String startDate, String endDate, String areaCode, String sql) {
        return asyncRawBtsMapper.selectCountByOpType(startDate, endDate, areaCode, sql);
    }

    /**
     * 根据扇区编号和用户查询
     */
    public AsyncRawBts selectOneByCellIdAndUserId(String cellId, String userId) {
        return asyncRawBtsMapper.selectOneByCellIdAndUserId(cellId, userId);
    }

    /**
     * 根据基站编号和扇区号拆线呢
     */
    public AsyncRawBts selectOneByBtsIdCellId(String btsId, String cellId, String techType) {
        return asyncRawBtsMapper.selectOneByBtsIdCellId(btsId, cellId, techType);
    }

//    /**
//     * 根据处理类型和用户查询全部
//     */
//    public List<AsyncRawBts> findAllByHandleAndUserId(String isHandle, String userId) {
//        AsyncRawBts asyncRawBts = new AsyncRawBts();
//        asyncRawBts.setIsHandle(isHandle);
//        asyncRawBts.setUserId(userId);
//        return asyncRawBtsMapper.findAll(asyncRawBts);
//    }


    /**
     * 根据btsIds 刪除
     */
    public void deleteByBtsIds(List<String> btsIds, String guid) {
        asyncRawBtsMapper.deleteByBtsIds(btsIds, guid);
    }

    /**
     * 根据扇区id查询一条记录
     */
    public AsyncRawBts getOneByCellId(String btsId, String cellId, String techType) {
        return asyncRawBtsMapper.getOneByCellId(btsId, cellId, techType);
    }

    /**
     * 根据userId修改处理状态
     */
    public int updateIsHandleByUserid(String isHandle, String userId) {
        return asyncRawBtsMapper.updateIsHandleByUserid(isHandle, userId);
    }

    /**
     * 根据运营商类型修改处理状态
     */
    public int updateIsHandleByOrgType(String isHandle, String orgType) {
        return asyncRawBtsMapper.updateIsHandleByOrgType(isHandle, orgType);
    }

    /**
     * 查询上传数据中没有的数据
     */
    public List<AsyncRawBts> findAllNotTransport(String jobGuid, String userId) {
        return asyncRawBtsMapper.findAllNotTransport(jobGuid, userId);
    }

    /**
     * 根据运营商、数据状态删除
     */
    public int deleteAllByUserDataType(String userId, String dataType) {
        return asyncRawBtsMapper.deleteAllByUserDataType(userId, dataType);
    }

    /**
     * 对比路测数据
     *
     * @param roadCellIds
     * @return
     */
    public List<AsyncRawBts> contrastWithRoadData(String[] roadCellIds) {
        return asyncRawBtsMapper.contrastWithRoadData(roadCellIds);
    }

    public int deleteByBtsIdCellCode(String btsId, String cellCode) {
        return asyncRawBtsMapper.deleteByBtsIdCellCode(btsId, cellCode);
    }

    /**
     * 获取执照到期时间
     */
    public Date getExpireDate(String statCode) {
        //获取此基站下频率最近的到期时间
        return asyncRawBtsMapper.findByUpdate(statCode);
    }

    /**
     * 根据btsIds 查询总表数据
     *
     * @param btsIds btsIds
     * @return list
     */
    public List<AsyncRawBts> findByBtsIds(List<String> btsIds) {
        //获取此基站下频率最近的到期时间
        return asyncRawBtsMapper.findByBtsIds(btsIds);
    }

    /**
     * 生成使用率评价excel
     *
     * @param vo vo
     * @return list
     */
    public List<EsAsyncRawBts> searchUsageEvaluation(EsAsyncRawBtsVO vo) {
        return asyncRawBtsMapper.searchUsageEvaluation(vo);
    }

    /**
     * 根据btsIds 查询总表数据
     *
     * @param btsId btsId
     * @return list
     */
    public List<AsyncRawBts> findByBtsId(String btsId) {
        return asyncRawBtsMapper.findByBtsId(btsId);
    }
}
