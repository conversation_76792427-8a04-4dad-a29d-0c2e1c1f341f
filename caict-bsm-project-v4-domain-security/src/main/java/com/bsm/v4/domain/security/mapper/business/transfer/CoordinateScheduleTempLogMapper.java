package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleTempLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTempLog;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface CoordinateScheduleTempLogMapper extends BasicMapper<CoordinateScheduleTempLog> {

    /**
     * 根据jobId、类型查询总数
     */
    @Select("select count(*) from COORDINATE_SCHEDULE_TEMP_LOG " +
            "where APP_GUID = #{appGuid} " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null)")
    int selectCountByJobIdDataType(@Param("appGuid") String appGuid, @Param("dataType") String dataType);

    /**
     * 根据jobId、类型分页查询
     */
    @Select("select COORDINATE_SCHEDULE_TEMP_LOG.* from COORDINATE_SCHEDULE_TEMP_LOG " +
            "where COORDINATE_SCHEDULE_TEMP_LOG.APP_GUID = #{dto.appGuid} " +
            "and (COORDINATE_SCHEDULE_TEMP_LOG.DATA_TYPE = #{dto.dataType,jdbcType=VARCHAR} or #{dto.dataType,jdbcType=VARCHAR} is null) " +
            "and (COORDINATE_SCHEDULE_TEMP_LOG.GEN_NUM = #{dto.genNum,jdbcType=VARCHAR} or #{dto.genNum,jdbcType=VARCHAR} is null) " +
            "order by COORDINATE_SCHEDULE_TEMP_LOG.JOB_GUID,COORDINATE_SCHEDULE_TEMP_LOG.CELL_ID ")
    List<CoordinateScheduleTempLogDTO> findAllPageByJobIdDataType(@Param("dto") CoordinateScheduleTempLogDTO dto);
}
