package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.RegionMapper;
import com.bsm.v4.system.model.dto.security.RegionDTO;
import com.bsm.v4.system.model.entity.security.Region;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: RegionService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service.security
 * @Date 2023/8/16 17:24
 * @description:
 */
@Service
public class RegionService extends BasicService<Region> {
    @Autowired
    private RegionMapper regionMapper;


    /**
     * 查询所有的RegionDTO
     */
    public List<RegionDTO> findAllDto() {
        return regionMapper.findAllDto();
    }

    /**
     * 根据code查询
     */
    public RegionDTO findOneByCode(String code) {
        return regionMapper.findOneByCode(code);
    }

    /**
     * 根据name查询
     */
    public RegionDTO findOneByName(String name) {
        return regionMapper.findOneByName(name);
    }

    /**
     * 根据name查询
     */
    public RegionDTO findParentOneByName(String name) {
        return regionMapper.findParentOneByName(name);
    }

    /**
     * 查询所有的Region（根据parentId)
     */
    public List<RegionDTO> findAllByParentId(String parentId) {
        return regionMapper.findAllByParentId(parentId);
    }

    /**
     * 根据id查询自身和下级的所有数据
     */
    public List<RegionDTO> findAllByIdOrParentId(String parentId) {
        return regionMapper.findAllByIdOrParentId(parentId);
    }

    /**
     * 按照市名称查询区
     */
    public List<RegionDTO> findAreaByProvince(String name) {
        return regionMapper.findAreaByProvince(name);
    }

    /**
     * 根据父级和数据级别查询
     */
    public List<RegionDTO> findAllByParentLevel(String parentId, int dataLevel) {
        return regionMapper.findAllByParentLevel(parentId, dataLevel);
    }

    /**
     * 根据组织查询所有区
     * */
    public List<RegionDTO> findAllByOrg(String orgCode){
        return regionMapper.findAllByOrg(orgCode);
    }
}
