package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.dto.business.station.StationScheduleDataDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportScheduleDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportScheduleShowDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportSchedule;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportScheduleMapper extends BasicMapper<TransportSchedule> {

    /**
     * 根据jobId、类型分页查询审核数据
     * */
//    @Select("select * from (" +
//            "select a.BTS_ID as btsId,a.BTS_NAME as btsName,a.TECH_TYPE as techType,a.LOCATION as location,a.COUNTY as county,a.ALTITUDE as altitude,a.LATITUDE as latitude,a.LONGITUDE as longitude,a.DATA_TYPE as dataType," +
//            "a.IS_HANDLE as isHandle,a.USER_GUID as userGuid,a.JOB_GUID as jobGuid,sys_guid() as stationGuid,ROWNUM rn  " +
//            "from (select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,DATA_TYPE,IS_HANDLE,USER_GUID,JOB_GUID from TRANSPORT_SCHEDULE where JOB_GUID = #{jobId} and DATA_TYPE = #{dataType}) a,dual " +
//            "where ROWNUM <= #{rowsEnd}) r where r.rn > #{rowsStart}")
//    List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataType(@Param("rowsStart")int rowsStart, @Param("rowsEnd")int rowsEnd, @Param("jobId")String jobId, @Param("dataType")String dataType);

    /**
     * 统计申请表基站数量和扇区数量
     */
//    @Select("select count(distinct BTS_ID) as STATION_COUNT,count(CELL_ID) as CELL_COUNT,CODE as REGION_CODE,BTS_DATA_TYPE as dataType,GEN_NUM,TECH_TYPE from " +
//            "(select BTS_ID,CELL_ID,BTS_DATA_TYPE,GEN_NUM,TECH_TYPE,(select CODE from SYS_REGION REGION_SHOW where REGION_SHOW.ID = REGION_HANDLE.PARENT_ID) as CODE " +
//            "from TRANSPORT_SCHEDULE,SYS_REGION REGION_HANDLE " +
//            "where TRANSPORT_SCHEDULE.COUNTY = REGION_HANDLE.NAME " +
//            "and JOB_GUID = #{jobId}) " +
//            "group by CODE,BTS_DATA_TYPE,GEN_NUM,TECH_TYPE")
    @Select("select count(distinct BTS_ID) as STATION_COUNT,count(CELL_ID) as CELL_COUNT,BTS_DATA_TYPE as dataType,GEN_NUM,TECH_TYPE from " +
            "                    TRANSPORT_SCHEDULE " +
            "                    where job_guid = #{jobId} " +
            "                    group by BTS_DATA_TYPE,GEN_NUM,TECH_TYPE")
    List<TransportJobBranchDTO> selectDistinctDataTypeAndGenNum(@Param("jobId") String jobId);

    /**
     * 根据jobGuid查询数量
     */
    @Select("select count(*) from (select count(BTS_ID) from TRANSPORT_SCHEDULE where JOB_GUID = #{jobGuid} group by BTS_ID) ")
    int selectCountByJobGuid(@Param("jobGuid") String jobGuid);

    /**
     * 根据扇区id，基站识别id查询
     */
    @Select("select * from TRANSPORT_SCHEDULE where CELL_ID = #{cellId} and BTS_ID = #{btsId} and TECH_TYPE = #{techType} and DATA_TYPE = #{dataType} ")
    TransportSchedule findOneByCellBtsUser(@Param("cellId") String cellId, @Param("btsId") String btsId, @Param("techType") String techType, @Param("dataType") String dataType);

    /**
     * 根据制式和类型查询
     */
    @Select("select * from TRANSPORT_SCHEDULE where TECH_TYPE = #{techType} and DATA_TYPE = #{dataType} ")
    List<TransportSchedule> findByTechTypeAndDataType(@Param("techType") String techType, @Param("dataType") String dataType);

    /**
     * 根据任务、用户、类型、制式删除
     */
    @Delete("delete TRANSPORT_SCHEDULE where JOB_GUID = {jobGuid} and USER_GUID = #{userGuid} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum}")
    int deleteByJobUserTypeGen(@Param("jobGuid") String jobGuid, @Param("userGuid") String userGuid, @Param("dataType") String dataType, @Param("genNum") String genNum);

    /**
     * 生成待办
     */
    @Insert("insert into TRANSPORT_SCHEDULE(GUID,JOB_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,REGION_CODE,BTS_DATA_TYPE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE) " +
            "select GUID,JOB_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,'0',USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION, " +
            "AT_RANG,AT_EANG,ST_SERV_R,REGION_CODE,BTS_DATA_TYPE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE " +
            "from TRANSPORT_RAW_BTS_DEAL " +
            "where job_guid = #{jobId} " +
            "and IS_VALID != 4 " +
            "and DATA_TYPE != '4'")
    int insertByTransportRawBts(@Param("jobId") String jobId);

    /**
     * 根据app删除全部
     */
    @Delete("delete TRANSPORT_SCHEDULE " +
            "where GUID in (select GUID from APPROVAL_RAW_BTS where APP_GUID = #{appGuid})")
    int deleteAllByAppGuid(@Param("appGuid") String appGuid);

    @Select("select count(*) from TRANSPORT_SCHEDULE " +
            "where job_guid = #{jobGuid} " +
            "and data_type != #{dataType}")
    int selectCountByJobGuidAndDataType(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType);

    /**
     * 查询同一个btsId下dataType不一致的基站ID和dataType
     *
     * @return list
     */
    @Select("SELECT BTS_ID FROM (select BTS_ID, DATA_TYPE from TRANSPORT_RAW_BTS_DEAL group by BTS_ID, DATA_TYPE) a group by a.BTS_ID having count(a.BTS_ID) > 1 ")
    List<TransportSchedule> selectDisAccordBtsIds();

    /**
     * 查询同一个btsId下dataType一致的基站ID
     *
     * @return list
     */
    @Select(" select BTS_ID, DATA_TYPE from TRANSPORT_RAW_BTS_DEAL where BTS_ID in (SELECT BTS_ID FROM ( " +
            " select BTS_ID, DATA_TYPE from TRANSPORT_RAW_BTS_DEAL group by BTS_ID, DATA_TYPE) a group by a.BTS_ID " +
            " having count(a.BTS_ID) = 1) group by BTS_ID, DATA_TYPE")
    List<TransportSchedule> selectAccordBtsIdsAndDataType();

    /**
     * 查询同一个btsId下dataType一致的基站ID
     *
     * @return list
     */
    @Select(" select CELL_ID, BTS_ID, DATA_TYPE from TRANSPORT_RAW_BTS_DEAL where CELL_ID in (SELECT CELL_ID FROM ( " +
            " select CELL_ID, BTS_ID, DATA_TYPE from TRANSPORT_RAW_BTS_DEAL group by CELL_ID, BTS_ID, DATA_TYPE) a group by a.CELL_ID " +
            " having count(a.CELL_ID) = 1) group by CELL_ID, BTS_ID, DATA_TYPE")
    List<TransportSchedule> selectAccordBtsIdCellIdAndDataType();

    /**
     * 根据btsId更新数据
     *
     * @param jobId  jobId
     * @param btsIds list
     */
    @Update("<script>update TRANSPORT_SCHEDULE set BTS_DATA_TYPE = #{dataType} where JOB_GUID = #{jobId} " +
            "and region_code = #{regionCode} " +
            " and BTS_ID in " +
            "<foreach collection='btsIds' item='btsId' open='(' separator=',' close=')'>" +
            " #{btsId} " +
            "</foreach>" +
            "</script>")
    void updateByBtsIds(@Param("dataType") String dataType, @Param("jobId") String jobId, @Param("btsIds") List<String> btsIds, @Param("regionCode") String regionCode);

    /**
     * 根据jobId删除
     */
    @Delete("delete TRANSPORT_SCHEDULE where JOB_GUID = #{jobId}")
    int deleteByJobId(@Param("jobId") String jobId);

    @Delete("delete TRANSPORT_SCHEDULE where JOB_GUID = #{jobId} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum}")
    int deleteByJobIdDataTypeGenNum(@Param("jobId") String jobId, @Param("dataType") String dataType, @Param("genNum") String genNum);

    /**
     * 根据流程类型查询代办数量
     */
    @Select({"<script>",
            "select TRANSPORT_JOB.GUID as jobGuid,FSA_USERS.TYPE,TRANSPORT_JOB.IS_COMPARE as compare, ",
            "(select count(*) from TRANSPORT_SCHEDULE,TRANSPORT_JOB_branch where TRANSPORT_SCHEDULE.data_type = '1' and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB_branch.job_GUID ",
            "and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB.GUID",
            "and TRANSPORT_JOB_BRANCH.Data_Type = TRANSPORT_SCHEDULE.data_type and TRANSPORT_JOB_BRANCH.gen_num = TRANSPORT_SCHEDULE.gen_num ",
            "and TRANSPORT_SCHEDULE.IS_HANDLE !=1 and TRANSPORT_JOB_branch.Is_Compare in ('3','4') ) as scheduleNew, ",
            "(select count(*) from TRANSPORT_SCHEDULE,TRANSPORT_JOB_branch where TRANSPORT_SCHEDULE.data_type = '2' and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB_branch.job_GUID ",
            "and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB.GUID",
            "and TRANSPORT_JOB_BRANCH.Data_Type = TRANSPORT_SCHEDULE.data_type and TRANSPORT_JOB_BRANCH.gen_num = TRANSPORT_SCHEDULE.gen_num ",
            "and TRANSPORT_SCHEDULE.IS_HANDLE !=1 and TRANSPORT_JOB_branch.Is_Compare in ('3','4') )  as scheduleUpdate, ",
            "(select count(*) from TRANSPORT_SCHEDULE,TRANSPORT_JOB_branch where TRANSPORT_SCHEDULE.data_type = '3' and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB_branch.job_GUID ",
            "and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB.GUID",
            "and TRANSPORT_JOB_BRANCH.Data_Type = TRANSPORT_SCHEDULE.data_type and TRANSPORT_JOB_BRANCH.gen_num = TRANSPORT_SCHEDULE.gen_num ",
            "and TRANSPORT_SCHEDULE.IS_HANDLE !=1 and TRANSPORT_JOB_branch.Is_Compare in ('3','4') )  as scheduleDelete ",
            "from TRANSPORT_JOB,FSA_USERS  ",
            "where TRANSPORT_JOB.USER_GUID = FSA_USERS.ID ",
            "and TRANSPORT_JOB.IS_COMPARE in ",
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>",
            "#{compare}",
            "</foreach>",
            "</script>"})
    List<TransportScheduleShowDTO> findAllTransportScheduleOld(@Param("compares") List<String> compares, @Param("isHandle") String isHandle);

    /**
     * 根据流程类型查询代办数量
     */
    @Select({"<script>" +
            "select TRANSPORT_JOB.GUID as jobGuid,FSA_USERS.TYPE,TRANSPORT_JOB.IS_COMPARE as compare,  " +
            "(select count(*) from ( " +
            " select count(BTS_ID), BTS_ID,TRANSPORT_SCHEDULE.job_guid from TRANSPORT_SCHEDULE,TRANSPORT_JOB_branch where TRANSPORT_SCHEDULE.BTS_DATA_TYPE = '1' " +
            " and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB_branch.job_GUID" +
            " and TRANSPORT_JOB_BRANCH.Data_Type = TRANSPORT_SCHEDULE.BTS_DATA_TYPE and TRANSPORT_JOB_BRANCH.gen_num = TRANSPORT_SCHEDULE.gen_num " +
            " and TRANSPORT_SCHEDULE.IS_HANDLE != 1 and TRANSPORT_JOB_branch.Is_Compare in ('3','4') group by BTS_ID,TRANSPORT_SCHEDULE.job_guid) a " +
            " where a.JOB_GUID = TRANSPORT_JOB.GUID   ) as scheduleNew, " +
            "(select count(*) from ( " +
            " select count(BTS_ID), BTS_ID,TRANSPORT_SCHEDULE.job_guid from TRANSPORT_SCHEDULE,TRANSPORT_JOB_branch where TRANSPORT_SCHEDULE.BTS_DATA_TYPE = '2' " +
            " and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB_branch.job_GUID  " +
            " and TRANSPORT_JOB_BRANCH.Data_Type = TRANSPORT_SCHEDULE.BTS_DATA_TYPE and TRANSPORT_JOB_BRANCH.gen_num = TRANSPORT_SCHEDULE.gen_num " +
            " and TRANSPORT_SCHEDULE.IS_HANDLE != 1 and TRANSPORT_JOB_branch.Is_Compare in ('3','4') group by BTS_ID,TRANSPORT_SCHEDULE.job_guid) a " +
            " where a.JOB_GUID = TRANSPORT_JOB.GUID) as scheduleUpdate, " +
            "(select count(*) from ( " +
            " select count(BTS_ID), BTS_ID,TRANSPORT_SCHEDULE.job_guid from TRANSPORT_SCHEDULE,TRANSPORT_JOB_branch where TRANSPORT_SCHEDULE.BTS_DATA_TYPE = '3' " +
            " and TRANSPORT_SCHEDULE.JOB_GUID = TRANSPORT_JOB_branch.job_GUID " +
            " and TRANSPORT_JOB_BRANCH.Data_Type = TRANSPORT_SCHEDULE.BTS_DATA_TYPE and TRANSPORT_JOB_BRANCH.gen_num = TRANSPORT_SCHEDULE.gen_num " +
            " and TRANSPORT_SCHEDULE.IS_HANDLE != 1 and TRANSPORT_JOB_branch.Is_Compare in ('3','4') group by BTS_ID,TRANSPORT_SCHEDULE.job_guid) a " +
            " where a.JOB_GUID = TRANSPORT_JOB.GUID) as scheduleDelete" +
            " from TRANSPORT_JOB,FSA_USERS where TRANSPORT_JOB.USER_GUID = FSA_USERS.ID and TRANSPORT_JOB.IS_COMPARE in " +
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>" +
            "#{compare}" +
            "</foreach>" +
            "</script>"})
    List<TransportScheduleShowDTO> findAllTransportSchedule(@Param("compares") List<String> compares, @Param("isHandle") String isHandle);

    /**
     * 根据jobId、类型查询总数
     */
    @Select("select count(*) from TRANSPORT_SCHEDULE " +
            "where (JOB_GUID = #{jobId,jdbcType=VARCHAR} or #{jobId,jdbcType=VARCHAR} is null) " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (IS_HANDLE = #{isHandle,jdbcType=VARCHAR} or #{isHandle,jdbcType=VARCHAR} is null)")
    int selectCountByJobIdDataType(@Param("jobId") String jobId, @Param("dataType") String dataType, @Param("isHandle") String isHandle);

    /**
     * 根据jobId、类型分页查询
     */
    @Select("select * from TRANSPORT_SCHEDULE " +
            "where (JOB_GUID = #{jobId,jdbcType=VARCHAR} or #{jobId,jdbcType=VARCHAR} is null) " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (IS_HANDLE = #{isHandle,jdbcType=VARCHAR} or #{isHandle,jdbcType=VARCHAR} is null) " +
            "order by JOB_GUID,CELL_ID")
    List<TransportScheduleDTO> findAllPageByJobIdDataType(@Param("jobId") String jobId, @Param("dataType") String dataType, @Param("isHandle") String isHandle);

    /**
     * 根据jobId、类型分页查询审核数据
     */
    @Select("select a.BTS_ID as btsId,a.BTS_NAME as btsName,a.TECH_TYPE as techType,a.LOCATION as location,a.COUNTY as county,a.ALTITUDE as altitude," +
            " a.LATITUDE as latitude,a.LONGITUDE as longitude,a.DATA_TYPE as dataType," +
            " a.IS_HANDLE as isHandle,a.USER_GUID as userGuid,a.JOB_GUID as jobGuid,sys_guid() as stationGuid,ROWNUM rn  " +
            " from (select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,DATA_TYPE,IS_HANDLE,USER_GUID,JOB_GUID " +
            " from TRANSPORT_SCHEDULE where JOB_GUID = #{jobId} and DATA_TYPE = #{dataType}) a,dual ")
    List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataType(@Param("jobId") String jobId, @Param("dataType") String dataType);

    /**
     * 根据jobGuid查询总数
     */
    @Select("select count(*) from TRANSPORT_SCHEDULE where JOB_GUID = #{jobGuid} and (IS_HANDLE = #{isHandle,jdbcType=VARCHAR} or #{isHandle,jdbcType=VARCHAR} is null)")
    int selectCountAllByJob(@Param("jobGuid") String jobGuid, @Param("isHandle") String isHandle);

    @Update("update TRANSPORT_SCHEDULE set is_confirm = '1' where job_guid = #{jobId} and data_type = #{dataType} and gen_num = #{genNum} ")
    void updateConfirm(@Param("jobId") String jobId, @Param("dataType") String dataType, @Param("genNum") String genNum);

}
