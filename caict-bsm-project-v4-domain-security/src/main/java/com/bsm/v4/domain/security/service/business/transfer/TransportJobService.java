package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.TransportJobMapper;
import com.bsm.v4.system.model.dto.business.transfer.*;
import com.bsm.v4.system.model.dto.business.transfer_in.TransportJobInDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportJob;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Service
public class TransportJobService extends BasicService<TransportJob> {

    @Autowired
    private TransportJobMapper transportJobMapper;

    /**
     * 根据用户类型、状态和流程查询数量
     */
    public int selectCountByTypeStateCompare(String userId, String isCompare, List<String> compares) {
        return transportJobMapper.selectCountByTypeStateCompare(userId, isCompare, compares);
    }

    /**
     * 根据用户类型、任务名称查询
     */
    public TransportJobDTO findOneByJobName(String userType, String jobName) {
        return transportJobMapper.findOneByJobName(userType, jobName);
    }

    /**
     * 根据用户类型、有无id查询
     */
    public TransportJobDTO findOneByJobId(String userType, String jobId) {
        return transportJobMapper.findOneByJobId(userType, jobId);
    }

    /**
     * 根据userType和任务流程状态查询不是同一个Job的任务
     */
    public TransportJobDTO findOneByUserTypeAndCompareNotJobId(String userType, String isCompare, String jobId) {
        return transportJobMapper.findOneByUserTypeAndCompareNotJobId(userType, isCompare, jobId);
    }

    /**
     * 条件查询总数
     */
    public int selectAllCount(TransportJobDTO transportJobDTO, String userId) {
        return transportJobMapper.selectAllCount(transportJobDTO, userId);
    }

    /**
     * 条件分页查询
     */
    public List<TransportJobDTO> findAllPage(TransportJobDTO transportJobDTO, String userId) {
        return transportJobMapper.findAllPage(transportJobDTO, userId);
    }

    /**
     * 根据事件名称查询
     */
    public List<TransportJobDTO> findAllLikeByJobNameAndJobState(String jobName, Long jobState, String userId) {
        return transportJobMapper.findAllLikeByJobNameAndJobState(jobName, jobState, userId);
    }

    /**
     * 查询任务文件异常总数
     */
    public int selectCountTransportJobVOLogCsv(String jobName, String userId, Long fileState) {
        return transportJobMapper.selectCountTransportJobVOLogCsv(jobName, userId, fileState);
    }

    /**
     * 分页查询任务文件异常列表
     */
    public List<TransportJobDTO> selectTransportJobVOLogCsv(String jobName, String userId, Long fileState) {
        return transportJobMapper.selectTransportJobVOLogCsv(jobName, userId, fileState);
    }

    /**
     * 根据业务流程状态查询总数
     */
    public int selectCountByIsCompareOrderGmtModified(String isCompare) {
        return transportJobMapper.selectCountByIsCompareOrderGmtModified(isCompare);
    }

    /**
     * 根据业务流程状态分页查询
     */
    public List<TransportJobDTO> findAllPageByIsCompareOrderGmtModified(String isCompare) {
        return transportJobMapper.findAllPageByIsCompareOrderGmtModified(isCompare);
    }

    /**
     * 查询详情
     */
    public TransportJob findOneByGuid(String jobGuid) {
        return transportJobMapper.findOneByGuid(jobGuid);
    }

    public List<TransportJob> findJob() {
        return transportJobMapper.findJob();
    }

    /**
     * 条件分页查询
     */
    public List<TransportJobInDTO> findInAllPage(TransportJobInDTO transportJobInDTO, UsersDTO usersDTO) {
        return transportJobMapper.findInAllPage(transportJobInDTO, usersDTO);
    }

    /**
     * 根据用户查询全部
     */
    public List<TransportJobDTO> findAllByUser(String userId) {
        return transportJobMapper.findAllByUser(userId);
    }

    /**
     * 根据用户查询全部
     */
    public List<TransportRawBtsDTO> findIncrease(String guid, String regionCode, String dataType) {
        return transportJobMapper.findIncrease(guid, regionCode, dataType);
    }

    /**
     * 根据branch地区code查询
     *
     * @param areaCode areaCode
     * @return list
     */
    public List<TransportJobDTO> findJobPageByBranchCode(String areaCode) {
        return transportJobMapper.findJobPageByBranchCode(areaCode);
    }

    public int updateIsDeleted(String jobId, String isDeal) {
        return transportJobMapper.updateIsDeleted(jobId, isDeal);
    }

    public TransportJob findByStatusOrderByDate() {
        return transportJobMapper.findByStatusOrderByDate();
    }

    public List<String> findAppCodeList(String jobGuid) {
        return transportJobMapper.findAppCodeList(jobGuid);
    }

    /**
     * 当前用户所创建任务查询
     */
    public List<TransportJobDTO> getCurrentTaskList(TransportJobVO dto) {
        return transportJobMapper.getCurrentTaskList(dto);
    }

    public List<TransportJobDTO> getTaskList(TransportJobVO dto){
        return transportJobMapper.getTaskList(dto);
    }

    /**
     * 任务相关文件信息查询
     */
    public List<TransportFileDTO> getTaskFileList(TransportJobVO dto) {
        return transportJobMapper.getTaskFileList(dto);
    }

    /**
     * 选中任务详细信息查询
     */
    public TransportJobDTO getCurrentTaskOne(TransportJobVO dto) {
        return transportJobMapper.getCurrentTaskOne(dto);
    }

    /**
     * 删除任务
     */
    public void deleteTransportJob(TransportJobVO dto) {
        transportJobMapper.deleteTransportJob(dto);
    }

    /**
     * 建立任务与文件之间关联关系
     */
    @Transactional
    public void associationSomeId(List<String> list ,String jobId) {
            //全部清空的情况
            if(list.isEmpty()) return;
            transportJobMapper.associationSomeId(list, jobId);
    }

    /**
     * 获取编辑时需要删除关联关系的fileId
     */
    public List<String> getMovFileIdForUp(List<String> list ,String jobId) {
        return transportJobMapper.getMovFileIdForUp(list, jobId);
    }

    /**
     * 获取任务删除关联关系的fileId
     */
    public List<String> getMovFileId(TransportJobVO dto) {
        return transportJobMapper.getMovFileId(dto);
    }

    /**
     * 更新任务名称
     */
    public void upTransportJobName(TransportJobVO dto) {
        transportJobMapper.upTransportJobName(dto);
    }

    /**
     * 获取该job下所有fileId
     */
    public List<String> getFileIdOfJob(TransportJobDTO dto) {
        return transportJobMapper.getFileIdOfJob(dto);
    }

    /**
     * 登录账号下待比对任务查询
     */
    public List<TransportJobDTO> getComparisonJob(TransportJobVO dto) {
        return transportJobMapper.getComparisonJob(dto);
    }

    /**
     * 任务下对应操作类型及数量
     */
    public List<transportDataDetailDTO> getComparisonJobType(TransportJobVO dto) {
        return transportJobMapper.getComparisonJobType(dto);
    }

    /**
     * 待比任务各类结果下数据查询
     */
    public List<LogTransportJobDTO> getComparisonData(TransportJobVO dto) {
        return transportJobMapper.getComparisonData(dto);
    }

    /**
     * 记录任务校验情况及数量
     */
    public void upJobCheckStatus(long statePartSuccess, TransportJobDTO dto) {
        transportJobMapper.upJobCheckStatus(statePartSuccess, dto);
    }

    /**
     * 获取待比任务结果状态选项
     */
    public List<LogTransportJobDTO> getTransportJobStateList() {
        return transportJobMapper.getTransportJobStateList();
    }
}
