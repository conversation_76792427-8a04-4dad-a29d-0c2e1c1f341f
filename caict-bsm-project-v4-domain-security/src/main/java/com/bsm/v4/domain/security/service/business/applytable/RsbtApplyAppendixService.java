package com.bsm.v4.domain.security.service.business.applytable;

import com.bsm.v4.domain.security.mapper.business.station.RsbtStationAppendixMapper;
import com.bsm.v4.system.model.entity.business.applytable.RsbtApplyAppendix;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Service
public class RsbtApplyAppendixService extends BasicService<RsbtApplyAppendix> {
    @Autowired
    private RsbtStationAppendixMapper stationAppendixMapper;
}
