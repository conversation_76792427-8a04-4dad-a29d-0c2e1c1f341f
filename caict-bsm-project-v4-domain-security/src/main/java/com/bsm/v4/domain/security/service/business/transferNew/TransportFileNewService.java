package com.bsm.v4.domain.security.service.business.transferNew;

import com.bsm.v4.domain.security.mapper.business.transferNew.TransportFileNewMapper;
import com.bsm.v4.system.model.dto.business.transferNew.TransportFileNewDTO;
import com.bsm.v4.system.model.entity.business.transferNew.TransportFileNew;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 传输文件表Service
 */
@Service
public class TransportFileNewService extends BasicService<TransportFileNew> {

    @Autowired
    private TransportFileNewMapper transportFileNewMapper;

    /**
     * 根据任务ID查询文件列表
     * @param jobId 任务ID
     * @return 文件列表
     */
    public List<TransportFileNewDTO> findByJobId(Long jobId) {
        return transportFileNewMapper.findByJobId(jobId);
    }

    /**
     * 根据任务ID和文件类型查询文件列表
     * @param jobId 任务ID
     * @param fileType 文件类型
     * @return 文件列表
     */
    public List<TransportFileNewDTO> findByJobIdAndFileType(Long jobId, String fileType) {
        return transportFileNewMapper.findByJobIdAndFileType(jobId, fileType);
    }

    /**
     * 根据文件名和任务ID查询文件数量（用于重复检查）
     * @param jobId 任务ID
     * @param fileName 文件名
     * @return 文件数量
     */
    public int countByJobIdAndFileName(Long jobId, String fileName) {
        return transportFileNewMapper.countByJobIdAndFileName(jobId, fileName);
    }

    /**
     * 根据文件MD5查询文件（用于重复检查）
     * @param fileMd5 文件MD5
     * @return 文件信息
     */
    public TransportFileNew findByMd5(String fileMd5) {
        return transportFileNewMapper.findByMd5(fileMd5);
    }

    /**
     * 根据文件状态查询文件列表
     * @param fileState 文件状态
     * @return 文件列表
     */
    public List<TransportFileNewDTO> findByFileState(Integer fileState) {
        return transportFileNewMapper.findByFileState(fileState);
    }

    /**
     * 更新文件状态
     * @param id 文件ID
     * @param fileState 新状态
     * @return 更新行数
     */
    public int updateFileState(Long id, Integer fileState) {
        return transportFileNewMapper.updateFileState(id, fileState);
    }

    /**
     * 软删除文件
     * @param id 文件ID
     * @return 更新行数
     */
    public int softDelete(Long id) {
        return transportFileNewMapper.softDelete(id);
    }

    /**
     * 根据任务ID统计各类型文件数量
     * @param jobId 任务ID
     * @return 统计结果
     */
    public List<TransportFileNewDTO> countByJobIdGroupByFileType(Long jobId) {
        return transportFileNewMapper.countByJobIdGroupByFileType(jobId);
    }

    /**
     * 根据扩展名查询文件列表
     * @param fileExtension 文件扩展名
     * @return 文件列表
     */
    public List<TransportFileNewDTO> findByFileExtension(String fileExtension) {
        return transportFileNewMapper.findByFileExtension(fileExtension);
    }

    /**
     * 分页查询文件列表（带条件）
     * @param dto 查询条件
     * @return 文件列表
     */
    public List<TransportFileNewDTO> findPageWithConditions(TransportFileNewDTO dto) {
        return transportFileNewMapper.findPageWithConditions(dto);
    }
}
