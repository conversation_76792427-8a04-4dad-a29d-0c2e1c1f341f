package com.bsm.v4.domain.security.mapper.business.station;

import com.bsm.v4.system.model.dto.business.station.*;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtStation;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Repository
public interface RsbtStationMapper extends BasicMapper<RsbtStation> {

    @Select("select distinct RSBT_STATION.ORG_CODE,RSBT_NET.NET_TS,RSBT_NET.ORG_GUID AS orgId from RSBT_STATION,RSBT_STATION_APPENDIX,RSBT_NET where RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID " +
            "AND RSBT_STATION.NET_GUID = RSBT_NET.GUID AND RSBT_STATION_APPENDIX.IS_APPLY = '0' ")
    @Results({
            @Result(property = "orgCode",column = "ORG_CODE"),
            @Result(property = "netType",column = "NET_TS"),
            @Result(property = "rsbtStations",column = "{orgCode=ORG_CODE,netType=NET_TS}",many = @Many(select = "com.caict.bsm.project.domain.business.repository.station.RsbtStationMapper.selectAllByOrg"))
    })
    List<StationDTO> selectAllNotApplyTable();

    //根据组织机构查询所有
    @Select("select * from RSBT_STATION,RSBT_STATION_T,RSBT_NET where RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
            "AND RSBT_STATION.NET_GUID = RSBT_NET.GUID " +
            " and RSBT_STATION.ORG_CODE = #{orgCode} and RSBT_NET.NET_TS = #{netType}")
    List<RsbtStation> selectAllByOrg(@Param("orgCode") String orgCode, @Param("netType") String netType);

    /**
     * 查询未生成执照的基站
     * */
    @Select("SELECT a.*,b.county,b.tech_type,b.org_type from RSBT_STATION a LEFT JOIN RSBT_STATION_APPENDIX b ON a.GUID= b.GUID LEFT JOIN RSBT_STATION_T c ON a.GUID = c.GUID " +
            "WHERE a.GUID not in(select STATION_GUID from RSBT_LICENSE) and b.IS_LICENSE = '0' order by c.ST_C_CODE")
    List<StationLicenseDTO> selectAllNotLicense();

    /**
     * 根据基站识别号查询
     * */
    @Select("select RSBT_STATION.* from RSBT_STATION LEFT JOIN RSBT_STATION_T ON RSBT_STATION_T.GUID = RSBT_STATION.GUID " +
            "LEFT JOIN RSBT_NET ON RSBT_STATION.NET_GUID = RSBT_NET.GUID " +
            "where RSBT_STATION_T.ST_C_CODE = #{bts} and RSBT_NET.NET_TS = #{techType} and RSBT_STATION.ORG_CODE = #{orgCode} ")
    RsbtStation querySattionByBts(@Param("bts") String bts, @Param("techType") String techType, @Param("orgCode") String orgCode);

    /**
     * 条件查询总数
     * */
    @Select("select count(distinct RSBT_STATION.STATION_GUID) from RSBT_STATION " +
            "LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION_APPENDIX.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN BSM_APPLYTABLE_STATION ON BSM_APPLYTABLE_STATION.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN RSBT_FREQ ON RSBT_FREQ.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_ORG.ORG_GUID = RSBT_STATION.ORG_GUID " +
            "where (RSBT_STATION_APPENDIX.IS_DELETED = #{stationDTO.isDeleted,jdbcType=VARCHAR} or #{stationDTO.isDeleted,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_LA >= #{stationDTO.minlat,jdbcType=VARCHAR} or #{stationDTO.minlat,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_LA <= #{stationDTO.maxlat,jdbcType=VARCHAR} or #{stationDTO.maxlat,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_LG >= #{stationDTO.minlng,jdbcType=VARCHAR} or #{stationDTO.minlng,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_LG >= #{stationDTO.maxlng,jdbcType=VARCHAR} or #{stationDTO.maxlng,jdbcType=VARCHAR} is null) " +
            "and (" +
//            "((RSBT_FREQ.FREQ_EFB <= #{stationDTO.freqEfb,jdbcType=VARCHAR} or #{stationDTO.freqEfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_EFE >= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null)) " +
//            "or ((RSBT_FREQ.FREQ_EFB <= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_EFE >= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null)) " +
//            "or " +
            "((RSBT_FREQ.FREQ_EFB >= #{stationDTO.freqEfb,jdbcType=VARCHAR} or #{stationDTO.freqEfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_EFE <= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null))" +
            ") " +
            "and (" +
//            "((RSBT_FREQ.FREQ_RFB <= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_RFE >= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null)) " +
//            "or ((RSBT_FREQ.FREQ_RFB <= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_RFE >= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null)) " +
//            "or " +
            "((RSBT_FREQ.FREQ_RFB >= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_RFE <= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null))" +
            ") " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{stationDTO.stationName,jdbcType=VARCHAR}),'%') or #{stationDTO.stationName,jdbcType=VARCHAR} is null)" +
            "and (RSBT_STATION.ST_C_CODE like concat(concat('%',#{stationDTO.stationCode,jdbcType=VARCHAR}),'%') or #{stationDTO.stationCode,jdbcType=VARCHAR} is null) "+
            "and (RSBT_STATION.GEN_NUM = #{stationDTO.genNum,jdbcType=VARCHAR} or #{stationDTO.genNum,jdbcType=VARCHAR} is null) "+
            "and (RSBT_STATION.STAT_STATUS = #{stationDTO.isLicense,jdbcType=VARCHAR} or #{stationDTO.isLicense,jdbcType=VARCHAR} is null) "+
            "and (RSBT_STATION.USER_GUID = #{stationDTO.userGuid,jdbcType=VARCHAR} or #{stationDTO.userGuid,jdbcType=VARCHAR} is null) "+
            "and (RSBT_ORG.ORG_NAME = #{stationDTO.orgName,jdbcType=VARCHAR} or #{stationDTO.orgName,jdbcType=VARCHAR} is null) ")
    int selectCountByWhere(@Param("stationDTO") StationDTO stationDTO);

    /**
     * 分页条件查询 yunyingshang
     *
     * @param stationDTO stationDTO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("SELECT DISTINCT " +
            "RSBT_STATION.GUID AS STATIONID," +
            "RSBT_STATION.STAT_NAME AS STATIONNAME," +
            "RSBT_STATION_T.ST_C_CODE AS STATIONCODE," +
            "RSBT_NET.NET_TS AS NETTYPE," +
            "RSBT_STATION.STAT_ADDR AS LOCATION," +
            "RSBT_STATION.STAT_LG AS LONGITUDE," +
            "RSBT_STATION.STAT_LA AS LATITUDE," +
            "RSBT_STATION_APPENDIX.IS_DELETED AS STATIONSTATE," +
            "RSBT_APPLY.GUID AS APPLYTABLEGUID," +
            "RSBT_STATION.STAT_STATUS AS ISLICENSE," +
            "RSBT_STATION_APPENDIX.EXPAND_STATION AS EXPANDSTATION," +
            "RSBT_STATION_APPENDIX.USER_GUID AS USERGUID," +
            "RSBT_STATION_APPENDIX.GEN_NUM AS GENNUM," +
            "RSBT_ORG_APPENDIX.TYPE AS orgType " +
            "FROM " +
            "RSBT_STATION " +
            "LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION_APPENDIX.GUID = RSBT_STATION.GUID " +
            "LEFT JOIN RSBT_STATION_T ON RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
            "LEFT JOIN RSBT_NET ON RSBT_NET.GUID = RSBT_STATION.NET_GUID " +
            "LEFT JOIN RSBT_APPLY ON RSBT_APPLY.APP_CODE = RSBT_STATION.APP_CODE " +
            "LEFT JOIN RSBT_FREQ ON RSBT_FREQ.STATION_GUID = RSBT_STATION.GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_ORG.GUID = RSBT_NET.ORG_GUID " +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            " where (RSBT_STATION_APPENDIX.IS_DELETED = #{stationDTO.isDeleted,jdbcType=VARCHAR} or #{stationDTO.isDeleted,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION.STAT_LA >= #{stationDTO.minlat,jdbcType=VARCHAR} or #{stationDTO.minlat,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LA <= #{stationDTO.maxlat,jdbcType=VARCHAR} or #{stationDTO.maxlat,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LG >= #{stationDTO.minlng,jdbcType=VARCHAR} or #{stationDTO.minlng,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LG <= #{stationDTO.maxlng,jdbcType=VARCHAR} or #{stationDTO.maxlng,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION_APPENDIX.EXPAND_STATION = #{stationDTO.expandStation,jdbcType=VARCHAR} or #{stationDTO.expandStation,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION_APPENDIX.ATTRIBUTE_STATION = #{stationDTO.attributeStation,jdbcType=VARCHAR} or #{stationDTO.attributeStation,jdbcType=VARCHAR} is null) " +
            "  and (" +
//            "  ((RSBT_FREQ.FREQ_EFB <= #{stationDTO.freqEfb,jdbcType=VARCHAR} or #{stationDTO.freqEfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_EFE >= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null)) " +
//            "   or ((RSBT_FREQ.FREQ_EFB <= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_EFE >= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null)) " +
//            "  or " +
            "((RSBT_FREQ.FREQ_EFB >= #{stationDTO.freqEfb,jdbcType=VARCHAR} or #{stationDTO.freqEfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_EFE <= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null)) " +
            "  ) " +
            "  and (" +
//            " ((RSBT_FREQ.FREQ_RFB <= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_RFE >= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null)) " +
//            " or ((RSBT_FREQ.FREQ_RFB <= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_RFE >= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null)) " +
//            "  or " +
            "((RSBT_FREQ.FREQ_RFB >= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null) and (RSBT_FREQ.FREQ_RFE <= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null))" +
            " ) " +
            " and (RSBT_STATION.STAT_NAME like concat(concat('%',#{stationDTO.stationName,jdbcType=VARCHAR}),'%') or #{stationDTO.stationName,jdbcType=VARCHAR} is null) " +
            " and  RSBT_STATION.STAT_AREA_CODE in (select CODE from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            " and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{stationDTO.stationCode,jdbcType=VARCHAR}),'%') or #{stationDTO.stationCode,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION_APPENDIX.GEN_NUM = #{stationDTO.genNum,jdbcType=VARCHAR} or #{stationDTO.genNum,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION.STAT_STATUS = #{stationDTO.isLicense,jdbcType=VARCHAR} or #{stationDTO.isLicense,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION_APPENDIX.USER_GUID = #{stationDTO.userGuid,jdbcType=VARCHAR} or #{stationDTO.userGuid,jdbcType=VARCHAR} is null) " +
            " and (RSBT_ORG.ORG_NAME = #{stationDTO.orgName,jdbcType=VARCHAR} or #{stationDTO.orgName,jdbcType=VARCHAR} is null) " +
            " and (RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} is null) " +
            " order by RSBT_STATION.STAT_NAME desc")
//    @Results({
//            @Result(property = "stationId",column = "STATIONID"),
//            // 查询的数据页面未用到且耗费甚巨
//            @Result(property = "sectionDTOList",column = "STATIONID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.station.RsbtStationMapper.getListVO"))
//    })
    List<StationDTO> findAllByWhere(@Param("stationDTO") StationDTO stationDTO, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 分页条件查询 wuwei
     *
     * @param stationDTO stationDTO
     * @param usersDTO   usersDTO
     * @return list
     */
    @Select("select * from (select RSBT_STATION_BAK.STATION_GUID AS STATIONID ,RSBT_STATION_BAK.STAT_NAME AS STATIONNAME,RSBT_STATION_T.ST_C_CODE AS STATIONCODE, " +
            " RSBT_STATION_T.ST_D_TEC_TYPE as NETTYPE, RSBT_STATION_BAK.STAT_ADDR AS LOCATION,RSBT_STATION_BAK.STAT_LG AS LONGITUDE, RSBT_STATION_BAK.STAT_LA AS LATITUDE," +
            " RSBT_STATION_APPENDIX.IS_DELETED AS STATIONSTATE,RSBT_STATION.STAT_STATUS AS ISLICENSE,RSBT_STATION_APPENDIX.EXPAND_STATION AS EXPANDSTATION," +
            " RSBT_STATION_APPENDIX.USER_GUID AS USERGUID,RSBT_STATION_APPENDIX.GEN_NUM AS GENNUM,RSBT_STATION_BAK.ORG_TYPE AS orgType," +
            " row_number() over(partition by RSBT_STATION_BAK.ST_C_CODE order by RSBT_STATION_BAK.BAK_DATE desc) rn from RSBT_STATION_BAK" +
            " INNER JOIN RSBT_STATION_APPENDIX ON RSBT_STATION_APPENDIX.GUID = RSBT_STATION_BAK.STATION_GUID INNER JOIN RSBT_STATION_T ON RSBT_STATION_BAK.STATION_GUID = RSBT_STATION_T.GUID" +
            " LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_STATION_BAK.ORG_TYPE = RSBT_ORG_APPENDIX.TYPE LEFT JOIN RSBT_ORG ON RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID" +
            " INNER JOIN RSBT_STATION ON RSBT_STATION.GUID = RSBT_STATION_BAK.STATION_GUID LEFT JOIN RSBT_FREQ ON RSBT_FREQ.STATION_GUID = RSBT_STATION_BAK.STATION_GUID " +
            " where RSBT_STATION_BAK.IS_SHOW = '1' and (RSBT_STATION_APPENDIX.IS_DELETED = #{stationDTO.isDeleted,jdbcType=VARCHAR} or #{stationDTO.isDeleted,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_LA >= #{stationDTO.minlat,jdbcType=VARCHAR} or #{stationDTO.minlat,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_LA <= #{stationDTO.maxlat,jdbcType=VARCHAR} or #{stationDTO.maxlat,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_LG >= #{stationDTO.minlng,jdbcType=VARCHAR} or #{stationDTO.minlng,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_BAK.STAT_LG <= #{stationDTO.maxlng,jdbcType=VARCHAR} or #{stationDTO.maxlng,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_APPENDIX.EXPAND_STATION = #{stationDTO.expandStation,jdbcType=VARCHAR} or #{stationDTO.expandStation,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_APPENDIX.ATTRIBUTE_STATION = #{stationDTO.attributeStation,jdbcType=VARCHAR} or #{stationDTO.attributeStation,jdbcType=VARCHAR} is null)" +
            " and (((RSBT_FREQ.FREQ_EFB >= #{stationDTO.freqEfb,jdbcType=VARCHAR} or #{stationDTO.freqEfb,jdbcType=VARCHAR} is null) " +
            "          and (RSBT_FREQ.FREQ_EFE <= #{stationDTO.freqEfe,jdbcType=VARCHAR} or #{stationDTO.freqEfe,jdbcType=VARCHAR} is null)))" +
            " and (((RSBT_FREQ.FREQ_RFB >= #{stationDTO.freqRfb,jdbcType=VARCHAR} or #{stationDTO.freqRfb,jdbcType=VARCHAR} is null) " +
            "          and (RSBT_FREQ.FREQ_RFE <= #{stationDTO.freqRfe,jdbcType=VARCHAR} or #{stationDTO.freqRfe,jdbcType=VARCHAR} is null)))" +
            " and (RSBT_STATION_BAK.STAT_NAME like concat(concat('%',#{stationDTO.stationName,jdbcType=VARCHAR}),'%') or #{stationDTO.stationName,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION.STAT_AREA_CODE in (select CODE from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID))" +
            " and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{stationDTO.stationCode,jdbcType=VARCHAR}),'%') or #{stationDTO.stationCode,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_APPENDIX.GEN_NUM = #{stationDTO.genNum,jdbcType=VARCHAR} or #{stationDTO.genNum,jdbcType=VARCHAR} is null)" +
            " and (RSBT_ORG.ORG_NAME = #{stationDTO.orgName,jdbcType=VARCHAR} or #{stationDTO.orgName,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION_APPENDIX.IS_LICENSE = #{stationDTO.isLicense,jdbcType=VARCHAR} or #{stationDTO.isLicense,jdbcType=VARCHAR} is null)" +
            " and (RSBT_STATION_APPENDIX.USER_GUID = #{stationDTO.userGuid,jdbcType=VARCHAR} or #{stationDTO.userGuid,jdbcType=VARCHAR} is null)" +
            " order by RSBT_STATION.STAT_NAME desc) c where c.rn = 1")
    List<StationDTO> findAllByWuWei(@Param("stationDTO") StationDTO stationDTO, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 查询详情DTO
     * */
    @Select("select RSBT_STATION.*,RSBT_STATION_T.ST_C_CODE as stationCode,RSBT_STATION_T.ST_D_TEC_TYPE  as netType from RSBT_STATION,RSBT_STATION_T where RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
            "AND RSBT_STATION.GUID = #{guid}")
    @Results({
            @Result(property = "guid",column = "GUID"),
            @Result(property = "sectionDTOList",column = "GUID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.station.RsbtStationMapper.getListVO"))
    })
    StationDTO findOneByGuid(@Param("guid") String guid);

    /**
     * 查询详情DTO
     */
    @Select("select * from (select RSBT_STATION_BAK.*,RSBT_STATION_T.ST_C_CODE as stationCode,RSBT_STATION_T.ST_D_TEC_TYPE as netType " +
            " from RSBT_STATION_BAK,RSBT_STATION,RSBT_STATION_T where RSBT_STATION_BAK.STATION_GUID = RSBT_STATION_T.GUID AND RSBT_STATION_BAK.STATION_GUID = #{guid}" +
            " and RSBT_STATION.GUID = RSBT_STATION_BAK.STATION_GUID order by RSBT_STATION_BAK.bak_date DESC) where ROWNUM = 1")
    StationDTO findOneByGuid1(@Param("guid") String guid);

    /**
     * 查询基站的扇区信息
     *
     * @param btsId   btsId
     * @param appGuid appGuid
     * @return list
     */
    @Select("select CELL_ID SECTION_CODE, CELL_NAME SECTION_NAME, HEIGHT ANT_HIGHT, SEND_START_FREQ freqEfb, SEND_END_FREQ freqEfe, ACC_END_FREQ freqRfe, ACC_START_FREQ freqRfb," +
            " MAX_EMISSIVE_POWER maxPower, MODEL_CODE equAuth, DEVICE_MODEL equMenu, POLARIZATION_MODE antPole, ANTENNA_MODEL antType, ANTENNA_FACTORY antMenu, FEEDER_LOSS feedLose," +
            " ANTENNA_AZIMUTH antAngle,ANTENNA_GAIN antGain from APPROVAL_SCHEDULE_LOG where BTS_ID =#{btsId} and APP_GUID = #{appGuid}")
    List<SectionDTO> getListDTO(@Param("btsId") String btsId, @Param("appGuid") String appGuid);

    @Select("SELECT * FROM " +
            "(SELECT RSBT_ANTFEED_T.AT_CCODE AS SECTION_CODE , RSBT_ANTFEED.ANT_HIGHT,RSBT_ANTFEED.ANT_TYPE,RSBT_ANTFEED.ANT_MENU,RSBT_ANTFEED.ANT_POLE,RSBT_ANTFEED.ANT_ANGLE,RSBT_ANTFEED.FEED_LOSE,RSBT_ANTFEED.ANT_GAIN FROM RSBT_ANTFEED LEFT JOIN RSBT_ANTFEED_APPENDIX ON RSBT_ANTFEED.GUID = RSBT_ANTFEED_APPENDIX.GUID LEFT JOIN  RSBT_ANTFEED_T ON RSBT_ANTFEED.GUID = RSBT_ANTFEED_T.GUID WHERE RSBT_ANTFEED_APPENDIX.IS_DELETED = 0 AND RSBT_ANTFEED.STATION_GUID = #{stationId}) RSBT_ANTFEED," +
            "(SELECT RSBT_EQU_T.ET_EQU_CCODE AS SECTION_CODE, RSBT_EQU_T.ET_EQU_UPOW AS MAX_POWER,RSBT_EQU.EQU_MENU,RSBT_EQU.EQU_AUTH FROM RSBT_EQU LEFT JOIN RSBT_EQU_APPENDIX ON RSBT_EQU.GUID = RSBT_EQU_APPENDIX.GUID LEFT JOIN RSBT_EQU_T ON RSBT_EQU.GUID = RSBT_EQU_T.GUID WHERE RSBT_EQU_APPENDIX.IS_DELETED = 0 AND RSBT_EQU.STATION_GUID = #{stationId}) RSBT_EQU," +
            "(SELECT RSBT_FREQ_T.FT_FREQ_CCODE AS SECTION_CODE, RSBT_FREQ.FREQ_EFB, RSBT_FREQ.FREQ_EFE, RSBT_FREQ.FREQ_RFB, RSBT_FREQ.FREQ_RFE, RSBT_FREQ_APPENDIX.SECTION_NAME FROM RSBT_FREQ LEFT JOIN RSBT_FREQ_T ON RSBT_FREQ.GUID = RSBT_FREQ_T.GUID LEFT JOIN RSBT_FREQ_APPENDIX ON RSBT_FREQ.GUID = RSBT_FREQ_APPENDIX.GUID WHERE RSBT_FREQ_APPENDIX.IS_DELETED = 0 AND RSBT_FREQ.STATION_GUID = #{stationId}) RSBT_FREQ" +
            " WHERE RSBT_ANTFEED.SECTION_CODE = RSBT_EQU.SECTION_CODE AND RSBT_ANTFEED.SECTION_CODE = RSBT_FREQ.SECTION_CODE AND RSBT_EQU.SECTION_CODE = RSBT_FREQ.SECTION_CODE")
    List<SectionDTO> getListVO(@Param("stationId") String stationId);

    @Select({"<script>" +
            "SELECT * FROM RSBT_STATION WHERE ST_C_CODE IN " +
            "<foreach item = 'item' index = 'index' collection = 'staSet'  open='(' separator=',' close=')'>" +
            " #{item}" +
            "</foreach>" +
            "</script>"})
    List<RsbtStation> selectExistStation(@Param("staSet") Set<String> staSet);


    @Select("SELECT COUNT(*) FROM RSBT_STATION_BAK where IS_SHOW = '1' ")
    int queryStationCount();

    @Select("SELECT a.COUNTY,COUNT(*) num FROM RSBT_STATION_BAK b,RSBT_STATION_APPENDIX a WHERE b.STATION_GUID = a.GUID and b.IS_SHOW = '1' GROUP BY a.COUNTY")
    @Results({
            @Result(property = "name", column = "COUNTY"),
            @Result(property = "value", column = "num"),
    })
    List<EchartsResDTO> queryStationCountyNumber();

    @Select("SELECT COUNT(*)" +
            "  FROM (SELECT count(*)" +
            "          FROM ASYNC_RAW_BTS t" +
            "          LEFT JOIN RSBT_STATION_T a" +
            "            ON t.BTS_ID = a.ST_C_CODE" +
            "          LEFT JOIN RSBT_STATION_APPENDIX b" +
            "            ON a.GUID = b.GUID" +
            "         WHERE t.DATA_TYPE = '1'" +
            "           AND TO_CHAR(b.GMT_CREATE, 'YYYY-MM') =" +
            "               TO_CHAR(SYSDATE, 'YYYY-MM')" +
            "         GROUP BY t.BTS_ID)")
    int queryNewStaByThisMonth();

    @Select("SELECT DISTINCT t.BTS_ID, t.BTS_NAME, t.ORG_TYPE, b.GMT_CREATE AS createDate FROM ASYNC_RAW_BTS t LEFT JOIN " +
            " RSBT_STATION_T a ON t.BTS_ID = a.ST_C_CODE LEFT JOIN RSBT_STATION_APPENDIX b ON a.GUID = b.GUID  WHERE t.DATA_TYPE = '1'")
    List<StaInfoDTO> queryNewInsertStation();

    /**
     * 根据guid 把 执照状态改为1
     * @param guidList
     */
    @Update({
            "<script>",
            "UPDATE RSBT_STATION_APPENDIX SET IS_LICENSE = 1 WHERE GUID IN",
            "<foreach collection='guidList' item='id' open='(' separator=',' close=')'>",
              "#{id}",
            "</foreach>",
            "</script>"
    })
    void updateStationLicenseStatus(@Param("guidList") List<String> guidList);

    /**
     * 查询区域排名（根据基站数排名)
     */
    @Select("SELECT a.COUNTY,COUNT(*) NUM from RSBT_STATION_BAK b,RSBT_STATION_APPENDIX a where b.STATION_GUID = a.GUID and b.IS_SHOW = '1' GROUP BY a.COUNTY")
    @Results({
            @Result(property = "regionName",column = "COUNTY"),
            @Result(property = "regionStationNum",column = "NUM"),
    })
    List<RegionSortDTO> selectRegionSortDTO();

    /**
     * 根据county获取OrgStationNumSortDTO
     * @param county
     * @return
     */
    @Select("SELECT c.ORG_CODE,COUNT(*) NU from (SELECT * from RSBT_STATION_BAK b,RSBT_STATION s,RSBT_STATION_APPENDIX a where b.STATION_GUID = a.GUID " +
            "and s.GUID = b.STATION_GUID and b.IS_SHOW = '1' and a.COUNTY=#{county}) c GROUP BY c.ORG_CODE")
    @Results({
            @Result(property = "orgName",column = "ORG_CODE",one = @One(select = ("com.caict.bsm.project.domain.business.mapper.station.RsbtStationMapper.getOrgName"))),
            @Result(property = "stationNumber",column = "NU")
    })
    List<OrgStationNumSortDTO> selectOrgSortDTO(@Param("county") String county);

    @Select("select ORG_NAME from RSBT_ORG where ORG_CODE=#{ORG_CODE}")
    String getOrgName(@Param("ORG_CODE") String ORG_CODE);

    /**
     * 台站分布统计
     */
    @Select("select FSA_REGION.name regionName,aa.regionStationNum from ( SELECT FSA_REGION.PARENT_ID,  COUNT(*) regionStationNum from RSBT_STATION_APPENDIX," +
            " RSBT_STATION_BAK, FSA_REGION where FSA_REGION.NAME = RSBT_STATION_APPENDIX.COUNTY AND RSBT_STATION_BAK.STATION_GUID = RSBT_STATION_APPENDIX.GUID " +
            " AND RSBT_STATION_BAK.IS_SHOW = '1' GROUP BY FSA_REGION.PARENT_ID) aa ,FSA_REGION where aa.PARENT_ID = FSA_REGION.id")
    List<RegionSortDTO> getDistributionStatistics();

    @Select("select RSBT_ORG.ORG_NAME,a.COT from (SELECT * FROM (select ORG_CODE,count(*) COT from RSBT_STATION_BAK bak,RSBT_STATION ss" +
            " WHERE ss.GUID = bak.STATION_GUID AND bak.IS_SHOW = '1' GROUP BY ORG_CODE ORDER BY COT DESC) WHERE ROWNUM <=3) a ,RSBT_ORG " +
            " WHERE a.ORG_CODE=RSBT_ORG.ORG_CODE ORDER BY a.COT DESC ")
    @Results({
            @Result(property = "orgName",column = "ORG_NAME"),
            @Result(property = "stationNumber",column = "COT")
    })
    List<OrgStationNumSortDTO> selectOrgStationNum();


    @Select("SELECT DISTINCT " +
            "RSBT_STATION.GUID AS STATIONID," +
            "RSBT_STATION.STAT_NAME AS STATIONNAME," +
            "RSBT_STATION_T.ST_C_CODE AS STATIONCODE," +
            "RSBT_NET.NET_TS AS NETTYPE," +
            "RSBT_STATION.STAT_ADDR AS LOCATION," +
            "RSBT_STATION.STAT_LG AS LONGITUDE," +
            "RSBT_STATION.STAT_LA AS LATITUDE," +
            "RSBT_STATION_APPENDIX.IS_DELETED AS STATIONSTATE," +
            "RSBT_APPLY.GUID AS APPLYTABLEGUID," +
            "RSBT_STATION.STAT_STATUS AS ISLICENSE, " +
            "RSBT_STATION_APPENDIX.USER_GUID AS USERGUID, " +
            "RSBT_STATION_APPENDIX.GEN_NUM AS GENNUM, " +
            "RSBT_ORG_APPENDIX.TYPE AS ORGTYPE " +
            "FROM " +
            "(select * from RSBT_STATION WHERE RSBT_STATION.STAT_LG > #{lowLng} AND RSBT_STATION.STAT_LG < #{highLng} " +
            "AND RSBT_STATION.STAT_LA > #{lowLat} AND RSBT_STATION.STAT_LA < #{highLat}) RSBT_STATION " +
            "LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION_APPENDIX.GUID = RSBT_STATION.GUID " +
            "LEFT JOIN RSBT_STATION_T ON RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
            "LEFT JOIN RSBT_NET ON RSBT_NET.GUID = RSBT_STATION.NET_GUID " +
            "LEFT JOIN RSBT_APPLY ON RSBT_APPLY.APP_CODE = RSBT_STATION.APP_CODE " +
            "LEFT JOIN RSBT_FREQ ON RSBT_FREQ.STATION_GUID = RSBT_STATION.GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_ORG.GUID = RSBT_NET.ORG_GUID " +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID "
            )
    List<StationDTO> queryStationByRange(@Param("lowLng") Double lowLng, @Param("lowLat") Double lowLat, @Param("highLng") Double highLng, @Param("highLat") Double highLat);

    @Select("select * from (select RSBT_STATION.*, rownum as rnum from " +
            "RSBT_STATION WHERE APP_CODE = #{appCode} " +
            "and rownum <= 9000) where rnum >= 1 ")
    List<RsbtStation> findListByAppCodePage(@Param("appCode") String appCode);

    @Update("update RSBT_STATION SET APP_CODE = #{newAppCode} WHERE APP_CODE = #{oldAppCode}")
    int updateAppCode(@Param("oldAppCode") String oldAppCode, @Param("newAppCode") String newAppCode);

    @Select("select * from RSBT_STATION WHERE GUID = #{guid}")
    RsbtStation selectByStationGuid(@Param("guid") String guid);

    @Select("select " +
            "RSBT_STATION.GUID AS STATIONID," +
            "RSBT_STATION.STAT_NAME AS STATIONNAME," +
            "RSBT_STATION.STAT_ADDR AS LOCATION," +
            "RSBT_STATION.STAT_LG AS LONGITUDE," +
            "RSBT_STATION.STAT_LA AS LATITUDE," +
            "RSBT_STATION_APPENDIX.ORG_TYPE AS orgType," +
            "RSBT_STATION_APPENDIX.COUNTY,RSBT_STATION_APPENDIX.IS_AREA,RSBT_STATION_APPENDIX.GEN_NUM,RSBT_STATION_T.ST_C_CODE AS stationCode,RSBT_STATION_APPENDIX.TECH_TYPE netType " +
            "from RSBT_STATION,RSBT_STATION_T,RSBT_STATION_APPENDIX " +
            "WHERE RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
            "AND RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID " +
            "AND (RSBT_STATION_APPENDIX.IS_AREA = #{stationDTO.isArea,jdbcType=VARCHAR} or #{stationDTO.isArea,jdbcType=VARCHAR} is null) " +
            "AND (RSBT_STATION_APPENDIX.GEN_NUM = #{stationDTO.genNum,jdbcType=VARCHAR} or #{stationDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and RSBT_STATION_APPENDIX.COUNTY in (select name from FSA_REGION start with name = #{stationDTO.county,jdbcType=VARCHAR} connect by prior ID = PARENT_ID)  " +
            "and RSBT_STATION.STAT_AREA_CODE in (select CODE from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) ")
    List<StationDTO> queryAreaRange(@Param("stationDTO") StationDTO stationDTO, @Param("usersDTO") UsersDTO usersDTO);

    /**
     * 根据btsIds 刪除
     */
    @Delete("<script>" +
            "delete from RSBT_STATION where GUID in " +
            " <foreach item='item' collection='guids' separator=',' open='(' close=')' index=''>" +
            "#{item} " +
            "</foreach>" +
            "</script>")
    void deleteByStationIds(@Param("guids") List<String> guids);

}
