package com.bsm.v4.domain.security.service.business.freqevaluation;

import com.bsm.v4.domain.security.mapper.business.freqevaluation.OperUserStatisMapper;
import com.bsm.v4.system.model.dto.business.freqevaluation.OperUserStatisDTO;
import com.bsm.v4.system.model.entity.business.freqevaluation.OperUserStatis;
import com.bsm.v4.system.model.vo.business.freqevaluation.OperUserStatisVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Service
public class OperUserStatisService extends BasicService<OperUserStatis> {

    @Autowired
    private OperUserStatisMapper mapper;

    /**
     * 查询数据
     *
     * @param vo vo
     * @return list
     */
    public List<OperUserStatisDTO> searchOperUserStatis(OperUserStatisVO vo) {
        return mapper.searchOperUserStatis(vo);
    }
}
