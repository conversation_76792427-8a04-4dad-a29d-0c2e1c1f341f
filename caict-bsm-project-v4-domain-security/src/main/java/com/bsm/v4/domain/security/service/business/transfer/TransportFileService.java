package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.TransportFileMapper;
import com.bsm.v4.system.model.dto.business.transfer.TransportFileDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportFile;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportFileService extends BasicService<TransportFile> {

    @Autowired
    private TransportFileMapper transportFileMapper;

    /**
     * 根据Job、文件名查询数量
     */
    public int selectCountByJobFileName(String jobId, String fileName) {
        return transportFileMapper.selectCountByJobFileName(jobId, fileName);
    }

    public List<TransportFileDTO> findByJobGuid(String jobGuid, String dataType, String genNum, String fileType) {
        return transportFileMapper.findByJobGuid(jobGuid, dataType, genNum, fileType);
    }

    public List<TransportFileDTO> findByGuidType(String jobGuid, String fileType) {
        return transportFileMapper.findByGuidType(jobGuid, fileType);
    }

    public List<TransportFileDTO> findFileDetailByJobId(String jobGuid, String fileType) {
        return transportFileMapper.findFileDetailByJobId(jobGuid, fileType);
    }
}
