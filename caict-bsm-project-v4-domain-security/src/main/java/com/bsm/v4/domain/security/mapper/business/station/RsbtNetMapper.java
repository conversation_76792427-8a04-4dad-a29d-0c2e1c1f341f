package com.bsm.v4.domain.security.mapper.business.station;


import com.bsm.v4.system.model.entity.business.station.RsbtNet;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface RsbtNetMapper extends BasicMapper<RsbtNet> {


    @Select("SELECT * FROM RSBT_NET net WHERE EXISTS(" +
            "   SELECT sta.NET_GUID FROM RSBT_STATION sta WHERE sta.GUID = #{stationGuid} AND net.GUID = sta.NET_GUID)")
    RsbtNet getRsbtNetByStationGuid(@Param("stationGuid") String stationGuid);
}
