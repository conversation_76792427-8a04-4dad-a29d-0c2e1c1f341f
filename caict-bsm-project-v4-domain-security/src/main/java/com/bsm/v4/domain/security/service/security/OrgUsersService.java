package com.bsm.v4.domain.security.service.security;

import com.bsm.v4.domain.security.mapper.security.RsbtOrgUsersMapper;
import com.bsm.v4.system.model.dto.security.OrgDTO;
import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.RsbtOrgUsers;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Title: OrgUsersService
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.service.security
 * @Date 2023/8/16 14:26
 * @description:
 */
@Service
public class OrgUsersService extends BasicService<RsbtOrgUsers> {

    @Autowired
    private RsbtOrgUsersMapper orgUsersMapper;

    /**
     * 根据组织机构删除所有
     */
    public int deleteAllByOrg(String orgId) {
        return orgUsersMapper.deleteAllByOrg(orgId);
    }

    /**
     * 根据userId删除
     */
    public int deleteAllByUserId(String usersId) {
        return orgUsersMapper.deleteAllByUsersId(usersId);
    }

    /**
     * 根据userId 查组织机构
     */
    public OrgDTO findOrgGuidByUserId(String userId) {
        return orgUsersMapper.findOrgGuidByUserId(userId);
    }

    /**
     * 根据userId 查组织机构
     */
    public OrgDTO findRsbtOrgByUserId(String userId) {
        return orgUsersMapper.findRsbtOrgByUserId(userId);
    }

    /**
     * 根据组织机构查询全部用户
     */
    public List<UsersDTO> findAllUsersByOrg(String orgGuid) {
        return orgUsersMapper.findAllUsersByOrg(orgGuid);
    }
}
