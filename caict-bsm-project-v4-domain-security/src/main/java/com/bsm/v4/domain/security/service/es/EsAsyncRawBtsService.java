package com.bsm.v4.domain.security.service.es;

import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.bsm.v4.domain.security.mapper.es.EsAsyncRawBtsMapper;
import com.bsm.v4.system.model.entity.es.EsAsyncRawBts;
import com.bsm.v4.system.model.vo.es.EsAsyncRawBtsVO;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: ycp
 * @createTime: 2023/08/10 10:12
 * @company: 成渝（成都）信息通信研究院
 * @description:
 */
@Service
public class EsAsyncRawBtsService extends ElasticsearchBasicService<EsAsyncRawBts, EsAsyncRawBtsVO> {

    private static final Logger LOG = LoggerFactory.getLogger(EsAsyncRawBtsService.class);

    @Autowired
    private EsAsyncRawBtsMapper esMapper;

    /**
     * 保存数据
     *
     * @param entities entities
     * @return list
     */
    public List<EsAsyncRawBts> saveList(List<EsAsyncRawBts> entities) {
        var list = new ArrayList<EsAsyncRawBts>();
        esMapper.saveAll(entities).forEach(list::add);
        return list;
    }

    /**
     * 根据guid查询数据
     *
     * @param ids ids
     * @return list
     */
    public List<EsAsyncRawBts> findAllById(List<String> ids) {
        var list = new ArrayList<EsAsyncRawBts>();
        esMapper.findAllById(ids).forEach(list::add);
        return list;
    }

    /**
     * 查询使用率评价
     *
     * @param vo vo
     */
    public List<EsAsyncRawBts> searchUsageEvaluation(EsAsyncRawBtsVO vo) {
        // 分页
        BoolQueryBuilder query = new BoolQueryBuilder();
        if (StringUtils.isNoneBlank(vo.getTechType()))
            query.must(QueryBuilders.matchQuery("TECH_TYPE", vo.getTechType()));
        if (StringUtils.isNoneBlank(vo.getCounty()))
            query.must(QueryBuilders.matchQuery("COUNTY", vo.getCounty()));
        if (StringUtils.isNoneBlank(vo.getGenNum()))
            query.must(QueryBuilders.matchQuery("GEN_NUM", vo.getGenNum()));
        if (StringUtils.isNoneBlank(vo.getOrgType()))
            query.must(QueryBuilders.matchQuery("ORG_TYPE", vo.getOrgType()));
        if (StringUtils.isNoneBlank(vo.getStScene()))
            query.must(QueryBuilders.matchQuery("ST_SCENE", vo.getStScene()));
        if (vo.getTrfDate() != null)
            query.must(QueryBuilders.matchQuery("TRF_DATE", vo.getTrfDate()));
        // 查询条数
        var size = this.count(query);

        int page = 1;
        int row = 10000;
        List<EsAsyncRawBts> search = new ArrayList<>();
        if (size > row) {
            for (var i = 0; i < size; i += row) {
                vo.setRows(row);
                vo.setPage(page);
                LOG.info("公众移动通信系统无线电频率使用率评价所需数据，页码：" + page);
                search.addAll(this.searchHitsPage(vo, query, "GUID").getList());
                page++;
            }
        } else {
            vo.setRows(row);
            vo.setPage(page);
            LOG.info("公众移动通信系统无线电频率使用率评价所需数据，页码：" + page);
            search.addAll(this.searchHitsPage(vo, query, "GUID").getList());
        }
        return search;
    }

    public void test(){
        BoolQueryBuilder query = new BoolQueryBuilder();
            query.must(QueryBuilders.matchQuery("BTS_ID", "btsId100000"));
        SearchHits bts_id = this.topHitsAggregationBuilder(query, "BTS_ID.keyword");
        System.out.println("aaaa");
    }

}
