package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.RoleDTO;
import com.bsm.v4.system.model.entity.security.RoleUsers;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * @Title: RoleUsersMapper
 * <AUTHOR>
 * @Package com.bsm.v4.domain.security.mapper.security
 * @Date 2023/8/16 11:30
 * @description:
 */
@Repository
public interface RoleUsersMapper extends BasicMapper<RoleUsers> {


    @Delete("delete from sys_role_users where role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") String roleId);

    @Delete("delete from sys_role_users where users_id = #{usersId}")
    int deleteByUsersId(@Param("usersId") String usersId);

    /**
     * 查询用户所属角色
     */
    @Select("select sys_role.* from sys_role,sys_role_users where sys_role.id = sys_role_users.role_id and sys_role_users.users_id = #{userId}")
    RoleDTO findRoleByUser(@Param("userId") String userId);
}
