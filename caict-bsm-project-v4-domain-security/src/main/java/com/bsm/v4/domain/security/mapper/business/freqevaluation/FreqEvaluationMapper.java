package com.bsm.v4.domain.security.mapper.business.freqevaluation;

import com.bsm.v4.system.model.dto.business.freqevaluation.FreqEvaluationDTO;
import com.bsm.v4.system.model.dto.business.freqevaluation.FreqEvaluationProvDTO;
import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluation;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationProvVO;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Repository
public interface FreqEvaluationMapper extends BasicMapper<FreqEvaluation> {

    /**
     * 查询数据
     *
     * @param vo vo
     * @return list
     */
    @Select("<script>select r.*, sr.NAME areaName from BSM_FREQ_EVALUATION_RESULTS r left join SYS_REGION sr on r.ORG_AREA_CODE = sr.CODE where 1=1 " +
            "<if test='vo.orgType != null and vo.orgType != \"\"'> and r.ORG_TYPE = #{vo.orgType}</if>" +
            "<if test='vo.startDate != null and vo.startDate != \"\"'>" +
            " AND r.CREATE_DATE &gt;= to_date(#{vo.startDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            "<if test='vo.endDate != null and vo.endDate != \"\"'>" +
            " AND r.CREATE_DATE &lt;= to_date(#{vo.endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            " order by r.ORG_LEVEL </script>")
    List<FreqEvaluationDTO> searchFREQEvaluation(@Param("vo") FreqEvaluationVO vo);

    /**
     * 查询数据
     *
     * @param vo vo
     * @return list
     */
    @Select("<script>select * from BSM_FREQ_EVALUATION_PROV r where 1=1 " +
            "<if test='vo.startDate != null and vo.startDate != \"\"'>" +
            " AND r.CREATE_DATE &gt;= to_date(#{vo.startDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            "<if test='vo.endDate != null and vo.endDate != \"\"'>" +
            " AND r.CREATE_DATE &lt;= to_date(#{vo.endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            "</script>")
    List<FreqEvaluationProvDTO> searchFREQEvaluationProv(@Param("vo") FreqEvaluationProvVO vo);

    /**
     * 查询市数据数量
     *
     * @param vo vo
     * @return int
     */
    @Select("<script>select COUNT(*) from(select count(ORG_AREA_CODE) from BSM_FREQ_EVALUATION_RESULTS r where r.ORG_LEVEL = '2' " +
            "<if test='vo.startDate != null and vo.startDate != \"\"'>" +
            " AND r.CREATE_DATE &gt;= to_date(#{vo.startDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            "<if test='vo.endDate != null and vo.endDate != \"\"'>" +
            " AND r.CREATE_DATE &lt;= to_date(#{vo.endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            " group by r.ORG_AREA_CODE)</script>")
    int searchFREQEvaluationNum(@Param("vo") FreqEvaluationVO vo);
}
