package com.bsm.v4.domain.security.service.business.applytable;

import com.bsm.v4.domain.security.mapper.business.applytable.ApplyJobInMapper;
import com.bsm.v4.system.model.dto.business.applytable.ApplyJobInDTO;
import com.bsm.v4.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.bsm.v4.system.model.entity.business.applytable.ApplyJobIn;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2021/5/30.
 */
@Service
public class ApplyJobInService extends BasicService<ApplyJobIn> {

    @Autowired
    private ApplyJobInMapper applyJobInMapper;

    public int updateByAppCode(String appGuid, String appCode, String status) {
        return applyJobInMapper.updateByAppCode(appGuid, appCode, status);
    }

    public List<ApplyJobInDTO> findDetailByPage(ApprovalTransportJobDTO approvalTransportJobDTO) {
        return applyJobInMapper.findDetailByPage(approvalTransportJobDTO.getGuid());
    }

    public ApplyJobInDTO selectByAppCode(String appCode) {
        return applyJobInMapper.selectByAppCode(appCode);
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return applyJobInMapper.updateAppCode(oldAppCode, newAppCode);
    }
}
