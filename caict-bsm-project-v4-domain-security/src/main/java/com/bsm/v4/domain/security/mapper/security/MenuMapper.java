package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.MenuDTO;
import com.caictframework.data.mapper.BasicMapper;
import com.bsm.v4.system.model.entity.security.Menu;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2023-02-10.
 */
@Repository
public interface MenuMapper extends BasicMapper<Menu> {

    @Select("select * from sys_menu order by sort")
    List<MenuDTO> findAllDto();

    @Select("select * from sys_menu where type = #{type}")
    List<MenuDTO> findAllByType(@Param("type") String type);

    @Select("select * from sys_menu where parent_id = #{parentId} " +
            "order by sort")
    List<MenuDTO> findAllByParentId(@Param("parentId") String parentId);

    /**
     * 更新父级
     */
    @Update("update sys_menu set parent_id = #{parentNew} where parent_id = #{parentOld}")
    int updateParent(@Param("parentOld") String parentOld, @Param("parentNew") String parentNew);

    /**
     * 根据用户查询菜单
     */
    @Select("select sys_menu.* from sys_menu,sys_role_menu,sys_role_users where sys_menu.id = sys_role_menu.menu_id and sys_role_menu.role_id = sys_role_users.role_id " +
            "and sys_role_users.users_id = #{userId} " +
            "and sys_menu.status = 1 " +
            "order by sort")
    List<MenuDTO> findAllByUser(@Param("userId") String userId);

    @Select("select sys_menu.* from sys_menu,sys_role_menu where sys_menu.id = sys_role_menu.menu_id and sys_role_menu.role_id = #{roleId}")
    List<MenuDTO> findAllMenuByRoleId(@Param("roleId") String roleId);
}
