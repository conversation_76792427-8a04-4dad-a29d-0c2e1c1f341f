package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtStationTMapper;
import com.bsm.v4.system.model.dto.business.station.StationNetDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtStationT;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtStationTService extends BasicService<RsbtStationT> {

    @Autowired
    private RsbtStationTMapper rsbtStationTMapper;

    public List<StationNetDTO> findExistRsbtStationByAppGuid(String appGuid) {
        return rsbtStationTMapper.findExistRsbtStationByAppGuid(appGuid);
    }

}
