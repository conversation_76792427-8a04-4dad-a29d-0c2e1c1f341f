package com.bsm.v4.domain.security.service.business.freqevaluation;

import com.bsm.v4.domain.security.mapper.business.freqevaluation.FreqEvaluationMapper;
import com.bsm.v4.system.model.dto.business.freqevaluation.FreqEvaluationDTO;
import com.bsm.v4.system.model.dto.business.freqevaluation.FreqEvaluationProvDTO;
import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluation;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationProvVO;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationService extends BasicService<FreqEvaluation> {

    @Autowired
    private FreqEvaluationMapper mapper;

    /**
     * 查询数据
     *
     * @param param param
     * @return list
     */
    public List<FreqEvaluationDTO> searchFREQEvaluation(FreqEvaluationVO param) {
        return mapper.searchFREQEvaluation(param);
    }

    /**
     * 查询数据
     *
     * @param param param
     * @return list
     */
    public List<FreqEvaluationProvDTO> searchFREQEvaluationProv(FreqEvaluationProvVO param) {
        return mapper.searchFREQEvaluationProv(param);
    }

    /**
     * 查询市数据数量
     *
     * @param vo vo
     * @return int
     */
    public int searchFREQEvaluationNum(FreqEvaluationVO vo) {
        return mapper.searchFREQEvaluationNum(vo);
    }
}
