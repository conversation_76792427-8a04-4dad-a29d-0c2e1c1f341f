package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.CoordinateScheduleTempMapper;
import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleShowDTO;
import com.bsm.v4.system.model.dto.business.transfer.CoordinateScheduleTempDTO;
import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTemp;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class CoordinateScheduleTempService extends BasicService<CoordinateScheduleTemp> {

    @Autowired
    private CoordinateScheduleTempMapper coordinateScheduleTempMapper;

    /**
     * 根据流程类型查询代办数量
     */
    public List<CoordinateScheduleShowDTO> findAllCoordinateSchedule(List<String> compares) {
        return coordinateScheduleTempMapper.findAllCoordinateSchedule(compares);
    }

    /**
     * 根据jobId、类型查询总数
     */
    public int selectCountByJobIdDataType(String appGuid, String dataType) {
        return coordinateScheduleTempMapper.selectCountByJobIdDataType(appGuid, dataType);
    }

    /**
     * 根据jobId、类型分页查询
     */
    public List<CoordinateScheduleTempDTO> findAllPageByJobIdDataType(String jobGuid, String dataType, String genNum) {
        return coordinateScheduleTempMapper.findAllPageByJobIdDataType(jobGuid, dataType, genNum);
    }

    /**
     * 根据appGuid删除
     */
    public int deleteAllByAppGuid(String appGuid) {
        return coordinateScheduleTempMapper.deleteAllByAppGuid(appGuid);
    }

    /**
     * 根据appGuid查询数量
     */
    public int selectCountByAppGuid(String appGuid) {
        return coordinateScheduleTempMapper.selectCountByAppGuid(appGuid);
    }
}
