package com.bsm.v4.domain.security.service.business.station;

import com.bsm.v4.domain.security.mapper.business.station.RsbtStationBakMapper;
import com.bsm.v4.system.model.dto.business.station.StationCountyNumDTO;
import com.bsm.v4.system.model.dto.business.station.StationDTO;
import com.bsm.v4.system.model.entity.business.stationbak.RsbtStationBak;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationBakService extends BasicService<RsbtStationBak> {

    @Autowired
    private RsbtStationBakMapper rsbtStationBakMapper;

    public List<RsbtStationBak> findByGuids(List<String> guids, String appGuid) {
        return rsbtStationBakMapper.findByGuids(guids, appGuid);
    }

    /**
     * 根据app_guid和ATTRIBUTE_STATION 查询数据
     *
     * @param appGuid  appGuid
     * @param dataType dataType
     * @param genNum   genNum
     * @return list
     */
    public List<RsbtStationBak> findByAppGuidAndIsShow(String genNum, String dataType, String appGuid) {
        return rsbtStationBakMapper.findByAppGuidAndIsShow(genNum, dataType, appGuid);
    }

    /**
     * 根据app_guid和IS_SHOW 查询数据
     *
     * @param appGuid appGuid
     * @return int
     */
    public int findUnCheckNumByAppGuidAndIsShow(String appGuid) {
        return rsbtStationBakMapper.findUnCheckNumByAppGuidAndIsShow(appGuid);
    }

    /**
     * 根据app_guid查询数据
     *
     * @param appGuid appGuid
     * @return int
     */
    public int findAllNumByAppGuidAndIsShow(String appGuid) {
        return rsbtStationBakMapper.findAllNumByAppGuidAndIsShow(appGuid);
    }

    /**
     * 根据app_guid查询数据
     *
     * @param appGuid appGuid
     * @return list
     */
    public List<RsbtStationBak> findAllByAppGuidAndIsShow(String appGuid) {
        return rsbtStationBakMapper.findAllByAppGuidAndIsShow(appGuid);
    }

    /**
     * 根据app_guid查询数据
     *
     * @param appGuid appGuid
     * @return int
     */
    public int findCheckByAppGuidAndIsShow(String appGuid) {
        return rsbtStationBakMapper.findCheckByAppGuidAndIsShow(appGuid);
    }

    public List<RsbtStationBak> findByPage(String appGuid, String isValid, String dataType, String genNum, int startRows, int endRows, String statName, String statAddr) {
        return rsbtStationBakMapper.findByPage(appGuid, isValid, dataType, genNum, startRows, endRows, statName, statAddr);
    }

    public List<RsbtStationBak> findByAppGuid(String appGuid) {
        return rsbtStationBakMapper.findByAppGuid(appGuid);
    }

    public int updateIsSync(List<String> guids, String appGuid) {
        return rsbtStationBakMapper.updateIsSync(guids, appGuid);
    }

    public int findCount(String appGuid, String isValid, String dataType, String genNum, String statName, String statAddr) {
        return rsbtStationBakMapper.findCount(appGuid, isValid, dataType, genNum, statName, statAddr);
    }

    public int findCountByAppGuid(String appGuid) {
        return rsbtStationBakMapper.findCountByAppGuid(appGuid);
    }

    public List<RsbtStationBak> findNotPassData(String appGuid) {
        return rsbtStationBakMapper.findNotPassData(appGuid);
    }

    public RsbtStationBak findByCondition(String appGuid, String stCCode) {
        return rsbtStationBakMapper.findByCondition(appGuid, stCCode);
    }

    /**
     * 查询经纬度范围内的台站
     *
     * @param appGuid appGuid
     * @param highLat highLat
     * @param highLng highLng
     * @param lowLat  lowLat
     * @param lowLng  lowLng
     * @return json
     */
    public List<StationDTO> findAllBtsInMap(Double lowLng, Double lowLat, Double highLng, Double highLat, String appGuid) {
        return rsbtStationBakMapper.findAllBtsInMap(lowLng, lowLat, highLng, highLat, appGuid);
    }

    /**
     * 查看基站信息
     *
     * @param appGuid appGuid
     * @param guid    guid
     * @return json
     */
    public StationDTO getStationInfo(String guid, String appGuid) {
        return rsbtStationBakMapper.getStationInfo(guid, appGuid);
    }

    /**
     * 查询经纬度范围内的台站数量
     *
     * @param appGuid appGuid
     * @return json
     */
    public List<StationCountyNumDTO> countByBtsInMap(String appGuid) {
        return rsbtStationBakMapper.countByBtsInMap(appGuid);
    }
}
