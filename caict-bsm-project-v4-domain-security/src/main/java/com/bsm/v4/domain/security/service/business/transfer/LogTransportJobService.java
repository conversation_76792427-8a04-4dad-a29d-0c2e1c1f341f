package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.LogTransportJobMapper;
import com.bsm.v4.system.model.contrust.DBBoolConst;
import com.bsm.v4.system.model.dto.business.transfer.LogTransportJobDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.system.model.dto.business.transfer_in.ConfirmDTO;
import com.bsm.v4.system.model.entity.business.transfer.LogTransportJob;
import com.bsm.v4.system.model.vo.business.transfer.LogTransportJobVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class LogTransportJobService extends BasicService<LogTransportJob> {

    @Autowired
    private LogTransportJobMapper logTransportJobMapper;

    /**
     * 根据日志类型和文件id查询
     */
    public List<LogTransportJobDTO> findAllByLogTypeFileId(Long logType, String fileGuid) {
        return logTransportJobMapper.findAllByLogTypeFileId(logType, fileGuid);
    }

    /**
     * 下载校验后错误的日志
     *
     * @param fileGuid
     * @return
     */
    public List<LogTransportJob> findErrorDataByFileId(String fileGuid) {
        return logTransportJobMapper.findErrorDataByFileId(fileGuid);
    }

    /**
     * 设置Log数据
     */
    public LogTransportJob setLogTransportJob(String jobId, Long type, String brief, String detail, String fileGuid) {
        LogTransportJob logTransportJob = new LogTransportJob();
        logTransportJob.setIsDeleted(DBBoolConst.FALSE);
        logTransportJob.setGmtCreate(new Date());
        logTransportJob.setGmtModified(new Date());
        logTransportJob.setJobGuid(jobId);
        logTransportJob.setLogType(type);
        logTransportJob.setLogShort(brief);
        logTransportJob.setLogDetail(detail);
        logTransportJob.setFileGuid(fileGuid);
        return logTransportJob;
    }

    /**
     * 根据fileId删除错误日志
     */
    public void deleteByFileId(String fileId) {
        logTransportJobMapper.deleteByFileId(fileId);
    }

    /**
     * 根据日志类型和FileId查询总条数
     */
    public int selectCountByTypeFileId(Long logType, String fileId) {
        return logTransportJobMapper.selectCountByTypeFileId(logType, fileId);
    }

    /**
     * 初始加载登录用户待办任务-运营商
     */
    public List<ConfirmDTO> getTaskDataForOperator(String userId) {
        return logTransportJobMapper.getTaskDataForOperator(userId);
    }

    /**
     * 初始加载登录用户待办任务-地市无委
     */
    public List<LogTransportJobDTO> getTaskDataForCity(String userId) {
        return logTransportJobMapper.getTaskDataForCity(userId);
    }

    /**
     * 初始加载登录用户待办任务-省级无委or管理员
     */
    public List<LogTransportJobDTO> getTaskDataForProvincialAdmin() {
        return logTransportJobMapper.getTaskDataForProvincialAdmin();
    }

    /**
     * 校验成功数据导出
     */
    public List<LogTransportJobDTO> findExportPassTaskDetail(LogTransportJobVO logTransportJobVO) {
        return logTransportJobMapper.findExportPassTaskDetail(logTransportJobVO);
    }

    /**
     * 校验不通过数据导出
     */
    public List<LogTransportJobDTO> findExportFailedTaskDetail(LogTransportJobVO logTransportJobVO) {
        return logTransportJobMapper.findExportFailedTaskDetail(logTransportJobVO);
    }

    public List<TransportRawBtsDealDTO> findByJobId(String jobId){
        return logTransportJobMapper.findByJobId(jobId);
    }
}
