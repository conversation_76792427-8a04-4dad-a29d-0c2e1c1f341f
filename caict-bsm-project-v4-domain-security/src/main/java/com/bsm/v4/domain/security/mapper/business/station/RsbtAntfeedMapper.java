package com.bsm.v4.domain.security.mapper.business.station;

import com.bsm.v4.system.model.entity.business.station.RsbtAntfeed;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtAntfeedMapper extends BasicMapper<RsbtAntfeed> {

    /**
     * 根据基站id和扇区id查询
     */
    @Select("select * from RSBT_ANTFEED LEFT JOIN RSBT_ANTFEED_T ON RSBT_ANTFEED.GUID = RSBT_ANTFEED_T.GUID" +
            " WHERE RSBT_ANTFEED.STATION_GUID = #{stationGuid} and RSBT_ANTFEED_T.AT_CCODE = #{cellId}")
    RsbtAntfeed findOneByStationCell(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);
}
