package com.bsm.v4.domain.security.service.business.applytable;

import com.bsm.v4.domain.security.mapper.business.applytable.ApplyLinkMapper;
import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class ApplyLinkService extends BasicService<ApplyLink> {

    @Autowired
    private ApplyLinkMapper applyLinkMapper;

    public void updateByJobGuid(String jobGuid, String syncStatus) {
        applyLinkMapper.updateByJobGuid(jobGuid, syncStatus);
    }

    public int updateAppCode(String oldAppCode, String newAppCode) {
        return applyLinkMapper.updateAppCode(oldAppCode, newAppCode);
    }

    public int updateByAppCode(String appCode, String status) {
        return applyLinkMapper.updateByAppCode(appCode, status);
    }
}
