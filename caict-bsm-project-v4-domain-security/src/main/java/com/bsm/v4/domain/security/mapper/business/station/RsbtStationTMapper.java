package com.bsm.v4.domain.security.mapper.business.station;

import com.bsm.v4.system.model.dto.business.station.StationNetDTO;
import com.bsm.v4.system.model.entity.business.station.RsbtStationT;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RsbtStationTMapper extends BasicMapper<RsbtStationT> {

    @Select("SELECT a.GUID, b.ST_C_CODE,a.NET_GUID FROM RSBT_STATION a LEFT JOIN RSBT_STATION_T b ON a.GUID = b.GUID WHERE EXISTS ( " +
            "SELECT DISTINCT BTS_ID, BTS_NAME, TECH_TYPE, LOCATION, COUNTY, ALTITUDE,LATITUDE, LONGITUDE, DATA_TYPE,IS_HANDLE, USER_GUID, JOB_GUID " +
            "FROM APPROVAL_SCHEDULE d WHERE d.APP_GUID = #{appGuid} AND d.DATA_TYPE = 1  AND b.ST_C_CODE = d.BTS_ID )")
    List<StationNetDTO> findExistRsbtStationByAppGuid(@Param("appGuid") String appGuid);


}
