package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.TransportRawBtsDealLogMapper;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.bsm.v4.system.model.dto.business.transfer.TransportRawBtsDealLogDTO;
import com.bsm.v4.system.model.entity.business.transfer.TransportRawBtsDealLog;
import com.bsm.v4.system.model.vo.business.transfer.TransportJobVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportRawBtsDealLogService extends BasicService<TransportRawBtsDealLog> {

    @Autowired
    private TransportRawBtsDealLogMapper transportRawBtsDealLogMapper;

    /**
     * 查询任务不同状态的数量
     */
    public List<TransportRawBtsDealLogDTO> selectCountByUserGroupValid(String jobGuid) {
        return transportRawBtsDealLogMapper.selectCountByUserGroupValid(jobGuid);
    }

    /**
     * 查询任务日志详细
     */
    public List<TransportRawBtsDealLogDTO> findAllDetailByJob(String jobGuid) {
        return transportRawBtsDealLogMapper.findAllDetailByJob(jobGuid);
    }

    /**
     * 生成下载数据
     */
    public int insertByTransportRawBts(String jobGuid) {
        return transportRawBtsDealLogMapper.insertByTransportRawBts(jobGuid);
    }

    /**
     * 查询数据
     *
     * @param jobId jobId
     * @param btsId btsId
     * @return list
     */
    public List<TransportRawBtsDealLog> findByBtsIdJobGuid(String btsId, String jobId) {
        return transportRawBtsDealLogMapper.findByBtsIdJobGuid(btsId, jobId);
    }

    /**
     * 根据btsId更新数据
     *
     * @param jobId  jobId
     * @param btsIds list
     */
    public void updateByBtsIds(String dataType, String jobId, List<String> btsIds, String regionCode) {
        transportRawBtsDealLogMapper.updateByBtsIds(dataType, jobId, btsIds, regionCode);
    }

    public List<TransportRawBtsDealDTO> findAllPageNew(String userId, TransportRawBtsDealDTO transportRawBtsDealDTO) {
        return transportRawBtsDealLogMapper.findAllPageNew(userId, transportRawBtsDealDTO);
    }

    public List<TransportRawBtsDealDTO> findDealDataListR(TransportJobVO vo){
        return transportRawBtsDealLogMapper.findDealDataListR(vo);
    }
}
