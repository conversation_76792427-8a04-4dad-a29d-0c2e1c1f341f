package com.bsm.v4.domain.security.service.business.transfer;

import com.bsm.v4.domain.security.mapper.business.transfer.ApprovalScheduleMapper;
import com.bsm.v4.system.model.dto.business.station.StationScheduleDataDTO;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalRawBts;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalSchedule;
import com.bsm.v4.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.bsm.v4.system.model.entity.business.transfer.CoordinateScheduleTemp;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class ApprovalScheduleService extends BasicService<ApprovalSchedule> {

    @Autowired
    private ApprovalScheduleMapper approvalScheduleMapper;

    /**
     * 根据appGuid插入审核日志
     */
    public int InsertApprovalScheduleLogByAppGuid(String appGuid, Long isApproved) {
        return approvalScheduleMapper.InsertApprovalScheduleLogByAppGuid(appGuid, isApproved);
    }

    /**
     * 根据appGuid查询返回日志记录
     */
    public List<ApprovalScheduleLog> findAllApprovalScheduleLogByAppGuid(String appGuid, Long isApproved) {
        return approvalScheduleMapper.findAllApprovalScheduleLogByAppGuid(appGuid, isApproved);
    }

    /**
     * 根据appGuid、类型审核数据数量
     */
    public int selectCountStationScheduleDataDTOPageByJobIdDataType(String appGuid, String dataType) {
        return approvalScheduleMapper.selectCountStationScheduleDataDTOPageByJobIdDataType(appGuid, dataType);
    }

    /**
     * 根据appGuid、类型审核数据
     */
    public List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataType(String appGuid, String dataType) {
//        if (dataType.equals("1")) {
//            return approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdAdd(appGuid, dataType);
//        } else {
//            return approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdUpdate(appGuid, dataType);
//        }
        List<StationScheduleDataDTO> transportScheduleListRedis;
        if (dataType.equals("3")) {
            transportScheduleListRedis = approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdDataTypeUpdate(appGuid, dataType, "3", "3", "3");
        } else if (dataType.equals("2")) {
            transportScheduleListRedis = approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdDataTypeUpdate(appGuid, dataType, "1", "2", "3");
            // 去重
            if (transportScheduleListRedis.size() > 1) {
                transportScheduleListRedis = transportScheduleListRedis.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StationScheduleDataDTO::getBtsId))), ArrayList::new));
            }
        } else {
            transportScheduleListRedis = approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdDataTypeAdd(appGuid, dataType, "1", "2", "3");
            if (transportScheduleListRedis.size() > 1) {
                transportScheduleListRedis = transportScheduleListRedis.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StationScheduleDataDTO::getBtsId))), ArrayList::new));
            }
        }
        return transportScheduleListRedis;
    }

    /**
     * 根据appGuid查询返回干扰协调数据
     */
    public List<CoordinateScheduleTemp> findAllCoordinateScheduleByAppGuid(String jobGuid, String appGuid) {
        return approvalScheduleMapper.findAllCoordinateScheduleByAppGuid(jobGuid, appGuid);
    }

    /**
     * 根据appGuid查询ApprovalRawBts
     */
    public List<ApprovalRawBts> findAllByAppGuid(String appGuid) {
        return approvalScheduleMapper.findAllByAppGuid(appGuid);
    }

    /**
     * 根据jobGuid查询总数
     */
    public int selectCountByJob(String jobGuid) {
        return approvalScheduleMapper.selectCountByJob(jobGuid);
    }

    public String selectIsValidByBtsId(String btsId, String userGuid, String techType) {
        return approvalScheduleMapper.selectIsValidByBtsId(btsId, userGuid, techType);
    }

    /**
     * 添加审核待办数据
     */
    public int insertByTransportRawBts(String appGuid) {
        return approvalScheduleMapper.insertByTransportRawBts(appGuid);
    }

    /**
     * 根据job删除
     */
    public int deleteAllByJob(String jobGuid) {
        return approvalScheduleMapper.deleteAllByJob(jobGuid);
    }


    /**
     * 根据appGuid删除全部
     */
    public int deleteAllByAppGuid(String appGuid) {
        return approvalScheduleMapper.deleteAllByAppGuid(appGuid);
    }

    /**
     * 根据审核记录集合批量删除
     */
    public void deleteBatchByApprovalScheduleLog(List<ApprovalScheduleLog> approvalScheduleLogList) {
        List<Object> ids = Collections.synchronizedList(new ArrayList<>());
        approvalScheduleLogList.parallelStream().forEach(approvalScheduleLog -> ids.add(approvalScheduleLog.getGuid()));
        this.deleteBatch(ids);
    }
}
