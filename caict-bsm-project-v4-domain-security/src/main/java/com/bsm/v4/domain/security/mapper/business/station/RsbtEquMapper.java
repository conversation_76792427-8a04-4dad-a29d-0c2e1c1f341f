package com.bsm.v4.domain.security.mapper.business.station;

import com.bsm.v4.system.model.entity.business.station.RsbtEqu;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtEquMapper extends BasicMapper<RsbtEqu> {

    /**
     * 根据基站id和扇区id查询
     */
    @Select("select * from RSBT_EQU LEFT JOIN RSBT_EQU_T ON RSBT_EQU.GUID = RSBT_EQU_T.GUID\n" +
            "where RSBT_EQU.STATION_GUID = #{stationGuid} and RSBT_EQU_T.ET_EQU_CCODE = #{cellId}")
    RsbtEqu findOneByStationSection(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);
}
