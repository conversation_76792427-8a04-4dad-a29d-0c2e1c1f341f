package com.bsm.v4.domain.security.mapper.business.applytable;

import com.bsm.v4.system.model.entity.business.applytable.ApplyLink;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Repository
public interface ApplyLinkMapper extends BasicMapper<ApplyLink> {

    @Update("update Apply_link set sync_status = #{syncStatus} where job_guid = #{jobGuid}")
    void updateByJobGuid(@Param("jobGuid") String jobGuid, @Param("syncStatus") String syncStatus);

    @Update("UPDATE APPLY_lINK SET app_code_in = #{newAppCode} where app_code_in = #{oldAppCode}")
    int updateAppCode(@Param("oldAppCode") String oldAppCode, @Param("newAppCode") String newAppCode);

    @Update("UPDATE APPLY_lINK SET SYNC_STATUS = #{status} where app_code_in = #{appCode}")
    int updateByAppCode(@Param("appCode") String appCode, @Param("status") String status);
}
