package com.bsm.v4.domain.security.mapper.security;

import com.bsm.v4.system.model.dto.security.UsersDTO;
import com.bsm.v4.system.model.entity.security.Login;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2019-10-24.
 */
@Repository
public interface LoginMapper extends BasicMapper<Login> {


    @Select("select sys_users.*," +
            "sys_login.login_name,sys_login.last_date,sys_login.last_way,sys_login.last_ip,sys_login.user_id " +
            "from sys_login,sys_users where sys_login.user_id = sys_users.id and sys_login.login_name = #{loginName} and sys_login.password = #{password}")
    @Results({
            @Result(property = "userId", column = "user_id"),
            @Result(property = "orgDTO", column = "user_id", one = @One(select = "com.bsm.v4.domain.security.mapper.security.RsbtOrgMapper.findOneDetailsByUsers")),
            @Result(property = "roleDTO", column = "user_id", one = @One(select = "com.bsm.v4.domain.security.mapper.security.RoleUsersMapper.findRoleByUser")),
            @Result(property = "regionId", column = "region_id"),
            @Result(property = "regionDTO", column = "region_id", one = @One(select = "com.bsm.v4.domain.security.mapper.security.RegionMapper.findOneById"))
    })
    UsersDTO login(@Param("loginName") String loginName, @Param("password") String password);

    @Update("update sys_login set password = #{password} where login_name = #{loginName}")
    int updatePasswordByLogin(@Param("loginName") String loginName, @Param("password") String password);

    @Select("select * from sys_login where user_id = #{userId}")
    Login findAllByUserId(@Param("userId") String userId);


    /**
     * 根据用户删除
     */
    @Delete("delete from sys_login where user_id = #{userId}")
    int deleteByUser(@Param("userId") String userId);
}
