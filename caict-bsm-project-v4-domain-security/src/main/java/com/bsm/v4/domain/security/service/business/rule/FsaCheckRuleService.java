package com.bsm.v4.domain.security.service.business.rule;

import com.bsm.v4.domain.security.mapper.business.rule.FsaCheckRuleMapper;
import com.bsm.v4.system.model.entity.business.rule.FsaCheckRule;
import com.bsm.v4.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.bsm.v4.system.model.vo.business.rule.FsaCheckRuleVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FsaCheckRuleService extends BasicService<FsaCheckRule> {

    @Autowired
    private FsaCheckRuleMapper fsaCheckRuleMapper;

    /**
     * 条件查询总数
     */
    public int selectCountByWhere(FsaCheckRuleVO fsaCheckRuleVO, String userId) {
        return fsaCheckRuleMapper.selectCountByWhere(fsaCheckRuleVO, userId);
    }

    /**
     * 分页条件查询
     */
    public List<FsaCheckRuleDTO> findAllPageByWhere(FsaCheckRuleVO fsaCheckRuleVO) {
        return fsaCheckRuleMapper.findAllPageByWhere(fsaCheckRuleVO);
    }

    /**
     * 根据guid查询单个详情
     */
    public FsaCheckRuleDTO findOneDto(String id) {
        return fsaCheckRuleMapper.findOneDto(id);
    }

    /**
     * 修改全部状态
     */
    public int updateAllState(String state) {
        return fsaCheckRuleMapper.updateAllState(state);
    }

    /**
     * 入库规则数量查询
     */
    public int selectCountByOrgAndNetTsAndGenNum(FsaCheckRuleVO fsaCheckRuleVO) {
        return fsaCheckRuleMapper.selectCountByOrgAndNetTsAndGenNum(fsaCheckRuleVO);
    }

    public List<FsaCheckRuleDTO> findByOrgGuidAndTechType(String type, String netType) {
        return fsaCheckRuleMapper.findByOrgGuidAndTechType(type, netType);
    }

    public List<FsaCheckRuleDTO> findByOrgGuidAndGenNum(String orgType, String genNum) {
        return fsaCheckRuleMapper.findByOrgGuidAndGenNum(orgType, genNum);
    }
}
