package com.bsm.v4.domain.security.mapper.business.transfer;

import com.bsm.v4.system.model.entity.business.transfer.AsyncRawBts;
import com.bsm.v4.system.model.entity.es.EsAsyncRawBts;
import com.bsm.v4.system.model.vo.es.EsAsyncRawBtsVO;
import com.caictframework.data.mapper.BasicMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Repository
public interface AsyncRawBtsMapper extends BasicMapper<AsyncRawBts> {

    @Select("SELECT COUNT(ASYNC_RAW_BTS.GUID)\n" +
            "FROM ASYNC_RAW_BTS\n" +
            "LEFT JOIN BSM_APPLYTABLE ON BSM_APPLYTABLE.APPLYTABLE_CODE = ASYNC_RAW_BTS.APPLICATION_NO " +
            "AND ASYNC_RAW_BTS.IS_HANDLE = 1 AND BSM_APPLYTABLE.IS_DELETED = 0 " +
            "AND ASYNC_RAW_BTS.DATA_TYPE = 1 " +
            "AND (TO_CHAR(BSM_APPLYTABLE.OP_DATE,'yyyy-MM-dd HH24:MI:SS') <= #{startDate} OR NVL(#{startDate},'NULL')='NULL') " +
            "AND (TO_CHAR(BSM_APPLYTABLE.OP_DATE,'yyyy-MM-dd HH24:MI:SS') >= #{endDate} OR NVL(#{endDate},'NULL')='NULL') " +
            "AND (ASYNC_RAW_BTS.COUNTY = #{areaCode,jdbcType=VARCHAR} OR #{areaCode,jdbcType=VARCHAR} is null) " +
            "${sql} \n" +
            "union all " +
            "SELECT COUNT(ASYNC_RAW_BTS.GUID)\n" +
            "FROM ASYNC_RAW_BTS\n" +
            "LEFT JOIN BSM_APPLYTABLE ON BSM_APPLYTABLE.APPLYTABLE_CODE = ASYNC_RAW_BTS.APPLICATION_NO " +
            "AND ASYNC_RAW_BTS.IS_HANDLE = 1 AND BSM_APPLYTABLE.IS_DELETED = 0 " +
            "AND ASYNC_RAW_BTS.DATA_TYPE = 2 " +
            "AND (TO_CHAR(BSM_APPLYTABLE.OP_DATE,'yyyy-MM-dd HH24:MI:SS') <= #{startDate} OR NVL(#{startDate},'NULL')='NULL') " +
            "AND (TO_CHAR(BSM_APPLYTABLE.OP_DATE,'yyyy-MM-dd HH24:MI:SS') >= #{endDate} OR NVL(#{endDate},'NULL')='NULL') " +
            "AND (ASYNC_RAW_BTS.COUNTY = #{areaCode,jdbcType=VARCHAR} OR #{areaCode,jdbcType=VARCHAR} is null) " +
            "${sql} \n" +
            "union all " +
            "SELECT COUNT(ASYNC_RAW_BTS.GUID)\n" +
            "FROM ASYNC_RAW_BTS\n" +
            "LEFT JOIN BSM_APPLYTABLE ON BSM_APPLYTABLE.APPLYTABLE_CODE = ASYNC_RAW_BTS.APPLICATION_NO " +
            "AND ASYNC_RAW_BTS.IS_HANDLE = 1 AND BSM_APPLYTABLE.IS_DELETED = 0 " +
            "AND ASYNC_RAW_BTS.DATA_TYPE = 3 " +
            "AND (TO_CHAR(BSM_APPLYTABLE.OP_DATE,'yyyy-MM-dd HH24:MI:SS') <= #{startDate} OR NVL(#{startDate},'NULL')='NULL') " +
            "AND (TO_CHAR(BSM_APPLYTABLE.OP_DATE,'yyyy-MM-dd HH24:MI:SS') >= #{endDate} OR NVL(#{endDate},'NULL')='NULL') " +
            "AND (ASYNC_RAW_BTS.COUNTY = #{areaCode,jdbcType=VARCHAR} OR #{areaCode,jdbcType=VARCHAR} is null) " +
            "${sql} "
    )
    List<Integer> selectCountByOpType(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("areaCode") String areaCode, @Param("sql") String sql);

    /**
     * 根据扇区编号和用户查询
     */
    @Select("select * from ASYNC_RAW_BTS where CELL_ID = #{cellId} and USER_GUID = #{userId}")
    AsyncRawBts selectOneByCellIdAndUserId(@Param("cellId") String cellId, @Param("userId") String userId);

    /**
     * 根据基站编号和扇区号拆线呢
     */
    @Select("select * from ASYNC_RAW_BTS where BTS_ID = #{btsId} and CELL_ID = #{cellId} and TECH_TYPE = #{techType}")
    AsyncRawBts selectOneByBtsIdCellId(@Param("btsId") String btsId, @Param("cellId") String cellId, @Param("techType") String techType);

    /**
     * 根据扇区id查询一条记录
     */
    @Select("select * from ASYNC_RAW_BTS where BTS_ID = #{btsId} and CELL_ID = #{cellId} and TECH_TYPE = #{techType} ")
    AsyncRawBts getOneByCellId(@Param("btsId") String btsId, @Param("cellId") String cellId, @Param("techType") String techType);

    /**
     * 根据userId修改处理状态
     */
    @Update("update ASYNC_RAW_BTS set IS_HANDLE = #{isHandle} where USER_GUID = #{userId}")
    int updateIsHandleByUserid(@Param("isHandle") String isHandle, @Param("userId") String userId);

    /**
     * 根据运营商类型修改处理状态
     */
    @Update("update ASYNC_RAW_BTS set IS_HANDLE = #{isHandle} where ORG_TYPE = #{orgType}")
    int updateIsHandleByOrgType(@Param("isHandle") String isHandle, @Param("orgType") String orgType);

    /**
     * 查询上传数据中没有的数据
     */
    @Select("select * from ASYNC_RAW_BTS where USER_GUID = #{userId} and (BTS_ID || '-'|| CELL_ID) not in(select (BTS_ID || '-' || CELL_ID) from TRANSPORT_RAW_BTS where JOB_GUID = #{jobGuid}) ")
    List<AsyncRawBts> findAllNotTransport(@Param("jobGuid") String jobGuid, @Param("userId") String userId);

    /**
     * 根据运营商、数据状态删除
     */
    @Delete("delete from ASYNC_RAW_BTS where USER_GUID = #{userId} and DATA_TYPE = #{dataType}")
    int deleteAllByUserDataType(@Param("userId") String userId, @Param("dataType") String dataType);

    /**
     * 对比路测数据
     *
     * @param roadCellIds
     * @return
     */
    @Select("<script>" +
            "select CELL_ID, LONGITUDE, LATITUDE from ASYNC_RAW_BTS where 1 = 1 " +
            " <foreach item='item' collection='roadCellIds' separator='or' open='and (' close=')' index=''>" +
            "  CELL_ID = #{item} " +
            "</foreach>" +
            "</script>")
    List<AsyncRawBts> contrastWithRoadData(@Param("roadCellIds") String[] roadCellIds);

    @Delete("delete from ASYNC_RAW_BTS where bts_id = #{btsId} " +
            "and cell_id = #{cellCode}")
    int deleteByBtsIdCellCode(@Param("btsId") String btsId, @Param("cellCode") String cellCode);

    @Select("select aa.up_date from( " +
            "select up_date,rownum rn from ASYNC_RAW_BTS a where bts_id = #{statCode} order by up_date desc ) aa " +
            "where aa.rn=1")
    Date findByUpdate(@Param("statCode") String statCode);

    /**
     * 根据btsIds 查询总表数据
     *
     * @param btsIds btsIds
     * @return list
     */
    @Select("<script>" +
            "select * from ASYNC_RAW_BTS where 1 = 1 AND BTS_ID IN " +
            " <foreach item='item' collection='btsIds' separator=',' open='(' close=')' index=''>" +
            " #{item} " +
            "</foreach>" +
            "</script>")
    List<AsyncRawBts> findByBtsIds(@Param("btsIds") List<String> btsIds);

    /**
     * 根据btsIds 刪除
     */
    @Delete("<script>" +
            "delete from ASYNC_RAW_BTS where bts_id in " +
            " <foreach item='item' collection='btsIds' separator=',' open='(' close=')' index=''>" +
            "#{item} " +
            "</foreach>" +
            " and JOB_GUID = #{guid} </script>")
    void deleteByBtsIds(@Param("btsIds") List<String> btsIds, @Param("guid") String guid);

    /**
     * 根据btsId 查询总表数据
     *
     * @param btsId btsId
     * @return list
     */
    @Select("select * from ASYNC_RAW_BTS where bts_id = #{btsId} " +
            "or bts_id like concat(concat('%',#{btsId}),'%') ")
    List<AsyncRawBts> findByBtsId(@Param("btsId")String btsId);

    /**
     * 生成使用率评价excel
     *
     * @param vo vo
     * @return list
     */
    @Select("<script>select a.*, u.NAME orgName from ASYNC_RAW_BTS a left join TRANSPORT_JOB j on a.JOB_GUID = j.GUID left join FSA_USERS u on u.ID = a.USER_GUID" +
            " where j.GMT_CREATE &gt;= to_date('2023-08-20 00:00:00','yyyy-mm-dd hh24:mi:ss') " +
            "<if test='vo.techType != null and vo.techType != \"\"'> AND a.TECH_TYPE = #{vo.techType,jdbcType=VARCHAR}</if> "+
            "<if test='vo.county != null and vo.county != \"\"'> AND a.COUNTY = #{vo.county,jdbcType=VARCHAR}</if> "+
            "<if test='vo.genNum != null and vo.genNum != \"\"'> AND a.GEN_NUM = #{vo.genNum,jdbcType=VARCHAR}</if> "+
            "<if test='vo.orgType != null and vo.orgType != \"\"'> AND a.ORG_TYPE = #{vo.orgType,jdbcType=VARCHAR}</if> "+
            "<if test='vo.stScene != null and vo.stScene != \"\"'> AND a.ST_SCENE = #{vo.stScene,jdbcType=VARCHAR}</if> "+
            "</script>")
    List<EsAsyncRawBts> searchUsageEvaluation(@Param("vo") EsAsyncRawBtsVO vo);
}
