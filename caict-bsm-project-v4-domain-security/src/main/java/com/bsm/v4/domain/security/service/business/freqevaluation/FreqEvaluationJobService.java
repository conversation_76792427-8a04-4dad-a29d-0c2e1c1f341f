package com.bsm.v4.domain.security.service.business.freqevaluation;

import com.bsm.v4.domain.security.mapper.business.freqevaluation.FreqEvaluationJobMapper;
import com.bsm.v4.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.bsm.v4.system.model.vo.business.freqevaluation.FreqEvaluationJobVO;
import com.caictframework.data.service.BasicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationJobService extends BasicService<FreqEvaluationJob> {

    @Autowired
    private FreqEvaluationJobMapper mapper;

    /**
     * 根据条件查询
     *
     * @param vo vo
     * @return list
     */
    public List<FreqEvaluationJob> findAllByWhere(FreqEvaluationJobVO vo) {
        return mapper.findAllByWhere(vo);
    }
}
