<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caict-bsm-project-v4</artifactId>
        <groupId>com.bsm.v4</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>caict-bsm-project-v4-domain-security</artifactId>
    <packaging>jar</packaging>

    <name>caict-bsm-project-v4-domain-security</name>
    <description>领域模块-安全资源管理</description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.bsm.v4</groupId>
            <artifactId>caict-bsm-project-v4-system-model</artifactId>
            <version>${system-model.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.bsm.v4</groupId>-->
<!--            <artifactId>caict-bsm-project-v4-api-web</artifactId>-->
<!--            <version>1.0-SNAPSHOT</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->

        <!--导出pdf-->
        <!-- https://mvnrepository.com/artifact/com.itextpdf/itextpdf -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.3.4</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.itextpdf/itext-asian -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
    </dependencies>

</project>